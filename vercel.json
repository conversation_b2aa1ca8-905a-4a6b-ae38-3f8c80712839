{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "regions": ["iad1"], "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}, {"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}], "redirects": [{"source": "/dashboard/(.*)", "has": [{"type": "cookie", "key": "sb-access-token", "value": "(?<token>.*)"}], "destination": "/dashboard/$1", "permanent": false}], "rewrites": [{"source": "/api/stripe/webhook", "destination": "/api/stripe/webhook"}], "env": {"NEXT_PUBLIC_APP_NAME": "Pulse.co.za", "NEXT_PUBLIC_APP_URL": "https://your-vercel-url.vercel.app", "NEXT_PUBLIC_SUPABASE_URL": "https://onrdrxddfmcsftmdqgit.supabase.co", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9ucmRyeGRkZm1jc2Z0bWRxZ2l0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0Nzg4MTMsImV4cCI6MjA2NTA1NDgxM30.2HEf2Illj4bKpxfGqbHjxnzqVxRlAWPZz3fP1LYJaGc"}}