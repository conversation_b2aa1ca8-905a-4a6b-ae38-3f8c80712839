-- Create blogs table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.blogs (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  title text NOT NULL,
  slug text UNIQUE NOT NULL,
  content text NOT NULL,
  author text NOT NULL,
  published_at timestamp with time zone DEFAULT now() NOT NULL,
  image_url text,
  tags text[],
  is_published boolean DEFAULT FALSE,
  view_count integer DEFAULT 0
);

-- Create material_type enum if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'material_type') THEN
        CREATE TYPE public.material_type AS ENUM ('pdf', 'video', 'audio', 'guide');
    END IF;
END
$$;

-- Create helpful_materials table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.helpful_materials (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  title text NOT NULL,
  description text,
  type public.material_type NOT NULL,
  category text NOT NULL,
  file_url text,
  file_size text,
  duration text,
  download_count integer DEFAULT 0,
  featured boolean DEFAULT FALSE,
  created_at timestamp with time zone DEFAULT now() NOT NULL,
  updated_at timestamp with time zone DEFAULT now() NOT NULL,
  CONSTRAINT helpful_materials_pkey PRIMARY KEY (id)
);

-- Seed data for blogs
INSERT INTO public.blogs (title, slug, content, author, is_published, tags, image_url, view_count)
VALUES
('The Ultimate Guide to Strength Training', 'ultimate-strength-training', 'This comprehensive guide covers everything you need to know about strength training, from beginner routines to advanced techniques. Learn how to build muscle, increase strength, and transform your body.', 'John Doe', TRUE, ARRAY['fitness', 'training'], 'https://picsum.photos/seed/strength/800/450', 150),
('Healthy Eating Habits for a Fitter You', 'healthy-eating-habits', 'Discover the power of nutrition with these simple yet effective healthy eating habits. Fuel your body, boost your energy, and achieve your fitness goals with a balanced diet.', 'Jane Smith', TRUE, ARRAY['nutrition', 'wellness'], 'https://picsum.photos/seed/eating/800/450', 200),
('Mindfulness for Athletes: Enhancing Performance', 'mindfulness-athletes', 'Explore how mindfulness and meditation can elevate your athletic performance. Improve focus, reduce stress, and gain a mental edge in your sport.', 'Emily White', TRUE, ARRAY['recovery', 'wellness', 'mindfulness'], 'https://picsum.photos/seed/mindfulness/800/450', 80),
('10 Tips for Staying Motivated on Your Fitness Journey', 'staying-motivated', 'Motivation can be tough to maintain. Here are 10 actionable tips to keep you inspired, committed, and consistent on your fitness journey, no matter the obstacles.', 'Chris Johnson', TRUE, ARRAY['motivation', 'wellness'], 'https://picsum.photos/seed/motivation/800/450', 120),
('Understanding HIIT: High-Intensity Interval Training', 'understanding-hiit', 'Dive into the world of High-Intensity Interval Training (HIIT). Learn what it is, its benefits, and how to incorporate it safely and effectively into your workout routine.', 'Sarah Lee', TRUE, ARRAY['fitness', 'training'], 'https://picsum.photos/seed/hiit/800/450', 95);

-- Seed data for helpful_materials
INSERT INTO public.helpful_materials (title, description, type, category, file_url, file_size, duration, download_count, featured)
VALUES
('Beginner Workout Plan PDF', 'A detailed 4-week workout plan designed for beginners to kickstart their fitness journey. Includes exercises, sets, reps, and tips.', 'pdf', 'Workout Plans', 'https://example.com/beginner_workout_plan.pdf', '1.5 MB', NULL, 75, FALSE),
('Nutrition Guide for Muscle Gain', 'A comprehensive guide on macronutrients, meal timing, and supplement recommendations for optimal muscle growth.', 'pdf', 'Nutrition', 'https://example.com/nutrition_guide_muscle_gain.pdf', '2.1 MB', NULL, 110, TRUE),
('Meditation for Stress Relief Audio', 'A 15-minute guided meditation audio session to help reduce stress and improve mental clarity.', 'audio', 'Wellness', 'https://example.com/meditation_stress_relief.mp3', NULL, '15:00', 40, FALSE),
('Advanced Strength Training Exercises', 'An advanced guide featuring complex exercises and techniques for experienced lifters looking to break plateaus.', 'pdf', 'Training', 'https://example.com/advanced_strength_exercises.pdf', '3.0 MB', NULL, 60, FALSE); 