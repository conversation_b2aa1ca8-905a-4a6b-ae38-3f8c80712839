{"name": "pulse-gym-management", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "setup": "node scripts/setup-env.js", "type-check": "tsc --noEmit", "build:analyze": "ANALYZE=true npm run build", "preview": "npm run build && npm run start", "clean": "rm -rf .next out", "postinstall": "npm run type-check"}, "dependencies": {"@google/generative-ai": "^0.13.0", "@headlessui/react": "^2.1.0", "@hookform/resolvers": "^3.6.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.1", "@stripe/stripe-js": "^4.1.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-query": "^5.45.1", "axios": "^1.7.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "framer-motion": "^12.16.0", "input-otp": "^1.2.4", "lucide-react": "^0.396.0", "next": "14.2.6", "next-themes": "^0.3.0", "react": "^18", "react-confetti": "^6.4.0", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-dom-confetti": "^0.2.0", "react-hook-form": "^7.52.0", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "sonner": "^1.7.4", "stripe": "^16.2.0", "tailwind-merge": "^2.3.0", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.0", "vaul": "^0.9.1", "zod": "^3.23.8", "zustand": "^4.5.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}