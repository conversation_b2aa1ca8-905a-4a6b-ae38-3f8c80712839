;/*FB_PKG_DELIM*/

__d("FollowButtonTappedFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("5386");b=d("FalcoLoggerInternal").create("follow_button_tapped",a);e=b;g["default"]=e}),98);
__d("GenaiTransparencySponsoredAdsEventFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("6785");b=d("FalcoLoggerInternal").create("genai_transparency_sponsored_ads_event",a);e=b;g["default"]=e}),98);
__d("IGAdTransparencyDisclaimerPlacement",[],(function(a,b,c,d,e,f){a=Object.freeze({ABOVE_IMAGE:1,BELOW_IMAGE:2,OVERFLOW_MENU:3});f["default"]=a}),66);
__d("InstagramOrganicBrandProfileFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("5797");b=d("FalcoLoggerInternal").create("instagram_organic_brand_profile",a);e=b;g["default"]=e}),98);
__d("PolarisGenAITransparencyLoggingUtils",["GenaiTransparencySponsoredAdsEventFalcoEvent","react-compiler-runtime","usePartialViewImpression"],(function(a,b,c,d,e,f,g){"use strict";var h=function(a){var b={ad_client_token:a.ad_client_token,ad_id:a.ad_id,event:"genai_transparency.tier_2.dialog",event_type:a.event_type,predicted_tier:1,source_surface:a.source_surface};c("GenaiTransparencySponsoredAdsEventFalcoEvent").log(function(){return b})},i=function(a){var b={ad_client_token:a.ad_client_token,ad_id:a.ad_id,event:"genai_transparency.tier_2.menu_item",event_type:a.event_type,predicted_tier:1,source_surface:a.source_surface};c("GenaiTransparencySponsoredAdsEventFalcoEvent").log(function(){return b})},j=function(a){var b={ad_client_token:a.ad_client_token,ad_id:a.ad_id,event:"genai_transparency.tier_3.dialog",event_type:a.event_type,predicted_tier:2,source_surface:a.source_surface};c("GenaiTransparencySponsoredAdsEventFalcoEvent").log(function(){return b})},k=function(a){var b={ad_client_token:a.ad_client_token,ad_id:a.ad_id,event:"genai_transparency.tier_3.label",event_type:a.event_type,predicted_tier:2,source_surface:a.source_surface};c("GenaiTransparencySponsoredAdsEventFalcoEvent").log(function(){return b})};function a(a,b){var e=d("react-compiler-runtime").c(5),f;e[0]!==b||e[1]!==a?(f=function(){bb2:switch(b){case"genai_transparency.tier_2.dialog":h(a);break bb2;case"genai_transparency.tier_2.menu_item":i(a);break bb2;case"genai_transparency.tier_3.dialog":j(a);break bb2;case"genai_transparency.tier_3.label":k(a)}},e[0]=b,e[1]=a,e[2]=f):f=e[2];f=f;var g;e[3]!==f?(g={onImpressionStart:f},e[3]=f,e[4]=g):g=e[4];return c("usePartialViewImpression")(g)}g.logTier2DialogEvent=h;g.logTier2MenuItemEvent=i;g.logTier3DialogEvent=j;g.logTier3LabelEvent=k;g.usePartialViewImpressionEvent=a}),98);
__d("PolarisMobilePostPageHeader.next.react",["fbt","PolarisGenericMobileHeader.react","PolarisMobileHeaderWrapper.react","PolarisNavBackButton.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=i||d("react"),k=h._(/*BTDS*/"Post");function a(){var a=d("react-compiler-runtime").c(1);if(a[0]===Symbol["for"]("react.memo_cache_sentinel")){var b=[j.jsx(c("PolarisNavBackButton.react"),{analyticsContext:"postPage"},"back")];b=j.jsx(c("PolarisGenericMobileHeader.react"),{leftActions:b,title:k});b=j.jsx(c("PolarisMobileHeaderWrapper.react"),{children:b});a[0]=b}else b=a[0];return b}g["default"]=a}),226);
__d("UnfollowButtonTappedFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("698");b=d("FalcoLoggerInternal").create("unfollow_button_tapped",a);e=b;g["default"]=e}),98);
__d("usePolarisToggleFollowUserFollowMutation_instagramRelayOperation",[],(function(a,b,c,d,e,f){e.exports="24654195424170959"}),null);
__d("usePolarisToggleFollowUserFollowMutation.graphql",["usePolarisToggleFollowUserFollowMutation_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a=function(){var a={defaultValue:null,kind:"LocalArgument",name:"container_module"},c={defaultValue:null,kind:"LocalArgument",name:"inventory_source"},d={defaultValue:null,kind:"LocalArgument",name:"media_id_attribution"},e={defaultValue:null,kind:"LocalArgument",name:"nav_chain"},f={defaultValue:null,kind:"LocalArgument",name:"ranking_info_token"},g={defaultValue:null,kind:"LocalArgument",name:"target_user_id"},h=[{alias:null,args:[{fields:[{kind:"Variable",name:"container_module",variableName:"container_module"},{kind:"Variable",name:"inventory_source",variableName:"inventory_source"},{kind:"Variable",name:"media_id_attribution",variableName:"media_id_attribution"},{kind:"Variable",name:"nav_chain",variableName:"nav_chain"},{kind:"Variable",name:"ranking_info_token",variableName:"ranking_info_token"}],kind:"ObjectValue",name:"_request_data"},{kind:"Variable",name:"target_user_id",variableName:"target_user_id"}],concreteType:"XDTFriendshipStatusResponse",kind:"LinkedField",name:"xdt_api__v1__friendships__create__target_user_id",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTRelationshipInfoDict",kind:"LinkedField",name:"friendship_status",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"following",storageKey:null}],storageKey:null}],storageKey:null}];return{fragment:{argumentDefinitions:[a,c,d,e,f,g],kind:"Fragment",metadata:null,name:"usePolarisToggleFollowUserFollowMutation",selections:h,type:"Mutation",abstractKey:null},kind:"Request",operation:{argumentDefinitions:[g,a,c,f,e,d],kind:"Operation",name:"usePolarisToggleFollowUserFollowMutation",selections:h},params:{id:b("usePolarisToggleFollowUserFollowMutation_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__friendships__create__target_user_id"]},name:"usePolarisToggleFollowUserFollowMutation",operationKind:"mutation",text:null}}}();e.exports=a}),null);
__d("usePolarisToggleFollowUserUnfollowMutation_instagramRelayOperation",[],(function(a,b,c,d,e,f){e.exports="23986526720931460"}),null);
__d("usePolarisToggleFollowUserUnfollowMutation.graphql",["usePolarisToggleFollowUserUnfollowMutation_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a=function(){var a={defaultValue:null,kind:"LocalArgument",name:"container_module"},c={defaultValue:null,kind:"LocalArgument",name:"inventory_source"},d={defaultValue:null,kind:"LocalArgument",name:"media_id_attribution"},e={defaultValue:null,kind:"LocalArgument",name:"nav_chain"},f={defaultValue:null,kind:"LocalArgument",name:"ranking_info_token"},g={defaultValue:null,kind:"LocalArgument",name:"target_user_id"},h=[{alias:null,args:[{fields:[{kind:"Variable",name:"container_module",variableName:"container_module"},{kind:"Variable",name:"inventory_source",variableName:"inventory_source"},{kind:"Variable",name:"media_id_attribution",variableName:"media_id_attribution"},{kind:"Variable",name:"nav_chain",variableName:"nav_chain"},{kind:"Variable",name:"ranking_info_token",variableName:"ranking_info_token"}],kind:"ObjectValue",name:"_request_data"},{kind:"Variable",name:"target_user_id",variableName:"target_user_id"}],concreteType:"XDTFriendshipStatusResponse",kind:"LinkedField",name:"xdt_api__v1__friendships__destroy__target_user_id",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTRelationshipInfoDict",kind:"LinkedField",name:"friendship_status",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"following",storageKey:null}],storageKey:null}],storageKey:null}];return{fragment:{argumentDefinitions:[a,c,d,e,f,g],kind:"Fragment",metadata:null,name:"usePolarisToggleFollowUserUnfollowMutation",selections:h,type:"Mutation",abstractKey:null},kind:"Request",operation:{argumentDefinitions:[g,a,c,f,e,d],kind:"Operation",name:"usePolarisToggleFollowUserUnfollowMutation",selections:h},params:{id:b("usePolarisToggleFollowUserUnfollowMutation_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__friendships__destroy__target_user_id"]},name:"usePolarisToggleFollowUserUnfollowMutation",operationKind:"mutation",text:null}}}();e.exports=a}),null);
__d("usePolarisToggleFollowUser_user.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisToggleFollowUser_user",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},{alias:null,args:null,concreteType:"XDTRelationshipInfoDict",kind:"LinkedField",name:"friendship_status",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"following",storageKey:null}],storageKey:null}],type:"XDTUserDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisToggleFollowUser",["CometRelay","FBLogger","FollowButtonTappedFalcoEvent","PolarisContainerModuleUtils","PolarisNavChain","QPLUserFlow","RelayFBConnectionHandler","UnfollowButtonTappedFalcoEvent","polarisGetXDTUserDict","qpl","react","react-compiler-runtime","usePolarisAnalyticsContext","usePolarisToggleFollowUserFollowMutation.graphql","usePolarisToggleFollowUserUnfollowMutation.graphql","usePolarisToggleFollowUser_user.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k;(k||d("react")).useCallback;var l=h!==void 0?h:h=b("usePolarisToggleFollowUserFollowMutation.graphql"),m=i!==void 0?i:i=b("usePolarisToggleFollowUserUnfollowMutation.graphql");function a(a,e){var f=d("react-compiler-runtime").c(10),g=d("CometRelay").useMutation(l),h=g[0];g=d("CometRelay").useMutation(m);var i=g[0],k=d("CometRelay").useFragment(j!==void 0?j:j=b("usePolarisToggleFollowUser_user.graphql"),a),n=((g=k.friendship_status)==null?void 0:g.following)!==!0,o=c("usePolarisAnalyticsContext")();f[0]!==o||f[1]!==h||f[2]!==i||f[3]!==e||f[4]!==k.pk||f[5]!==n?(a=function(){var a,b=n?c("qpl")._(379204720,"1722"):c("qpl")._(379193744,"299"),f=function(a){var b=d("RelayFBConnectionHandler").getAllConnectionsWithKey(a,a.getRoot(),"PolarisFeedPage__xdt_api__v1__feed__timeline__connection");b==null?void 0:b.forEach(function(a){var b;b=(b=a==null?void 0:a.getValue("unfollowed_users"))!=null?b:[];Array.isArray(b)&&(n?a.setValue(b.filter(function(a){return a!==k.pk}),"unfollowed_users"):a.setValue([].concat(b,[k.pk]),"unfollowed_users"))});b=c("polarisGetXDTUserDict")(a,k.pk);if(b==null){c("FBLogger")("ig_web").warn("Cannot find user to update friendship status");return}a=b.getOrCreateLinkedRecord("friendship_status","XDTRelationshipInfoDict");a.setValue(n,"following");n||b.setValue(!1,"outgoing_request")};a={container_module:d("PolarisContainerModuleUtils").getContainerModule(o),inventory_source:e==null?void 0:e.inventory_source,media_id_attribution:e==null?void 0:e.media_id,nav_chain:(a=c("PolarisNavChain").getInstance())==null?void 0:a.getNavChainForSend(),ranking_info_token:e==null?void 0:e.ranking_info_token,target_user_id:k.pk};f={onCompleted:function(){c("QPLUserFlow").endSuccess(b)},onError:function(){c("QPLUserFlow").endFailure(b,"request_failed")},optimisticUpdater:f,updater:f,variables:a};if(n){e&&c("FollowButtonTappedFalcoEvent").log(function(){var a;return{container_module:d("PolarisContainerModuleUtils").getContainerModule(o),entity_follow_status:"following",m_pk:e.m_pk,nav_chain:(a=c("PolarisNavChain").getInstance())==null?void 0:a.getNavChainForSend(),ranking_info_token:e.ranking_info_token,request_type:"create"}});return h(f)}else{e&&c("UnfollowButtonTappedFalcoEvent").log(function(){var a;return{canonical_nav_chain:(a=c("PolarisNavChain").getInstance())==null?void 0:a.getNavChainForSend(),media_id:e.media_id,module:d("PolarisContainerModuleUtils").getContainerModule(o),ranking_info_token:e.ranking_info_token,target_id:k.pk}});return i(f)}},f[0]=o,f[1]=h,f[2]=i,f[3]=e,f[4]=k.pk,f[5]=n,f[6]=a):a=f[6];g=a;a=((a=k.friendship_status)==null?void 0:a.following)===!0;var p;f[7]!==g||f[8]!==a?(p=[g,a],f[7]=g,f[8]=a,f[9]=p):p=f[9];return p}g["default"]=a}),98);