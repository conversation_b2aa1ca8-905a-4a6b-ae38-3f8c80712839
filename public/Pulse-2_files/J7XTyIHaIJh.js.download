;/*FB_PKG_DELIM*/

__d("QPLFlow",["QPLUserFlow","Random"],(function(a,b,c,d,e,f,g){"use strict";var h=0;function i(a,b){a=new j(a,b);a.start(b==null?void 0:b.annotations);return a}var j=function(){function a(a,b){this.$4=!1;this.$1=a;this.$2=(a=b==null?void 0:b.instanceKey)!=null?a:h++;this.$3=b==null?void 0:b.timeoutInMs}var b=a.prototype;b.start=function(a){this.$4=!0;c("QPLUserFlow").start(this.$1,{annotations:a,instanceKey:this.$2,timeoutInMs:(a=this.$3)!=null?a:void 0})};b.addPoint=function(a,b){c("QPLUserFlow").addPoint(this.$1,a,{instanceKey:this.$2}),b!=null&&this.$1!=null&&c("QPLUserFlow").addAnnotations(this.$1,b,{instanceKey:this.$2})};b.addAnnotations=function(a){c("QPLUserFlow").addAnnotations(this.$1,a,{instanceKey:this.$2})};b.endSuccess=function(a){c("QPLUserFlow").endSuccess(this.$1,{annotations:a,instanceKey:this.$2}),this.$4=!1};b.endFail=function(a,b){c("QPLUserFlow").endFailure(this.$1,a,{annotations:b,instanceKey:this.$2}),this.$4=!1};b.isActive=function(){return this.$4};b.getQPLAttrs=function(){return{event:this.$1,instanceKey:this.$2}};return a}();function k(a,b){var c=!0;return{addAnnotations:function(a){},addPoint:function(a,b){},endFail:function(a,b){c=!1},endSuccess:function(a){c=!1},getQPLAttrs:function(){return{event:a,instanceKey:0}},isActive:function(){return c},start:function(a){}}}function a(a,b,c){if(d("Random").coinflip(b))return i(a,c);else return k(a,c)}g.startQPLFlow=i;g.startQplFlowWithCoinflip=a}),98);
__d("Worm",["FBLogger","QPLFlow","WormReadOnlyStoreAccessor","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h=1e4;a=function(){function a(a,b){this.$2=a,this.$1=b}var e=a.prototype;e.init=function(a){return this.$2.init(a)};e.close=function(){this.$2.close()};e.store=function(a){return new(d("WormReadOnlyStoreAccessor").ReadOnlyStoreAccessor)(this.$2,a,this.$1)};e.runInTransaction=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e,f){var g=this.$1!=null?d("QPLFlow").startQPLFlow(this.$1,{annotations:{string:{operationType:f}},timeoutInMs:h}):void 0;try{a=(yield this.$2.runInTransaction(a,b,e,{eventFlow:g}));g==null?void 0:g.endSuccess();return a}catch(a){g==null?void 0:g.endFail("error");c("FBLogger")("worm").catching(a).mustfix("Error performing %s",f);throw a}});function e(b,c,d,e){return a.apply(this,arguments)}return e}();return a}();g.OP_TIMEOUT_MS=h;g.WormDatabase=a}),98);
__d("WormPromise",["Promise"],(function(a,b,c,d,e,f){"use strict";var g;a=typeof self==="object"?self.Promise:typeof globalThis==="object"?globalThis.Promise:g||b("Promise");f.WormPromise=a}),66);
__d("WormStoreFunctions",["WormPromise","asyncToGeneratorRuntime","err"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,c,e){var f=[];return d("WormPromise").WormPromise.all(e.map(function(b){b=b.selector;return a.getByIndex(c,b)})).then(function(c){var g=[],h=[];c.forEach(function(a,c){c=e[c].item;if(a){var d=babelHelpers["extends"]({},c);a=a[b.primaryKey];d[b.primaryKey]=a;g.push(d);f.push(a)}else h.push(c),f.push(null)});return d("WormPromise").WormPromise.all([a.bulkPut(g),a.bulkAdd(h).then(function(a){var b=0;f.forEach(function(c,d){c==null&&(f[d]=a[b++])})})])}).then(function(){return f.filter(Boolean)})}function e(a,b,c,d){return a.readIndex(b,c,babelHelpers["extends"]({},d,{limit:1})).then(function(a){return(a=a[0])!=null?a:null})}var h=function(){function a(a,b,c,d,e){this.query=a,this.cursorBuilder=b,this.order=c,this.filter=e,this.boundaries=d,(d==null?void 0:d.greaterThan)!=null?this.lowerBoundary={greaterThan:d.greaterThan}:(d==null?void 0:d.greaterThanOrEqual)!=null&&(this.lowerBoundary={greaterThanOrEqual:d.greaterThanOrEqual}),(d==null?void 0:d.lessThan)!=null?this.upperBoundary={lessThan:d.lessThan}:(d==null?void 0:d.lessThanOrEqual)!=null&&(this.upperBoundary={lessThanOrEqual:d.lessThanOrEqual})}var d=a.prototype;d.read=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var d=this.order==="asc",e;a==null?e=this.boundaries:d?e=babelHelpers["extends"]({},this.upperBoundary,{greaterThan:a}):e=babelHelpers["extends"]({},this.lowerBoundary,{lessThan:a});d=(yield this.query(e,{filter:this.filter,limit:b+1,order:this.order}));var f;e=null;if(d.length<=b)f=d;else if(d.length===b+1)f=d.slice(0,b),e=d[b];else throw c("err")("unreachable");d=f[0];b=f[f.length-1];return{cursorInfo:{endCursor:b&&this.cursorBuilder(b),hasNext:e!=null,hasPrevious:a!=null,startCursor:d&&this.cursorBuilder(d)},data:f}});function d(b,c){return a.apply(this,arguments)}return d}();return a}();function f(a,b,d,e,f,g){b=b.indexes;if(b==null)throw c("err")("No indexes found");var i=b[d];if(i==null)throw c("err")("Index not found");return new h(function(b,c){return a(d,(b=b)!=null?b:void 0,c)},function(a){return i.fields.map(function(b){return a[b]})},e,f,g)}g.bulkUpsert=a;g.getByIndex=e;g.getIndexRangeIterator=f}),98);
__d("WormReadOnlyStoreAccessor",["FBLogger","QPLFlow","Worm","WormStoreFunctions","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b,c){this.$1=a,this.$2=b,this.$3=c}var e=a.prototype;e.$4=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){b=this.$2+"."+b+"_inline";var e=this.$3!=null?d("QPLFlow").startQPLFlow(this.$3,{annotations:{string:{operationType:b}},timeoutInMs:d("Worm").OP_TIMEOUT_MS}):void 0;try{a=(yield this.$1.runInTransaction([this.$2],"readonly",a,{eventFlow:e}));e==null?void 0:e.endSuccess();return a}catch(a){e==null?void 0:e.endFail("error");c("FBLogger")("worm").catching(a).mustfix("Error performing %s",b);throw a}});function e(b,c){return a.apply(this,arguments)}return e}();e.bulkGet=function(a){var b=this;return this.$4(function(c){return c.stores[b.$2].bulkGet(a)},"bulkGet")};e.count=function(){var a=this;return this.$4(function(b){return b.stores[a.$2].count()},"count")};e.get=function(a){var b=this;return this.$4(function(c){return c.stores[b.$2].get(a)},"get")};e.getByIndex=function(a,b,c){var d=this;return this.$4(function(e){return e.stores[d.$2].getByIndex(a,b,c)},"getByIndex")};e.getIndexRangeIterator=function(a,b,c,e){var f=this;return d("WormStoreFunctions").getIndexRangeIterator(function(a,b,c){return f.$4(function(d){return d.stores[f.$2].readIndexRange(a,b,c)},"getIndexRangeIterator")},this.$1.getSchema()[this.$2],a,b,c,e)};e.readAll=function(a){var b=this;return this.$4(function(c){return c.stores[b.$2].readAll(a)},"readAll")};e.readByKeyRange=function(a,b){var c=this;return this.$4(function(d){return d.stores[c.$2].readByKeyRange(a,b)},"readByKeyRange")};e.readIndex=function(a,b,c){var d=this;return this.$4(function(e){return e.stores[d.$2].readIndex(a,b,c)},"readIndex")};e.readIndexKeys=function(a,b,c){var d=this;return this.$4(function(e){return e.stores[d.$2].readIndexKeys(a,b,c)},"readIndexKeys")};e.readIndexRange=function(a,b,c){var d=this;return this.$4(function(e){return e.stores[d.$2].readIndexRange(a,b,c)},"readIndexRange")};e.readKeys=function(a){var b=this;return this.$4(function(c){return c.stores[b.$2].readKeys(a)},"readKeys")};e.readKeysByIndexRange=function(a,b,c){var d=this;return this.$4(function(e){return e.stores[d.$2].readKeysByIndexRange(a,b,c)},"readKeysByIndexRange")};return a}();g.ReadOnlyStoreAccessor=a}),98);
__d("WACryptoSha256",["WABase64","WACryptoDependencies"],(function(a,b,c,d,e,f,g){"use strict";function h(a){return d("WACryptoDependencies").getCrypto().subtle.digest({name:"SHA-256"},a)}function i(a){return h(a).then(d("WABase64").encodeB64)}function a(a){return i(j(a))}function j(a){var b=new ArrayBuffer(a.length),c=new Uint8Array(b);for(var d=0,e=a.length;d<e;d++)c[d]=a.charCodeAt(d);return b}g.sha256=h;g.sha256Base64=i;g.sha256Str=a}),98);
__d("WACryptoSha256BuilderNoWABinary",[],(function(a,b,c,d,e,f){"use strict";var g=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],h=64,i=4;a=function(){function a(){this.h0=0,this.h1=0,this.h2=0,this.h3=0,this.h4=0,this.h5=0,this.h6=0,this.h7=0,this.tail=new Uint8Array(0),this.size=0,this.reset()}var b=a.prototype;b.reset=function(){this.h0=1779033703,this.h1=3144134277,this.h2=1013904242,this.h3=2773480762,this.h4=1359893119,this.h5=2600822924,this.h6=528734635,this.h7=1541459225,this.tail=new Uint8Array(0),this.size=0};b.update=function(a){var b=new Uint8Array(this.tail.length+a.length);b.set(this.tail);b.set(a,this.tail.length);this.size+=a.length*8;while(b.length>=h){a=b.subarray(0,h);this.$1(a);b=b.slice(h)}this.tail=b;return this};b.$2=function(){var a=new Uint8Array(64);a.set(this.tail);a.set(new Uint8Array([128]),this.tail.length);if(this.tail.length+9>h){this.$1(a);var b=o(new Uint8Array(0),this.size);this.$1(b)}else{b=o(a,this.size);this.$1(b)}};b.finish=function(){this.$2();var a=new DataView(new ArrayBuffer(32));a.setUint32(0,this.h0);a.setUint32(4,this.h1);a.setUint32(8,this.h2);a.setUint32(12,this.h3);a.setUint32(16,this.h4);a.setUint32(20,this.h5);a.setUint32(24,this.h6);a.setUint32(28,this.h7);this.reset();return new Uint8Array(a.buffer)};b.$1=function(a){var b=[];for(var c=0;c<16;c++){var d=a.subarray(c*i,i*(c+1));d=d[0]<<24|d[1]<<16|d[2]<<8|d[3];b.push(d)}for(d=16;d<64;d++){a=j(b[d-15]);c=k(b[d-2]);a=b[d-16]+(b[d-7]+a+c)>>>0;b.push(a)}c=this.h0;a=this.h1;d=this.h2;var e=this.h3,f=this.h4,h=this.h5,m=this.h6,n=this.h7;for(var o=0;o<64;o++){var p=l(f,6)^l(f,11)^l(f,25),q=f&h^~f&m,r=l(c,2)^l(c,13)^l(c,22),s=c&a^c&d^a&d;p=n+p+q+g[o]+b[o];q=r+s;n=m;m=h;h=f;f=e+p>>>0;e=d;d=a;a=c;c=p+q>>>0}this.h0=this.h0+c>>>0;this.h1=this.h1+a>>>0;this.h2=this.h2+d>>>0;this.h3=this.h3+e>>>0;this.h4=this.h4+f>>>0;this.h5=this.h5+h>>>0;this.h6=this.h6+m>>>0;this.h7=this.h7+n>>>0};return a}();function j(a){var b=l(a,7),c=l(a,18);a=m(a,3);return b^c^a}function k(a){var b=l(a,17),c=l(a,19);a=m(a,10);return b^c^a}function l(a,b){return a>>>b|a<<32-b}function m(a,b){return a>>>b}function n(a){return new Uint8Array(new Uint32Array([a]).buffer)}function o(a,b){var c=new Uint8Array(h);c.set(a);a=n(b);c.set(a.subarray(0,1),c.length-1);c.set(a.subarray(1,2),c.length-2);c.set(a.subarray(2,3),c.length-3);c.set(a.subarray(3,4),c.length-4);c.set(a.subarray(4,5),c.length-5);c.set(a.subarray(5,6),c.length-6);c.set(a.subarray(6,7),c.length-7);c.set(a.subarray(7,8),c.length-8);return c}f.Sha256Builder=a;f.sigma0=j;f.sigma1=k;f.rotateRight=l;f.shiftRight=m}),66);
__d("WACryptoSha256HmacBuilderNoWABinary",["WACryptoSha256BuilderNoWABinary"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a){this.$1=new(d("WACryptoSha256BuilderNoWABinary").Sha256Builder)(),this.$2=new Uint8Array(0),this.reset(a)}var b=a.prototype;b.reset=function(a){this.$2=new Uint8Array(64);a.length>64?this.$2.set(new(d("WACryptoSha256BuilderNoWABinary").Sha256Builder)().update(a).finish(),0):this.$2.set(a,0);a=this.$2.map(function(a){return a^54});this.$1=new(d("WACryptoSha256BuilderNoWABinary").Sha256Builder)().update(a)};b.update=function(a){this.$1.update(a);return this};b.finish=function(){var a=this.$2.map(function(a){return a^92}),b=this.$1.finish();return new(d("WACryptoSha256BuilderNoWABinary").Sha256Builder)().update(a).update(b).finish()};return a}();g.Sha256HMacBuilder=a}),98);
__d("WormEncoding",["msgpack-msgpack"],(function(a,b,c,d,e,f,g){"use strict";function a(){var a=new(d("msgpack-msgpack").ExtensionCodec)(),b=0;a.register({decode:function(a){a=d("msgpack-msgpack").decode(a);return a.buffer.slice(a.byteOffset,a.byteLength+a.byteOffset)},encode:function(a){if(a instanceof ArrayBuffer)return d("msgpack-msgpack").encode(new Uint8Array(a))},type:b});b=1;a.register({decode:function(a){a=d("msgpack-msgpack").decode(a);return new Uint8Array(a.buffer.slice(a.byteOffset,a.byteLength+a.byteOffset))},encode:function(a){if(a instanceof Uint8Array)return d("msgpack-msgpack").encode(a)},type:b});b=2;a.register({decode:function(a){a=d("msgpack-msgpack").decode(a);return BigInt(a)},encode:function(a){if(typeof a==="bigint")return d("msgpack-msgpack").encode(String(a))},type:b});b=3;a.register({decode:function(a){a=d("msgpack-msgpack").decode(a);return new Set(a)},encode:function(a){if(a instanceof Set)return d("msgpack-msgpack").encode(Array.from(a))},type:b});return{decode:function(b){return d("msgpack-msgpack").decode(b,{extensionCodec:a})},encode:function(b){return d("msgpack-msgpack").encode(b,{extensionCodec:a,ignoreUndefined:!0})}}}g.createEncoding=a}),98);
__d("WormGlobalConfig",[],(function(a,b,c,d,e,f){"use strict";var g={handleNotFoundErrorExperiment:function(){return!1},handleUnknownErrorExperiment:function(){return!1},treatMalformedEncryptedEntityAsDecryptionError:function(){return!1},useNoWABinary:function(){return!1}};function a(){return g}function b(a){g=a}f.getWormGlobalConfig=a;f.setWormGlobalConfig=b}),66);
__d("WormIDbGetMinMaxKeySelector",[],(function(a,b,c,d,e,f){"use strict";a=-Infinity;var g;b=function(){if(g!=null)return g;try{IDBKeyRange.only([[]]);g=[[]];return g}catch(b){var a=String.fromCharCode(65535);g=a;return a}};f.MIN_KEY=a;f.getMaxSelector=b}),66);
__d("WormEAR",["FBLogger","Promise","WAArrayBufferUtils","WABinary","WAByteArray","WACryptoDependencies","WACryptoSha256","WACryptoSha256HmacBuilder","WACryptoSha256HmacBuilderNoWABinary","WATimeUtils","WormEncoding","WormGlobalConfig","WormIDbGetMinMaxKeySelector","asyncToGeneratorRuntime","err","tweetnacl"],(function(a,b,c,d,e,f,g){"use strict";var h,i="_encryptedContent",j=function(b){babelHelpers.inheritsLoose(a,b);function a(a){var c;c=b.call(this,a)||this;c.name="WormEAREncryptionError";c.message=a;return c}return a}(babelHelpers.wrapNativeSuper(Error)),k=function(b){babelHelpers.inheritsLoose(a,b);function a(a,c,d){var e;e=b.call(this,a)||this;e.name="WormEARDecryptionError";e.message=a;e.store=c;e.maybeHashedPk=d;return e}return a}(babelHelpers.wrapNativeSuper(Error));function l(a,b,e){if(typeof a==="number")return a;b=b.encode(a);a=new(d("WACryptoSha256HmacBuilder").Sha256HMacBuilder)(new Uint8Array(e)).update(b).finish();if(d("WormGlobalConfig").getWormGlobalConfig().useNoWABinary()){e=new(d("WACryptoSha256HmacBuilderNoWABinary").Sha256HMacBuilder)(new Uint8Array(e)).update(b).finish();b=new Uint8Array(new ArrayBuffer(64),0,32);b.set(e,0);if(a.length!==b.length||a.byteLength!==b.byteLength||a.byteOffset!==b.byteOffset||a.buffer.byteLength!==b.buffer.byteLength)c("FBLogger")("worm").mustfix("Sha256HMacBuilderNoWABinary: incorrect length %s !== %s",a.buffer.byteLength,b.buffer.byteLength);else if(!d("WAArrayBufferUtils").uint8ArraysEqualUNSAFE(a,b))c("FBLogger")("worm").mustfix("Sha256HMacBuilderNoWABinary: incorrect output");else return b.buffer}return a.buffer}var m=32,n=2,o=128,p=12,q=256,r=new Uint8Array(0).buffer,s="AES-GCM",t="HKDF",u="SHA-256";a=60*60;e=24*a;var v=0,w=e*30*6;f=function(){function a(a,b,c,e){this.$7=0;this.$9=!1;this.$10=!1;this.$2=a;this.$1=b;this.$8=(a=e==null?void 0:e.encKeyTtl)!=null?a:w;this.$3=d("WormEncoding").createEncoding();this.$4=c;this.$6=crypto.subtle.importKey("raw",c,{name:s},!1,["encrypt","decrypt"]);this.$5=new Map()}var e=a.prototype;e.isSecureStore=function(a){return this.$2[a].secure!==!1};e.$11=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){return(yield d("WACryptoSha256").sha256Base64(this.$4)).substring(0,5)});function c(){return a.apply(this,arguments)}return c}();e.prepareNewKeyVersion=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var a=d("WACryptoDependencies").getCrypto(),b=a.getRandomValues(new Uint8Array(m)).buffer,c=a.getRandomValues(new Uint8Array(m)).buffer,e=a.getRandomValues(new Uint8Array(p)),f=(yield this.$6);a=(yield a.subtle.encrypt({iv:e,name:s,tagLength:o},f,c));if(d("WormGlobalConfig").getWormGlobalConfig().useNoWABinary()){f=new Uint8Array(e.length+a.byteLength);f.set(e,0);f.set(new Uint8Array(a),e.length);c=d("WAByteArray").uint8ArrayToBuffer(f)}else c=d("WABinary").Binary.build(e,a).readBuffer();return{clientKey:b,expiration:d("WATimeUtils").castToUnixTime(d("WATimeUtils").unixTime()+this.$8),keyHash:yield this.$11(),salt:c}});function c(){return a.apply(this,arguments)}return c}();e.init=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,f){var g=this;this.$5=new Map();this.$7=0;this.$9=e.incorrectVersions;this.$10=e.versionsLoss;var i=d("WACryptoDependencies").getCrypto(),j=(yield this.$11()),k=!0;try{yield (h||(h=b("Promise"))).all(a.map(function(a){k=k&&a.keyHash===j;var c=a.salt,d=c.slice(0,p),e=c.slice(p);return g.$6.then(function(c){return(h||(h=b("Promise"))).all([i.subtle.importKey("raw",a.clientKey,{name:t},!1,["deriveKey"]),i.subtle.decrypt({iv:new Uint8Array(d),name:s,tagLength:o},c,e)])}).then(function(a){var b=a[0];a=a[1];return i.subtle.deriveKey({hash:{name:u},info:r,name:t,salt:a},b,{length:q,name:s},!0,["encrypt","decrypt"])}).then(function(a){return i.subtle.exportKey("raw",a)}).then(function(b){g.$7=Math.max(g.$7,a.version),g.$5.set(a.version,b)})}))}catch(a){c("FBLogger")("worm").catching(a).mustfix("Error on keychain initialisation keyHashCheck: %s",k);throw a}finally{f==null?void 0:(e=f.eventFlow)==null?void 0:e.addAnnotations({bool:{keyHashCheck:k}})}});function e(b,c,d){return a.apply(this,arguments)}return e}();e.$12=function(a){if(this.$7===0)throw new j("EAR.Encryption. EAR is not initialised ");var b=this.$5.get(this.$7);if(b==null)throw new j("EAR.Encryption.Key does not exist for version");var c=d("tweetnacl").randomBytes(d("tweetnacl").secretbox.nonceLength);a=d("tweetnacl").secretbox(a,c,new Uint8Array(b));if(d("WormGlobalConfig").getWormGlobalConfig().useNoWABinary()){b=new Uint8Array(2+c.length+a.length);b.set([v],0);b.set([this.$7],1);b.set(c,2);b.set(a,2+c.length);b=d("WAByteArray").uint8ArrayToBuffer(b)}else b=d("WABinary").Binary.build(v,this.$7,c,a).readBuffer();return b};e.$13=function(a,b,c){var e=a.slice(0,n);e=new DataView(e).getUint8(1);var f=this.$5.get(e);if(f==null)throw new k("EAR.Decryption.Key does not exist for version: "+e+"; db: "+this.$1+"; store: "+b+"; incorrectVersions: "+this.$9.toString()+" versionsLoss: "+this.$10.toString(),b,c);e=a.slice(n,n+d("tweetnacl").secretbox.nonceLength);a=a.slice(n+d("tweetnacl").secretbox.nonceLength);a=d("tweetnacl").secretbox.open(new Uint8Array(a),new Uint8Array(e),new Uint8Array(f));if(a==null)throw new k("EAR.Decryption. Unable to decrypt; db: "+this.$1+"; store: "+b+"; incorrectVersions: "+this.$9.toString()+" versionsLoss: "+this.$10.toString(),b,c);return a};e.encryptEntity=function(a,b){b=this.$2[b];var c=new Set();b.autoIncrement!==!0&&c.add(b.primaryKey);var d=b.indexes;if(d!=null)for(var e of Object.keys(d)){var f=d[e];for(f of f.fields)c.add(f)}f={};for(e of c){f[e]=((d=b.nonEncryptedFields)==null?void 0:d.has(e))?a[e]:l(a[e],this.$3,this.$4)}b.autoIncrement===!0&&a[b.primaryKey]!=null&&(f[b.primaryKey]=a[b.primaryKey]);return babelHelpers["extends"]({},f,(c={},c[i]=this.$12(this.$3.encode(a)),c))};e.$14=function(a,b,d){(a==null||typeof a!=="object")&&c("FBLogger")("worm").mustfix("WORM.EAR: dbRecord is null or not object: %s, store: %s, reason %s",a,b,d);if((a==null?void 0:a[i])!=null){d=a[this.$2[b].primaryKey];return{encrypted:!0,encryptedEntity:a,pk:d}}else return{encrypted:!1,entity:a}};e.maybeDecrypt=function(a,b,e,f,g){if(a==null)return a;if(this.isSecureStore(b)===!1)return a;e=this.$14(a,b,e);if(!e.encrypted){if(g==null?void 0:g.shouldNotFailIfEntityIsNotEncrypted)return a;if(d("WormGlobalConfig").getWormGlobalConfig().treatMalformedEncryptedEntityAsDecryptionError()===!0){g=a[this.$2[b].primaryKey];throw new k("EAR.Decryption. Attempt to decrypt malformed encrypted entity in  db: "+this.$1+", store: "+b+". Missing '"+i+"' property. Pk is "+(g==null?"empty":"not empty"),b,g)}else throw c("err")("Attempt to decrypt not encrypted entity")}f==null?void 0:f.addPoint("decrypt_start");a=this.decryptEntity(e.encryptedEntity,b,e.pk);f==null?void 0:f.addPoint("decrypt_end");g=this.$2[b];if(g.autoIncrement===!0){return babelHelpers["extends"]({},a,(f={},f[g.primaryKey]=e.pk,f))}return a};e.maybeEncrypt=function(a,b,c){if(this.isSecureStore(b)===!1)return a;c==null?void 0:c.addPoint("encrypt_start");a=this.encryptEntity(a,b);c==null?void 0:c.addPoint("encrypt_end");return a};e.decryptEntity=function(a,b,c){return this.$3.decode(this.$13(a[i],b,c))};e.hashPk=function(a,b,c){return((a=this.$2[a].nonEncryptedFields)==null?void 0:a.has(b))?c:l(c,this.$3,this.$4)};e.hashSelector=function(a,b,c){var e;a=[].concat(a);e=(c=(e=this.$2[b].indexes)==null?void 0:(e=e[c])==null?void 0:e.fields)!=null?c:[];for(c=0;c<a.length;c++){var f,g=a[c],h=e[c];if(g===d("WormIDbGetMinMaxKeySelector").getMaxSelector())continue;if(g===-Infinity)continue;if((f=this.$2[b].nonEncryptedFields)==null?void 0:f.has(h))continue;a[c]=l(g,this.$3,this.$4)}return a};return a}();g.ENCRYPTED_COLUMN_NAME=i;g.EncryptionError=j;g.DecryptionError=k;g.WormEAR=f}),98);
__d("WormIDbStore",["Promise","WormIDbGetMinMaxKeySelector","WormPromise","WormStoreFunctions","err"],(function(a,b,c,d,e,f,g){"use strict";var h,i=function(){};a=function(){function a(a,b,c,d,e){var f=this;this.readIndexRange=function(a,b,c){return b==null?f.$11(function(){return f.$1.index(a).openCursor(null,(c==null?void 0:c.order)==="desc"?"prev":"next")},c):f.$11(function(){return f.$1.index(a).openCursor(f.$12(b,a),(c==null?void 0:c.order)==="desc"?"prev":"next")},c)};this.$1=b.objectStore(a);this.$2=a;this.$3=d;this.$4=c;this.$5=e}var e=a.prototype;e.$6=function(a,b){var c;c=((c=this.$4.eventFlow)==null?void 0:c.isActive())?this.$4.eventFlow:null;c==null?void 0:c.addPoint(b+"_start");a=a();c==null?void 0:c.addPoint(b+"_end");return a};e.$7=function(){return this.$3.isSecureStore(this.$2)};e.$8=function(a){var b=this;return!this.$7()?a:this.$6(function(){return b.$3.hashPk(b.$2,b.$5.primaryKey,a)},"hashPk")};e.$9=function(a,b){var c=this;if(!this.$7())return a;return a==null?a:this.$6(function(){return c.$3.hashSelector(a,c.$2,b)},"hashSelector")};e.$10=function(a){var b=this;return new(d("WormPromise").WormPromise)(function(c,d){var e=a();e.onsuccess=function(){try{c(e.result.map(function(a){return b.$3.maybeDecrypt(a,b.$2,"readArray",b.$4.eventFlow)}))}catch(a){d(a)}};e.onerror=function(){return d(e.error)}})};e.$11=function(a,b){var c=this;return new(d("WormPromise").WormPromise)(function(d,e){var f,g=[],h=(b==null?void 0:b.limit)!=null,i=Math.max(0,(f=b==null?void 0:b.limit)!=null?f:0);if(h&&i===0){d(g);return}var j=a();j.onsuccess=function(){var a=j.result;if(a){var f;try{f=c.$3.maybeDecrypt(a.value,c.$2,"readCursor",c.$4.eventFlow)}catch(a){e(a);return}(!(b==null?void 0:b.filter)||b.filter(f))&&g.push(f);if(h&&i===g.length){d(g);return}a["continue"]()}else d(g)};j.onerror=function(){return e(j.error)}})};e.bulkAdd=function(a){var c=this;return(h||(h=b("Promise"))).all(a.map(function(a){return new(d("WormPromise").WormPromise)(function(b,d){var e=c.$1.add(c.$3.maybeEncrypt(a,c.$2,c.$4.eventFlow));e.onsuccess=function(){c.$5.autoIncrement===!0?b(e.result):b(a[c.$5.primaryKey])};e.onerror=function(){return d(e.error)}})}))};e.bulkPut=function(a){var c=this;return(h||(h=b("Promise"))).all(a.map(function(a){return new(d("WormPromise").WormPromise)(function(b,d){var e,f=c.$1.put(c.$3.maybeEncrypt(a,c.$2,(e=c.$4.eventFlow)!=null?e:void 0));f.onsuccess=b;f.onerror=function(){return d(f.error)}})})).then(i)};e.bulkUpsert=function(a,b){return d("WormStoreFunctions").bulkUpsert(this,this.$5,a,b)};e.bulkGet=function(a){var c=this;return(h||(h=b("Promise"))).all(a.map(function(a){return new(d("WormPromise").WormPromise)(function(b,d){var e=c.$1.get(c.$8(a));e.onsuccess=function(){try{var a;b(c.$3.maybeDecrypt(e.result,c.$2,"bulkGet",(a=c.$4.eventFlow)!=null?a:void 0))}catch(a){d(a)}};e.onerror=function(){return d(e.error)}})}))};e.get=function(a){return this.bulkGet([a]).then(function(a){return a[0]})};e.getByIndex=function(a,b,c){return d("WormStoreFunctions").getByIndex(this,a,b,c)};e.readByKeyRange=function(a,b){var c=this;return a==null?this.$11(function(){return c.$1.openCursor(null,(b==null?void 0:b.order)==="desc"?"prev":"next")},b):this.$11(function(){return c.$1.openCursor(c.$12(a,c.$5.primaryKey),(b==null?void 0:b.order)==="desc"?"prev":"next")},b)};e.$13=function(a,b,e){if(this.$5.indexes==null)throw c("err")("No indexes found for store");a=this.$5.indexes[a].fields;if(b.length===a.length)return b;a=a.map(function(a,c){return(a=b[c])!=null?a:e==="max"?d("WormIDbGetMinMaxKeySelector").getMaxSelector():d("WormIDbGetMinMaxKeySelector").MIN_KEY});return a};e.$12=function(a,b){var d=b===this.$5.primaryKey;if(a.only!=null){if(this.$5.indexes==null)throw c("err")("No indexes found for store");var e=this.$5.indexes[b].fields;return a.only.length===e.length?IDBKeyRange.only(this.$9(a.only,b)):IDBKeyRange.bound(d?this.$8(a.only):this.$9(this.$13(b,a.only,"min"),b),d?this.$8(a.only):this.$9(this.$13(b,a.only,"max"),b),!0,!0)}if(a.lessThan!=null||a.lessThanOrEqual!=null){e=(e=a.lessThan)!=null?e:a.lessThanOrEqual;if(a.greaterThan!=null||a.greaterThanOrEqual!=null){var f;f=(f=a.greaterThan)!=null?f:a.greaterThanOrEqual;return IDBKeyRange.bound(d?this.$8(f):this.$9(this.$13(b,f,"min"),b),d?this.$8(e):this.$9(this.$13(b,e,"max"),b),a.greaterThan!=null,a.lessThan!=null)}return IDBKeyRange.upperBound(d?this.$8(e):this.$9(this.$13(b,e,"max"),b),a.lessThan!=null)}else{e=(f=a.greaterThan)!=null?f:a.greaterThanOrEqual;if(e!=null)return IDBKeyRange.lowerBound(d?this.$8(e):this.$9(this.$13(b,e,"min"),b),a.greaterThan!=null);else throw c("err")("Invalid range")}};e.readIndex=function(a,b,c){var d=this;return(c==null?void 0:c.filter)==null&&(c==null?void 0:c.order)!=="desc"?this.$10(function(){var e;return d.$1.index(a).getAll(d.$9(b,a),(e=c==null?void 0:c.limit)!=null?e:void 0)}):this.$11(function(){return d.$1.index(a).openCursor(d.$9(b,a),(c==null?void 0:c.order)==="desc"?"prev":"next")},c)};e.readIndexKeys=function(a,b,c){var d=this;return this.readIndex(a,b,c).then(function(a){return a.map(function(a){return a[d.$5.primaryKey]})})};e.readKeysByIndexRange=function(a,b,c){var d=this;return this.readIndexRange(a,b,c).then(function(a){return a.map(function(a){return a[d.$5.primaryKey]})})};e.getIndexRangeIterator=function(a,b,c,e){return d("WormStoreFunctions").getIndexRangeIterator(this.readIndexRange,this.$5,a,b,c,e)};e.readAll=function(a){var b=this;return(a==null?void 0:a.filter)===null&&(a==null?void 0:a.order)!=="desc"?this.$10(function(){var c;return b.$1.getAll((c=a==null?void 0:a.limit)!=null?c:void 0)}):this.$11(function(){return b.$1.openCursor(null,(a==null?void 0:a.order)==="desc"?"prev":"next")},a)};e.readKeys=function(a){var b=this;return this.readAll(a).then(function(a){return a.map(function(a){return a[b.$5.primaryKey]})})};e.bulkDelete=function(a){var c=this;return(h||(h=b("Promise"))).all(a.map(function(a){return new(d("WormPromise").WormPromise)(function(b,d){var e=c.$1["delete"](c.$8(a));e.onsuccess=b;e.onerror=function(){return d(e.error)}})})).then(i)};e["delete"]=function(a){var b=this;return new(d("WormPromise").WormPromise)(function(c,d){var e=b.$1["delete"](b.$8(a));e.onsuccess=function(){return c()};e.onerror=function(){return d(e.error)}})};e.clear=function(){var a=this;return new(d("WormPromise").WormPromise)(function(b,c){var d=a.$1.clear();d.onsuccess=b;d.onerror=function(){return c(d.error)}}).then(i)};e.count=function(){var a=this;return new(d("WormPromise").WormPromise)(function(b,c){var d=a.$1.count();d.onsuccess=function(){return b(d.result)};d.onerror=function(){return c(d.error)}})};return a}();g.WormIDbStore=a}),98);
__d("WormIDbTransaction",["WAResolvable","WormIDbStore"],(function(a,b,c,d,e,f,g){"use strict";var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){var a,c="WormUnknownError";a=b.call(this,c)||this;a.name=c;a.message=c;return a}return a}(babelHelpers.wrapNativeSuper(Error)),i=function(b){babelHelpers.inheritsLoose(a,b);function a(){var a,c="WormUnknownAbortError";a=b.call(this,c)||this;a.name=c;a.message=c;return a}return a}(babelHelpers.wrapNativeSuper(Error));a=function(){function a(a,b,c,e,f){var g=this;this.$1=a;this.$2=new(d("WAResolvable").Resolvable)();this.$1.oncomplete=function(){g.$2.resolve()};this.$1.onerror=function(a){a=(a=(a=(a=a.target)==null?void 0:a.error)!=null?a:g.$1.error)!=null?a:new h();g.$2.reject(a)};this.$1.onabort=function(a){a=(a=(a=(a=a.target)==null?void 0:a.error)!=null?a:g.$1.error)!=null?a:new i();g.$2.reject(a)};this.$3=e.reduce(function(a,e){return babelHelpers["extends"]({},a,(a={},a[e]=new(d("WormIDbStore").WormIDbStore)(e,g.$1,b,c,f[e]),a))},(a=this.$3)!=null?a:{})}var b=a.prototype;b.commit=function(){this.$1.commit==null?void 0:this.$1.commit();return this.$2.promise};b.abort=function(){this.$2.isSettled||this.$1.abort()};babelHelpers.createClass(a,[{key:"stores",get:function(){return this.$3}}]);return a}();g.WormIDbTransaction=a}),98);
__d("WormIDbTypes",[],(function(a,b,c,d,e,f){"use strict";a="_worm_db_version_";b="_worm_ear_";c="schema_hash";d="creation_ts";f.DB_VERSION_STORE=a;f.EAR_STORE=b;f.SCHEMA_HASH_KEY=c;f.DB_CREATION_TS=d}),66);
__d("WAIDBTypes",[],(function(a,b,c,d,e,f){"use strict";function a(){return indexedDB}f.idb=a}),66);
__d("WormIDbUtils",["Promise","WAIDBTypes","err"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){return new(h||(h=b("Promise")))(function(b,c){a.onsuccess=function(){return b(a.result)},a.onerror=function(){return c(a.error)}})}function e(a,e,f){var g=e.onBecomeStale,i=e.onClose,j=e.onError,k=e.onUpgrade;return new(h||(h=b("Promise")))(function(b,e){var h=d("WAIDBTypes").idb().open(a,f);k!=null&&(h.onupgradeneeded=function(){if(h.transaction==null)throw c("err")("Transaction is null on upgradeneeded event");k(h.result,h.transaction)});h.onsuccess=function(){var a=h.result;i!=null&&(a.onclose=i);a.onversionchange=function(){a.close(),g!=null&&g()};b(a)};h.onerror=function(){e(h.error),j!=null&&j(h.error)}})}g.promisifyIDbRequest=a;g.openIDb=e}),98);
__d("WormIDbUpgrade",["WATimeUtils","WormIDbTypes","WormIDbUtils","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";function h(a,b,c){var d=[],e=[],f=new Map(),g=Array.from(Object.keys(c)).map(function(a){return a.toString()});g=new Set(g);for(var h of Object.keys(c))if(!a.objectStoreNames.contains(h))d.push(h);else{var i=c[h];i=(i=i.indexes)!=null?i:{};var j=b.objectStore(h),k=j.indexNames,l=new Set(Object.keys(i)),m=[],n=[];for(var o of l)!k.contains(o)?m.push(o):j.index(o).unique!==i[o].unique&&(m.push(o),n.push(o));for(j=0;j<k.length;j++){o=k[j];l.has(o)||n.push(o)}(m.length>0||n.length>0)&&f.set(h,{indexesToCreate:m,indexesToDelete:n})}for(i=0;i<a.objectStoreNames.length;i++){o=a.objectStoreNames[i];g.has(o)||e.push(o)}return{storesToCreate:d,storesToDelete:e,storesToModify:f}}function a(a,b){return i.apply(this,arguments)}function i(){i=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){if(!a.objectStoreNames.contains(d("WormIDbTypes").DB_VERSION_STORE))return{isNewDb:!0,shouldUpgrade:!0};a=a.transaction(d("WormIDbTypes").DB_VERSION_STORE,"readonly");a=(yield d("WormIDbUtils").promisifyIDbRequest(a.objectStore(d("WormIDbTypes").DB_VERSION_STORE).get(d("WormIDbTypes").SCHEMA_HASH_KEY)));return{isNewDb:!1,shouldUpgrade:(a==null?void 0:a.value)!==b}});return i.apply(this,arguments)}function j(a,b,c){for(c of c){var d=b[c],e=a.createObjectStore(c,{autoIncrement:!!d.autoIncrement,keyPath:d.primaryKey});d=d.indexes;if(d==null)continue;for(var f of Object.keys(d)){var g=d[f];e.createIndex(f,g.fields.map(function(a){return a.toString()}),{unique:g.unique})}}}function c(a,b,c,e,f){var g=h(a,b,c),i=g.storesToCreate,k=g.storesToDelete;g=g.storesToModify;j(a,c,i);if(f.onlyCreateNewStores)return;for(i of k)f.safeToDeleteStores.has(i)&&a.deleteObjectStore(i);a=function(a){var d=a[0];k=a[1];a=k.indexesToCreate;var e=k.indexesToDelete;for(e of e)b.objectStore(d).deleteIndex(e);e=c[d].indexes;if(e==null)return"continue";var g=!1;for(a of a){var h=e[a];b.objectStore(d).createIndex(a,h.fields,{unique:h.unique});g=!0}if(g&&c[d].secure===!0&&f.maybeEar!=null){var i=d,j=f.maybeEar,l=b.objectStore(i).getAll();l.onsuccess=function(){var a=l.result;a.forEach(function(a){if(a==null)return;b.objectStore(d).put(j.maybeEncrypt(j.maybeDecrypt(a,i,"dbUpgrade",void 0,{shouldNotFailIfEntityIsNotEncrypted:!0}),i,void 0))})}}};for(i of g){g=a(i);if(g==="continue")continue}b.objectStore(d("WormIDbTypes").DB_VERSION_STORE).put({key:d("WormIDbTypes").SCHEMA_HASH_KEY,value:e});f.isNewDbInstance&&b.objectStore(d("WormIDbTypes").DB_VERSION_STORE).put({key:d("WormIDbTypes").DB_CREATION_TS,value:d("WATimeUtils").performanceAbsoluteNow()})}g.shouldUpgradeDb=a;g.createNewStores=j;g.upgradeDb=c}),98);
__d("WormIDbDriver",["FBLogger","Promise","WACryptoSha256","WAResolvable","WATimeUtils","WormGlobalConfig","WormIDbTransaction","WormIDbTypes","WormIDbUpgrade","WormIDbUtils","asyncToGeneratorRuntime","err"],(function(a,b,c,d,e,f,g){"use strict";var h,i={_worm_db_version_:{primaryKey:"key",secure:!1},_worm_ear_:{primaryKey:"version",secure:!1}};a=function(){function a(a,b,d,e,f,g){var h=this;this.$5=null;this.$8=[];this.$13=function(){h.$12.log(h.$3+".error.db_stale")};this.$14=function(){h.$12.log(h.$3+".error.db_close")};this.$15=function(a){c("FBLogger")("worm").catching(a).mustfix("Unexpected error  %s",h.$3)};this.$2=a;this.$3=b;this.$4=d;this.$6=(a=g==null?void 0:g.safeToDeleteStores)!=null?a:new Set();this.$9=g==null?void 0:g.blockingErrorThreshold;this.$10=(b=g==null?void 0:g.onBlockingError)!=null?b:function(){};this.$11=(d=g==null?void 0:g.onTransactionError)!=null?d:function(){};this.$7=e;this.$12=f}var e=a.prototype;e.getSchema=function(){return this.$4};e.$16=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e,f){this.$5!=null&&(yield this.$5.promise);if(this.$1==null)throw c("err")("IDB is not initialised: "+this.$3);b=new(d("WormIDbTransaction").WormIDbTransaction)(this.$1.transaction(a.map(function(a){return a.toString()}),b,{durability:"relaxed"}),f,this.$7,a,this.$4);var g;try{g=(yield e(b)),yield b.commit()}catch(a){b.abort();throw a}return g});function e(b,c,d,e){return a.apply(this,arguments)}return e}();e.$17=function(a){var b;if(this.$9==null)return;var d=this.$9;a=(b=(b=(b=a==null?void 0:a.name)!=null?b:a==null?void 0:a.message)!=null?b:a==null?void 0:a.toString())!=null?b:"unknown";this.$8.push(a);if(this.$8.length<Math.max(d,1))return;var e=this.$8[this.$8.length-1];b=this.$8.every(function(a){return a===e})?e:"mixed";c("FBLogger")("worm").mustfix("Unrecoverable error (%s): %s",this.$3,b);this.$10(b);this.$8=[]};e.$18=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c,e){if(this.$1==null){var f;(f=e.eventFlow)==null?void 0:f.addPoint("db_reinit_start",{bool:{isDbNull:!0}});yield this.$19();(f=e.eventFlow)==null?void 0:f.addPoint("db_reinit_end")}var g;try{g=(yield this.$16(a,b,c,e))}catch(i){f=d("WormGlobalConfig").getWormGlobalConfig();var h=f.handleNotFoundErrorExperiment;f=f.handleUnknownErrorExperiment;if((i==null?void 0:i.name)==="InvalidStateError"||(i==null?void 0:i.name)==="NotFoundError"&&h()===!0||(i==null?void 0:i.name)==="UnknownError"&&f()===!0){(f=e.eventFlow)==null?void 0:f.addPoint("db_reinit_start",{bool:{isDbInvalid:!0}});yield this.$19({forceUpgrade:(i==null?void 0:i.name)==="NotFoundError"&&h()===!0});(f=e.eventFlow)==null?void 0:f.addPoint("db_reinit_end");g=(yield this.$16(a,b,c,e))}else throw i}return g});function c(b,c,d,e){return a.apply(this,arguments)}return c}();e.runInTransaction=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c,d){var e;try{e=(yield this.$18(a,b,c,d)),this.$8=[]}catch(a){this.$11(a);this.$17(a);throw a}return e});function c(b,c,d,e){return a.apply(this,arguments)}return c}();e.close=function(){var a;(a=this.$1)==null?void 0:a.close()};e.init=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){try{yield this.$19({eventFlow:(a=a==null?void 0:a.eventFlow)!=null?a:void 0})}catch(a){if((a==null?void 0:a.name)==="InvalidStateError")yield this.$19();else throw a}});function c(b){return a.apply(this,arguments)}return c}();e.$20=function(a,b){var c=a.version;a.close();return d("WormIDbUtils").openIDb(this.$2,{onBecomeStale:this.$13,onClose:this.$14,onError:this.$15,onUpgrade:function(a,c){return d("WormIDbUpgrade").upgradeDb(a,c,i,b,{isNewDbInstance:!1,maybeEar:null,onlyCreateNewStores:!0,safeToDeleteStores:new Set()})}},c+1)};e.$19=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=this,e=!1;if(this.$5){e=!0;return this.$5.promise}this.$5=new(d("WAResolvable").Resolvable)();var f=!1;this.$1!=null&&(this.$1.close(),this.$1=null,f=!0);var g=!0;try{var h=babelHelpers["extends"]({},this.$4,i),j=JSON.stringify(h);if(j==null)throw c("err")("DB schema cannot be serialized: "+this.$3);var k=(yield d("WACryptoSha256").sha256Str(j));j=null;var l=(yield d("WormIDbUtils").openIDb(this.$2,{onBecomeStale:this.$13,onClose:this.$14,onError:this.$15,onUpgrade:function(a,c){j={isNewDb:!0,shouldUpgrade:!1};return d("WormIDbUpgrade").upgradeDb(a,c,h,k,{isNewDbInstance:!0,maybeEar:null,onlyCreateNewStores:!1,safeToDeleteStores:b.$6})}}));try{if(Object.keys(i).some(function(a){return l.objectStoreNames.contains(a)===!1})){var m;a==null?void 0:(m=a.eventFlow)==null?void 0:m.addPoint("ear_schema_recovery_start");l=(yield this.$20(l,k));a==null?void 0:(m=a.eventFlow)==null?void 0:m.addPoint("ear_schema_recovery_end")}}catch(b){a==null?void 0:(m=a.eventFlow)==null?void 0:m.addPoint("ear_schema_recovery_fail");c("FBLogger")("worm").catching(b).mustfix("EAR initialization error")}this.$1=l;a==null?void 0:(m=a.eventFlow)==null?void 0:m.addPoint("ear_init_start");try{yield this.$21((m=(m=j)==null?void 0:m.isNewDb)!=null?m:!1,{eventFlow:(m=a==null?void 0:a.eventFlow)!=null?m:void 0})}catch(b){a==null?void 0:(m=a.eventFlow)==null?void 0:m.addPoint("ear_init_err");c("FBLogger")("worm").catching(b).mustfix("EAR initialization error");throw b}a==null?void 0:(m=a.eventFlow)==null?void 0:m.addPoint("ear_init_end");var n=j!=null?j:yield d("WormIDbUpgrade").shouldUpgradeDb(l,k);g=n.shouldUpgrade||((m=a==null?void 0:a.forceUpgrade)!=null?m:!1);if(g){this.$1=null;m=l.version;l.close();this.$1=(yield d("WormIDbUtils").openIDb(this.$2,{onBecomeStale:this.$13,onClose:this.$14,onError:this.$15,onUpgrade:function(a,c){return d("WormIDbUpgrade").upgradeDb(a,c,h,k,{isNewDbInstance:n.isNewDb,maybeEar:b.$7,onlyCreateNewStores:!1,safeToDeleteStores:b.$6})}},m+1))}(m=this.$5)==null?void 0:m.resolve()}catch(a){(m=this.$5)==null?void 0:m.reject(a);throw a}finally{a==null?void 0:(m=a.eventFlow)==null?void 0:m.addAnnotations({bool:{isClosingDb:f,isCompetingInit:e,shouldUpgrade:g}});this.$5=null}});function e(b){return a.apply(this,arguments)}return e}();e.$21=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e){var f=this,g=(yield this.$7.prepareNewKeyVersion()),i=!1,k=!1,l=(yield new(h||(h=b("Promise")))(function(b,e){if(f.$1==null)throw c("err")("IDB is not initialised: "+f.$3);var h=[],l=f.$1.transaction(d("WormIDbTypes").EAR_STORE,"readwrite");l.oncomplete=function(){return b(h)};l.onerror=l.onabort=function(){return e(l.error)};var m=l.objectStore(d("WormIDbTypes").EAR_STORE),n=m.getAll();n.onsuccess=function(){var c;c=(c=n.result)!=null?c:[];h=c.filter(j);i=c.length!==h.length;h.sort(function(a,b){return a.version-b.version});c=h.length===0||!d("WATimeUtils").isInFuture(h[h.length-1].expiration);if(!c){b(h);return}c=h.length===0?0:h[h.length-1].version;k=!a&&c===0;c=babelHelpers["extends"]({},g,{version:c+1});m.add(c);h.push(c)};n.onerror=function(){return e(n.error)}}));yield this.$7.init(l,{incorrectVersions:i,versionsLoss:k},{eventFlow:e==null?void 0:e.eventFlow})});function e(b,c){return a.apply(this,arguments)}return e}();return a}();function j(a){if(a==null)return!1;if(a.version==null)return!1;if(a.expiration==null)return!1;if(a.clientKey==null||a.clientKey.byteLength===0)return!1;return a.salt==null||a.salt.byteLength===0?!1:!0}g.sysSchema=i;g.WormIDbDriver=a}),98);
__d("EBDB",["$InternalEnum","Promise","QPLFlow","WALogger","WAResolvable","Worm","WormEAR","WormIDbDriver","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(){var a=babelHelpers.taggedTemplateLiteralLoose(["Error performing makeEBDB: ",""]);i=function(){return a};return a}e=b("$InternalEnum")({UI:0,Worker:1});var j={auto_restore_opt_out:{primaryKey:"optOutKey"},device_metadata:{primaryKey:"pk"},device_state:{primaryKey:"env"},encrypted_backups:{primaryKey:"pk"},encrypted_backups_virtual_devices:{primaryKey:"pk"},experiences_shared_state:{primaryKey:"stateKey"},migration:{primaryKey:"key",secure:!1},secure_encrypted_backups_client_state:{primaryKey:"backupId"},secure_encrypted_backups_epochs:{primaryKey:"epochId"},secure_encrypted_backups_recovery_code_status:{primaryKey:"pk"}},k=1e4,l=new(d("WAResolvable").Resolvable)();function a(a,b,c,d,e){return m.apply(this,arguments)}function m(){m=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c,e,f){e=d("QPLFlow").startQPLFlow(b,{annotations:{"int":{env:e},string:{operationType:"initEBDB"}},timeoutInMs:k});try{var g="ebdb";c=new(d("WormEAR").WormEAR)(j,g,c);a=new(d("Worm").WormDatabase)(new(d("WormIDbDriver").WormIDbDriver)(a,g,j,c,f,{onTransactionError:function(a){if(a instanceof d("WormEAR").DecryptionError){a=a;if(a.store==="device_state"){void o();return}else void q()}}}),b);yield a.init({eventFlow:e});e.endSuccess();l.resolve(a)}catch(a){e.endFail("error");d("WALogger").ERROR(i(),a);l.reject(a);throw a}});return m.apply(this,arguments)}function n(){return l.promise}function o(){return p.apply(this,arguments)}function p(){p=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var a=(yield n());return a.runInTransaction(["device_state"],"readwrite",function(a){return a.stores.device_state.clear()},"EBDB - clearDeviceState")});return p.apply(this,arguments)}function q(){return r.apply(this,arguments)}function r(){r=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var a=(yield n());yield a.runInTransaction(["encrypted_backups_virtual_devices","secure_encrypted_backups_recovery_code_status","device_metadata","secure_encrypted_backups_epochs","secure_encrypted_backups_client_state","encrypted_backups","experiences_shared_state","auto_restore_opt_out"],"readwrite",function(a){return(h||(h=b("Promise"))).all([a.stores.encrypted_backups_virtual_devices.clear(),a.stores.secure_encrypted_backups_recovery_code_status.clear(),a.stores.device_metadata.clear(),a.stores.secure_encrypted_backups_epochs.clear(),a.stores.secure_encrypted_backups_client_state.clear(),a.stores.encrypted_backups.clear(),a.stores.experiences_shared_state.clear(),a.stores.auto_restore_opt_out.clear()])},"EBDB - clearEbStores")});return r.apply(this,arguments)}function c(){return s.apply(this,arguments)}function s(){s=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var a=Object.keys(j),c=(yield n());return c.runInTransaction(a,"readonly",function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(c){var d={};yield (h||(h=b("Promise"))).all(a.map(function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=(yield c.stores[a].readAll());d[a]=b});return function(b){return a.apply(this,arguments)}}()));return d});return function(a){return c.apply(this,arguments)}}(),"EBDB - Dump")["catch"](function(){return{}})});return s.apply(this,arguments)}g.EBDBEnvironment=e;g.ebdbDescriptor=j;g.makeEBDB=a;g.getEBDB=n;g.clearDeviceState=o;g.clearEbStores=q;g.getDbDump=c}),98);
__d("EBDBConsistencyApi",["$InternalEnum","EBDB","FBLogger","I64","QPLUserFlow","ReQL","asyncToGeneratorRuntime","justknobx","qpl"],(function(a,b,c,d,e,f,g){"use strict";var h,i=["encrypted_backups_virtual_devices","secure_encrypted_backups_recovery_code_status","device_metadata","secure_encrypted_backups_epochs","secure_encrypted_backups_client_state","encrypted_backups","experiences_shared_state","auto_restore_opt_out"],j=null;function a(){return j}function k(a,b){return l.apply(this,arguments)}function l(){l=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){try{var e=(yield d("EBDB").getEBDB()),g=BigInt((h||(h=d("I64"))).to_string(b));yield e.runInTransaction(["device_state"],"readwrite",function(b){return b.stores.device_state.bulkPut([{deviceId:g,env:a}])},"EBDB - AddDevice",f.id+":55");j=g}catch(a){c("FBLogger")("labyrinth_web").catching(a).mustfix("Error on EBDB - AddDevice")}});return l.apply(this,arguments)}function m(a){return d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.tables.secure_encrypted_backups_client_state)).then(function(a){return a==null?void 0:a.deviceId})}function e(a,b,c){return n.apply(this,arguments)}function n(){n=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,d){if(d!=null)return k(b,d);try{d=(yield m(a));if(d==null)return;return k(b,d)}catch(a){c("FBLogger")("labyrinth_web").catching(a).mustfix("Error on fetching secure_encrypted_backups_client_state")}});return n.apply(this,arguments)}function o(a,b){return p.apply(this,arguments)}function p(){p=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e){c("QPLUserFlow").addPoint(e,"track_overprompting_start");try{var g=(yield d("EBDB").getEBDB()),h=!1;yield g.runInTransaction(["device_state"],"readwrite",function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){b=(yield b.stores.device_state.get(a));(b==null?void 0:b.deviceId)!=null&&(h=!0)});return function(a){return c.apply(this,arguments)}}(),"EBDB - AddDevice",f.id+":108");c("QPLUserFlow").addAnnotations(e,{bool:{overprompting:h}});c("QPLUserFlow").addPoint(e,"track_overprompting_end")}catch(a){c("QPLUserFlow").addPoint(e,"track_overprompting_fail"),c("FBLogger")("labyrinth_web").catching(a).mustfix("Error on track overpropting")}});return p.apply(this,arguments)}var q=b("$InternalEnum")({Disabled:0,EbsmInconsistent:1,EbdbInconsistent:2,Enabled:3,Inconsistent:4});function r(a){return s.apply(this,arguments)}function s(){s=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){a=(yield m(a));a=a!=null?BigInt((h||(h=d("I64"))).to_string(a)):null;if(a==null&&j==null)return q.Disabled;else if(a==null&&j!=null)return q.EbsmInconsistent;else if(a!=null&&j==null)return q.EbdbInconsistent;else if(a===j)return q.Enabled;else return q.Inconsistent});return s.apply(this,arguments)}function t(a,b,c,d){return u.apply(this,arguments)}function u(){u=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,d,e){var f=1/c("justknobx")._("3914");if(Math.random()>=f)return;f=c("qpl")._(521471316,"2831");try{c("QPLUserFlow").start(f);a=(yield r(a));e!=null&&c("QPLUserFlow").addAnnotations(f,e());c("QPLUserFlow").endSuccess(f,{annotations:{"int":{consistency:a,env:b,operation:d}}})}catch(a){c("QPLUserFlow").endFailure(f,"error"),c("FBLogger")("labyrinth_web").catching(a).mustfix("Error on trackConsistency in %s",b)}});return u.apply(this,arguments)}function v(){return w.apply(this,arguments)}function w(){w=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){yield d("EBDB").clearDeviceState(),j=null});return w.apply(this,arguments)}var x;function y(a,b){return z.apply(this,arguments)}function z(){z=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){x!=null&&x();yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.tables.encrypted_backups)).then(function(a){a!=null&&((a==null?void 0:a.backupId)==null||(a==null?void 0:a.isUserOptedOut)===!0)&&void v()})["catch"](function(a){c("FBLogger")("labyrinth_web").catching(a).mustfix("EBDBApi: EBLS error on fetching data from encrypted_backups")});x=a.tables.encrypted_backups.subscribe(function(a,b){b.operation!=="delete"&&((b.value.backupId==null||b.value.isUserOptedOut===!0)&&void v())});var e=(yield d("EBDB").getEBDB()),g=(yield e.runInTransaction(["device_state"],"readonly",function(a){return a.stores.device_state.get(b)},"EBDB - readDeviceState",f.id+":251")["catch"](function(a){c("FBLogger")("labyrinth_web").catching(a).mustfix("EBDB error: fetching device_state")}));j=A(g==null?void 0:g.deviceId);if(j==null){g=(yield m(a));if(g==null)return;var i=BigInt((h||(h=d("I64"))).to_string(g));j=i;yield e.runInTransaction(["device_state"],"readwrite",function(a){return a.stores.device_state.bulkPut([{deviceId:i,env:b}])},"EBDB - writeDeviceState",f.id+":273")["catch"](function(a){c("FBLogger")("labyrinth_web").catching(a).mustfix("EBDB error: write device_state")})}});return z.apply(this,arguments)}function A(a){if(a==null)return;if(typeof a==="bigint")return a;if(typeof a==="number")return BigInt(a);if(typeof a==="string")try{return BigInt(a)}catch(a){c("FBLogger")("labyrinth_web").catching(a).mustfix("EBDBApi: Parsing string as BigInt");void v();return}if(Array.isArray(a))try{return BigInt((h||(h=d("I64"))).to_string(a))}catch(a){c("FBLogger")("labyrinth_web").catching(a).mustfix("EBDBApi: Parsing I64 as BigInt");void v();return}c("FBLogger")("labyrinth_web").mustfix("EBDBApi: Parsing unknown as BigInt %s",String(a));void v()}g.EB_STORES=i;g.getDeviceId=a;g.addNewDevice=e;g.trackOverprompting=o;g.EBConsistencyResult=q;g.checkRegistrationConsistency=r;g.trackConsistency=t;g.clearDeviceState=v;g.startListeningDeviceRegistrations=y}),98);
__d("EBDBEbsmApi",["$InternalEnum","EBDB","FBLogger","I64","Promise","QPLUserFlow","ReStorePersistence","asyncToGeneratorRuntime","qpl"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=["encrypted_backups_virtual_devices","secure_encrypted_backups_recovery_code_status","device_metadata","secure_encrypted_backups_epochs","secure_encrypted_backups_client_state","encrypted_backups","experiences_shared_state","auto_restore_opt_out"],k=b("$InternalEnum").Mirrored(["Success","EmptyDb"]);function l(a){var b={};for(var c of Object.keys(a)){var e=a[c];typeof e==="bigint"?b[c]=(i||(i=d("I64"))).of_string(String(e)):b[c]=e}return b}function a(a){return m.apply(this,arguments)}function m(){m=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var c=(yield d("EBDB").getEBDB()),e=Array.from(a.keys());yield c.runInTransaction(e,"readwrite",function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(c){var e=[],f=function(a){var b=a[0];j=a[1];a=j.toAdd;var f=j.toDelete,g=d("EBDB").ebdbDescriptor==null?void 0:d("EBDB").ebdbDescriptor[b];e.push(c.stores[b].bulkDelete(f.map(function(a){a=typeof a==="object"?a==null?void 0:a[g.primaryKey]:null;if(a==null)return;return BigInt((i||(i=d("I64"))).to_string(a))}).filter(function(a){return!!a})),c.stores[b].bulkPut(a.filter(function(a){return typeof a==="object"&&(a==null?void 0:a[g.primaryKey])!=null}).map(function(a){var b={};for(var c of Object.keys(a)){var e=a[c];(i||(i=d("I64"))).isI64(e)?b[c]=BigInt((i||(i=d("I64"))).to_string(e)):b[c]=e}return b})))};for(var g of a){var j;f(g)}yield (h||(h=b("Promise"))).all(e)});return function(a){return c.apply(this,arguments)}}(),"EBDB - flush EBSM state")});return m.apply(this,arguments)}function n(a){return o.apply(this,arguments)}function o(){o=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=(yield a.next());while(b.done===!1)b=(yield a.next());return b.value});return o.apply(this,arguments)}function p(a,b,c){return q.apply(this,arguments)}function q(){q=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c){a=a(b);a=new(d("ReStorePersistence").ReStoreDbStoreTable)(a,b,function(){},{get:function(){},logError:function(){},shouldInline:function(){return!1}},"readwrite",function(){});for(c of c){var e=l(c),f=e[d("EBDB").ebdbDescriptor[b].primaryKey];yield n(a.tableSet(new WeakMap(),[f],e))}});return q.apply(this,arguments)}function e(a){return r.apply(this,arguments)}function r(){r=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){c("QPLUserFlow").addPoint(c("qpl")._(521481876,"1407"),"EBSM_HYDRATION_LOAD_DATA_FROM_EBDB");var e=(yield d("EBDB").getEBDB()),f=!0,g=new Map();yield e.runInTransaction(j,"readonly",function(a){return(h||(h=b("Promise"))).all(j.map(function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){var c=(yield a.stores[b].readAll());f=f&&c.length===0;g.set(b,c)});return function(a){return c.apply(this,arguments)}}()))},"EBDB - fetch for rehydration EBSM");c("QPLUserFlow").addPoint(c("qpl")._(521481876,"1407"),"EBSM_HYDRATION_LOAD_DATA_FROM_EBDB_DONE");if(f)return k.EmptyDb;var i;try{yield (h||(h=b("Promise"))).all(Array.from(g).map(function(){var d=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){var d=b[0];b=b[1];try{yield p(a,d,b),c("QPLUserFlow").addPoint(c("qpl")._(521481876,"1407"),"EBSM_HYDRATION_DONE_ON_"+d.toUpperCase())}catch(a){i=d;c("QPLUserFlow").addPoint(c("qpl")._(521481876,"1407"),"EBSM_HYDRATION_FAILED_ON_"+d.toUpperCase());throw a}});return function(a){return d.apply(this,arguments)}}()))}catch(b){c("FBLogger")("labyrinth_web").catching(b).mustfix("rehydrateEBSMFromEBDB: Invalid data to build BTree: %s",i);void d("EBDB").clearEbStores();for(e of j){var l=a(e);l.clear()}return k.EmptyDb}return k.Success});return r.apply(this,arguments)}g.EB_STORES=j;g.RehydrationResult=k;g.flushEBSMtoEBDB=a;g.rehydrateEBSMFromEBDB=e}),98);
__d("EBMainThreadEBDBApi",["$InternalEnum","EBDB","EBDBConsistencyApi","FBLogger","I64","LSDatabaseSingleton","Promise","ReQL","WAResolvable","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=new(d("WAResolvable").Resolvable)();function a(){return l.apply(this,arguments)}function l(){l=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var a=(yield (i||(i=d("LSDatabaseSingleton"))).LSDatabaseSingleton);try{yield d("EBDBConsistencyApi").startListeningDeviceRegistrations(a,d("EBDB").EBDBEnvironment.UI)}catch(a){c("FBLogger")("labyrinth_web").catching(a).mustfix("Error on startListeningDeviceRegistrations in UI")}finally{k.resolve(a)}});return l.apply(this,arguments)}function e(a){return m.apply(this,arguments)}function m(){m=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){yield d("EBDBConsistencyApi").trackOverprompting(d("EBDB").EBDBEnvironment.UI,a)});return m.apply(this,arguments)}function n(a){return o.apply(this,arguments)}function o(){o=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=(yield k.promise);yield d("EBDBConsistencyApi").addNewDevice(b,d("EBDB").EBDBEnvironment.UI,a)});return o.apply(this,arguments)}function p(){return d("EBDB").getDbDump()}function q(){return r.apply(this,arguments)}function r(){r=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var a=(yield d("EBDB").getEBDB());return a.runInTransaction(["device_state"],"readwrite",function(a){return a.stores.device_state.bulkDelete([d("EBDB").EBDBEnvironment.Worker])},"EBDB - clear worker state",f.id+":64")});return r.apply(this,arguments)}var s=b("$InternalEnum")({Disabled:0,UIInconsistent:1,WorkerInconsistent:2,Consistent:3});function t(a,b){return u.apply(this,arguments)}function u(){u=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var c=(yield k.promise);void d("EBDBConsistencyApi").trackConsistency(c,d("EBDB").EBDBEnvironment.UI,a,function(){var a=s.Disabled,c=d("EBDBConsistencyApi").getDeviceId();c!=null&&b!=null?a=s.Consistent:c!=null&&b==null?a=s.WorkerInconsistent:c==null&&b!=null&&(a=s.UIInconsistent);return{"int":{crossEnvConsistency:a}}})});return u.apply(this,arguments)}function v(a){var b={};for(var c of Object.keys(a)){var e=a[c];(j||(j=d("I64"))).isI64(e)?b[c]=BigInt((j||(j=d("I64"))).to_string(e)):b[c]=e}return b}function w(a){return x.apply(this,arguments)}function x(){x=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var e=(yield d("EBDB").getEBDB());try{var g=(yield e.store("migration").get("isMigrated"));if((g==null?void 0:g.value)===!0)return;var i=(yield (h||(h=b("Promise"))).all(d("EBDBConsistencyApi").EB_STORES.map(function(b){return d("ReQL").toArrayAsync(d("ReQL").fromTableAscending(a.tables[b]))})));yield e.runInTransaction([].concat(d("EBDBConsistencyApi").EB_STORES,["migration"]),"readwrite",function(a){return(h||(h=b("Promise"))).all([].concat(d("EBDBConsistencyApi").EB_STORES.map(function(b,c){return a.stores[b].bulkPut(i[c].map(v))}),[a.stores.migration.bulkPut([{key:"isMigrated",value:!0}])]))},"EBDB - migration EBSM -> WORM",f.id+":132")}catch(a){c("FBLogger")("labyrinth_web").catching(a).mustfix("Error on migrating EBSM to WORM in UI"),void e.runInTransaction(["migration"],"readwrite",function(a){return a.stores.migration.bulkPut([{key:"isMigrated",value:!0}])},"EBDB - migration EBSM -> WORM: no-migration",f.id+":152")}});return x.apply(this,arguments)}g.startListeningDeviceRegistrations=a;g.trackOverprompting=e;g.addNewDevice=n;g.getEBDBDump=p;g.clearWorkerState=q;g.EBCrossEnvConsistencyResult=s;g.trackConsistency=t;g.migrateEBSMtoWORM=w}),98);
__d("LSDatabase",[],(function(a,b,c,d,e,f){}),null);
__d("LSSyncGroupsUtils",["CurrentLocale","LSIntEnum","LSMailboxInitialSyncCursor"],(function(a,b,c,d,e,f,g){"use strict";var h;a={locale:c("CurrentLocale").get()};b={canIgnoreTimestamp:!1,currentCursor:void 0,dataTraceId:void 0,groupId:(h||(h=d("LSIntEnum"))).ofNumber(0),initTraceTimestampMs:void 0,lastSyncCompletedTimestampMs:void 0,lastSyncRequestTimestampMs:h.ofNumber(0),minTimeToSyncTimestampMs:h.ofNumber(-1),priority:h.ofNumber(0),regionHint:void 0,sendSyncParams:!0,syncChannel:d("LSMailboxInitialSyncCursor").syncChannel,syncParams:JSON.stringify(a),syncStatus:h.ofNumber(0)};e={epochId:void 0,failureCount:h.ofNumber(0),lastDelayedRequestTimestampMs:void 0,lastSentTimestampMs:h.ofNumber(0),lastSyncRequestTimestampMs:h.ofNumber(0),networkTaskIdentifier:void 0,syncDatabaseId:h.ofNumber(0),taskQueueName:""};f=h.ofNumber(9999999999999);g.defaultSyncParams=a;g.defaultSyncGroup=b;g.defaultNetworkRequest=e;g.neverSyncTimestamp=f}),98);
__d("MNetRankType",[],(function(a,b,c,d,e,f){a=Object.freeze({UNDEFINED_RANKING_TYPE:-1,INBOX_ACTIVE_NOW:0,MESSENGER_USER_SEARCH:1,MONTAGE_USER:2,BROADCAST_FLOW_TOP_CONTACTS:3,BROADCAST_FLOW_NEEDY_CONTACTS:4,MESSENGER_GROUP_SEARCH:5,RTC_TOP_CONTACTS:6,PSTN_TOP_CONTACTS:7,MESSENGER_NON_CONTACT_SEARCH:8,MESSENGER_SEARCH_BOOTSTRAP:9,ACTIVE_BEEPER:10,MONTAGE_AND_ACTIVE_NOW:11,MESSENGER_PAGE_SEARCH:12,MESSENGER_GAME_SEARCH:13,WWW_NULLSTATE:14,RTC_GROWTH:15,MESSENGER_OMNIPICKER_NULLSTATE:16,RTC_SEQUENTIAL_TOP_CONTACTS:17,MESSENGER_USER_SEARCH_NULLSTATE:18,MLITE_DIODE_PROMOTION:19,MESSENGER_PENDING_REQUEST:20,MESSENGER_CLOSE_CONNECTION:21,MESSENGER_MONTAGE_SEEN_SHEET:22,MESSENGER_OMNIPICKER_KEYPRESS:23,MESSENGER_NOTIF_QP_TARGETING_UPSELL_TYPE:24,CONTACT_TAB_ACTIVE_NOW:26,MESSENGER_UNIV_NULLSTATE_BLEND:27,MESSENGER_INBOX_THREADS:28,INBOX_ACTIVE_NOW_NO_BOOSTING:29,BROADCAST_FLOW_TOP_CONTACTS_FB_SHARE:30,BROADCAST_FLOW_TOP_CONTACTS_MESSENGER_SHARE:31,BROADCAST_FLOW_TOP_CONTACTS_EXTERNAL_SHARE:32,MESSENGER_TRENDING_STICKERS:33,MESSENGER_BROADCAST_FLOW_TOP_THREADS:34,MESSENGER_SENDS_28D:35,MESSENGER_ROOM_INVITE:36,MESSENGER_ROOM_INVITE_SEARCH:37,INBOX_ACTIVE_NOW_PREFETCH:38,MESSENGER_BLENDED_KEYPRESS:39,MESSENGER_BLENDED_NULLSTATE:40,MESSENGER_CARRIER_MESSAGING:41,MESSENGER_ROOM_INVITE_GROUP:42,MESSENGER_INBOX_BIRTHDAY_ITEM:43,INSTAGRAM_DIRECT_SEARCH_NULLSTATE:44,BROADCAST_FLOW_TOP_CONTACTS_AND_TOP_THREAD_FB_SHARE:45,BROADCAST_FLOW_TOP_THREADS_FB_SHARE:46,FB_H_SCROLL_RANKING:47,FB_MESSAGING_USER_SEARCH:48,FB_MESSAGING_USER_SEARCH_NULLSTATE:49,MESSENGER_ACTIVE_NOW_TRAY_ACTIVE_CC:50,MESSENGER_SHARE_SHEET:51,MESSENGER_COMMUNITY_TAB_UNJOINED_COMMUNITIES:52,MESSENGER_BCF_FREQUENTS:53,FB_ORIGINAL_PRIVATE_SHARING:54,MESSENGER_SEARCH_NULLSTATE_FOLLOWER:55});f["default"]=a}),66);
__d("isSearchQualityEnabled",["gkx"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("gkx")("3283")}g["default"]=a}),98);
__d("seedUserInfo",["CurrentMessengerUser","I64","LSIntEnum","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a){return j.apply(this,arguments)}function j(){j=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("CurrentMessengerUser").getIDorEIMU();yield a._user_info.put({facebookUserId:(h||(h=d("I64"))).of_string(b),id:(i||(i=d("LSIntEnum"))).ofNumber(1)})});return j.apply(this,arguments)}g["default"]=a}),98);
__d("LSDefaultSyncGroups",["CurrentEnvironment","I64","LSE2EEMetadataSyncGroupUtils","LSIntEnum","LSMailboxInitialSyncCursor","LSPlatformLsInitLog","LSSyncGroupsUtils","MNetRankType","Promise","ReQL","asyncToGeneratorRuntime","cr:17238","gkx","isInstamadillo","isSearchQualityEnabled","justknobx","promiseDone","seedUserInfo"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=new Set();d("LSE2EEMetadataSyncGroupUtils").shouldAlwaysResetE2EEMetadata()&&k.add(95);var l=c("justknobx")._("3638")||c("isInstamadillo")();e=c("justknobx")._("3637");f=d("LSE2EEMetadataSyncGroupUtils").getE2EEMetadataSyncGroup();e=[{groupId:1,lastSyncRequestTimestampMs:d("LSMailboxInitialSyncCursor").lastSyncTimestampMs,syncChannel:d("LSMailboxInitialSyncCursor").syncChannel,syncParams:d("LSMailboxInitialSyncCursor").syncParams},{groupId:2,lastSyncRequestTimestampMs:d("LSMailboxInitialSyncCursor").lastSyncTimestampMs,syncChannel:d("LSMailboxInitialSyncCursor").syncChannel},{groupId:16,syncChannel:d("LSSyncGroupsUtils").defaultSyncGroup.syncChannel},c("gkx")("4246")?null:{groupId:28,syncChannel:d("LSSyncGroupsUtils").defaultSyncGroup.syncChannel,syncParams:JSON.stringify(babelHelpers["extends"]({},d("LSSyncGroupsUtils").defaultSyncParams,{size_type:"wide"}))},{groupId:118,minTimeToSyncTimestampMs:d("LSSyncGroupsUtils").neverSyncTimestamp,syncChannel:d("LSSyncGroupsUtils").defaultSyncGroup.syncChannel},{groupId:198},l?{groupId:89}:null,e?{groupId:197}:null];var m=[].concat(e,[c("gkx")("10282")?null:{groupId:6},{groupId:7,syncParams:JSON.stringify({mnet_rank_types:[c("MNetRankType").INSTAGRAM_DIRECT_SEARCH_NULLSTATE]})},d("LSE2EEMetadataSyncGroupUtils").getE2EEMetadataSyncGroupForIGDSyncGroup()]),n=[].concat(e,[{groupId:7,minTimeToSyncTimestampMs:d("LSSyncGroupsUtils").neverSyncTimestamp,syncParams:JSON.stringify({mnet_rank_types:[c("MNetRankType").MESSENGER_USER_SEARCH,c("isSearchQualityEnabled")()?c("MNetRankType").MESSENGER_BLENDED_NULLSTATE:c("MNetRankType").MESSENGER_USER_SEARCH_NULLSTATE,c("MNetRankType").INBOX_ACTIVE_NOW,c("MNetRankType").MESSENGER_OMNIPICKER_NULLSTATE,c("MNetRankType").MESSENGER_BROADCAST_FLOW_TOP_THREADS,c("MNetRankType").BROADCAST_FLOW_TOP_CONTACTS]})},{groupId:15,minTimeToSyncTimestampMs:d("LSSyncGroupsUtils").neverSyncTimestamp,syncChannel:d("LSSyncGroupsUtils").defaultSyncGroup.syncChannel},{groupId:12,minTimeToSyncTimestampMs:d("LSSyncGroupsUtils").neverSyncTimestamp,syncChannel:d("LSSyncGroupsUtils").defaultSyncGroup.syncChannel},{groupId:26},{groupId:196},f,c("gkx")("24113")?{groupId:120,syncChannel:d("LSSyncGroupsUtils").defaultSyncGroup.syncChannel}:null,c("gkx")("26390")?{groupId:6,minTimeToSyncTimestampMs:d("LSSyncGroupsUtils").neverSyncTimestamp}:null,{groupId:145,syncChannel:d("LSSyncGroupsUtils").defaultSyncGroup.syncChannel},c("gkx")("5985")?{groupId:202}:null]).concat(c("gkx")("22843")?[{groupId:104,syncChannel:d("LSSyncGroupsUtils").defaultSyncGroup.syncChannel},{groupId:143},{groupId:142},{groupId:140},{groupId:141}]:[]);function o(a,b){return p.apply(this,arguments)}function p(){p=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c){var e=(yield d("ReQL").toArrayAsync(d("ReQL").fromTableAscending(a.network_requests).getKeyRange(""))),f=e.map(function(a){a=a.syncDatabaseId;return(j||(j=d("LSIntEnum"))).toNumber(a)});return(h||(h=b("Promise"))).all(c.map(function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){b!=null&&(!f.includes(b.groupId)||k.has(b.groupId))&&(yield a.network_requests.put(babelHelpers["extends"]({},d("LSSyncGroupsUtils").defaultNetworkRequest,{syncDatabaseId:(j||(j=d("LSIntEnum"))).ofNumber(b.groupId)})))});return function(a){return c.apply(this,arguments)}}()))});return p.apply(this,arguments)}function q(a){return r.apply(this,arguments)}function r(){r=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){a=(yield d("ReQL").toArrayAsync(d("ReQL").fromTableAscending(a.sync_groups)));return new Map(a.map(function(a){return[(j||(j=d("LSIntEnum"))).toNumber(a.groupId),a]}))});return r.apply(this,arguments)}function s(a,b){return t.apply(this,arguments)}function t(){t=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c){var e=(yield q(a));function f(a){return g.apply(this,arguments)}function g(){g=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){var c=b.groupId;b=babelHelpers.objectWithoutPropertiesLoose(b,["groupId"]);var f=e.get(c);f==null||k.has(c)?yield a.sync_groups.put(babelHelpers["extends"]({},d("LSSyncGroupsUtils").defaultSyncGroup,b,{groupId:(j||(j=d("LSIntEnum"))).ofNumber(c)})):f!=null&&(f.syncParams!==b.syncParams||b.syncChannel!=null&&!(i||(i=d("I64"))).equal(f.syncChannel,b.syncChannel))&&(yield a.sync_groups.put(babelHelpers["extends"]({},f,{syncChannel:b.syncChannel!=null?b.syncChannel:f.syncChannel,syncParams:b.syncParams})))});return g.apply(this,arguments)}yield (h||(h=b("Promise"))).all(c.map(function(a){return a&&f(a)}))});return t.apply(this,arguments)}function u(a){return a!=null?a:c("CurrentEnvironment").instagramdotcom?m:n}function a(a,b){return v.apply(this,arguments)}function v(){v=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e){e=u(e);d("LSPlatformLsInitLog").addPoint("db_seed_start");yield c("seedUserInfo")(a);yield o(a,e);yield s(a,e);l&&b("cr:17238")!=null&&c("promiseDone")(b("cr:17238").prewarmAcsVoprfWasmModule());d("LSPlatformLsInitLog").addPoint("db_seed_end")});return v.apply(this,arguments)}g.e2eeMetadataSyncGroup=f;g.igdSyncGroups=m;g.defaultSyncGroups=n;g.seedDb=a}),98);
__d("MAWDropCutoverThreadsTable",["Promise","ReQL"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){var c=d("ReQL").fromTableAscending(a.cutover_threads);return d("ReQL").toArrayAsync(c).then(function(c){return c.reduce(function(b,c){return b.then(function(){return a.cutover_threads["delete"](c.openThreadId)})},(h||(h=b("Promise"))).resolve())})}g.call=a}),98);
__d("ReStoreDecryptionFailure",[],(function(a,b,c,d,e,f){"use strict";var g="ReStoreDecryptionFailure";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c){var d;b=b+" - Encrypted Restore was unable to decrypt an entity for table "+c;d=a.call(this,b)||this;d.message=b;d.name=g;d.tableName=c;return d}var c=b.prototype;c.getTableName=function(){return this.tableName};return b}(babelHelpers.wrapNativeSuper(Error));f.ERROR_NAME=g;f.ReStoreDecryptionFailure=a}),66);
__d("ReStoreEARSetupFailure",[],(function(a,b,c,d,e,f){"use strict";var g="Encryption keychain could not be setup",h="ReStoreEARSetupFailure";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b){var c;b=g+": "+b;c=a.call(this,b)||this;c.message=b;c.name=h;return c}return b}(babelHelpers.wrapNativeSuper(Error));f.ERROR_MESSAGE=g;f.ERROR_NAME=h;f.ReStoreEARSetupFailure=a}),66);
__d("ReStoreEvent",[],(function(a,b,c,d,e,f){"use strict";a=function(){};f["default"]=a}),66);
__d("ReStoreIndexedDbClosedEvent",["ReStoreEvent"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c){var d;d=a.call(this)||this;d.idb=b;d.error=c;return d}var c=b.prototype;c.getError=function(){return this.error};return b}(c("ReStoreEvent"));g["default"]=a}),98);
__d("ReStoreIndexedDbFailToOpenEvent",["ReStoreEvent"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(){return a.apply(this,arguments)||this}return b}(c("ReStoreEvent"));g["default"]=a}),98);
__d("ReStoreIndexedDbUpgradedEvent",["ReStoreEvent"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(){return a.apply(this,arguments)||this}return b}(c("ReStoreEvent"));g["default"]=a}),98);
__d("ReStoreUnexpectedlyClosed",[],(function(a,b,c,d,e,f){"use strict";a="ReStoreUnexpectedlyClosed";b=function(a){babelHelpers.inheritsLoose(b,a);function b(){return a.apply(this,arguments)||this}return b}(babelHelpers.wrapNativeSuper(Error));f.ERROR_NAME=a;f.ReStoreUnexpectedlyClosed=b}),66);
__d("GetLsDatabase",["ExecutionEnvironment","LSDefaultSyncGroups","LSPlatformErrorChannel","LSPlatformLsInitLog","MAWDropCutoverThreadsTable","MessengerLogHistory","MultipleTabsLogger","Promise","ReStoreDecryptionFailure","ReStoreEARSetupFailure","ReStoreIndexedDbClosedEvent","ReStoreIndexedDbFailToOpenEvent","ReStoreIndexedDbUpgradedEvent","ReStoreUnexpectedlyClosed","UserTimingUtils","asyncToGeneratorRuntime","cr:10099","cr:10200","cr:10755","cr:4489","cr:6218","cr:665","cr:7000","cr:7351","cr:8659","cr:8915","justknobx","promiseDone","qex","qpl"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=(e=b("cr:6218"))!=null?e:b("cr:10755"),k=(e=b("cr:7000"))!=null?e:b("cr:7351");function l(a){if(a instanceof c("ReStoreIndexedDbUpgradedEvent")&&j!=null&&c("justknobx")._("1410"))c("promiseDone")(j.killSharedWorker(!0,"ls_upgrade"));else if(a instanceof c("ReStoreIndexedDbClosedEvent")){a=a.getError();b("cr:8915")!=null&&a instanceof(b("cr:8915")==null?void 0:b("cr:8915").ReStoreDbVersionChange)?c("LSPlatformErrorChannel").emit(a):a instanceof d("ReStoreDecryptionFailure").ReStoreDecryptionFailure?c("LSPlatformErrorChannel").emit(a):a instanceof d("ReStoreEARSetupFailure").ReStoreEARSetupFailure?c("LSPlatformErrorChannel").emit(a):a instanceof d("ReStoreUnexpectedlyClosed").ReStoreUnexpectedlyClosed&&b("cr:10099")!=null?(b("cr:10099")==null?void 0:b("cr:10099").releaseIfExist(),c("LSPlatformErrorChannel").emit(a)):a instanceof c("ReStoreIndexedDbFailToOpenEvent")&&b("cr:10099")!=null&&(b("cr:10099")==null?void 0:b("cr:10099").releaseIfExist())}}var m=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var a=d("MessengerLogHistory").getInstance("db_init");c("promiseDone")(d("MultipleTabsLogger").getMultipleTabsAnnotation().then(function(b){a.debug("Has multiple tabs: "+b),d("MultipleTabsLogger").addAnnotationToQPLEvent(c("qpl")._(25305590,"1127"))}));a.debug("====Creating DB====");d("LSPlatformLsInitLog").addPoint("vaulting_setup_start");b("cr:4489")!=null&&b("cr:665")!=null&&(i||(i=c("ExecutionEnvironment"))).isInBrowser&&(a.debug("Vaulting setup started"),b("cr:665").setupMainWaLogger(),yield d("UserTimingUtils").asyncMeasure("Setup Vaulting Materials",b("asyncToGeneratorRuntime").asyncToGenerator(function*(){yield b("cr:4489").setupVaultMaterials()})),a.debug("Vaulting setup finished"));d("LSPlatformLsInitLog").addPoint("vaulting_setup_end");a.debug("Connecting to DB");var e=b("cr:8659")!=null?yield b("cr:8659")():yield b("cr:10200").createDB(l);a.debug("Connection established");yield e.runInTransaction(function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(c){var e=k==null?[(h||(h=b("Promise"))).resolve(),(h||(h=b("Promise"))).resolve()]:[k.call(c),d("MAWDropCutoverThreadsTable").call(c)],f=e[0];e=e[1];yield (h||(h=b("Promise"))).all([f,e]);d("LSPlatformLsInitLog").addPoint("success_maw_data_deletion");a.debug("Seeding DB");yield d("LSDefaultSyncGroups").seedDb(c,void 0);a.debug("DB seeded")});return function(a){return c.apply(this,arguments)}}(),"readwrite","ui",void 0,f.id+":119");a.debug("====DB created====");return e});return function(){return a.apply(this,arguments)}}(),n;function a(){n==null&&(n=(i||(i=c("ExecutionEnvironment"))).isInBrowser||c("qex")._("3809")?m():new(h||(h=b("Promise")))(function(){}));return n}g.get=a}),98);
__d("LSVoprfWasmPrewarmDeferred",["WAWasmModuleCache","asyncToGeneratorRuntime","requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h=c("requireDeferred")("LSVoprfWasm").__setRef("LSVoprfWasmPrewarmDeferred");function a(){return i.apply(this,arguments)}function i(){i=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var a=(yield h.load());a=a.locateFile;yield d("WAWasmModuleCache").loadWasmModule(a())});return i.apply(this,arguments)}g.prewarmAcsVoprfWasmModule=a}),98);
__d("MAWBridgeDropAllSecureThreadsHandler",["I64","LSIntEnum","LSMessagingThreadTypeUtil","Promise","ReQL"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;function k(a){var c=d("ReQL").fromTableAscending(a.threads_ranges_v2__generated).filter(function(a){return(i||(i=d("I64"))).equal(a.parentThreadKey,(j||(j=d("LSIntEnum"))).ofNumber(-1))||(i||(i=d("I64"))).equal(a.parentThreadKey,(j||(j=d("LSIntEnum"))).ofNumber(-10))});return d("ReQL").toArrayAsync(c).then(function(c){return c.reduce(function(b,c){return b.then(function(){return a.threads_ranges_v2__generated["delete"](c.parentThreadKey,c.minThreadKey,c.minLastActivityTimestampMs)})},(h||(h=b("Promise"))).resolve())})}function l(a){var c=d("ReQL").fromTableAscending(a.threads).filter(function(a){return d("LSMessagingThreadTypeUtil").isArmadilloSecure(a.threadType)});return d("ReQL").toArrayAsync(c).then(function(c){return c.reduce(function(b,c){return b.then(function(){return a.threads["delete"](c.threadKey)})},(h||(h=b("Promise"))).resolve())})}function a(a){return(h||(h=b("Promise"))).all([k(a),l(a)]).then(function(){return(h||(h=b("Promise"))).resolve()})}g.call=a}),98);
__d("MWConditionallySetupEBStateDB",["EBDB","EBDBEbsmApi","EBReadyNotifier","ExecutionEnvironment","MAWCurrentUser","MAWIndexedDbMetadata","MessengerWebInitData","ODS","WAHex","WAOdsEnums","WormGlobalConfig","asyncToGeneratorRuntime","cr:6693","cr:8233","gkx","qpl"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a){return j.apply(this,arguments)}function j(){j=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){a===void 0&&(a=!1);if(b("cr:6693")!=null&&(i||(i=c("ExecutionEnvironment"))).isInBrowser){d("WormGlobalConfig").setWormGlobalConfig({handleNotFoundErrorExperiment:function(){return c("gkx")("11548")},handleUnknownErrorExperiment:function(){return c("gkx")("11549")},treatMalformedEncryptedEntityAsDecryptionError:function(){return c("gkx")("12682")},useNoWABinary:function(){return c("gkx")("14807")}});b("cr:8233")==null?void 0:b("cr:8233").setupMainWaLogger();void d("EBDB").makeEBDB(d("MAWIndexedDbMetadata").ebdbName(d("MAWCurrentUser").getID()),c("qpl")._(**********,"2716"),d("WAHex").parseHex(c("MessengerWebInitData").accountKeyV2),d("EBDB").EBDBEnvironment.UI,{log:function(a){return(h||(h=d("ODS"))).bumpEntityKey(3185,d("WAOdsEnums").Entity.WORM,a)}});a=(yield b("cr:6693").makeEBStateDB({clearEbStores:d("EBDB").clearEbStores,flushEBSMtoEBDB:d("EBDBEbsmApi").flushEBSMtoEBDB,rehydrateEBSMFromEBDB:d("EBDBEbsmApi").rehydrateEBSMFromEBDB},{force:a,getCache:!0,isWorker:!1}));d("EBReadyNotifier").markEBReady();return a}});return j.apply(this,arguments)}g.conditionallyMakeEBStateDB=a}),98);
__d("MWLSSchemaEphemeral",["EBMainThreadEBDBApi","FBLogger","LSJSInMemoryStorage","LSMetadata","LSPlatformLsInitLog","LSReStoreWrapper","MAWLSVaultingHooks","MWConditionallySetupEBStateDB","MessengerLogHistory","asyncToGeneratorRuntime","cr:8624"],(function(a,b,c,d,e,f,g){"use strict";var h=d("MessengerLogHistory").getInstance("db_init");function a(a){return i.apply(this,arguments)}function i(){i=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){d("LSPlatformLsInitLog").addPoint("init_start");var e;try{a=(yield d("MWConditionallySetupEBStateDB").conditionallyMakeEBStateDB());a!=null&&(h.debug("Creating EB State DB"),e=d("LSReStoreWrapper").createLSReStore(a,d("LSMetadata").schema,[c("MAWLSVaultingHooks")],void 0,d("LSPlatformLsInitLog").lsInitLogger),yield b("cr:8624")==null?void 0:b("cr:8624")(e),yield d("EBMainThreadEBDBApi").migrateEBSMtoWORM(e),h.debug("EB State DB inited"))}catch(a){c("FBLogger")("messenger_web_clients").catching(a).mustfix("EBSM failed")}e==null&&(h.debug("Creating EDB"),e=c("LSJSInMemoryStorage")(d("LSMetadata").schema),h.debug("EDB inited"));d("LSPlatformLsInitLog").addPoint("init_end");return e});return i.apply(this,arguments)}g.createDB=a}),98);