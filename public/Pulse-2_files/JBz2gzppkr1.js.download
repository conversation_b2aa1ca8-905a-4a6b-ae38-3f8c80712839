;/*FB_PKG_DELIM*/

__d("PolarisProfileTaggedTabContentQuery_connection_instagramRelayOperation",[],(function(a,b,c,d,e,f){e.exports="10023104167784295"}),null);
__d("PolarisProfileTaggedTabContentQuery_connection.graphql",["PolarisProfileTaggedTabContentQuery_connection_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a=function(){var a=[{defaultValue:null,kind:"LocalArgument",name:"after"},{defaultValue:null,kind:"LocalArgument",name:"before"},{defaultValue:null,kind:"LocalArgument",name:"count"},{defaultValue:null,kind:"LocalArgument",name:"first"},{defaultValue:null,kind:"LocalArgument",name:"last"},{defaultValue:null,kind:"LocalArgument",name:"user_id"}],c=[{kind:"Variable",name:"after",variableName:"after"},{kind:"Variable",name:"before",variableName:"before"},{kind:"Variable",name:"count",variableName:"count"},{kind:"Variable",name:"first",variableName:"first"},{kind:"Variable",name:"last",variableName:"last"},{kind:"Variable",name:"user_id",variableName:"user_id"}],d={alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},e={alias:null,args:null,kind:"ScalarField",name:"media_type",storageKey:null},f={alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},g={alias:null,args:null,kind:"ScalarField",name:"text",storageKey:null},h={alias:null,args:null,concreteType:"XDTImageVersion2",kind:"LinkedField",name:"image_versions2",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTImageCandidate",kind:"LinkedField",name:"candidates",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"height",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"url",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"width",storageKey:null}],storageKey:null}],storageKey:null};h={name:"client__srcSet",args:null,fragment:{kind:"InlineFragment",selections:[e,{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"carousel_media",plural:!0,selections:[h,f],storageKey:null},h],type:"XDTMediaDict",abstractKey:null},kind:"RelayResolver",storageKey:null,isOutputType:!0};var i=[{alias:null,args:null,kind:"ScalarField",name:"crop_bottom",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"crop_left",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"crop_right",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"crop_top",storageKey:null}],j={alias:null,args:null,kind:"ScalarField",name:"__typename",storageKey:null},k={alias:null,args:null,kind:"ScalarField",name:"action",storageKey:null},l={alias:null,args:null,kind:"ScalarField",name:"action_url",storageKey:null},m={alias:null,args:null,kind:"ScalarField",name:"button_type",storageKey:null},n={alias:null,args:null,kind:"ScalarField",name:"has_chevron",storageKey:null},o={alias:null,args:null,concreteType:"XDTIconSpec",kind:"LinkedField",name:"icon",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"icon_glyph",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"icon_type",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"name",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"src",storageKey:null}],storageKey:null},p={alias:null,args:null,kind:"ScalarField",name:"is_text_centered",storageKey:null},q={alias:null,args:null,kind:"ScalarField",name:"secondary_text",storageKey:null},r=[{alias:null,args:null,kind:"ScalarField",name:"dark",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"light",storageKey:null}],s={alias:null,args:null,concreteType:"XDTTextColorSpec",kind:"LinkedField",name:"secondary_text_color",plural:!1,selections:r,storageKey:null};r={alias:null,args:null,concreteType:"XDTTextColorSpec",kind:"LinkedField",name:"text_color",plural:!1,selections:r,storageKey:null};var t={alias:null,args:null,concreteType:"XDTSpritesheetInfo",kind:"LinkedField",name:"thumbnails",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"sprite_height",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"sprite_urls",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"sprite_width",storageKey:null}],storageKey:null};return{fragment:{argumentDefinitions:a,kind:"Fragment",metadata:null,name:"PolarisProfileTaggedTabContentQuery_connection",selections:[{args:null,kind:"FragmentSpread",name:"PolarisProfileTaggedTabContentFragment"}],type:"Query",abstractKey:null},kind:"Request",operation:{argumentDefinitions:a,kind:"Operation",name:"PolarisProfileTaggedTabContentQuery_connection",selections:[{alias:null,args:c,concreteType:"XDTProfileFeedConnection",kind:"LinkedField",name:"xdt_api__v1__usertags__user_id__feed_connection",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTProfileFeedEdge",kind:"LinkedField",name:"edges",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"node",plural:!1,selections:[d,e,{alias:null,args:null,kind:"ScalarField",name:"code",storageKey:null},f,{alias:null,args:null,kind:"ScalarField",name:"accessibility_caption",storageKey:null},{alias:null,args:null,concreteType:"XDTCommentDict",kind:"LinkedField",name:"caption",plural:!1,selections:[d,g],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"audience",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"carousel_media_count",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"display_uri",storageKey:null},h,{alias:null,args:null,concreteType:"XDTMediaCroppingInfo",kind:"LinkedField",name:"media_cropping_info",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTMediaCroppingCoordinates",kind:"LinkedField",name:"square_crop",plural:!1,selections:i,storageKey:null},{alias:null,args:null,concreteType:"XDTMediaCroppingCoordinates",kind:"LinkedField",name:"four_by_three_crop",plural:!1,selections:i,storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"profile_grid_thumbnail_fitting_style",storageKey:null},{alias:null,args:null,concreteType:"XDTMediaOverlayPayloadSchema",kind:"LinkedField",name:"media_overlay_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"overlay_layout",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"overlay_type",storageKey:null},j,{alias:null,args:null,concreteType:"XDTButtonSpec",kind:"LinkedField",name:"banner",plural:!1,selections:[k,l,m,n,o,p,q,s,g,r],storageKey:null},{alias:null,args:null,concreteType:"XDTBloksRenderResponse",kind:"LinkedField",name:"bloks_data",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"layout",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTButtonSpec",kind:"LinkedField",name:"buttons",plural:!0,selections:[k,l,m,n,o,p,q,s,g,r,{alias:null,args:null,kind:"ScalarField",name:"headline_text",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"description",storageKey:null},o,{alias:null,args:null,kind:"ScalarField",name:"misinformation_type",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"overlay_applied_timestamp",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"session_id",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"sub_category",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"title",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"preview",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"product_type",storageKey:null},{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"carousel_media",plural:!0,selections:[h,t,{alias:null,args:null,concreteType:"XDTUserTagInfosDict",kind:"LinkedField",name:"usertags",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTUserTagInfoDict",kind:"LinkedField",name:"in",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[d,f],storageKey:null}],storageKey:null}],storageKey:null},f],storageKey:null},t,{alias:null,args:null,kind:"ScalarField",name:"timeline_pinned_user_ids",storageKey:null},{alias:null,args:null,concreteType:"XDTUpcomingEventDict",kind:"LinkedField",name:"upcoming_event",plural:!1,selections:[j,f],storageKey:null},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[d,{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},f],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"like_count",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"like_and_view_counts_disabled",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"boosted_status",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"boost_unavailable_identifier",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"boost_unavailable_reason",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"comment_count",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"comments_disabled",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"view_count",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"original_height",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"original_width",storageKey:null},j],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"cursor",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTPageInfo",kind:"LinkedField",name:"page_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"end_cursor",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"has_next_page",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"has_previous_page",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"start_cursor",storageKey:null}],storageKey:null}],storageKey:null},{alias:null,args:c,filters:["user_id","count"],handle:"connection",key:"PolarisProfileTaggedTabContentQuery_xdt_api__v1__usertags__user_id__feed_connection",kind:"LinkedHandle",name:"xdt_api__v1__usertags__user_id__feed_connection"}]},params:{id:b("PolarisProfileTaggedTabContentQuery_connection_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__usertags__user_id__feed_connection"]},name:"PolarisProfileTaggedTabContentQuery_connection",operationKind:"query",text:null}}}();e.exports=a}),null);
__d("PolarisProfileTaggedTabContentFragment.graphql",["PolarisProfileTaggedTabContentQuery_connection.graphql"],(function(a,b,c,d,e,f){"use strict";a=function(){var a=["xdt_api__v1__usertags__user_id__feed_connection"];return{argumentDefinitions:[{kind:"RootArgument",name:"after"},{kind:"RootArgument",name:"before"},{kind:"RootArgument",name:"count"},{kind:"RootArgument",name:"first"},{kind:"RootArgument",name:"last"},{kind:"RootArgument",name:"user_id"}],kind:"Fragment",metadata:{connection:[{count:null,cursor:null,direction:"bidirectional",path:a}],refetch:{connection:{forward:{count:"first",cursor:"after"},backward:{count:"last",cursor:"before"},path:a},fragmentPathInResult:[],operation:b("PolarisProfileTaggedTabContentQuery_connection.graphql")}},name:"PolarisProfileTaggedTabContentFragment",selections:[{alias:"xdt_api__v1__usertags__user_id__feed_connection",args:[{kind:"Variable",name:"count",variableName:"count"},{kind:"Variable",name:"user_id",variableName:"user_id"}],concreteType:"XDTProfileFeedConnection",kind:"LinkedField",name:"__PolarisProfileTaggedTabContentQuery_xdt_api__v1__usertags__user_id__feed_connection_connection",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTProfileFeedEdge",kind:"LinkedField",name:"edges",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"node",plural:!1,selections:[{args:[{kind:"Literal",name:"isTaggedGrid",value:!0}],kind:"FragmentSpread",name:"PolarisProfilePostsGrid_media"},{alias:null,args:null,kind:"ScalarField",name:"__typename",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"cursor",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTPageInfo",kind:"LinkedField",name:"page_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"end_cursor",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"has_next_page",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"has_previous_page",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"start_cursor",storageKey:null}],storageKey:null}],storageKey:null}],type:"Query",abstractKey:null}}();e.exports=a}),null);
__d("PolarisProfileTaggedTabContentQuery.graphql",["PolarisProfileTaggedTabContentQuery_instagramRelayOperation","relay-runtime"],(function(a,b,c,d,e,f){"use strict";a=function(){var a={defaultValue:null,kind:"LocalArgument",name:"after"},c={defaultValue:null,kind:"LocalArgument",name:"before"},d={defaultValue:null,kind:"LocalArgument",name:"count"},e={defaultValue:null,kind:"LocalArgument",name:"first"},f={defaultValue:null,kind:"LocalArgument",name:"last"},g={defaultValue:null,kind:"LocalArgument",name:"user_id"},h=[{kind:"Variable",name:"after",variableName:"after"},{kind:"Variable",name:"before",variableName:"before"},{kind:"Variable",name:"count",variableName:"count"},{kind:"Variable",name:"first",variableName:"first"},{kind:"Variable",name:"last",variableName:"last"},{kind:"Variable",name:"user_id",variableName:"user_id"}],i={alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},j={alias:null,args:null,kind:"ScalarField",name:"media_type",storageKey:null},k={alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},l={alias:null,args:null,kind:"ScalarField",name:"text",storageKey:null},m={alias:null,args:null,concreteType:"XDTImageVersion2",kind:"LinkedField",name:"image_versions2",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTImageCandidate",kind:"LinkedField",name:"candidates",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"height",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"url",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"width",storageKey:null}],storageKey:null}],storageKey:null};m={name:"client__srcSet",args:null,fragment:{kind:"InlineFragment",selections:[j,{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"carousel_media",plural:!0,selections:[m,k],storageKey:null},m],type:"XDTMediaDict",abstractKey:null},kind:"RelayResolver",storageKey:null,isOutputType:!0};var n=[{alias:null,args:null,kind:"ScalarField",name:"crop_bottom",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"crop_left",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"crop_right",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"crop_top",storageKey:null}],o={alias:null,args:null,kind:"ScalarField",name:"__typename",storageKey:null},p={alias:null,args:null,kind:"ScalarField",name:"action",storageKey:null},q={alias:null,args:null,kind:"ScalarField",name:"action_url",storageKey:null},r={alias:null,args:null,kind:"ScalarField",name:"button_type",storageKey:null},s={alias:null,args:null,kind:"ScalarField",name:"has_chevron",storageKey:null},t={alias:null,args:null,concreteType:"XDTIconSpec",kind:"LinkedField",name:"icon",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"icon_glyph",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"icon_type",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"name",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"src",storageKey:null}],storageKey:null},u={alias:null,args:null,kind:"ScalarField",name:"is_text_centered",storageKey:null},v={alias:null,args:null,kind:"ScalarField",name:"secondary_text",storageKey:null},w=[{alias:null,args:null,kind:"ScalarField",name:"dark",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"light",storageKey:null}],x={alias:null,args:null,concreteType:"XDTTextColorSpec",kind:"LinkedField",name:"secondary_text_color",plural:!1,selections:w,storageKey:null};w={alias:null,args:null,concreteType:"XDTTextColorSpec",kind:"LinkedField",name:"text_color",plural:!1,selections:w,storageKey:null};var y={alias:null,args:null,concreteType:"XDTSpritesheetInfo",kind:"LinkedField",name:"thumbnails",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"sprite_height",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"sprite_urls",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"sprite_width",storageKey:null}],storageKey:null};return{fragment:{argumentDefinitions:[a,c,d,e,f,g],kind:"Fragment",metadata:null,name:"PolarisProfileTaggedTabContentQuery",selections:[{args:null,kind:"FragmentSpread",name:"PolarisProfileTaggedTabContentFragment"}],type:"Query",abstractKey:null},kind:"Request",operation:{argumentDefinitions:[g,d,e,f,c,a],kind:"Operation",name:"PolarisProfileTaggedTabContentQuery",selections:[{alias:null,args:h,concreteType:"XDTProfileFeedConnection",kind:"LinkedField",name:"xdt_api__v1__usertags__user_id__feed_connection",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTProfileFeedEdge",kind:"LinkedField",name:"edges",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"node",plural:!1,selections:[i,j,{alias:null,args:null,kind:"ScalarField",name:"code",storageKey:null},k,{alias:null,args:null,kind:"ScalarField",name:"accessibility_caption",storageKey:null},{alias:null,args:null,concreteType:"XDTCommentDict",kind:"LinkedField",name:"caption",plural:!1,selections:[i,l],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"audience",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"carousel_media_count",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"display_uri",storageKey:null},m,{alias:null,args:null,concreteType:"XDTMediaCroppingInfo",kind:"LinkedField",name:"media_cropping_info",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTMediaCroppingCoordinates",kind:"LinkedField",name:"square_crop",plural:!1,selections:n,storageKey:null},{alias:null,args:null,concreteType:"XDTMediaCroppingCoordinates",kind:"LinkedField",name:"four_by_three_crop",plural:!1,selections:n,storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"profile_grid_thumbnail_fitting_style",storageKey:null},{alias:null,args:null,concreteType:"XDTMediaOverlayPayloadSchema",kind:"LinkedField",name:"media_overlay_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"overlay_layout",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"overlay_type",storageKey:null},o,{alias:null,args:null,concreteType:"XDTButtonSpec",kind:"LinkedField",name:"banner",plural:!1,selections:[p,q,r,s,t,u,v,x,l,w],storageKey:null},{alias:null,args:null,concreteType:"XDTBloksRenderResponse",kind:"LinkedField",name:"bloks_data",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"layout",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTButtonSpec",kind:"LinkedField",name:"buttons",plural:!0,selections:[p,q,r,s,t,u,v,x,l,w,{alias:null,args:null,kind:"ScalarField",name:"headline_text",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"description",storageKey:null},t,{alias:null,args:null,kind:"ScalarField",name:"misinformation_type",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"overlay_applied_timestamp",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"session_id",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"sub_category",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"title",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"preview",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"product_type",storageKey:null},{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"carousel_media",plural:!0,selections:[m,y,{alias:null,args:null,concreteType:"XDTUserTagInfosDict",kind:"LinkedField",name:"usertags",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTUserTagInfoDict",kind:"LinkedField",name:"in",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[i,k],storageKey:null}],storageKey:null}],storageKey:null},k],storageKey:null},y,{alias:null,args:null,kind:"ScalarField",name:"timeline_pinned_user_ids",storageKey:null},{alias:null,args:null,concreteType:"XDTUpcomingEventDict",kind:"LinkedField",name:"upcoming_event",plural:!1,selections:[o,k],storageKey:null},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[i,{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},k],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"like_count",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"like_and_view_counts_disabled",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"boosted_status",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"boost_unavailable_identifier",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"boost_unavailable_reason",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"comment_count",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"comments_disabled",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"view_count",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"original_height",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"original_width",storageKey:null},o],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"cursor",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTPageInfo",kind:"LinkedField",name:"page_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"end_cursor",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"has_next_page",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"has_previous_page",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"start_cursor",storageKey:null}],storageKey:null}],storageKey:null},{alias:null,args:h,filters:["user_id","count"],handle:"connection",key:"PolarisProfileTaggedTabContentQuery_xdt_api__v1__usertags__user_id__feed_connection",kind:"LinkedHandle",name:"xdt_api__v1__usertags__user_id__feed_connection"}]},params:{id:b("PolarisProfileTaggedTabContentQuery_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__usertags__user_id__feed_connection"]},name:"PolarisProfileTaggedTabContentQuery",operationKind:"query",text:null}}}();b("relay-runtime").PreloadableQueryRegistry.set(a.params.id,a);e.exports=a}),null);
__d("PolarisProfileTaggedTabContent.react",["CometRelay","PolarisProfileEmptyState.react","PolarisProfileEmptyStateTypes","PolarisProfilePostsGrid.react","PolarisProfileTaggedTabContentFragment.graphql","PolarisProfileTaggedTabContentQuery.graphql","react","react-compiler-runtime","usePolarisProfileTabNextPageLoader","usePolarisViewer"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||(j=d("react"));e=j;e.useMemo;var l=e.useState,m=h!==void 0?h:h=b("PolarisProfileTaggedTabContentQuery.graphql");function a(a){var e=d("react-compiler-runtime").c(12),f=a.contentQuery;a=a.userID;var g=c("usePolarisViewer")();g=(g==null?void 0:g.id)===a;f=d("CometRelay").usePreloadedQuery(m,f);var h=l(!1),j=h[0];h=h[1];f=d("CometRelay").usePaginationFragment(i!==void 0?i:i=b("PolarisProfileTaggedTabContentFragment.graphql"),f);var o=f.data,p=f.hasNext,q=f.isLoadingNext;f=f.loadNext;var r;e[0]!==o.xdt_api__v1__usertags__user_id__feed_connection.edges?(r=o.xdt_api__v1__usertags__user_id__feed_connection.edges.flatMap(n),e[0]=o.xdt_api__v1__usertags__user_id__feed_connection.edges,e[1]=r):r=e[1];o=r;r=o;e[2]!==f?(o={loadNext:f,setIsLoadingError:h},e[2]=f,e[3]=o):o=e[3];h=c("usePolarisProfileTabNextPageLoader")(o);e[4]!==p||e[5]!==j||e[6]!==q||e[7]!==g||e[8]!==r||e[9]!==h||e[10]!==a?(f=k.jsx("div",{children:r.length===0&&!p&&!q?k.jsx(c("PolarisProfileEmptyState.react"),{type:g?d("PolarisProfileEmptyStateTypes").ProfileEmptyStateKeys.OWN_PROFILE_TAGGED:d("PolarisProfileEmptyStateTypes").ProfileEmptyStateKeys.TAGGED}):k.jsx(c("PolarisProfilePostsGrid.react"),{analyticsContext:g?"selfProfilePage":"profilePage",hasNext:p,isLoadingError:j,isLoadingNext:q,media:r,onLoadNext:h,profileUserID:a})}),e[4]=p,e[5]=j,e[6]=q,e[7]=g,e[8]=r,e[9]=h,e[10]=a,e[11]=f):f=e[11];return f}function n(a){a=a.node;return a}g["default"]=a}),98);
__d("PolarisProfileTaggedTabRoot.react",["CometPlaceholder.react","PolarisProfileTabContentSpinner.react","deferredLoadComponent","react","react-compiler-runtime","requireDeferredForDisplay"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j=c("deferredLoadComponent")(c("requireDeferredForDisplay")("PolarisProfileTaggedTabContent.react").__setRef("PolarisProfileTaggedTabRoot.react"));function a(a){var b=d("react-compiler-runtime").c(4),e=a.props;a=a.queries;var f;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(f=i.jsx(c("PolarisProfileTabContentSpinner.react"),{}),b[0]=f):f=b[0];b[1]!==e.userID||b[2]!==a.contentQuery?(f=i.jsx(c("CometPlaceholder.react"),{fallback:f,children:i.jsx(j,{contentQuery:a.contentQuery,userID:e.userID})}),b[1]=e.userID,b[2]=a.contentQuery,b[3]=f):f=b[3];return f}g["default"]=a}),98);