;/*FB_PKG_DELIM*/

__d("EBReadyNotifier",["MAWMpsGating","Promise","WAResolvable","gkx"],(function(a,b,c,d,e,f,g){"use strict";var h,i=new(d("WAResolvable").Resolvable)();function a(){i.resolve()}function e(){i.resolveWasCalled()&&(i=new(d("WAResolvable").Resolvable)())}function f(){return c("gkx")("14351")||d("MAWMpsGating").isMpsM3OrLater()?i.promise:(h||(h=b("Promise"))).resolve()}g.markEBReady=a;g.resetEBReady=e;g.waitForEBReady=f}),98);
__d("I64MigrationHelper",["I64","nullthrows"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){return c("nullthrows")((h||(h=d("I64"))).cast(a))}g.cast=a}),98);
__d("IntervalList",["FBLogger","JSONStringifyBigIntSafe","ReQLBounds","ReStoreKeyComparer"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(a){if(a==null)throw c("FBLogger")("messenger_web").mustfixThrow("must not be null");return a}function a(a,b){var e=new Map(),f=new Map(),g=new Map();function k(b,c){return c==null?!0:(c.gt!==void 0?a(b,c.gt)>0:c.gte!==void 0?a(b,c.gte)>=0:!0)&&(c.lt!==void 0?a(b,c.lt)<0:c.lte!==void 0?a(b,c.lte)<=0:!0)}function* l(a){var g=0;while(g<a.length&&f.size){g++;var i=a.slice(0,g);g<b&&i.push((h||(h=d("ReStoreKeyComparer"))).MAX_KEY);i=f.get(c("JSONStringifyBigIntSafe")(i));if(i!=null)for(i of i)yield i}for(i of e.entries()){g=i[0];var j=i[1];e.has(g)&&k(a,j)&&(yield g)}}function m(a){var b=g.get(a);if(b!=null){g["delete"](a);var c=j(f.get(b));c["delete"](a);c.size===0&&f["delete"](b)}else e["delete"](a)}function n(a,h){var j;if(a&&a.gte!=null&&a.lte!=null&&(j=c("JSONStringifyBigIntSafe")((i||(i=d("ReQLBounds"))).nextKey(a.gte,b)))===c("JSONStringifyBigIntSafe")(a.lte)){var k=f.get(j);k==null&&(k=new Set(),f.set(j,k));k.add(h);g.set(h,j)}else e.set(h,a)}return{"delete":m,findIntersecting:l,set:n}}g["default"]=a}),98);
__d("LSClientSchemaType",[],(function(a,b,c,d,e,f){"use strict";a="maw";f.MAW_SCHEMA_NAME=a}),66);
__d("LSConstants",[],(function(a,b,c,d,e,f){"use strict";a="ls";f.LS_SCHEMA_NAME=a}),66);
__d("LSDbV1.upgrade",[],(function(a,b,c,d,e,f){"use strict";a={};f.afterUpgrade=a}),66);
__d("ReStoreDefaultValuesMetadata",["FBLogger"],(function(a,b,c,d,e,f,g){"use strict";function a(a){var b={c:"string",t:"string"};for(var d in b){if(!Object.prototype.hasOwnProperty.call(a,d)||a[d]==null){c("FBLogger")("messenger_web").warn('Malformed default values data, missing key "%s"',d);return null}var e=a[d],f=b[d];e=typeof e;if(e!==f){c("FBLogger")("messenger_web").warn('Malformed default values data, key "%s" has type "%s" but expected "%s"',d,e,f);return null}}if(typeof a.c!=="string"||typeof a.t!=="string")return null;e=[];f=a.t;try{e=JSON.parse(a.c);for(d of e)if(typeof d!=="string")return null}catch(a){return null}return{columns:e,tableName:f}}function b(a){return{c:JSON.stringify(a.columns),t:a.tableName}}d="_db_defaults_meta";g.tryParseDefaultValuesData=a;g.defaultValuesColumnToValue=b;g.defaultValuesTableName=d}),98);
__d("ReStoreCommonUtils",["isPromise"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){function d(b){if(b.done)return b.value;return!(h||(h=c("isPromise")))(b.value)?d(a.next(b.value)):b.value.then(function(b){return d(a.next(b))})}return d(a.next(b))}g.gen=a}),98);
__d("ReStoreOperationLock",["ReStoreCommonUtils"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return a==="\u2757"}c=function(){function a(a){var b=this;this.isSync=!1;this.waitFor=null;this.waitFor=a.then(function(a){return b.waitFor=null})}var b=a.prototype;b.chainPromiseOrValue=function(a){if(this.waitFor!=null)return this.waitFor.then(function(){return a("\ud83d\udd12")});else return a("\ud83d\udd12")};return a}();e=function(){function a(){this.isSync=!0}var b=a.prototype;b.chainPromiseOrValue=function(a){return a("\u2757")};return a}();f=function(){function a(){this.isSync=!1,this.$1=Promise.resolve()}var b=a.prototype;b.chainPromiseOrValue=function(a){var b=this;return new Promise(function(c,d){b.$1=b.$1.then(async function(){try{c(await a("\ud83d\udd12"))}catch(a){d(a)}})})};return a}();function b(a,b){return{next:function(c){return b.chainPromiseOrValue(function(b){return d("ReStoreCommonUtils").gen(a.next(b,c))})}}}g.isNoopLockProof=a;g.WaitForPromiseLock=c;g.NoopLock=e;g.ReStoreOperationLock=f;g.lockIterator=b}),98);
__d("ReStoreIndex",["ReQLBounds","ReStoreOperationLock","ReStoreUtils"],(function(a,b,c,d,e,f,g){"use strict";var h;a=function(){function a(a,b,c,e,f){var g=this;this.entries=function(a,b,c){b===void 0&&(b="asc");return d("ReStoreOperationLock").lockIterator(d("ReStoreUtils").entries(g.$5,a,g.$1,g.$3,b,c,g.$2),g.$4)};this.get=function(){for(var a=arguments.length,b=new Array(a),c=0;c<a;c++)b[c]=arguments[c];return g.$4.chainPromiseOrValue(function(a){return d("ReStoreUtils").getLocked(g.$5,new WeakMap(),g.$1.name,g.$3,b,a,g.$2)})};this.getKey=function(){for(var a=arguments.length,b=new Array(a),c=0;c<a;c++)b[c]=arguments[c];return g.$4.chainPromiseOrValue(function(a){return g.$6(g.$5,new WeakMap(),b,a)})};this.keys=function(a,b,c){b===void 0&&(b="asc");return d("ReStoreOperationLock").lockIterator(d("ReStoreUtils").keys(g.$5,a,g.$1,g.$3,b,c),g.$4)};this.$3=a;this.keyFields=b.indexes[a];this.$1=b;this.$2=c;this.$4=e;this.$5=f}var b=a.prototype;b.$6=function(a,b,c,e){var f=this;a=d("ReStoreUtils").getLocked(a,b,this.$1.name,this.$3,c,e,this.$2);return a.then(function(a){return a===void 0?void 0:d("ReStoreUtils").searchKey(f.$1,a)})};return a}();b=function(a){babelHelpers.inheritsLoose(b,a);function b(){return a.apply(this,arguments)||this}return b}(a);c=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,e,f,g,i){var j;j=a.call(this,b,c,e,f,g)||this;j.subscribe=function(a,b,c){return j.$ReStoreIndex$p_3.subscribeIndex(j.$ReStoreIndex$p_1.name,j.$ReStoreIndex$p_2,a,(h||(h=d("ReQLBounds"))).extendBounds(b,j.$ReStoreIndex$p_1.indexes[j.$ReStoreIndex$p_2].length),c)};j.$ReStoreIndex$p_2=b;j.$ReStoreIndex$p_1=c;j.$ReStoreIndex$p_3=i;return j}return b}(a);g.ReStoreTransactionIndex=b;g.ReStoreIndex=c}),98);
__d("ReStoreTable",["FBLogger","I64","JSONStringifyBigIntSafe","LSSynchronousPromise","PromiseOrValue","ReQLBounds","ReStoreCommonUtils","ReStoreIndex","ReStoreOperationLock","ReStoreUtils"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;function k(a){a=(h||(h=d("I64"))).cast(a);if(a===void 0)throw c("FBLogger")("messenger_web").mustfixThrow("Expected int64");return h.to_float(a)}function l(a,b,d,e,f){b=c("JSONStringifyBigIntSafe")([b,d]);d=a.get(b);a.set(b,[d==null?e:d[0],f])}a=function(){function a(a,b,e,f){var g=this;this.entries=function(a,b,c){b===void 0&&(b="asc");return d("ReStoreOperationLock").lockIterator(d("ReStoreUtils").entries(g.$4,a,g.$1,void 0,b,c,g.$2),g.$3)};this.get=function(){for(var a=arguments.length,b=new Array(a),c=0;c<a;c++)b[c]=arguments[c];return g.$3.chainPromiseOrValue(function(a){return d("ReStoreUtils").getLocked(g.$4,new WeakMap(),g.$1.name,void 0,b,a,g.$2)})};this.keys=function(a,b,c){b===void 0&&(b="asc");return d("ReStoreOperationLock").lockIterator(d("ReStoreUtils").keys(g.$4,a,g.$1,void 0,b,c),g.$3)};this.peekNextAutoIncrementId=function(){return g.$5(g.$3.chainPromiseOrValue(function(a){a=g.$1.autoIncrement;if(!a)throw c("FBLogger")("messenger_web").mustfixThrow("only supported for auto-incrementing tables");return d("LSSynchronousPromise").wrapInMaybeSyncPromiseIfNotPromise(d("ReStoreCommonUtils").gen(g.$4.table(g.$1.name).readSeed(new WeakMap()))).then(function(a){if(a===void 0)throw c("FBLogger")("messenger_web").mustfixThrow("unexpected undefined autoIncrementSeed");return(h||(h=d("I64"))).of_float(a)})}))};this.keyFields=a.primaryKeyIds;this.$1=a;this.$2=b;this.$3=e;this.$4=f}var b=a.prototype;b.$5=function(a){return this.$3.isSync?d("LSSynchronousPromise").wrapInMaybeSyncPromiseIfNotPromise(a):Promise.resolve(a)};return a}();b=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,e,f,g,h,j,k,l,m){var n;b=c.tableNames[b];n=a.call(this,b,e,f,g)||this;n.$ReStoreTransactionTable$p_1=new Map();n.$ReStoreTransactionTable$p_10=new Map();n.add=function(a){return n.$ReStoreTransactionTable$p_12((i||(i=d("PromiseOrValue"))).map(i.map(n.$ReStoreTransactionTable$p_4.beforeAdd({tableName:n.$ReStoreTransactionTable$p_3.name,transaction:n.$ReStoreTransactionTable$p_8,value:a}),function(a){return n.$ReStoreTransactionTable$p_13(function(b,c){var e;(e=n.$ReStoreTransactionTable$p_9)==null?void 0:e.recordLogicalWrite();return d("ReStoreCommonUtils").gen(n.$ReStoreTransactionTable$p_14(n.$ReStoreTransactionTable$p_10,c,new WeakMap(),n.$ReStoreTransactionTable$p_3.name,a,b))})}),function(b){return(i||(i=d("PromiseOrValue"))).map(n.$ReStoreTransactionTable$p_4.afterAdd({key:b,tableName:n.$ReStoreTransactionTable$p_3.name,transaction:n.$ReStoreTransactionTable$p_8,value:a}),function(){return b})}))};n.delete=function(){for(var a=arguments.length,b=new Array(a),c=0;c<a;c++)b[c]=arguments[c];return n.$ReStoreTransactionTable$p_12((i||(i=d("PromiseOrValue"))).map(n.$ReStoreTransactionTable$p_4.beforeDelete({key:b,tableName:n.$ReStoreTransactionTable$p_3.name,transaction:n.$ReStoreTransactionTable$p_8}),function(a){return n.$ReStoreTransactionTable$p_13(function(b,c){var e=babelHelpers.assertThisInitialized(n);return d("ReStoreCommonUtils").gen(function*(){var f;(f=e.$ReStoreTransactionTable$p_9)==null?void 0:f.recordLogicalWrite();yield* d("ReStoreUtils").deleteItemLocked(e.$ReStoreTransactionTable$p_2,e.$ReStoreTransactionTable$p_10,c,new WeakMap(),e.$ReStoreTransactionTable$p_3.name,a,b);yield* d("ReStoreUtils").cascadeKeyUpdatesLocked(e.$ReStoreTransactionTable$p_2,e.$ReStoreTransactionTable$p_10,c,new WeakMap(),e.$ReStoreTransactionTable$p_3.name,a,void 0,e.$ReStoreTransactionTable$p_4,b,e.$ReStoreTransactionTable$p_11)}())})}))};n.put=function(a){return n.$ReStoreTransactionTable$p_12((i||(i=d("PromiseOrValue"))).map(i.map(n.$ReStoreTransactionTable$p_4.beforePut({tableName:n.$ReStoreTransactionTable$p_3.name,transaction:n.$ReStoreTransactionTable$p_8,value:a}),function(a){return n.$ReStoreTransactionTable$p_13(function(b,c){var e;(e=n.$ReStoreTransactionTable$p_9)==null?void 0:e.recordLogicalWrite();return d("ReStoreCommonUtils").gen(d("ReStoreUtils").putLocked(n.$ReStoreTransactionTable$p_2,n.$ReStoreTransactionTable$p_10,c,new WeakMap(),n.$ReStoreTransactionTable$p_3.name,a,n.$ReStoreTransactionTable$p_4,b,n.$ReStoreTransactionTable$p_11))})}),function(b){return(i||(i=d("PromiseOrValue"))).map(n.$ReStoreTransactionTable$p_4.afterPut({key:b,tableName:n.$ReStoreTransactionTable$p_3.name,transaction:n.$ReStoreTransactionTable$p_8,value:a}),function(){return b})}))};n.upsert=function(a,b){return n.$ReStoreTransactionTable$p_12((i||(i=d("PromiseOrValue"))).map(n.$ReStoreTransactionTable$p_4.beforeUpsert({tableName:n.$ReStoreTransactionTable$p_3.name,transaction:n.$ReStoreTransactionTable$p_8,value:b}),function(c){return n.$ReStoreTransactionTable$p_13(function(e,f){var g=babelHelpers.assertThisInitialized(n);return d("ReStoreCommonUtils").gen(function*(){var h;(h=g.$ReStoreTransactionTable$p_9)==null?void 0:h.recordLogicalWrite();yield* d("ReStoreUtils").upsertLocked(g.$ReStoreTransactionTable$p_2,g.$ReStoreTransactionTable$p_10,f,new WeakMap(),g.$ReStoreTransactionTable$p_3.name,a,c,g.$ReStoreTransactionTable$p_4,e,g.$ReStoreTransactionTable$p_11);yield* d("ReStoreUtils").cascadeKeyUpdatesLocked(g.$ReStoreTransactionTable$p_2,g.$ReStoreTransactionTable$p_10,f,new WeakMap(),g.$ReStoreTransactionTable$p_3.name,a,d("ReStoreUtils").searchKey(g.$ReStoreTransactionTable$p_3,b),g.$ReStoreTransactionTable$p_4,e,g.$ReStoreTransactionTable$p_11)}())})}))};n.index=function(a){return d("ReStoreUtils").getOrCreate(n.$ReStoreTransactionTable$p_1,a,function(){return new(d("ReStoreIndex").ReStoreTransactionIndex)(a,n.$ReStoreTransactionTable$p_3,n.$ReStoreTransactionTable$p_4,n.$ReStoreTransactionTable$p_5,n.$ReStoreTransactionTable$p_6)})};n.$ReStoreTransactionTable$p_3=b;n.$ReStoreTransactionTable$p_2=c;n.$ReStoreTransactionTable$p_4=e;n.$ReStoreTransactionTable$p_5=f;n.$ReStoreTransactionTable$p_6=g;n.$ReStoreTransactionTable$p_7=h;n.$ReStoreTransactionTable$p_8=j;n.$ReStoreTransactionTable$p_9=k;n.$ReStoreTransactionTable$p_10=l;n.$ReStoreTransactionTable$p_11=m;return n}var c=b.prototype;c.$ReStoreTransactionTable$p_12=function(a){return this.$ReStoreTransactionTable$p_5.isSync?d("LSSynchronousPromise").wrapInMaybeSyncPromiseIfNotPromise(a):Promise.resolve(a)};c.$ReStoreTransactionTable$p_13=function(a){var b=this;return this.$ReStoreTransactionTable$p_5.chainPromiseOrValue(function(c){var e=b.$ReStoreTransactionTable$p_6.createNested(b.$ReStoreTransactionTable$p_9);return(i||(i=d("PromiseOrValue"))).map(a(c,e),function(a){e.flush();return a})})};c.$ReStoreTransactionTable$p_14=function*(a,b,c,e,f,g){var i=b.table(e),j=this.$ReStoreTransactionTable$p_2.tableNames[e];if(j.autoIncrement){var m=f[j.primaryKeyIds[0]];m=(h||(h=d("I64"))).of_float(yield* i.incrementSeed(c,m===void 0?m:k(m)));f[j.primaryKeyIds[0]]=m}m=d("ReStoreUtils").appendDefaultValues(f,j.name,this.$ReStoreTransactionTable$p_11);f=d("ReStoreUtils").searchKey(j,m);if(f==null)throw new Error("Evaluating the object store's key path did not yield a value");if((yield* i.tableGet(c,f))==null){j=d("ReStoreUtils").indicesWithKeyForValue(this.$ReStoreTransactionTable$p_3,Object.keys(j.indexes),m);yield* d("ReStoreUtils").deconflictIndicesLocked(this.$ReStoreTransactionTable$p_2,a,b,c,e,j,this.$ReStoreTransactionTable$p_4,g,this.$ReStoreTransactionTable$p_11);g=(yield* i.tableSet(c,f,m));i=g[0];yield* d("ReStoreUtils").addIdToIndices(b,c,e,i,j);l(a,e,f,void 0,m)}return f};return b}(a);e=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,e,f,g){var h;h=a.call(this,b,c,e,f)||this;h.$ReStoreTable$p_1=new Map();h.index=function(a){return d("ReStoreUtils").getOrCreate(h.$ReStoreTable$p_1,a,function(){return new(d("ReStoreIndex").ReStoreIndex)(a,h.$ReStoreTable$p_2,h.$ReStoreTable$p_3,h.$ReStoreTable$p_4,h.$ReStoreTable$p_5,h.$ReStoreTable$p_6)})};h.subscribe=function(a,b,c){return h.$ReStoreTable$p_6.subscribeTable(h.$ReStoreTable$p_2.name,a,(j||(j=d("ReQLBounds"))).extendBounds(b,h.$ReStoreTable$p_2.primaryKeyIds.length),c)};h.$ReStoreTable$p_2=b;h.$ReStoreTable$p_3=c;h.$ReStoreTable$p_4=e;h.$ReStoreTable$p_5=f;h.$ReStoreTable$p_6=g;return h}return b}(a);g.ReStoreTransactionTable=b;g.ReStoreTable=e}),98);
__d("LSStorage",["ReStoreKeyComparer"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){return(h||(h=d("ReStoreKeyComparer"))).compareKey(a,b)===0}g.areKeysEqual=a}),98);
__d("ReStoreUtils",["FBLogger","I64","JSONStringifyBigIntSafe","LSStorage","LSSynchronousPromise","Random","ReQLBounds","ReStoreCommonUtils","ReStoreKeyComparer","ReStoreOperationLock","ReStoreTable","gkx","nullthrows"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=500;async function a(a){var b=[],c=await a.next();while(!c.done)b.push(c.value),c=await a.next();return b}function l(a,b,d){var e=a.indexes;a=a.primaryKeyIds;a=d==null?a:e[d];return a.map(function(a){if(b[a]!==void 0||d!=null)return b[a];throw c("FBLogger")("maw_db").mustfixThrow("primary key must be defined on object")})}function m(a,b,c){var d=c[b];return!d?a:Object.keys(d).reduce(function(a,b){Object.prototype.hasOwnProperty.call(a,b)||(a[b]=d[b]);return a},babelHelpers.extends({},a))}function n(a,b,c,e,f,g,i){function j(){var k=a.table(c.name),l=babelHelpers.extends({},(h||(h=d("ReQLBounds"))).extendBounds(g,(e===void 0?c.primaryKeyIds:c.indexes[e]).length),{dir:f}),m=e==null?k.tableEntries(b,l):k.indexEntries(b,e,l);return k={next:function*(a,b){a=(yield* m.next(b));if(a.done)return{done:!0};b=a.value[1];return{done:!1,value:[a.value[0],i==null?b:i.afterEntriesNext({tableName:c.name,value:b})]}}},k[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]=j,k}return j()}function b(a,b,c,e,f,g){function i(){var j=a.table(c.name),k=j.keys(b,e,babelHelpers.extends({},(h||(h=d("ReQLBounds"))).extendBounds(g,(e===void 0?c.primaryKeyIds:c.indexes[e]).length),{dir:f}));return j={next:function*(a,b){return yield* k.next(b)}},j[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]=i,j}return i()}function e(a,b,c,e,f,g,h){a=a.table(c);g=d("ReStoreOperationLock").isNoopLockProof(g)?d("LSSynchronousPromise").wrapInMaybeSyncPromiseIfNotPromise:Promise.resolve.bind(Promise);e!=null?e=g(d("ReStoreCommonUtils").gen(a.indexGet(b,e,f))):e=g(d("ReStoreCommonUtils").gen(a.tableGet(b,f)));return e.then(function(a){return a===void 0?void 0:h.afterGet({tableName:c,value:a})})}function o(a,b,c){return(a.has(b)?a:a.set(b,c())).get(b)}function p(a){a=(i||(i=d("I64"))).cast(a);if(a===void 0)throw c("FBLogger")("maw_db").mustfixThrow("Expected int64");return i.to_float(a)}function q(a,b,d,e,f){b=c("JSONStringifyBigIntSafe")([b,d]);d=a.get(b);(d==null?e:d[0])==null&&f==null?a.delete(b):a.set(b,[d==null?e:d[0],f])}function* r(a,b,c,e,f,g,h,i,k,m){function o(a,b){return a.length>=b?a:[].concat(a,[(j||(j=d("ReStoreKeyComparer"))).MAX_KEY])}if(h!=null&&(j||(j=d("ReStoreKeyComparer"))).compareKey(g,h)===0)return;f=a.tableNames[f];var p=f.cascadeUpdates,q=f.primaryKeyIds;f=function*(d){var f=d.indexName;d=d.tableName;var j=a.tableNames[d],p=j.indexes,s=j.primaryKeyIds,v=f==null?s:p[f];s={gte:g,lte:o(g,v.length)};p=n(c,e,j,f,"asc",s);j=[];f=(yield* p.next(k));while(!f.done)j.push(f.value),f=(yield* p.next(k));for(s of j){s[0];p=s[1];f=l(a.tableNames[d],p);if(h===void 0)yield* u(a,b,c,e,d,f,k),yield* r(a,b,c,e,d,f,void 0,i,k,m);else{j=q.reduce(function(a,b,c){if(c>=h.length)return a;a[v[c]]=h[c];return a},babelHelpers.extends({},p));yield* t(a,b,c,e,d,f,j,i,k,m);yield* r(a,b,c,e,d,f,l(a.tableNames[d],j),i,k,m)}}};for(p of p)yield* f(p)}function* s(a,b,c,e,f,g,h,k,n){var o=c.table(f),r=a.tableNames[f];if(r.autoIncrement){var s=g[r.primaryKeyIds[0]];s=(i||(i=d("I64"))).of_float(yield* o.incrementSeed(e,s===void 0?s:p(s)));g[r.primaryKeyIds[0]]=s}s=l(r,g);var t={},u=(yield* o.tableGet(e,s)),y=g;u!==void 0?t=v(r,Object.keys(r.indexes),u):y=m(g,r.name,n);g=(yield* o.tableSet(e,s,y));var z=g[0];g=g[1];r=v(r,Object.keys(r.indexes),y);var A=babelHelpers.extends({},r);for(var B of Object.keys(A))t[B]!==void 0&&(j||(j=d("ReStoreKeyComparer"))).compareKey(t[B],A[B])===0&&(delete A[B],delete t[B]);yield* w(a,b,c,e,f,A,h,k,n);yield* x(c,e,f,z,g?r:A);for(B of Object.keys(t))yield* o.indexDelete(e,B,t[B]);q(b,f,s,u,y);return s}function* t(a,b,c,e,f,g,h,i,j,k){var m=l(a.tableNames[f],h);d("LSStorage").areKeysEqual(g,m)||(yield* u(a,b,c,e,f,g,j));yield* s(a,b,c,e,f,h,i,j,k)}function* u(a,b,c,d,e,f,g){g=c.table(e);c=a.tableNames[e];a=(yield* g.tableGet(d,f));if(a!==void 0){if(Object.keys(c.indexes).length)for(var h of Object.keys(c.indexes)){var i=l(c,a,h);i!=null&&(yield* g.indexDelete(d,h,i))}yield* g.tableDelete(d,f);q(b,e,f,a,void 0)}}function v(a,b,c){return b.reduce(function(b,d){var e;e=(e=a.indexPredicates)==null?void 0:e[d];if(e!=null&&!e(c))return b;e=l(a,c,d);b[d]=e;return b},{})}function* w(a,b,c,d,e,f,g,h,i){var j=c.table(e);for(var k of Object.keys(f)){var m=f[k];m=(yield* j.indexGet(d,k,m));if(m!=null){m=l(a.tableNames[e],m);yield* u(a,b,c,d,e,m,h);yield* r(a,b,c,d,e,m,void 0,g,h,i)}}}function* x(a,b,c,d,e){a=a.table(c);for(c of Object.keys(e)){var f=e[c];f===void 0?yield* a.indexDelete(b,c,f):yield* a.indexSet(b,c,f,d)}}function* f(a,b,d,e,f,g,h,i,j,l){var m=d.table(i),o=g.tableNames[i];for(var p of j)yield* m.clearIds(e,p);p=j.filter(function(a){return(o==null?void 0:o.indexes[a])!=null});if(p.length===0)return;j=n(d,e,o,void 0,"asc",void 0,f);var q=[],r=(yield* j.next(l));while(!r.done){while(!r.done&&q.length<=k)q.push(r.value),r=(yield* j.next(l));for(var s of q){var t=s[0],u=s[1];u=v(g.tableNames[i],p,u);yield* w(g,a,d,e,i,u,f,l,h);t=c("nullthrows")(yield* m.getId(e,t));yield* x(d,e,i,t,u)}q.length=0;b==null?void 0:b()}}var y="__internal__transaction_id";function z(a,b,e,f,g,h,i){var j=null,k=new(d("ReStoreOperationLock").NoopLock)(),l=new(d("ReStoreOperationLock").ReStoreOperationLock)(),m=i==null?void 0:i.qplFlow,n=new Map(),p=(i={},i[y]=c("Random").uint32(),i.reportUsedTablesForSyncAccess=function(a){j=a&&a.every(function(a){return g.tablePermitsSynchronousIO(a)})&&j!==!1},i.table=function(c){return new(d("ReStoreTable").ReStoreTransactionTable)(c,a,e,l,g,f,p,m,h,b)},i.transactionTable=function(i,q){var r=q!=null?i+"::"+q:i;return o(n,r,function(){return new(d("ReStoreTable").ReStoreTransactionTable)(i,a,e,j===!0&&q!=null&&c("gkx")("2839")?k:l,g,f,p,m,h,b)})},i);i=function(a){Object.defineProperty(p,a,{get:function(){return p.transactionTable(a)}})};for(var q of Object.keys(a.tableNames))i(q);return p}function A(a){return a.types.includes("indexeddb")}g.collectIterator=a;g.searchKey=l;g.appendDefaultValues=m;g.entries=n;g.keys=b;g.getLocked=e;g.getOrCreate=o;g.cascadeKeyUpdatesLocked=r;g.putLocked=s;g.upsertLocked=t;g.deleteItemLocked=u;g.indicesWithKeyForValue=v;g.deconflictIndicesLocked=w;g.addIdToIndices=x;g.rebuildIndicesLocked=f;g.createReStoreTransaction=z;g.isPDB=A}),98);
__d("ReStoreDefaultValueMigration",["LSPlatformLsInitLog","ReStoreDefaultValuesMetadata","ReStoreUtils","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";function h(a,b){return i.apply(this,arguments)}function i(){i=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var c={};b=(yield d("ReStoreUtils").collectIterator(b[d("ReStoreDefaultValuesMetadata").defaultValuesTableName].entries(a)));for(a of b){b=a[0];var e=a[1];if(b!=null){var f;b=d("ReStoreDefaultValuesMetadata").tryParseDefaultValuesData(e);if(b==null)continue;e=b.columns;b=b.tableName;c[b]=(f=c[b])!=null?f:new Set();for(f of e)c[b].add(f)}}return c});return i.apply(this,arguments)}function j(a,b,c,d){return k.apply(this,arguments)}function k(){k=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c,d){a=(yield h(a,b));b={};for(c in c.tableNames){var e=d[c];if(e!=null)for(var f in e){var g;g=(g=a[c])==null?void 0:g.has(f);if(!g){b[c]=(g=b[c])!=null?g:{};b[c][f]=e[f]}}}return b});return k.apply(this,arguments)}function l(a,b,c){return m.apply(this,arguments)}function m(){m=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c){for(b in b){var e=Object.keys(c[b]||{});e={columns:e,tableName:b};yield a[d("ReStoreDefaultValuesMetadata").defaultValuesTableName].put(d("ReStoreDefaultValuesMetadata").defaultValuesColumnToValue(e))}});return m.apply(this,arguments)}function a(a,b,c,d){return n.apply(this,arguments)}function n(){n=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c,e){var f=new WeakMap();d("LSPlatformLsInitLog").addPoint("default_values_update_start");var g=function(a,b){d("LSPlatformLsInitLog").addPoint("default_values_update_end_failure",{string:{default_values_update_failure_point:b,default_values_update_failure_reason:String(a)}})};try{b=(yield j(f,a,b,e));try{if(c===!1)for(c in b){var h=a[c].entries(f),i=(yield h.next());while(!i.done){var k=babelHelpers["extends"]({},i.value[1]);k=d("ReStoreUtils").appendDefaultValues(k,c,e);yield a[c].put(k);i=(yield h.next())}}yield l(a,b,e);d("LSPlatformLsInitLog").addPoint("default_values_update_end")}catch(a){g(a,"setDefaultValues")}}catch(a){g(a,"findDefaultValuesToUpdate")}});return n.apply(this,arguments)}g.runMigrationForTableDefaultValuesIfNeeded=a}),98);
__d("ReStoreIndexMetadata",["FBLogger"],(function(a,b,c,d,e,f,g){"use strict";function h(a){a=typeof a==="string"?a:null;if(a==null)return null;try{a=JSON.parse(a);for(var b of a)if(typeof b!=="string")return null;return a}catch(a){return null}}function a(a){var b={indexName:"string",keyIds:"string","predicate?":"string",tableName:"string"};for(var d in b){var e=d.endsWith("?");if(e)e=a[d.slice(0,-1)];else{if(!Object.prototype.hasOwnProperty.call(a,d)||a[d]==null){c("FBLogger")("messenger_web").warn('Malformed index data, missing key "%s"',d);return null}e=a[d]}var f=b[d],g=typeof e;if(e!=null&&g!==f){c("FBLogger")("messenger_web").warn('Malformed index data, key "%s" has type "%s" but expected "%s"',d,g,f);return null}}e=h(a.keyIds);if(e==null){c("FBLogger")("messenger_web").warn('Malformed index data, key IDs "%s" are invalid',typeof a.keyIds==="string"?a.keyIds:"<"+typeof a.keyIds+">");return null}return typeof a.indexName!=="string"||typeof a.tableName!=="string"||typeof a.predicate!=="string"&&a.predicate!=null?null:babelHelpers["extends"]({data:e,indexName:a.indexName,tableName:a.tableName},a.predicate==null?{}:{predicate:a.predicate})}function b(a,b){if(a[0]==null)return!0;if(a[0].length!==b[0].length)return!0;for(var c=0;c<a[0].length;c++)if(a[0][c]!==b[0][c])return!0;return a[1]!==b[1]?!0:!1}function d(a){return babelHelpers["extends"]({indexName:a.indexName,keyIds:JSON.stringify(a.data),tableName:a.tableName},a.predicate==null?{}:{predicate:a.predicate})}e="_db_indices_meta";g.tryParseIndexData=a;g.indicesDiffer=b;g.indexDataToValue=d;g.indicesTableName=e}),98);
__d("ReStoreIndicesMigration",["LSPlatformLsInitLog","ReStoreCommonUtils","ReStoreIndexMetadata","ReStoreOperationLock","ReStoreUtils","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";function h(a,b){return i.apply(this,arguments)}function i(){i=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var c={};b=(yield d("ReStoreUtils").collectIterator(b[d("ReStoreIndexMetadata").indicesTableName].entries(a)));for(a of b){b=a[0];var e=a[1];if(b!=null){var f;e=d("ReStoreIndexMetadata").tryParseIndexData(e);if(e==null)continue;var g=e.indexName,h=e.tableName;c[h]=(f=c[h])!=null?f:{};c[h][g]=babelHelpers["extends"]({index:e.data,key:b},e.predicate==null?{}:{predicate:e.predicate})}}return c});return i.apply(this,arguments)}function j(a,b,c){return k.apply(this,arguments)}function k(){k=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c){a=(yield h(a,b));b={};for(var e in c.tableNames){var f=c.tableNames[e];for(var g in f.indexes){var i,j;i=[f.indexes[g],(i=f.indexPredicates)==null?void 0:(i=i[g])==null?void 0:i.toString()];j=(j=a[e])==null?void 0:j[g];if(d("ReStoreIndexMetadata").indicesDiffer([j==null?void 0:j.index,j==null?void 0:j.predicate],i)){b[e]=(j=b[e])!=null?j:{};b[e][g]=i}}}j={};for(i in c.tableNames)if(i in a){g=a[i];for(f in g){var k;e=g[f];k=(k=c.tableNames[i])==null?void 0:(k=k.indexes)==null?void 0:k[f];if(k==null){j[i]=(k=j[i])!=null?k:{};j[i][f]=e}}}return{toRemove:j,toUpdate:b}});return k.apply(this,arguments)}function l(a,b){return m.apply(this,arguments)}function m(){m=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){for(var c in b){var e=b[c];for(var f in e){var g=e[f],h=g[0];g=g[1];h=babelHelpers["extends"]({data:h,indexName:f,tableName:c},g==null?{}:{predicate:g});yield a[d("ReStoreIndexMetadata").indicesTableName].put(d("ReStoreIndexMetadata").indexDataToValue(h))}}});return m.apply(this,arguments)}function n(a,b){return o.apply(this,arguments)}function o(){o=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){for(var c in b){var e=b[c];for(var f in e){var g,h=e[f].key;yield (g=a[d("ReStoreIndexMetadata").indicesTableName])["delete"].apply(g,h)}}});return o.apply(this,arguments)}function p(a,b,c,e,f){return new(d("ReStoreOperationLock").ReStoreOperationLock)().chainPromiseOrValue(function(g){return d("ReStoreCommonUtils").gen(d("ReStoreUtils").rebuildIndicesLocked(a.changedKeys,a.clearCache,a.storeTx,new WeakMap(),a.hooksManager,c,e,b,f,g))})}function a(a,c,e,f){var g=new WeakMap();d("LSPlatformLsInitLog").addPoint("index_migration_start");var h=function(a,b){d("LSPlatformLsInitLog").addPoint("index_migration_end_failure",{string:{index_migration_failure_point:b,index_migration_failure_reason:String(a)}})};return j(g,a.transaction,c).then(function(){var g=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){var g=b.toRemove;b=b.toUpdate;var h=Array.from(new Set([].concat(Object.keys(b),Object.keys(g))));d("LSPlatformLsInitLog").addAnnotations({"int":{index_migration_rebuild_tables_count:h.length}});if(f===!1)for(h of h)if(c.tableNames[h]!=null){var i;i=Array.from(new Set([].concat(Object.keys((i=b[h])!=null?i:{}),Object.keys((i=g[h])!=null?i:{}))));yield p(a,h,c,e,i)}yield l(a.transaction,b);yield n(a.transaction,g);d("LSPlatformLsInitLog").addPoint("index_migration_end")});return function(a){return g.apply(this,arguments)}}(),function(a){return h(a,"findIndicesToUpdateOrRemove")})["catch"](function(a){return h(a,"rebuildIndices")})}g.runMigrationForIndicesIfNeeded=a}),98);
__d("ReStoreMigrateUtil",["$InternalEnum","Promise","ReQL","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h;f=b("$InternalEnum").Mirrored(["UPDATE_METADATA","UPGRADE_COMPLETE","NO_UPGRADE_NEEDED"]);function a(a,b){b="V"+b;return b in a?b:null}function i(a,b){return j.apply(this,arguments)}function j(){j=babelHelpers.wrapAsyncGenerator(function*(a,b){a=a.iterator(new WeakMap());var c=[],d=(yield babelHelpers.awaitAsyncGenerator(a.next()));while(!d.done){var e=d.value;e[0];e=e[1];c.push(e);c.length===b&&(yield c,c=[]);d=(yield babelHelpers.awaitAsyncGenerator(a.next()))}c.length&&(yield c)});return j.apply(this,arguments)}function c(a,b,c){return k.apply(this,arguments)}function k(){k=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c,e){var f=!0,g=!1,j;try{for(var k=babelHelpers.asyncIterator(i(d("ReQL").fromTableAscending(a),100)),l,l;l=(yield k.next()),f=l.done,l=(yield l.value),!f;f=!0){l=l;yield (h||(h=b("Promise"))).all(l.map(function(b){return c(b).then(a.put)}));e==null?void 0:e()}}catch(a){g=!0,j=a}finally{try{!f&&k["return"]!=null&&(yield k["return"]())}finally{if(g)throw j}}});return k.apply(this,arguments)}function e(a,b){function c(b){if(b==null)return void 0;var c={};for(var d in b)c[a+"_"+d]=b[d];return c}return{bool:c(b.bool),"int":c(b["int"]),string:c(b.string)}}g.ReStoreMigrateResult=f;g.getVersionFromNumber=a;g.fetchReQLBatch=i;g.upgradeTableEntries=c;g.prefixAnnotations=e}),98);
__d("ReStoreHashMigration",["JSONStringifyBigIntSafe","ReStoreDefaultValueMigration","ReStoreIndicesMigration","ReStoreMigrateUtil","ReStorePersistedMetadata","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";function a(a){a=Object.values(a.tableNames).map(function(a){return babelHelpers["extends"]({},a,{indexPredicates:Object.entries((a=a.indexPredicates)!=null?a:{}).map(function(a){var b=a[0];a=a[1];return[b,a.toString()]})})});return c("JSONStringifyBigIntSafe")(a)}function e(a,b,c,d){return h.apply(this,arguments)}function h(){h=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c,e){var f,g,h,i=e.loggers,j=i.logHistory;i=i.userFlow;var k=e.storeTx,l=e.transaction,m=d("ReStorePersistedMetadata").ReStorePersistedDefaultValuesVersion.hash(c),n=d("ReStorePersistedMetadata").ReStorePersistedSchemaVersion.hash(b);i==null?void 0:i.addAnnotations(d("ReStoreMigrateUtil").prefixAnnotations(a,{"int":{defaultValuesVersion:m,schemaVersion:n}}));var o=new(d("ReStorePersistedMetadata").ReStorePersistedSchemaVersion)(a),p=new(d("ReStorePersistedMetadata").ReStorePersistedDefaultValuesVersion)(a);f=(f=(yield o.read(k)))==null?void 0:f.schemaVersion;g=(g=(yield p.read(k)))==null?void 0:g.defaultValuesVersion;j==null?void 0:j.debug("storeSchemaVersion: "+((h=f)!=null?h:"null"));j==null?void 0:j.debug("storeDefaultValuesVersion: "+((h=g)!=null?h:"null"));f!=null&&(i==null?void 0:i.addAnnotations(d("ReStoreMigrateUtil").prefixAnnotations(a,{"int":{storeDefaultValuesVersion:g,storeSchemaVersion:f}})));if(f===n&&g===m)return d("ReStoreMigrateUtil").ReStoreMigrateResult.NO_UPGRADE_NEEDED;h=f==null;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(l,b,h,c);j==null?void 0:j.debug("migration default values finished");yield d("ReStoreIndicesMigration").runMigrationForIndicesIfNeeded(e,b,c,h);j==null?void 0:j.debug("migration indices finished");o.write(k,{schemaVersion:n});p.write(k,{defaultValuesVersion:m});j==null?void 0:j.debug("version is written");j==null?void 0:j.debug("migration hash end");i==null?void 0:i.addPoint("migration_hash_end");if(h)return d("ReStoreMigrateUtil").ReStoreMigrateResult.UPDATE_METADATA;else return d("ReStoreMigrateUtil").ReStoreMigrateResult.UPGRADE_COMPLETE});return h.apply(this,arguments)}g.serializeTableData=a;g.runHashMigration=e}),98);
__d("ReStorePersistenceIds",[],(function(a,b,c,d,e,f){"use strict";b="nextId";c="seed";function a(a){return a}f.idForNextId=b;f.idForSeed=c;f.castPersistenceId=a}),66);
__d("ReStorePersistedMetadata",["JSONStringifyBigIntSafe","ReStoreCommonUtils","ReStoreHashMigration","ReStorePersistenceIds","hashString"],(function(a,b,c,d,e,f,g){"use strict";var h="_db_schema_meta";a=function(){function a(a){this.$1=d("ReStorePersistenceIds").castPersistenceId(a)}var b=a.prototype;b.read=async function(a){a=await d("ReStoreCommonUtils").gen(a.table(h).readData(new WeakMap(),this.$1));return a};b.write=function(a,b){a.table(h).writeData(new WeakMap(),this.$1,b)};b.getId=function(){return this.$1};return a}();b=function(a){babelHelpers.inheritsLoose(b,a);function b(b){return a.call(this,b+"_schema_version")||this}b.hash=function(a){return c("hashString")(d("ReStoreHashMigration").serializeTableData(a))};return b}(a);e=function(a){babelHelpers.inheritsLoose(b,a);function b(){return a.call(this,"custom_migration_value_version")||this}return b}(a);f=function(a){babelHelpers.inheritsLoose(b,a);function b(b){return a.call(this,b+"_default_value_version")||this}b.hash=function(a){return c("hashString")(c("JSONStringifyBigIntSafe")(a))};return b}(a);g.schemaMetadataTableName=h;g.ReStorePersistedMetadata=a;g.ReStorePersistedSchemaVersion=b;g.ReStorePersistedCustomMigrationVersion=e;g.ReStorePersistedDefaultValuesVersion=f}),98);
__d("ReStoreMetadata",["ReStoreDefaultValuesMetadata","ReStoreIndexMetadata","ReStorePersistedMetadata"],(function(a,b,c,d,e,f,g){"use strict";function h(a){return!0}function a(a){var b=a.reduce(function(a,b){return function(c){return c[b]!=null&&a(c)}},h);b.toString=function(){return"dynamic predicate on ["+a.join(",")+"]"};return b}function b(a){return function(b){var c={},d={};for(b of b)for(var e of b){var f;e.id!=null&&(c[e.id]=e.name);d[e.name]={autoIncrement:(f=e.auto_increment)!=null?f:!1,cascadeUpdates:(f=a[e.name])!=null?f:[],indexes:(f=Object.fromEntries(Object.entries((f=e.indexes)!=null?f:{}).map(function(a){var b=a[0];a=a[1].columns;return[b,a]})))!=null?f:{},indexPredicates:Object.fromEntries(Object.entries((f=e.indexes)!=null?f:{}).filter(function(a){a[0];a=a[1];return a.predicate}).map(function(a){var b=a[0];a=a[1];return[b,a.predicate]})),name:e.name,primaryKeyIds:e.primary_key}}return{tableIds:c,tableNames:d}}}e=(c={},c[d("ReStoreDefaultValuesMetadata").defaultValuesTableName]={autoIncrement:!1,cascadeUpdates:[],indexes:{},name:d("ReStoreDefaultValuesMetadata").defaultValuesTableName,primaryKeyIds:["t"]},c[d("ReStoreIndexMetadata").indicesTableName]={autoIncrement:!1,cascadeUpdates:[],indexes:{},name:d("ReStoreIndexMetadata").indicesTableName,primaryKeyIds:["tableName","indexName"]},c[d("ReStorePersistedMetadata").schemaMetadataTableName]={autoIncrement:!1,cascadeUpdates:[],indexes:{},name:d("ReStorePersistedMetadata").schemaMetadataTableName,primaryKeyIds:[]},c);g.createPredicate=a;g.getBuildTableData=b;g.RESTORE_METADATA_TABLES=e}),98);
__d("ReStoreVersionedSchemaProviderUtil",["ReStoreMetadata"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){var c={tableIds:{},tableNames:d("ReStoreMetadata").RESTORE_METADATA_TABLES},e={};for(var f of Object.keys(a)){var g=a[f],h={},i={};for(var j in g.indexes)h[j]=g.indexes[j].fields,g.indexes[j].ignoreNulls.length>0&&(i[j]=d("ReStoreMetadata").createPredicate(g.indexes[j].ignoreNulls));c.tableNames[f]={autoIncrement:g.autoIncrement,cascadeUpdates:(j=b==null?void 0:b[f])!=null?j:[],indexes:h,indexPredicates:i,name:f,primaryKeyIds:g.primaryKey.fields};g.id!=null&&(c.tableIds[g.id]=f);Object.keys(g.defaults).length>0&&(e[f]=g.defaults)}return{defaults:e,tableData:c}}g.getTableData=a}),98);
__d("LSDbV1",["I64MigrationHelper","LSDbV1.upgrade","ReStoreDefaultValueMigration","ReStoreIndicesMigration","ReStoreVersionedSchemaProviderUtil","asyncToGeneratorRuntime"],(function(a,b,c,d,e,aa,ba){"use strict";c=Object.freeze({_user_info:{autoIncrement:!1,defaults:{},id:4,indexes:{},primaryKey:{fields:["id"],ignoreNulls:[]}},acs_request_context:{autoIncrement:!1,defaults:{},id:310,indexes:{},primaryKey:{fields:["taskId"],ignoreNulls:[]}},ad_context:{autoIncrement:!1,defaults:{},id:160,indexes:{},primaryKey:{fields:["adId"],ignoreNulls:[]}},admin_message_ctas:{autoIncrement:!1,defaults:{showAdChoiceIcon:!1,timestampMs:(a=d("I64MigrationHelper")).cast([0,0])},id:27,indexes:{ctaId:{fields:["ctaId"],ignoreNulls:[]}},primaryKey:{fields:["threadKey","messageId","ctaId"],ignoreNulls:[]}},ai_bot_feedback_submission_status:{autoIncrement:!1,defaults:{},id:315,indexes:{},primaryKey:{fields:["botResponseId"],ignoreNulls:[]}},ai_bot_search_metadata:{autoIncrement:!1,defaults:{contactViewerRelationship:a.cast([0,0]),displayName:"",score:0},id:298,indexes:{},primaryKey:{fields:["botId"],ignoreNulls:[]}},anonymous_task_context:{autoIncrement:!1,defaults:{failureCount:a.cast([0,0])},id:311,indexes:{},primaryKey:{fields:["taskId"],ignoreNulls:[]}},attachment_conversion:{autoIncrement:!1,defaults:{},id:164,indexes:{},primaryKey:{fields:["attachmentFbid"],ignoreNulls:[]}},attachment_ctas:{autoIncrement:!1,defaults:{},id:19,indexes:{fk_attachments:{fields:["threadKey","messageId","attachmentFbid","ctaId"],ignoreNulls:[]}},primaryKey:{fields:["ctaId"],ignoreNulls:[]}},attachment_items:{autoIncrement:!1,defaults:{attachmentIndex:a.cast([0,0])},id:18,indexes:{fk_attachments:{fields:["threadKey","messageId","attachmentFbid","attachmentIndex"],ignoreNulls:[]}},primaryKey:{fields:["attachmentFbid","attachmentIndex"],ignoreNulls:[]}},attachments:{autoIncrement:!1,defaults:{attachmentIndex:a.cast([0,0]),attachmentType:a.cast([0,0]),authorityLevel:a.cast([0,0]),hasMedia:!1,hasXma:!1,isSharable:!1,timestampMs:a.cast([0,0]),transportKey:"FBBroker"},id:16,indexes:{fk_messages:{fields:["threadKey","timestampMs","messageId","attachmentFbid"],ignoreNulls:[]},idx_attachments_collapsible_id:{fields:["threadKey","collapsibleId","messageId","attachmentFbid"],ignoreNulls:[]}},primaryKey:{fields:["threadKey","messageId","attachmentFbid"],ignoreNulls:[]}},attachments_ranges_v2__generated:{autoIncrement:!1,defaults:{hasMoreAfter:!1,hasMoreBefore:!1,isLoadingAfter:!1,isLoadingBefore:!1,mediaGroup:a.cast([0,0])},id:17,indexes:{},primaryKey:{fields:["threadKey","mediaGroup","minTimestampMs"],ignoreNulls:[]}},audio_channel_events:{autoIncrement:!1,defaults:{},id:238,indexes:{},primaryKey:{fields:["eventId","roomId"],ignoreNulls:[]}},available_reactions:{autoIncrement:!1,defaults:{},id:188,indexes:{},primaryKey:{fields:["emojiIdx"],ignoreNulls:[]}},avatar_settings:{autoIncrement:!1,defaults:{enableAvatarHotlike:!1},id:209,indexes:{},primaryKey:{fields:["threadId"],ignoreNulls:[]}},bot_profile_info_v2:{autoIncrement:!1,defaults:{hasEmbodiment:!1},id:299,indexes:{},primaryKey:{fields:["botId"],ignoreNulls:[]}},bots:{autoIncrement:!1,defaults:{},id:230,indexes:{},primaryKey:{fields:["threadKey","botId"],ignoreNulls:[]}},business_comm_items:{autoIncrement:!1,defaults:{},id:234,indexes:{},primaryKey:{fields:["threadKey","entId"],ignoreNulls:[]}},business_support_case_details:{autoIncrement:!1,defaults:{},id:222,indexes:{},primaryKey:{fields:["threadId"],ignoreNulls:[]}},business_support_notification_settings:{autoIncrement:!1,defaults:{},id:236,indexes:{},primaryKey:{fields:["userId"],ignoreNulls:[]}},business_thread_info:{autoIncrement:!1,defaults:{},id:260,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}},business_thread_suggestions:{autoIncrement:!1,defaults:{},id:290,indexes:{},primaryKey:{fields:["threadKey","suggestionType"],ignoreNulls:[]}},client_accounts:{autoIncrement:!0,defaults:{},id:272,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}},client_message_translation_info:{autoIncrement:!1,defaults:{},id:324,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}},client_messages:{autoIncrement:!0,defaults:{disabledActionsReason:a.cast([0,0]),isForwarded:!1,isHidden:!1,isReadonly:!1,isReplyOnly:!1,isTombstoned:!1,messageContentSubtype:a.cast([0,0]),messageContentType:a.cast([0,0]),messageCreationType:a.cast([0,0]),messageEphemeralityType:a.cast([0,0]),messageSource:a.cast([0,0]),replyMessageTextSize:a.cast([0,0]),replyStatus:a.cast([0,0]),replyVicinityStatus:a.cast([0,0]),sendStatus:a.cast([0,0]),shouldHideInSnippet:!1,shouldPersist:!1,textSize:a.cast([0,0])},id:273,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}},client_participants:{autoIncrement:!0,defaults:{capabilities1:a.cast([0,0]),capabilities2:a.cast([0,0]),isPending:!1,readActionTsMs:a.cast([0,0])},id:274,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}},client_thread_proactive_warning_settings:{autoIncrement:!1,defaults:{},id:241,indexes:{},primaryKey:{fields:["threadPk"],ignoreNulls:[]}},client_threads:{autoIncrement:!0,defaults:{capabilities1:a.cast([0,0]),capabilities2:a.cast([0,0]),groupMemberAddMode:a.cast([0,0]),isAdminSnippet:!1,isHidden:!1,isHotlikeSnippet:!1,isPending:!1,isShadowThread:!1,isTombstoned:!1,pinnedMessagesCount:a.cast([0,0])},id:276,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}},client_web_pinned_messages:{autoIncrement:!1,defaults:{},id:328,indexes:{pinnedMessageDisplayOrder:{fields:["threadKey","pinnedTimestampMs","offlineThreadingId"],ignoreNulls:[]}},primaryKey:{fields:["threadKey","offlineThreadingId","pinnedTimestampMs"],ignoreNulls:[]}},client_xmas:{autoIncrement:!0,defaults:{},id:314,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}},cm_category_list:{autoIncrement:!1,defaults:{},id:182,indexes:{},primaryKey:{fields:["communityId","categoryId"],ignoreNulls:[]}},cm_channel_events:{autoIncrement:!1,defaults:{},id:201,indexes:{eventsInThread:{fields:["threadId","eventId"],ignoreNulls:[]}},primaryKey:{fields:["eventId","threadId"],ignoreNulls:[]}},cm_channel_list:{autoIncrement:!1,defaults:{},id:167,indexes:{threadIdCommunityId:{fields:["threadId","communityId"],ignoreNulls:[]}},primaryKey:{fields:["communityId","threadId"],ignoreNulls:[]}},cm_community_list:{autoIncrement:!1,defaults:{},id:166,indexes:{},primaryKey:{fields:["communityId"],ignoreNulls:[]}},cm_search_nullstate_metadata:{autoIncrement:!1,defaults:{score:0},id:289,indexes:{},primaryKey:{fields:["threadKey","scoreType"],ignoreNulls:[]}},community_chat_admin_assist_command_sequences:{autoIncrement:!1,defaults:{},id:291,indexes:{},primaryKey:{fields:["commandSequenceId"],ignoreNulls:[]}},community_chat_message_seen_count:{autoIncrement:!1,defaults:{},id:221,indexes:{},primaryKey:{fields:["threadKey","messageId"],ignoreNulls:[]}},community_chat_poll_options:{autoIncrement:!1,defaults:{sortKeyCreationTimestamp:a.cast([0,0]),sortKeyVotingTimestamp:a.cast([0,0]),voteCount:a.cast([0,0])},id:224,indexes:{},primaryKey:{fields:["pollId","optionId"],ignoreNulls:[]}},community_chat_poll_votes:{autoIncrement:!1,defaults:{},id:225,indexes:{},primaryKey:{fields:["pollId","optionId","contactId"],ignoreNulls:[]}},community_chat_poll_votes_ranges_v2__generated:{autoIncrement:!1,defaults:{},id:244,indexes:{},primaryKey:{fields:["pollId","optionId","minTimestampMs"],ignoreNulls:[]}},community_chat_polls:{autoIncrement:!1,defaults:{pollType:a.cast([0,0])},id:223,indexes:{},primaryKey:{fields:["pollId"],ignoreNulls:[]}},community_direct_invites_presets:{autoIncrement:!1,defaults:{fallbackUrl:"",pictureUrlExpirationTimestampMs1:a.cast([0,0]),pictureUrlExpirationTimestampMs2:a.cast([0,0]),presetTypeV2:"ALL_MEMBERS"},id:199,indexes:{},primaryKey:{fields:["communityId","presetType","source"],ignoreNulls:[]}},community_events:{autoIncrement:!1,defaults:{},id:202,indexes:{},primaryKey:{fields:["eventId","communityId"],ignoreNulls:[]}},community_folders:{autoIncrement:!1,defaults:{capabilities:a.cast([0,0]),capabilities2:a.cast([0,0]),capabilities3:a.cast([0,0]),inviteStatus:a.cast([0,0]),notificationMutedUntil:a.cast([0,0])},id:162,indexes:{byFbGroupId:{fields:["fbGroupId","folderId"],ignoreNulls:[]}},primaryKey:{fields:["folderId"],ignoreNulls:[]}},community_members:{autoIncrement:!1,defaults:{adminActions:a.cast([0,0]),channelBadges:a.cast([0,0]),contactCapabilities:a.cast([0,0]),requestId:"",source:a.cast([0,0]),threadRoles:a.cast([0,0])},id:170,indexes:{communityContact:{fields:["communityId","contactId","requestId","source","name"],ignoreNulls:[]},communitySource:{fields:["communityId","source","requestId","contactId","name"],ignoreNulls:[]}},primaryKey:{fields:["communityId","contactId","source","requestId"],ignoreNulls:[]}},community_members_ranges_v2__generated:{autoIncrement:!1,defaults:{},id:189,indexes:{},primaryKey:{fields:["communityId","isAdmin","minName","source","requestId"],ignoreNulls:[]}},community_messaging_aggregated_copresence_counts_for_chat:{autoIncrement:!1,defaults:{},id:204,indexes:{},primaryKey:{fields:["threadId"],ignoreNulls:[]}},community_messaging_aggregated_user_presence_counts_for_community:{autoIncrement:!1,defaults:{},id:203,indexes:{},primaryKey:{fields:["folderId"],ignoreNulls:[]}},community_rules:{autoIncrement:!1,defaults:{},id:254,indexes:{},primaryKey:{fields:["communityId","ruleId"],ignoreNulls:[]}},community_surface_ranges:{autoIncrement:!1,defaults:{},id:253,indexes:{},primaryKey:{fields:["surfaceType","communityKey"],ignoreNulls:[]}},community_thread_sync_info:{autoIncrement:!1,defaults:{},id:294,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}},connectivity_status:{autoIncrement:!0,defaults:{},id:114,indexes:{},primaryKey:{fields:["statusId"],ignoreNulls:[]}},contact_upload_settings:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0])},id:74,indexes:{},primaryKey:{fields:["userid"],ignoreNulls:[]}},contacts:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),blockedByViewerStatus:a.cast([0,0]),canViewerMessage:!0,capabilities2:a.cast([0,0]),contactReachabilityStatusType:a.cast([0,0]),contactType:a.cast([0,0]),contactTypeExact:a.cast([0,0]),gender:a.cast([0,0]),isEmployee:!1,isMemorialized:!1,isMessengerUser:!1,optimisticBlockedByViewerStatus:a.cast([0,0]),optimisticBlockedByViewerStatusTimestampMs:a.cast([0,0]),rank:0,restrictionType:a.cast([0,0]),waConnectStatus:a.cast([0,0])},id:7,indexes:{blockedByViewerStatusId:{fields:["blockedByViewerStatus","id"],ignoreNulls:[]}},primaryKey:{fields:["id"],ignoreNulls:[]}},contextual_profile_v1:{autoIncrement:!1,defaults:{},id:216,indexes:{associatedEntityIdAndOwner:{fields:["associatedEntityId","ownerId"],ignoreNulls:[]}},primaryKey:{fields:["ownerId","associatedEntityId"],ignoreNulls:[]}},cowatch_sessions:{autoIncrement:!1,defaults:{},id:80,indexes:{},primaryKey:{fields:["cowatchSessionId"],ignoreNulls:[]}},ctas:{autoIncrement:!1,defaults:{},id:15,indexes:{},primaryKey:{fields:["ctaId"],ignoreNulls:[]}},custom_thread_commands:{autoIncrement:!1,defaults:{isOpenPersistentMenu:!1},id:296,indexes:{commandId:{fields:["commandId"],ignoreNulls:[]},rank:{fields:["threadKey","rank"],ignoreNulls:[]}},primaryKey:{fields:["threadKey","commandId"],ignoreNulls:[]}},cutover_threads:{autoIncrement:!1,defaults:{isMigrated:!1,showOpenMessageHistory:!1},id:193,indexes:{},primaryKey:{fields:["openThreadId"],ignoreNulls:[]}},data_trace_addon:{autoIncrement:!0,defaults:{timestampMs:a.cast([0,0])},id:154,indexes:{traceIdAddonId:{fields:["traceId","addonId"],ignoreNulls:[]}},primaryKey:{fields:["addonId"],ignoreNulls:[]}},data_trace_meta:{autoIncrement:!1,defaults:{foregroundTimestampMs:a.cast([0,0]),initTimestampMs:a.cast([0,0]),shouldFlush:!1,traceType:a.cast([0,0])},id:153,indexes:{shouldFlushInitTimestampMsTraceId:{fields:["shouldFlush","initTimestampMs","traceId"],ignoreNulls:[]}},primaryKey:{fields:["traceId"],ignoreNulls:[]}},device_metadata:{autoIncrement:!0,defaults:{},id:185,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}},discoverable_chat_participants:{autoIncrement:!1,defaults:{source:a.cast([0,0])},id:248,indexes:{},primaryKey:{fields:["threadId","contactId","source"],ignoreNulls:[]}},discoverable_chat_participants_ranges_v2__generated:{autoIncrement:!1,defaults:{},id:249,indexes:{},primaryKey:{fields:["threadId","source","minName"],ignoreNulls:[]}},discoverable_chats_personal_inbox_info:{autoIncrement:!1,defaults:{},id:326,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}},e2ee_composer_draft_link_preview:{autoIncrement:!1,defaults:{},id:329,indexes:{},primaryKey:{fields:["draftId"],ignoreNulls:[]}},e2ee_dummy_table_for_sync:{autoIncrement:!1,defaults:{},id:171,indexes:{},primaryKey:{fields:["placeholder"],ignoreNulls:[]}},edit_message_history:{autoIncrement:!0,defaults:{sendStatus:a.cast([0,0])},id:322,indexes:{originalMsgPkEditTs:{fields:["originalMessagePk","serverAdjustedEditTsMs"],ignoreNulls:[]}},primaryKey:{fields:["pk"],ignoreNulls:[]}},emoji_sets:{autoIncrement:!1,defaults:{},id:151,indexes:{},primaryKey:{fields:["categoryIdx","emojiIdx","type_"],ignoreNulls:[]}},encrypted_backup_restore_task_payload_context:{autoIncrement:!1,defaults:{},id:190,indexes:{},primaryKey:{fields:["taskId"],ignoreNulls:[]}},encrypted_backups:{autoIncrement:!0,defaults:{authorityLevel:a.cast([0,0]),hasOtcEligibleDevices:!1},id:172,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}},encrypted_backups_client_restore_status:{autoIncrement:!1,defaults:{},id:231,indexes:{},primaryKey:{fields:["threadId"],ignoreNulls:[]}},encrypted_backups_debug_metadata:{autoIncrement:!1,defaults:{},id:206,indexes:{},primaryKey:{fields:["backupId"],ignoreNulls:[]}},encrypted_backups_debug_metadata_v2:{autoIncrement:!1,defaults:{},id:217,indexes:{},primaryKey:{fields:["backupId"],ignoreNulls:[]}},encrypted_backups_dyi_backup_restore_status:{autoIncrement:!0,defaults:{},id:208,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}},encrypted_backups_epoch_debug_metadata:{autoIncrement:!1,defaults:{},id:207,indexes:{},primaryKey:{fields:["epochId"],ignoreNulls:[]}},encrypted_backups_epoch_debug_metadata_v2:{autoIncrement:!1,defaults:{},id:218,indexes:{},primaryKey:{fields:["epochId"],ignoreNulls:[]}},encrypted_backups_message_backup_status:{autoIncrement:!1,defaults:{},id:213,indexes:{},primaryKey:{fields:["messagePk"],ignoreNulls:[]}},encrypted_backups_metadata:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0])},id:196,indexes:{},primaryKey:{fields:["backupId"],ignoreNulls:[]}},encrypted_backups_otc_devices:{autoIncrement:!1,defaults:{},id:261,indexes:{},primaryKey:{fields:["deviceId"],ignoreNulls:[]}},encrypted_backups_otc_notification_sending_status:{autoIncrement:!0,defaults:{},id:285,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}},encrypted_backups_status_trigger:{autoIncrement:!0,defaults:{},id:195,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}},encrypted_backups_virtual_devices:{autoIncrement:!0,defaults:{removalStatus:a.cast([0,1]),requiresHsmMigration:!1,virtualDeviceType:a.cast([0,1])},id:184,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}},epd_cookie_settings:{autoIncrement:!1,defaults:{},id:163,indexes:{},primaryKey:{fields:[],ignoreNulls:[]}},experiences_shared_state:{autoIncrement:!1,defaults:{},id:282,indexes:{},primaryKey:{fields:["stateKey"],ignoreNulls:[]}},fb_events:{autoIncrement:!1,defaults:{},id:200,indexes:{},primaryKey:{fields:["eventId"],ignoreNulls:[]}},fb_transport_contacts:{autoIncrement:!0,defaults:{},id:240,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}},feature_limits:{autoIncrement:!1,defaults:{},id:150,indexes:{},primaryKey:{fields:["type_"],ignoreNulls:[]}},filtered_messages_ranges_v2__generated:{autoIncrement:!1,defaults:{},id:211,indexes:{},primaryKey:{fields:["threadKey","mailboxType","minTimestampMs","minMessageId","messageRangeFilter"],ignoreNulls:[]}},filtered_threads_ranges_v3__generated:{autoIncrement:!1,defaults:{folderName:"inbox",hasMoreAfter:!1,hasMoreBefore:!1,isLoadingAfter:!1,isLoadingBefore:!1,maxLastActivityTimestampMs:a.cast([0,0]),maxThreadKey:a.cast([0,0]),minLastActivityTimestampMs:a.cast([0,0]),minThreadKey:a.cast([0,0]),secondaryThreadRangeFilter:a.cast([0,0]),threadRangeFilterValue:""},id:247,indexes:{},primaryKey:{fields:["parentThreadKey","minThreadKey","minLastActivityTimestampMs","threadRangeFilter","folderName","secondaryThreadRangeFilter","threadRangeFilterValue"],ignoreNulls:[]}},focus_mode_ctas:{autoIncrement:!1,defaults:{},id:30,indexes:{},primaryKey:{fields:["threadKey","messageId","ctaId"],ignoreNulls:[]}},folder_metadata:{autoIncrement:!1,defaults:{},id:35,indexes:{},primaryKey:{fields:["parentThreadKey"],ignoreNulls:[]}},forward_content:{autoIncrement:!1,defaults:{},id:129,indexes:{},primaryKey:{fields:["value","type_"],ignoreNulls:[]}},gdpr_settings:{autoIncrement:!1,defaults:{},id:75,indexes:{},primaryKey:{fields:["userid"],ignoreNulls:[]}},gradient_colors:{autoIncrement:!1,defaults:{},id:117,indexes:{},primaryKey:{fields:["themeFbid","gradientIndex"],ignoreNulls:[]}},group_invitations_pending:{autoIncrement:!1,defaults:{communityJoinRequestStatus:a.cast([0,0]),communityParticipationControlRequestStatus:a.cast([0,0]),communityParticipationControlSetting:a.cast([0,0])},id:235,indexes:{},primaryKey:{fields:["linkHash"],ignoreNulls:[]}},group_invites:{autoIncrement:!1,defaults:{},id:197,indexes:{},primaryKey:{fields:["threadKey","inviterId","inviteeId"],ignoreNulls:[]}},group_membership_approval_requests:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0])},id:26,indexes:{},primaryKey:{fields:["threadKey","contactId"],ignoreNulls:[]}},horizon_contact_info:{autoIncrement:!1,defaults:{},id:323,indexes:{},primaryKey:{fields:["contactId"],ignoreNulls:[]}},ig_contact_info:{autoIncrement:!1,defaults:{e2eeEligibility:a.cast([0,0])},id:176,indexes:{},primaryKey:{fields:["contactId"],ignoreNulls:[]}},ig_thread_info:{autoIncrement:!1,defaults:{igDmSettingsMode:a.cast([0,0]),igDmSettingsTtlSec:a.cast([-1,4294967295])},id:194,indexes:{igThreadID:{fields:["igThreadId"],ignoreNulls:[]}},primaryKey:{fields:["threadKey"],ignoreNulls:[]}},ig_thread_labels:{autoIncrement:!1,defaults:{labelTimestampMs:a.cast([0,0])},id:318,indexes:{},primaryKey:{fields:["threadKey","labelType"],ignoreNulls:[]}},igd_xma_receiver_fetch:{autoIncrement:!1,defaults:{},id:316,indexes:{},primaryKey:{fields:["messageId"],ignoreNulls:[]}},in_thread_banner:{autoIncrement:!1,defaults:{},id:118,indexes:{},primaryKey:{fields:["threadKey","bannerId"],ignoreNulls:[]}},in_thread_banner_ctas:{autoIncrement:!1,defaults:{},id:119,indexes:{},primaryKey:{fields:["bannerId","ctaId"],ignoreNulls:[]}},in_thread_banner_overflow_menu:{autoIncrement:!1,defaults:{},id:120,indexes:{},primaryKey:{fields:["threadKey","bannerId"],ignoreNulls:[]}},inbox_threads_ranges:{autoIncrement:!1,defaults:{hasMoreBefore:!1,isLoadingBefore:!1,minLastActivityTimestampMs:a.cast([0,0]),minThreadKey:a.cast([0,0])},id:198,indexes:{},primaryKey:{fields:["syncGroup"],ignoreNulls:[]}},inbox_view_state:{autoIncrement:!1,defaults:{value:""},id:277,indexes:{},primaryKey:{fields:["interface_","configName"],ignoreNulls:[]}},inbox_view_state_v2:{autoIncrement:!1,defaults:{value:""},id:281,indexes:{},primaryKey:{fields:["configName"],ignoreNulls:[]}},lightspeed_task_context:{autoIncrement:!1,defaults:{requiresAuthentication:!1},id:306,indexes:{},primaryKey:{fields:["taskId"],ignoreNulls:[]}},linked_groups:{autoIncrement:!1,defaults:{},id:130,indexes:{threadKey:{fields:["threadKey"],ignoreNulls:[]}},primaryKey:{fields:["id"],ignoreNulls:[]}},live_location_sharers:{autoIncrement:!1,defaults:{},id:149,indexes:{},primaryKey:{fields:["threadKey","userId"],ignoreNulls:[]}},local_message_persistence_store:{autoIncrement:!0,defaults:{},id:301,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}},local_message_persistence_store_deleted_messages:{autoIncrement:!0,defaults:{},id:303,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}},local_message_persistence_store_supplemental:{autoIncrement:!0,defaults:{},id:302,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}},local_message_persistence_store_tag_index:{autoIncrement:!0,defaults:{},id:304,indexes:{offlineThreadingId:{fields:["offlineThreadingId","threadId"],ignoreNulls:[]}},primaryKey:{fields:["pk"],ignoreNulls:[]}},m_suggestions:{autoIncrement:!1,defaults:{},id:21,indexes:{},primaryKey:{fields:["threadKey","timestampMs","suggestionId"],ignoreNulls:[]}},mailbox_metadata:{autoIncrement:!1,defaults:{},id:25,indexes:{},primaryKey:{fields:["id"],ignoreNulls:[]}},mailbox_task_completion_api_tasks:{autoIncrement:!1,defaults:{},id:233,indexes:{},primaryKey:{fields:["taskId"],ignoreNulls:[]}},mailbox_task_completion_notification_context:{autoIncrement:!1,defaults:{},id:232,indexes:{},primaryKey:{fields:["notificationScopeKey"],ignoreNulls:[]}},media_send_jobs:{autoIncrement:!1,defaults:{},id:51,indexes:{},primaryKey:{fields:["offlineAttachmentId"],ignoreNulls:[]}},media_staging:{autoIncrement:!1,defaults:{progress:0},id:161,indexes:{},primaryKey:{fields:["offlineAttachmentId"],ignoreNulls:[]}},message_requests:{autoIncrement:!1,defaults:{},id:34,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}},message_search_queries:{autoIncrement:!1,defaults:{},id:70,indexes:{},primaryKey:{fields:["type_","query","threadKeyV2"],ignoreNulls:[]}},message_search_results:{autoIncrement:!1,defaults:{},id:69,indexes:{},primaryKey:{fields:["type_","query","threadKey","globalIndex"],ignoreNulls:[]}},messages:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),displayedContentTypes:a.cast([0,1]),hasQuickReplies:!1,isAdminMessage:!1,isCollapsed:!1,isExpired:!1,isUnsent:!1,messageRenderingType:a.cast([0,0]),primarySortKey:a.cast([0,0]),quickReplyType:a.cast([0,0]),replyAttachmentType:a.cast([0,0]),replyStatus:a.cast([0,0]),secondarySortKey:a.cast([0,0]),sendStatus:a.cast([0,0]),sendStatusV2:a.cast([0,0]),textHasLinks:!1,timestampMs:a.cast([0,0]),transportKey:"FBBroker",unsentTimestampMs:a.cast([0,0]),viewFlags:a.cast([0,0])},id:12,indexes:{ephemeralExpirationTs:{fields:["ephemeralExpirationTs","messageId"],ignoreNulls:["ephemeralExpirationTs"]},messageDisplayOrder:{fields:["threadKey","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:[]},messageGroupId:{fields:["groupId","messageId"],ignoreNulls:["groupId"]},messageId:{fields:["messageId"],ignoreNulls:[]},messageSubthreadKey:{fields:["subthreadKey","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:["subthreadKey"]},optimistic:{fields:["offlineThreadingId"],ignoreNulls:[]},replySourceIdMessageID:{fields:["replySourceId","messageId"],ignoreNulls:["replySourceId"]},threadKeyPrimarySortKeySecondarySortKeyBotResponseId:{fields:["threadKey","primarySortKey","secondarySortKey","botResponseId","messageId"],ignoreNulls:["botResponseId"]}},primaryKey:{fields:["threadKey","timestampMs","messageId"],ignoreNulls:[]}},messages_optimistic_context:{autoIncrement:!1,defaults:{},id:31,indexes:{},primaryKey:{fields:["taskId"],ignoreNulls:[]}},messages_ranges_v2__generated:{autoIncrement:!1,defaults:{hasMoreAfter:!1,hasMoreBefore:!1,isLoadingAfter:!1,isLoadingBefore:!1},id:13,indexes:{},primaryKey:{fields:["threadKey","minTimestampMs","minMessageId"],ignoreNulls:[]}},messages_status:{autoIncrement:!1,defaults:{timestampMs:a.cast([0,0])},id:33,indexes:{},primaryKey:{fields:["messageId","threadKey","timestampMs","threadKeyFromParticipants","contactId","type_"],ignoreNulls:[]}},messaging_privacy_settings:{autoIncrement:!1,defaults:{e2eeXmaPreviewsDisabled:!1,readReceiptsDisabled:a.cast([0,0])},id:288,indexes:{},primaryKey:{fields:["userId"],ignoreNulls:[]}},messaging_settings:{autoIncrement:!1,defaults:{},id:83,indexes:{},primaryKey:{fields:["id"],ignoreNulls:[]}},mi_act_mapping_table:{autoIncrement:!1,defaults:{},id:173,indexes:{chat_id:{fields:["clientThreadPk"],ignoreNulls:[]},fk_threads:{fields:["serverThreadKey"],ignoreNulls:[]},jid:{fields:["jid"],ignoreNulls:[]}},primaryKey:{fields:["serverThreadKey","clientThreadPk","jid"],ignoreNulls:[]}},msg_pinned_messages_v2:{autoIncrement:!1,defaults:{},id:205,indexes:{pinnedMessageDisplayOrder:{fields:["threadKey","pinnedTimestampMs","messageId"],ignoreNulls:[]}},primaryKey:{fields:["threadKey","messageId","pinnedTimestampMs"],ignoreNulls:[]}},msgr_quiet_time:{autoIncrement:!1,defaults:{},id:317,indexes:{quietTimeId:{fields:["quietTimeId"],ignoreNulls:[]}},primaryKey:{fields:["quietTimeId"],ignoreNulls:[]}},mwb_safety_interventions:{autoIncrement:!1,defaults:{},id:327,indexes:{},primaryKey:{fields:["interventionId","interventionEntId"],ignoreNulls:[]}},network_requests:{autoIncrement:!1,defaults:{},id:3,indexes:{},primaryKey:{fields:["taskQueueName","syncDatabaseId"],ignoreNulls:[]}},new_payment_credential_option:{autoIncrement:!1,defaults:{},id:90,indexes:{},primaryKey:{fields:["credentialType"],ignoreNulls:[]}},notification_settings:{autoIncrement:!1,defaults:{includeInSwitchAccountBadges:!1,mutePageAccountMessageReminder:!1},id:175,indexes:{},primaryKey:{fields:["id"],ignoreNulls:[]}},occamadillo_most_recent_message_per_thread:{autoIncrement:!1,defaults:{},id:292,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}},offline_queue_thread_status:{autoIncrement:!1,defaults:{},id:295,indexes:{},primaryKey:{fields:["threadJid"],ignoreNulls:[]}},ohai_gateway_key_configs:{autoIncrement:!1,defaults:{},id:313,indexes:{},primaryKey:{fields:["keyId"],ignoreNulls:[]}},p2m_charge:{autoIncrement:!1,defaults:{},id:97,indexes:{},primaryKey:{fields:["orderId"],ignoreNulls:[]}},p2m_invoice:{autoIncrement:!1,defaults:{},id:94,indexes:{},primaryKey:{fields:["orderId"],ignoreNulls:[]}},p2m_invoice_attachment:{autoIncrement:!1,defaults:{},id:95,indexes:{},primaryKey:{fields:["orderId","productItemId"],ignoreNulls:[]}},p2p_request_v2:{autoIncrement:!1,defaults:{},id:100,indexes:{optimistic:{fields:["optimisticTransactionId"],ignoreNulls:[]}},primaryKey:{fields:["timestampMs","transactionId"],ignoreNulls:[]}},p2p_transfer_v2:{autoIncrement:!1,defaults:{},id:101,indexes:{optimistic:{fields:["optimisticTransactionId"],ignoreNulls:[]}},primaryKey:{fields:["timestampMs","transactionId"],ignoreNulls:[]}},pake_messages:{autoIncrement:!1,defaults:{},id:258,indexes:{},primaryKey:{fields:["sessionId"],ignoreNulls:[]}},participant_list_queries:{autoIncrement:!1,defaults:{},id:219,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}},participant_search_queries:{autoIncrement:!1,defaults:{},id:214,indexes:{},primaryKey:{fields:["query","threadKey"],ignoreNulls:[]}},participants:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),deliveredWatermarkTimestampMs:a.cast([0,0]),groupParticipantJoinState:a.cast([0,0]),readActionTimestampMs:a.cast([0,0]),readWatermarkTimestampMs:a.cast([0,0]),threadRoles:a.cast([0,0])},id:14,indexes:{contactIdThreadKey:{fields:["contactId","threadKey"],ignoreNulls:[]},threadKeyDeliveredWatermarkTimestampMs:{fields:["threadKey","deliveredWatermarkTimestampMs","contactId"],ignoreNulls:[]},threadKeyReadWatermarkTimestampMs:{fields:["threadKey","readWatermarkTimestampMs","contactId"],ignoreNulls:[]}},primaryKey:{fields:["threadKey","contactId"],ignoreNulls:[]}},payment_auth_request_results:{autoIncrement:!1,defaults:{},id:108,indexes:{},primaryKey:{fields:["requestId"],ignoreNulls:[]}},payment_client_auth_token:{autoIncrement:!1,defaults:{},id:107,indexes:{},primaryKey:{fields:["userId","deviceId","appId"],ignoreNulls:[]}},payment_method:{autoIncrement:!1,defaults:{},id:89,indexes:{},primaryKey:{fields:["paymentMethodId"],ignoreNulls:[]}},payment_p2p_risk_verification:{autoIncrement:!1,defaults:{},id:98,indexes:{},primaryKey:{fields:["requestId","transactionId"],ignoreNulls:[]}},payment_pin_verification_results:{autoIncrement:!1,defaults:{},id:113,indexes:{},primaryKey:{fields:["requestId"],ignoreNulls:[]}},payment_price_list_item:{autoIncrement:!1,defaults:{},id:96,indexes:{},primaryKey:{fields:["orderId","id"],ignoreNulls:[]}},payment_server_request_results:{autoIncrement:!1,defaults:{},id:109,indexes:{},primaryKey:{fields:["requestId"],ignoreNulls:[]}},payment_transaction_v2:{autoIncrement:!1,defaults:{},id:99,indexes:{optimistic:{fields:["optimisticTransactionId"],ignoreNulls:[]}},primaryKey:{fields:["timestampMs","transactionId"],ignoreNulls:[]}},payment_type_bank_v2:{autoIncrement:!1,defaults:{},id:88,indexes:{},primaryKey:{fields:["paymentMethodId"],ignoreNulls:[]}},payment_type_card:{autoIncrement:!1,defaults:{},id:86,indexes:{},primaryKey:{fields:["paymentCredentialId"],ignoreNulls:[]}},payment_type_paypal:{autoIncrement:!1,defaults:{},id:85,indexes:{},primaryKey:{fields:["paymentCredentialId"],ignoreNulls:[]}},payment_user_auth:{autoIncrement:!1,defaults:{},id:106,indexes:{},primaryKey:{fields:["userId"],ignoreNulls:[]}},payments_eligibility:{autoIncrement:!1,defaults:{},id:92,indexes:{},primaryKey:{fields:["userId"],ignoreNulls:[]}},payments_ofac_blacklisted_countries:{autoIncrement:!1,defaults:{},id:93,indexes:{},primaryKey:{fields:["isoCountryCode"],ignoreNulls:[]}},payments_transaction_details_core:{autoIncrement:!1,defaults:{},id:103,indexes:{},primaryKey:{fields:["transactionId"],ignoreNulls:[]}},payments_transaction_details_ctas:{autoIncrement:!1,defaults:{},id:104,indexes:{},primaryKey:{fields:["transactionId","ctaId"],ignoreNulls:[]}},payments_transaction_details_rows:{autoIncrement:!1,defaults:{},id:105,indexes:{},primaryKey:{fields:["transactionId","rowOrder"],ignoreNulls:[]}},paypal_funding_options:{autoIncrement:!1,defaults:{},id:91,indexes:{},primaryKey:{fields:["optionId"],ignoreNulls:[]}},pending_backups_context_v2:{autoIncrement:!0,defaults:{isInstamadillo:!1},id:177,indexes:{fk_pending_tasks:{fields:["pendingBackupTaskId"],ignoreNulls:[]}},primaryKey:{fields:["pk"],ignoreNulls:[]}},pending_fire_and_forget_tasks:{autoIncrement:!0,defaults:{enqueueTimestampMs:a.cast([0,0]),priority:a.cast([0,0])},id:5,indexes:{},primaryKey:{fields:["taskId"],ignoreNulls:[]}},pending_protobuf_backups_context:{autoIncrement:!0,defaults:{},id:305,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}},pending_tasks:{autoIncrement:!0,defaults:{enqueueTimestampMs:a.cast([0,0]),failureCount:a.cast([0,0]),firstExecutedTimestampMs:a.cast([0,0]),minTimeToSyncTimestampMs:a.cast([0,0]),pluginType:a.cast([0,0]),priority:a.cast([0,0]),taskDispatchPriority:a.cast([0,0])},id:2,indexes:{queueNameTaskId:{fields:["queueName","taskId"],ignoreNulls:[]}},primaryKey:{fields:["taskId"],ignoreNulls:[]}},persistent_menu_ctas:{autoIncrement:!1,defaults:{ctaType:"fallback"},id:251,indexes:{},primaryKey:{fields:["threadKey","ctaId"],ignoreNulls:[]}},persistent_menu_items:{autoIncrement:!1,defaults:{},id:77,indexes:{},primaryKey:{fields:["threadKey","ctaId"],ignoreNulls:[]}},pinned_messages_v2:{autoIncrement:!1,defaults:{},id:155,indexes:{},primaryKey:{fields:["threadKey","messageId"],ignoreNulls:[]}},pinned_threads:{autoIncrement:!1,defaults:{},id:82,indexes:{pinnedTimestamp:{fields:["pinnedTimestamp","threadKey"],ignoreNulls:[]}},primaryKey:{fields:["threadKey"],ignoreNulls:[]}},poll_options_v2:{autoIncrement:!1,defaults:{sortKeyCreationTimestamp:a.cast([0,0]),sortKeyVotingTimestamp:a.cast([0,0]),voteCount:a.cast([0,0])},id:39,indexes:{},primaryKey:{fields:["pollId","optionId"],ignoreNulls:[]}},poll_votes_v2:{autoIncrement:!1,defaults:{},id:40,indexes:{},primaryKey:{fields:["pollId","optionId","contactId"],ignoreNulls:[]}},polls:{autoIncrement:!1,defaults:{pollType:a.cast([0,0])},id:38,indexes:{},primaryKey:{fields:["pollId"],ignoreNulls:[]}},presence_settings:{autoIncrement:!1,defaults:{hasConvertedToViewSideSettings:!1,isActiveStatusViewable:!1,isActiveStatusViewableOnFb:!1,isActiveStatusViewableOnMessenger:!1},id:187,indexes:{},primaryKey:{fields:["userId"],ignoreNulls:[]}},presence_states:{autoIncrement:!1,defaults:{},id:186,indexes:{statusContactId:{fields:["status","contactId"],ignoreNulls:[]}},primaryKey:{fields:["contactId"],ignoreNulls:[]}},profile_sheet_information:{autoIncrement:!1,defaults:{},id:256,indexes:{},primaryKey:{fields:["userId"],ignoreNulls:[]}},quick_reply_ctas:{autoIncrement:!1,defaults:{},id:20,indexes:{},primaryKey:{fields:["threadKey","messageId","ctaId"],ignoreNulls:[]}},ranking_requests:{autoIncrement:!1,defaults:{},id:135,indexes:{},primaryKey:{fields:["scoreType"],ignoreNulls:[]}},ranking_scores:{autoIncrement:!1,defaults:{score:0,scoreIndex:a.cast([0,0])},id:134,indexes:{scoreTypeContactId:{fields:["scoreType","contactId"],ignoreNulls:[]}},primaryKey:{fields:["contactId","scoreType"],ignoreNulls:[]}},reachability_settings:{autoIncrement:!1,defaults:{},id:148,indexes:{},primaryKey:{fields:["audience"],ignoreNulls:[]}},reaction_v2_types:{autoIncrement:!1,defaults:{},id:229,indexes:{},primaryKey:{fields:["reactionFbid"],ignoreNulls:[]}},reactions:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),reactionCreationTimestampMs:a.cast([0,0]),timestampMs:a.cast([0,0]),transportKey:"FBBroker"},id:8,indexes:{fk_messages:{fields:["threadKey","timestampMs","messageId","actorId"],ignoreNulls:[]}},primaryKey:{fields:["threadKey","messageId","actorId"],ignoreNulls:[]}},reactions_v2:{autoIncrement:!1,defaults:{lastUpdatedTimestampMs:a.cast([0,0]),viewerReactionTimestampMs:a.cast([0,0])},id:226,indexes:{optimistic:{fields:["threadKey","messageId","reactionFbid"],ignoreNulls:[]}},primaryKey:{fields:["threadKey","messageId","reactionFbid","messageTimestamp"],ignoreNulls:[]}},reactions_v2_details:{autoIncrement:!1,defaults:{},id:239,indexes:{},primaryKey:{fields:["threadId","messageId","reactorId","reactionFbid"],ignoreNulls:[]}},reactions_v2_details_ranges_v2__generated:{autoIncrement:!1,defaults:{},id:243,indexes:{},primaryKey:{fields:["threadId","messageId","reactionFbid","minTimestampMs"],ignoreNulls:[]}},reactions_v2_details_users:{autoIncrement:!1,defaults:{},id:227,indexes:{},primaryKey:{fields:["reactorId","reactionFbid"],ignoreNulls:[]}},reactions_v2_details_users_ranges_v2__generated:{autoIncrement:!1,defaults:{},id:228,indexes:{},primaryKey:{fields:["reactorId","reactionFbid","minTimestampMs"],ignoreNulls:[]}},roll_call_contributions_v2:{autoIncrement:!1,defaults:{contributionSource:a.cast([0,0]),contributorId:a.cast([0,0])},id:255,indexes:{optimistic:{fields:["rollCallContributionId","rollCallId","messageId","messageTimestampMs"],ignoreNulls:[]}},primaryKey:{fields:["rollCallContributionId","rollCallId","messageId","messageTimestampMs","threadKey"],ignoreNulls:[]}},roll_calls:{autoIncrement:!1,defaults:{canViewWithoutContributing:!1,isBlurred:!1,viewerCanEditPrompt:!1,viewerHasContributed:!1},id:250,indexes:{},primaryKey:{fields:["rollCallId","threadKey"],ignoreNulls:[]}},room_participants:{autoIncrement:!1,defaults:{},id:64,indexes:{optimistic:{fields:["roomId","participantId"],ignoreNulls:[]}},primaryKey:{fields:["roomId","participantId","type_"],ignoreNulls:[]}},rooms:{autoIncrement:!1,defaults:{notificationMutedUntilMs:a.cast([0,0])},id:63,indexes:{optimistic:{fields:["optimisticClientToken"],ignoreNulls:[]}},primaryKey:{fields:["roomId"],ignoreNulls:[]}},rtc_call_events:{autoIncrement:!0,defaults:{isProcessed:!1},id:245,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}},rtc_multiway_call_initiation_conference_names:{autoIncrement:!1,defaults:{},id:24,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}},rtc_ongoing_calls_on_threads_v2:{autoIncrement:!1,defaults:{},id:23,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}},rtc_rooms_on_threads:{autoIncrement:!1,defaults:{},id:181,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}},rtc_signals:{autoIncrement:!1,defaults:{},id:22,indexes:{},primaryKey:{fields:["timestampMs","type_"],ignoreNulls:[]}},saved_messages:{autoIncrement:!1,defaults:{},id:165,indexes:{},primaryKey:{fields:["threadKey","messageId"],ignoreNulls:[]}},screen_time:{autoIncrement:!0,defaults:{},id:321,indexes:{},primaryKey:{fields:["recordId"],ignoreNulls:[]}},search_queries:{autoIncrement:!1,defaults:{surfaceType:a.cast([0,1])},id:67,indexes:{},primaryKey:{fields:["query","surfaceType"],ignoreNulls:[]}},secure_acs_blinded_tokens:{autoIncrement:!0,defaults:{},id:308,indexes:{},primaryKey:{fields:["tokenId"],ignoreNulls:[]}},secure_acs_configurations:{autoIncrement:!1,defaults:{},id:307,indexes:{},primaryKey:{fields:["configId"],ignoreNulls:[]}},secure_acs_tokens:{autoIncrement:!1,defaults:{},id:309,indexes:{},primaryKey:{fields:["tokenId"],ignoreNulls:[]}},secure_encrypted_backups_client_state:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),encryptionVersion:a.cast([0,0]),revisionVersion:a.cast([0,0])},id:168,indexes:{},primaryKey:{fields:["backupId"],ignoreNulls:[]}},secure_encrypted_backups_device_supported_versions:{autoIncrement:!1,defaults:{},id:192,indexes:{fk_secure_encrypted_backups_device_supported_versions_device_id:{fields:["deviceId","supportedVersion"],ignoreNulls:[]}},primaryKey:{fields:["deviceId","supportedVersion"],ignoreNulls:[]}},secure_encrypted_backups_devices:{autoIncrement:!1,defaults:{},id:191,indexes:{},primaryKey:{fields:["deviceId"],ignoreNulls:[]}},secure_encrypted_backups_epochs:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0])},id:169,indexes:{fk_secure_encrypted_backups_client_state:{fields:["backupId","epochId"],ignoreNulls:[]}},primaryKey:{fields:["epochId"],ignoreNulls:[]}},secure_encrypted_backups_generated_recovery_code:{autoIncrement:!1,defaults:{virtualDeviceType:a.cast([0,1])},id:174,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}},secure_encrypted_backups_message_thread_id_context:{autoIncrement:!1,defaults:{},id:180,indexes:{fk_pending_tasks:{fields:["taskId"],ignoreNulls:[]}},primaryKey:{fields:["taskId","listId"],ignoreNulls:[]}},secure_encrypted_backups_recovery_code_status:{autoIncrement:!1,defaults:{},id:178,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}},secure_get_secrets_context:{autoIncrement:!1,defaults:{},id:259,indexes:{},primaryKey:{fields:["deviceRegistrationId"],ignoreNulls:[]}},secure_recovery_code_data:{autoIncrement:!1,defaults:{},id:179,indexes:{fk_pending_tasks:{fields:["taskId"],ignoreNulls:[]}},primaryKey:{fields:["taskId"],ignoreNulls:[]}},self_profile:{autoIncrement:!1,defaults:{},id:81,indexes:{},primaryKey:{fields:["userId"],ignoreNulls:[]}},server_search_results:{autoIncrement:!1,defaults:{},id:65,indexes:{},primaryKey:{fields:["query","resultId","globalIndex"],ignoreNulls:[]}},server_search_sections:{autoIncrement:!1,defaults:{},id:66,indexes:{},primaryKey:{fields:["query","globalIndex"],ignoreNulls:[]}},shared_album_contributions:{autoIncrement:!1,defaults:{creationTimestampMs:a.cast([0,0]),creatorId:a.cast([0,0])},id:280,indexes:{},primaryKey:{fields:["sharedAlbumId","sharedAlbumContributionId","threadKey","messageId"],ignoreNulls:[]}},shared_albums:{autoIncrement:!1,defaults:{contributionCount:a.cast([0,0]),lastActivityTimestampMs:a.cast([0,0])},id:279,indexes:{},primaryKey:{fields:["sharedAlbumId","threadKey"],ignoreNulls:[]}},shared_albums_multimedia_upload_jobs:{autoIncrement:!1,defaults:{},id:283,indexes:{},primaryKey:{fields:["offlineThreadingId"],ignoreNulls:[]}},shared_albums_multimedia_upload_subjobs_status:{autoIncrement:!1,defaults:{},id:284,indexes:{},primaryKey:{fields:["offlineThreadingId","jobId"],ignoreNulls:[]}},status:{autoIncrement:!1,defaults:{},id:62,indexes:{optimistic:{fields:["optimisticClientId"],ignoreNulls:[]}},primaryKey:{fields:["statusId"],ignoreNulls:[]}},sticker_pack_details:{autoIncrement:!1,defaults:{},id:127,indexes:{},primaryKey:{fields:["packId"],ignoreNulls:[]}},sticker_packs:{autoIncrement:!1,defaults:{},id:122,indexes:{},primaryKey:{fields:["packId"],ignoreNulls:[]}},sticker_search_featured_tags:{autoIncrement:!1,defaults:{},id:125,indexes:{},primaryKey:{fields:["tagIndex"],ignoreNulls:[]}},sticker_search_queries:{autoIncrement:!1,defaults:{},id:124,indexes:{},primaryKey:{fields:["query"],ignoreNulls:[]}},sticker_search_results:{autoIncrement:!1,defaults:{},id:123,indexes:{},primaryKey:{fields:["resultIndex","query","type_"],ignoreNulls:[]}},sticker_store_pack_indices:{autoIncrement:!1,defaults:{},id:128,indexes:{},primaryKey:{fields:["packId"],ignoreNulls:[]}},sticker_to_collection:{autoIncrement:!1,defaults:{},id:126,indexes:{},primaryKey:{fields:["stickerId","collectionId"],ignoreNulls:[]}},stickers:{autoIncrement:!1,defaults:{},id:121,indexes:{stickerPackId:{fields:["stickerPackId","stickerId"],ignoreNulls:[]}},primaryKey:{fields:["stickerId"],ignoreNulls:[]}},stories:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0])},id:54,indexes:{optimistic:{fields:["optimisticClientId"],ignoreNulls:[]}},primaryKey:{fields:["storyId"],ignoreNulls:[]}},story_ad_card:{autoIncrement:!1,defaults:{},id:58,indexes:{},primaryKey:{fields:["adPosition","clientToken","cardId"],ignoreNulls:[]}},story_ad_ctas:{autoIncrement:!1,defaults:{},id:59,indexes:{},primaryKey:{fields:["ctaId"],ignoreNulls:[]}},story_ad_unit:{autoIncrement:!1,defaults:{shouldShowAdChoice:!1},id:57,indexes:{},primaryKey:{fields:["adPosition","clientToken"],ignoreNulls:[]}},story_buckets:{autoIncrement:!1,defaults:{pageNum:a.cast([0,0]),readState:a.cast([0,0])},id:53,indexes:{optimistic:{fields:["ownerId","bucketType"],ignoreNulls:[]}},primaryKey:{fields:["bucketId"],ignoreNulls:[]}},story_buckets_paginated_queries:{autoIncrement:!1,defaults:{},id:61,indexes:{},primaryKey:{fields:["queryId"],ignoreNulls:[]}},story_overlays:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0])},id:60,indexes:{},primaryKey:{fields:["storyId","storyOverlayId"],ignoreNulls:[]}},story_reactions:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0])},id:56,indexes:{optimistic:{fields:["optimisticClientId"],ignoreNulls:[]}},primaryKey:{fields:["reactionId"],ignoreNulls:[]}},story_viewers:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),interactionType:a.cast([0,1])},id:55,indexes:{},primaryKey:{fields:["storyId","viewerContactId","interactionType"],ignoreNulls:[]}},supervision_edge:{autoIncrement:!1,defaults:{},id:319,indexes:{},primaryKey:{fields:["edgeId"],ignoreNulls:[]}},supervision_metadata:{autoIncrement:!1,defaults:{},id:320,indexes:{},primaryKey:{fields:["supervisionMetadataId"],ignoreNulls:[]}},support_translations:{autoIncrement:!1,defaults:{},id:300,indexes:{},primaryKey:{fields:["messageId","targetLocale"],ignoreNulls:[]}},sync_group_threads_ranges:{autoIncrement:!1,defaults:{hasMoreBefore:!1,isLoadingBefore:!1,minLastActivityTimestampMs:a.cast([0,0]),minThreadKey:a.cast([0,0])},id:220,indexes:{},primaryKey:{fields:["syncGroup","parentThreadKey"],ignoreNulls:[]}},sync_groups:{autoIncrement:!1,defaults:{canIgnoreTimestamp:!1,lastSyncCompletedTimestampMs:a.cast([0,0]),lastSyncRequestTimestampMs:a.cast([0,0]),minTimeToSyncTimestampMs:a.cast([0,0]),priority:a.cast([0,0]),sendSyncParams:!1,syncChannel:a.cast([0,1]),syncStatus:a.cast([0,0])},id:1,indexes:{},primaryKey:{fields:["groupId"],ignoreNulls:[]}},taken_down_threads:{autoIncrement:!1,defaults:{},id:212,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}},third_party_id_store:{autoIncrement:!1,defaults:{},id:242,indexes:{},primaryKey:{fields:["platform"],ignoreNulls:[]}},thread_bans:{autoIncrement:!1,defaults:{},id:297,indexes:{},primaryKey:{fields:["threadKey","contactId"],ignoreNulls:[]}},thread_creation_status:{autoIncrement:!1,defaults:{},id:278,indexes:{},primaryKey:{fields:["taskId"],ignoreNulls:[]}},thread_label_mappings:{autoIncrement:!1,defaults:{sortKey:a.cast([0,0])},id:237,indexes:{},primaryKey:{fields:["threadKey","labelId"],ignoreNulls:[]}},thread_labels:{autoIncrement:!1,defaults:{},id:215,indexes:{},primaryKey:{fields:["labelId"],ignoreNulls:[]}},thread_limits:{autoIncrement:!1,defaults:{},id:287,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}},thread_nullstate:{autoIncrement:!1,defaults:{ctaType:a.cast([0,0]),privacyTextCtaType:a.cast([0,0])},id:28,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}},thread_nullstate_ctas:{autoIncrement:!1,defaults:{},id:29,indexes:{threadKeyCtaId:{fields:["threadKey","ctaId"],ignoreNulls:[]}},primaryKey:{fields:["ctaId"],ignoreNulls:[]}},thread_point_query_ttrc:{autoIncrement:!1,defaults:{},id:286,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}},thread_proactive_warning_settings:{autoIncrement:!1,defaults:{},id:183,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}},thread_seen_heads_queries:{autoIncrement:!1,defaults:{},id:325,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}},thread_themes:{autoIncrement:!1,defaults:{backgroundUrl:"",iconUrl:"",iconUrlFallback:"",isDeprecated:!1},id:116,indexes:{},primaryKey:{fields:["fbid"],ignoreNulls:[]}},threads:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),capabilities:a.cast([0,0]),capabilities2:a.cast([0,0]),capabilities3:a.cast([0,0]),capabilities4:a.cast([0,0]),disableComposerInput:!1,hasPersistentMenu:!1,isAdminSnippet:!1,isCustomThreadPicture:!1,isDisappearingMode:!1,isHidden:!1,isReadReceiptsDisabled:!1,lastActivityTimestampMs:a.cast([0,0]),lastReadWatermarkTimestampMs:a.cast([0,0]),muteCallsExpireTimeMs:a.cast([0,0]),muteExpireTimeMs:a.cast([0,0]),muteMentionExpireTimeMs:a.cast([0,0]),ongoingCallState:a.cast([0,0]),readReceiptsDisabledV2:a.cast([0,0]),removeWatermarkTimestampMs:a.cast([0,0]),snippetHasEmoji:!1,threadInvitesEnabled:a.cast([0,0]),threadInvitesEnabledV2:a.cast([0,0]),unreadDisappearingMessageCount:a.cast([0,0]),unsendLimitMs:a.cast([0,0])},id:9,indexes:{clientThreadKey:{fields:["clientThreadKey","threadKey"],ignoreNulls:["clientThreadKey"]},lastActivityTimestampMs:{fields:["lastActivityTimestampMs","threadKey"],ignoreNulls:[]},parentThreadKeyLastActivityTimestampMs:{fields:["parentThreadKey","lastActivityTimestampMs","threadKey"],ignoreNulls:[]},secondaryParentThreadKeyLastActivityTimestampMs:{fields:["secondaryParentThreadKey","lastActivityTimestampMs","threadKey"],ignoreNulls:["secondaryParentThreadKey"]},syncGroupParentThreadKeyLastActivityTimestampMs:{fields:["syncGroup","parentThreadKey","lastActivityTimestampMs","threadKey"],ignoreNulls:[]},threadTypeLastActivityTimestampMs:{fields:["threadType","lastActivityTimestampMs","threadKey"],ignoreNulls:[]}},primaryKey:{fields:["threadKey"],ignoreNulls:[]}},threads_optimistic_context:{autoIncrement:!1,defaults:{},id:32,indexes:{},primaryKey:{fields:["taskId"],ignoreNulls:[]}},threads_optimistic_metadata:{autoIncrement:!1,defaults:{},id:312,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}},threads_ranges_v2__generated:{autoIncrement:!1,defaults:{hasMoreAfter:!1,hasMoreBefore:!1,isLoadingAfter:!1,isLoadingBefore:!1,maxLastActivityTimestampMs:a.cast([0,0]),maxThreadKey:a.cast([0,0]),minLastActivityTimestampMs:a.cast([0,0]),minThreadKey:a.cast([0,0])},id:10,indexes:{},primaryKey:{fields:["parentThreadKey","minThreadKey","minLastActivityTimestampMs"],ignoreNulls:[]}},transaction_history:{autoIncrement:!1,defaults:{timestampMs:a.cast([0,0])},id:102,indexes:{},primaryKey:{fields:["transactionId"],ignoreNulls:[]}},typing_indicator:{autoIncrement:!1,defaults:{},id:52,indexes:{},primaryKey:{fields:["threadKey","senderId"],ignoreNulls:[]}},universal_search_recent_searches:{autoIncrement:!1,defaults:{lastAccessedTimestampMs:a.cast([0,0])},id:68,indexes:{},primaryKey:{fields:["resultId"],ignoreNulls:[]}},user_visible_errors:{autoIncrement:!0,defaults:{},id:6,indexes:{},primaryKey:{fields:["errorId"],ignoreNulls:[]}},user_visible_network_connectivity_error:{autoIncrement:!0,defaults:{},id:115,indexes:{},primaryKey:{fields:["statusId"],ignoreNulls:[]}},value_model_features:{autoIncrement:!1,defaults:{},id:137,indexes:{},primaryKey:{fields:["feature"],ignoreNulls:[]}},value_model_output:{autoIncrement:!1,defaults:{},id:138,indexes:{},primaryKey:{fields:["product","contactId"],ignoreNulls:[]}},value_model_rules:{autoIncrement:!1,defaults:{},id:136,indexes:{},primaryKey:{fields:["product","ruleIndex"],ignoreNulls:[]}},video_chat_links_attempted_joiners:{autoIncrement:!1,defaults:{},id:79,indexes:{},primaryKey:{fields:["userId","url"],ignoreNulls:[]}},video_chat_links_joining:{autoIncrement:!1,defaults:{canAnonymousUsersJoin:!1},id:78,indexes:{optimistic:{fields:["optimisticClientToken"],ignoreNulls:[]}},primaryKey:{fields:["url","optimisticClientToken"],ignoreNulls:[]}},work_contact_info:{autoIncrement:!1,defaults:{},id:210,indexes:{},primaryKey:{fields:["id"],ignoreNulls:[]}},work_genai:{autoIncrement:!1,defaults:{},id:293,indexes:{},primaryKey:{fields:["threadKey","messageId"],ignoreNulls:[]}},workroom_creation_requests:{autoIncrement:!1,defaults:{},id:252,indexes:{},primaryKey:{fields:["creationId"],ignoreNulls:[]}},workroom_invites:{autoIncrement:!1,defaults:{},id:257,indexes:{},primaryKey:{fields:["inviteId"],ignoreNulls:[]}},workrooms_co_presence_states:{autoIncrement:!1,defaults:{},id:246,indexes:{userCoPresence:{fields:["userId","coPresenceObjectId"],ignoreNulls:[]}},primaryKey:{fields:["userId"],ignoreNulls:[]}}});var f=Object.freeze(babelHelpers["extends"]({},c,{e2ee_composer_draft_link_preview:{autoIncrement:!1,defaults:{},id:329,indexes:{},primaryKey:{fields:["draftId"],ignoreNulls:[]}}})),g=Object.freeze(babelHelpers["extends"]({},f,{occamadillo_most_recent_message_per_thread:{autoIncrement:!1,defaults:{},id:292,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}}})),h=Object.freeze(babelHelpers["extends"]({},g,{occamadillo_most_recent_message_per_thread:{autoIncrement:!1,defaults:{},id:292,indexes:{fetchTimestamp:{fields:["fetchTimestampMs"],ignoreNulls:[]}},primaryKey:{fields:["threadKey"],ignoreNulls:[]}}})),i=Object.freeze(babelHelpers["extends"]({},h,{messages_optimistic_context:{autoIncrement:!1,defaults:{},id:31,indexes:{},primaryKey:{fields:["taskId"],ignoreNulls:[]}}})),j=Object.freeze(babelHelpers["extends"]({},i,{auto_restore_opt_out:{autoIncrement:!1,defaults:{},id:330,indexes:{},primaryKey:{fields:["optOutKey"],ignoreNulls:[]}}})),k=Object.freeze(babelHelpers["extends"]({},j,{messages_optimistic_context:{autoIncrement:!1,defaults:{transportKey:"FBBroker"},id:31,indexes:{},primaryKey:{fields:["taskId"],ignoreNulls:[]}}})),l=Object.freeze(babelHelpers["extends"]({},k,{messenger_fts_threads:{autoIncrement:!1,defaults:{nextMessageTimestamp:a.cast([-1,4294967295]),status:a.cast([0,0])},id:331,indexes:{},primaryKey:{fields:["threadId"],ignoreNulls:[]}},messenger_fts_threads_queries:{autoIncrement:!1,defaults:{sessionId:""},id:332,indexes:{},primaryKey:{fields:["queryId"],ignoreNulls:[]}}})),m=Object.freeze(babelHelpers["extends"]({},l,{messenger_fts_threads:{autoIncrement:!1,defaults:{nextMessageTimestamp:a.cast([-1,4294967295]),status:a.cast([0,0]),threadType:a.cast([0,0])},id:331,indexes:{},primaryKey:{fields:["threadId"],ignoreNulls:[]}}})),n=Object.freeze(babelHelpers["extends"]({},m,{filtered_threads_ranges_v3__generated:{autoIncrement:!1,defaults:{folderName:"inbox",hasMoreAfter:!1,hasMoreBefore:!1,isLoadingAfter:!1,isLoadingBefore:!1,maxLastActivityTimestampMs:a.cast([0,0]),maxThreadKey:a.cast([0,0]),minLastActivityTimestampMs:a.cast([0,0]),minThreadKey:a.cast([0,0]),secondaryThreadRangeFilter:a.cast([0,0]),syncGroup:a.cast([0,1]),threadRangeFilterValue:""},id:247,indexes:{},primaryKey:{fields:["parentThreadKey","minThreadKey","minLastActivityTimestampMs","threadRangeFilter","folderName","secondaryThreadRangeFilter","threadRangeFilterValue"],ignoreNulls:[]}}})),o=Object.freeze(babelHelpers["extends"]({},n,{messages_optimistic_context:{autoIncrement:!1,defaults:{transportKey:"FBBroker"},id:31,indexes:{},primaryKey:{fields:["taskId"],ignoreNulls:[]}}})),p=Object.freeze(babelHelpers["extends"]({},o,{attachments:{autoIncrement:!1,defaults:{attachmentIndex:a.cast([0,0]),attachmentType:a.cast([0,0]),authorityLevel:a.cast([0,0]),hasMedia:!1,hasXma:!1,isSharable:!1,timestampMs:a.cast([0,0]),transportKey:"FBBroker"},id:16,indexes:{fk_messages:{fields:["threadKey","timestampMs","messageId","attachmentFbid"],ignoreNulls:[]},idx_attachments_collapsible_id:{fields:["threadKey","collapsibleId","messageId","attachmentFbid"],ignoreNulls:[]}},primaryKey:{fields:["threadKey","messageId","attachmentFbid"],ignoreNulls:[]}}})),q=Object.freeze(babelHelpers["extends"]({},p,{media_receiver_fetch_transport_mappings:{autoIncrement:!1,defaults:{},id:333,indexes:{},primaryKey:{fields:["receiverFetchId"],ignoreNulls:[]}}})),r=Object.freeze(babelHelpers["extends"]({},q,{threads:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),capabilities:a.cast([0,0]),capabilities2:a.cast([0,0]),capabilities3:a.cast([0,0]),capabilities4:a.cast([0,0]),disableComposerInput:!1,hasPersistentMenu:!1,isAdminSnippet:!1,isCustomThreadPicture:!1,isDisappearingMode:!1,isHidden:!1,isReadReceiptsDisabled:!1,lastActivityTimestampMs:a.cast([0,0]),lastReadWatermarkTimestampMs:a.cast([0,0]),muteCallsExpireTimeMs:a.cast([0,0]),muteExpireTimeMs:a.cast([0,0]),muteMentionExpireTimeMs:a.cast([0,0]),ongoingCallState:a.cast([0,0]),readReceiptsDisabledV2:a.cast([0,0]),removeWatermarkTimestampMs:a.cast([0,0]),snippetHasEmoji:!1,threadInvitesEnabled:a.cast([0,0]),threadInvitesEnabledV2:a.cast([0,0]),unreadDisappearingMessageCount:a.cast([0,0]),unsendLimitMs:a.cast([0,0])},id:9,indexes:{clientThreadKey:{fields:["clientThreadKey","threadKey"],ignoreNulls:["clientThreadKey"]},lastActivityTimestampMs:{fields:["lastActivityTimestampMs","threadKey"],ignoreNulls:[]},parentThreadKeyLastActivityTimestampMs:{fields:["parentThreadKey","lastActivityTimestampMs","threadKey"],ignoreNulls:[]},secondaryParentThreadKeyLastActivityTimestampMs:{fields:["secondaryParentThreadKey","lastActivityTimestampMs","threadKey"],ignoreNulls:["secondaryParentThreadKey"]},syncGroupParentThreadKeyLastActivityTimestampMs:{fields:["syncGroup","parentThreadKey","lastActivityTimestampMs","threadKey"],ignoreNulls:[]},threadTypeLastActivityTimestampMs:{fields:["threadType","lastActivityTimestampMs","threadKey"],ignoreNulls:[]}},primaryKey:{fields:["threadKey"],ignoreNulls:[]}}})),s=Object.freeze(babelHelpers["extends"]({},r,{attachments:{autoIncrement:!1,defaults:{attachmentIndex:a.cast([0,0]),attachmentType:a.cast([0,0]),authorityLevel:a.cast([0,0]),hasMedia:!1,hasXma:!1,isSharable:!1,timestampMs:a.cast([0,0]),transportKey:"FBBroker"},id:16,indexes:{fk_messages:{fields:["threadKey","timestampMs","messageId","attachmentFbid"],ignoreNulls:[]},idx_attachments_collapsible_id:{fields:["threadKey","collapsibleId","messageId","attachmentFbid"],ignoreNulls:[]}},primaryKey:{fields:["threadKey","messageId","attachmentFbid"],ignoreNulls:[]}}})),t=Object.freeze(babelHelpers["extends"]({},s,{attachment_items:{autoIncrement:!1,defaults:{attachmentIndex:a.cast([0,0])},id:18,indexes:{fk_attachments:{fields:["threadKey","messageId","attachmentFbid","attachmentIndex"],ignoreNulls:[]}},primaryKey:{fields:["attachmentFbid","attachmentIndex"],ignoreNulls:[]}},attachments:{autoIncrement:!1,defaults:{attachmentIndex:a.cast([0,0]),attachmentType:a.cast([0,0]),authorityLevel:a.cast([0,0]),hasMedia:!1,hasXma:!1,isSharable:!1,timestampMs:a.cast([0,0]),transportKey:"FBBroker"},id:16,indexes:{fk_messages:{fields:["threadKey","timestampMs","messageId","attachmentFbid"],ignoreNulls:[]},idx_attachments_collapsible_id:{fields:["threadKey","collapsibleId","messageId","attachmentFbid"],ignoreNulls:[]}},primaryKey:{fields:["threadKey","messageId","attachmentFbid"],ignoreNulls:[]}}})),u=Object.freeze(babelHelpers["extends"]({},t,{client_thread_proactive_warning_settings:{autoIncrement:!1,defaults:{},id:241,indexes:{},primaryKey:{fields:["threadPk"],ignoreNulls:[]}}})),v=Object.freeze(babelHelpers["extends"]({},u,{pake_messages:{autoIncrement:!1,defaults:{},id:258,indexes:{},primaryKey:{fields:["sessionId"],ignoreNulls:[]}}})),w=Object.freeze(babelHelpers["extends"]({},v,{threads:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),capabilities:a.cast([0,0]),capabilities2:a.cast([0,0]),capabilities3:a.cast([0,0]),capabilities4:a.cast([0,0]),disableComposerInput:!1,hasPersistentMenu:!1,isAdminSnippet:!1,isCustomThreadPicture:!1,isDisappearingMode:!1,isHidden:!1,isReadReceiptsDisabled:!1,lastActivityTimestampMs:a.cast([0,0]),lastReadWatermarkTimestampMs:a.cast([0,0]),muteCallsExpireTimeMs:a.cast([0,0]),muteExpireTimeMs:a.cast([0,0]),muteMentionExpireTimeMs:a.cast([0,0]),ongoingCallState:a.cast([0,0]),readReceiptsDisabledV2:a.cast([0,0]),removeWatermarkTimestampMs:a.cast([0,0]),snippetHasEmoji:!1,threadInvitesEnabled:a.cast([0,0]),threadInvitesEnabledV2:a.cast([0,0]),unreadDisappearingMessageCount:a.cast([0,0]),unsendLimitMs:a.cast([0,0])},id:9,indexes:{clientThreadKey:{fields:["clientThreadKey","threadKey"],ignoreNulls:["clientThreadKey"]},lastActivityTimestampMs:{fields:["lastActivityTimestampMs","threadKey"],ignoreNulls:[]},parentThreadKeyLastActivityTimestampMs:{fields:["parentThreadKey","lastActivityTimestampMs","threadKey"],ignoreNulls:[]},secondaryParentThreadKeyLastActivityTimestampMs:{fields:["secondaryParentThreadKey","lastActivityTimestampMs","threadKey"],ignoreNulls:["secondaryParentThreadKey"]},syncGroupParentThreadKeyLastActivityTimestampMs:{fields:["syncGroup","parentThreadKey","lastActivityTimestampMs","threadKey"],ignoreNulls:[]},threadTypeLastActivityTimestampMs:{fields:["threadType","lastActivityTimestampMs","threadKey"],ignoreNulls:[]}},primaryKey:{fields:["threadKey"],ignoreNulls:[]}}})),x=Object.freeze(babelHelpers["extends"]({},w,{contacts:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),blockedByViewerStatus:a.cast([0,0]),canViewerMessage:!0,capabilities2:a.cast([0,0]),contactReachabilityStatusType:a.cast([0,0]),contactType:a.cast([0,0]),contactTypeExact:a.cast([0,0]),gender:a.cast([0,0]),isEmployee:!1,isMemorialized:!1,isMessengerUser:!1,optimisticBlockedByViewerStatus:a.cast([0,0]),optimisticBlockedByViewerStatusTimestampMs:a.cast([0,0]),rank:0,restrictionType:a.cast([0,0]),waConnectStatus:a.cast([0,0])},id:7,indexes:{blockedByViewerStatusId:{fields:["blockedByViewerStatus","id"],ignoreNulls:[]}},primaryKey:{fields:["id"],ignoreNulls:[]}}})),y=Object.freeze(babelHelpers["extends"]({},x,{encrypted_backups_otc_devices:{autoIncrement:!1,defaults:{},id:261,indexes:{},primaryKey:{fields:["deviceId"],ignoreNulls:[]}}})),z=Object.freeze(babelHelpers["extends"]({},y,{business_thread_info:{autoIncrement:!1,defaults:{},id:260,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}}})),A=Object.freeze(babelHelpers["extends"]({},z,{messages:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),displayedContentTypes:a.cast([0,1]),hasQuickReplies:!1,isAdminMessage:!1,isCollapsed:!1,isExpired:!1,isUnsent:!1,messageRenderingType:a.cast([0,0]),primarySortKey:a.cast([0,0]),quickReplyType:a.cast([0,0]),replyAttachmentType:a.cast([0,0]),replyStatus:a.cast([0,0]),secondarySortKey:a.cast([0,0]),sendStatus:a.cast([0,0]),sendStatusV2:a.cast([0,0]),textHasLinks:!1,timestampMs:a.cast([0,0]),transportKey:"FBBroker",unsentTimestampMs:a.cast([0,0]),viewFlags:a.cast([0,0])},id:12,indexes:{ephemeralExpirationTs:{fields:["ephemeralExpirationTs","messageId"],ignoreNulls:["ephemeralExpirationTs"]},messageDisplayOrder:{fields:["threadKey","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:[]},messageGroupId:{fields:["groupId","messageId"],ignoreNulls:["groupId"]},messageId:{fields:["messageId"],ignoreNulls:[]},messageSubthreadKey:{fields:["subthreadKey","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:["subthreadKey"]},optimistic:{fields:["offlineThreadingId"],ignoreNulls:[]},replySourceIdMessageID:{fields:["replySourceId","messageId"],ignoreNulls:["replySourceId"]},threadKeyPrimarySortKeySecondarySortKeyBotResponseId:{fields:["threadKey","primarySortKey","secondarySortKey","botResponseId","messageId"],ignoreNulls:["botResponseId"]}},primaryKey:{fields:["threadKey","timestampMs","messageId"],ignoreNulls:[]}}})),B=Object.freeze(babelHelpers["extends"]({},A,{account_synced_fields:{autoIncrement:!1,defaults:{},id:334,indexes:{},primaryKey:{fields:["accountId","syncedField"],ignoreNulls:[]}}})),C=Object.freeze(babelHelpers["extends"]({},B,{attachments:{autoIncrement:!1,defaults:{attachmentIndex:a.cast([0,0]),attachmentType:a.cast([0,0]),authorityLevel:a.cast([0,0]),hasMedia:!1,hasXma:!1,isSharable:!1,timestampMs:a.cast([0,0]),transportKey:"FBBroker"},id:16,indexes:{fk_messages:{fields:["threadKey","timestampMs","messageId","attachmentFbid"],ignoreNulls:[]},idx_attachments_collapsible_id:{fields:["threadKey","collapsibleId","messageId","attachmentFbid"],ignoreNulls:[]}},primaryKey:{fields:["threadKey","messageId","attachmentFbid"],ignoreNulls:[]}}})),D=Object.freeze(babelHelpers["extends"]({},C,{encrypted_backup_user_preferences:{autoIncrement:!0,defaults:{},id:335,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}}})),E=Object.freeze(babelHelpers["extends"]({},D,{support_translation_feedback:{autoIncrement:!1,defaults:{},id:336,indexes:{},primaryKey:{fields:["messageId","targetLocale"],ignoreNulls:[]}}})),F=Object.freeze(babelHelpers["extends"]({},E,{support_translations:{autoIncrement:!1,defaults:{},id:300,indexes:{},primaryKey:{fields:["messageId","targetLocale"],ignoreNulls:[]}}})),G=Object.freeze(babelHelpers["extends"]({},F,{ig_thread_info:{autoIncrement:!1,defaults:{igDmSettingsMode:a.cast([0,0]),igDmSettingsTtlSec:a.cast([-1,4294967295])},id:194,indexes:{igThreadID:{fields:["igThreadId"],ignoreNulls:[]}},primaryKey:{fields:["threadKey"],ignoreNulls:[]}}})),H=Object.freeze(babelHelpers["extends"]({},G,{e2ee_composer_draft_link_preview:{autoIncrement:!1,defaults:{},id:329,indexes:{},primaryKey:{fields:["draftId"],ignoreNulls:[]}}})),I=Object.freeze(babelHelpers["extends"]({},H,{attachments:{autoIncrement:!1,defaults:{attachmentIndex:a.cast([0,0]),attachmentType:a.cast([0,0]),authorityLevel:a.cast([0,0]),hasMedia:!1,hasXma:!1,isSharable:!1,timestampMs:a.cast([0,0]),transportKey:"FBBroker"},id:16,indexes:{fk_messages:{fields:["threadKey","timestampMs","messageId","attachmentFbid"],ignoreNulls:[]},idx_attachments_collapsible_id:{fields:["threadKey","collapsibleId","messageId","attachmentFbid"],ignoreNulls:[]}},primaryKey:{fields:["threadKey","messageId","attachmentFbid"],ignoreNulls:[]}}})),J=Object.freeze(babelHelpers["extends"]({},I,{threads:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),capabilities:a.cast([0,0]),capabilities2:a.cast([0,0]),capabilities3:a.cast([0,0]),capabilities4:a.cast([0,0]),disableComposerInput:!1,hasPersistentMenu:!1,isAdminSnippet:!1,isCustomThreadPicture:!1,isDisappearingMode:!1,isHidden:!1,isReadReceiptsDisabled:!1,lastActivityTimestampMs:a.cast([0,0]),lastReadWatermarkTimestampMs:a.cast([0,0]),muteCallsExpireTimeMs:a.cast([0,0]),muteExpireTimeMs:a.cast([0,0]),muteMentionExpireTimeMs:a.cast([0,0]),ongoingCallState:a.cast([0,0]),readReceiptsDisabledV2:a.cast([0,0]),removeWatermarkTimestampMs:a.cast([0,0]),snippetHasEmoji:!1,threadInvitesEnabled:a.cast([0,0]),threadInvitesEnabledV2:a.cast([0,0]),typingIndicatorDisabled:a.cast([0,0]),unreadDisappearingMessageCount:a.cast([0,0]),unsendLimitMs:a.cast([0,0])},id:9,indexes:{clientThreadKey:{fields:["clientThreadKey","threadKey"],ignoreNulls:["clientThreadKey"]},lastActivityTimestampMs:{fields:["lastActivityTimestampMs","threadKey"],ignoreNulls:[]},parentThreadKeyLastActivityTimestampMs:{fields:["parentThreadKey","lastActivityTimestampMs","threadKey"],ignoreNulls:[]},secondaryParentThreadKeyLastActivityTimestampMs:{fields:["secondaryParentThreadKey","lastActivityTimestampMs","threadKey"],ignoreNulls:["secondaryParentThreadKey"]},syncGroupParentThreadKeyLastActivityTimestampMs:{fields:["syncGroup","parentThreadKey","lastActivityTimestampMs","threadKey"],ignoreNulls:[]},threadTypeLastActivityTimestampMs:{fields:["threadType","lastActivityTimestampMs","threadKey"],ignoreNulls:[]}},primaryKey:{fields:["threadKey"],ignoreNulls:[]}}})),K=Object.freeze(babelHelpers["extends"]({},J,{messages:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),displayedContentTypes:a.cast([0,1]),hasQuickReplies:!1,isAdminMessage:!1,isCollapsed:!1,isExpired:!1,isUnsent:!1,messageRenderingType:a.cast([0,0]),primarySortKey:a.cast([0,0]),quickReplyType:a.cast([0,0]),replyAttachmentType:a.cast([0,0]),replyStatus:a.cast([0,0]),secondarySortKey:a.cast([0,0]),sendStatus:a.cast([0,0]),sendStatusV2:a.cast([0,0]),textHasLinks:!1,timestampMs:a.cast([0,0]),transportKey:"FBBroker",unsentTimestampMs:a.cast([0,0]),viewFlags:a.cast([0,0])},id:12,indexes:{ephemeralExpirationTs:{fields:["ephemeralExpirationTs","messageId"],ignoreNulls:["ephemeralExpirationTs"]},messageDisplayOrder:{fields:["threadKey","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:[]},messageDisplayOrderAuthority:{fields:["threadKey","authorityLevel","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:[]},messageGroupId:{fields:["groupId","messageId"],ignoreNulls:["groupId"]},messageId:{fields:["messageId"],ignoreNulls:[]},messageSubthreadKey:{fields:["subthreadKey","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:["subthreadKey"]},optimistic:{fields:["offlineThreadingId"],ignoreNulls:[]},replySourceIdMessageID:{fields:["replySourceId","messageId"],ignoreNulls:["replySourceId"]},threadKeyPrimarySortKeySecondarySortKeyBotResponseId:{fields:["threadKey","primarySortKey","secondarySortKey","botResponseId","messageId"],ignoreNulls:["botResponseId"]}},primaryKey:{fields:["threadKey","timestampMs","messageId"],ignoreNulls:[]}}})),L=Object.freeze(babelHelpers["extends"]({},K,{admin_message_ctas:{autoIncrement:!1,defaults:{showAdChoiceIcon:!1,timestampMs:a.cast([0,0])},id:27,indexes:{ctaId:{fields:["ctaId"],ignoreNulls:[]}},primaryKey:{fields:["threadKey","messageId","ctaId"],ignoreNulls:[]}}})),M=Object.freeze(babelHelpers["extends"]({},L,{messages:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),displayedContentTypes:a.cast([0,1]),hasQuickReplies:!1,isAdminMessage:!1,isCollapsed:!1,isExpired:!1,isUnsent:!1,isVideoQuickSend:!1,messageRenderingType:a.cast([0,0]),primarySortKey:a.cast([0,0]),quickReplyType:a.cast([0,0]),replyAttachmentType:a.cast([0,0]),replyStatus:a.cast([0,0]),secondarySortKey:a.cast([0,0]),sendStatus:a.cast([0,0]),sendStatusV2:a.cast([0,0]),textHasLinks:!1,timestampMs:a.cast([0,0]),transportKey:"FBBroker",unsentTimestampMs:a.cast([0,0]),viewFlags:a.cast([0,0])},id:12,indexes:{ephemeralExpirationTs:{fields:["ephemeralExpirationTs","messageId"],ignoreNulls:["ephemeralExpirationTs"]},messageDisplayOrder:{fields:["threadKey","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:[]},messageDisplayOrderAuthority:{fields:["threadKey","authorityLevel","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:[]},messageGroupId:{fields:["groupId","messageId"],ignoreNulls:["groupId"]},messageId:{fields:["messageId"],ignoreNulls:[]},messageSubthreadKey:{fields:["subthreadKey","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:["subthreadKey"]},optimistic:{fields:["offlineThreadingId"],ignoreNulls:[]},replySourceIdMessageID:{fields:["replySourceId","messageId"],ignoreNulls:["replySourceId"]},threadKeyPrimarySortKeySecondarySortKeyBotResponseId:{fields:["threadKey","primarySortKey","secondarySortKey","botResponseId","messageId"],ignoreNulls:["botResponseId"]}},primaryKey:{fields:["threadKey","timestampMs","messageId"],ignoreNulls:[]}}})),N=Object.freeze(babelHelpers["extends"]({},M,{messages:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),displayedContentTypes:a.cast([0,1]),hasQuickReplies:!1,isAdminMessage:!1,isCollapsed:!1,isExpired:!1,isUnsent:!1,messageRenderingType:a.cast([0,0]),primarySortKey:a.cast([0,0]),quickReplyType:a.cast([0,0]),replyAttachmentType:a.cast([0,0]),replyStatus:a.cast([0,0]),secondarySortKey:a.cast([0,0]),sendStatus:a.cast([0,0]),sendStatusV2:a.cast([0,0]),textHasLinks:!1,timestampMs:a.cast([0,0]),transportKey:"FBBroker",unsentTimestampMs:a.cast([0,0]),viewFlags:a.cast([0,0])},id:12,indexes:{ephemeralExpirationTs:{fields:["ephemeralExpirationTs","messageId"],ignoreNulls:["ephemeralExpirationTs"]},messageDisplayOrder:{fields:["threadKey","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:[]},messageDisplayOrderAuthority:{fields:["threadKey","authorityLevel","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:[]},messageGroupId:{fields:["groupId","messageId"],ignoreNulls:["groupId"]},messageId:{fields:["messageId"],ignoreNulls:[]},messageSubthreadKey:{fields:["subthreadKey","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:["subthreadKey"]},optimistic:{fields:["offlineThreadingId"],ignoreNulls:[]},replySourceIdMessageID:{fields:["replySourceId","messageId"],ignoreNulls:["replySourceId"]},threadKeyPrimarySortKeySecondarySortKeyBotResponseId:{fields:["threadKey","primarySortKey","secondarySortKey","botResponseId","messageId"],ignoreNulls:["botResponseId"]}},primaryKey:{fields:["threadKey","timestampMs","messageId"],ignoreNulls:[]}}})),O=Object.freeze(babelHelpers["extends"]({},N,{threads:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),capabilities:a.cast([0,0]),capabilities2:a.cast([0,0]),capabilities3:a.cast([0,0]),capabilities4:a.cast([0,0]),capabilities5:a.cast([0,0]),disableComposerInput:!1,hasPersistentMenu:!1,isAdminSnippet:!1,isCustomThreadPicture:!1,isDisappearingMode:!1,isHidden:!1,isReadReceiptsDisabled:!1,lastActivityTimestampMs:a.cast([0,0]),lastReadWatermarkTimestampMs:a.cast([0,0]),muteCallsExpireTimeMs:a.cast([0,0]),muteExpireTimeMs:a.cast([0,0]),muteMentionExpireTimeMs:a.cast([0,0]),ongoingCallState:a.cast([0,0]),readReceiptsDisabledV2:a.cast([0,0]),removeWatermarkTimestampMs:a.cast([0,0]),snippetHasEmoji:!1,threadInvitesEnabled:a.cast([0,0]),threadInvitesEnabledV2:a.cast([0,0]),typingIndicatorDisabled:a.cast([0,0]),unreadDisappearingMessageCount:a.cast([0,0]),unsendLimitMs:a.cast([0,0])},id:9,indexes:{clientThreadKey:{fields:["clientThreadKey","threadKey"],ignoreNulls:["clientThreadKey"]},lastActivityTimestampMs:{fields:["lastActivityTimestampMs","threadKey"],ignoreNulls:[]},parentThreadKeyLastActivityTimestampMs:{fields:["parentThreadKey","lastActivityTimestampMs","threadKey"],ignoreNulls:[]},secondaryParentThreadKeyLastActivityTimestampMs:{fields:["secondaryParentThreadKey","lastActivityTimestampMs","threadKey"],ignoreNulls:["secondaryParentThreadKey"]},syncGroupParentThreadKeyLastActivityTimestampMs:{fields:["syncGroup","parentThreadKey","lastActivityTimestampMs","threadKey"],ignoreNulls:[]},threadTypeLastActivityTimestampMs:{fields:["threadType","lastActivityTimestampMs","threadKey"],ignoreNulls:[]}},primaryKey:{fields:["threadKey"],ignoreNulls:[]}}})),P=Object.freeze(babelHelpers["extends"]({},O,{attachments:{autoIncrement:!1,defaults:{attachmentIndex:a.cast([0,0]),attachmentType:a.cast([0,0]),authorityLevel:a.cast([0,0]),hasMedia:!1,hasXma:!1,isSharable:!1,timestampMs:a.cast([0,0]),transportKey:"FBBroker"},id:16,indexes:{fk_messages:{fields:["threadKey","timestampMs","messageId","attachmentFbid"],ignoreNulls:[]},idx_attachments_collapsible_id:{fields:["threadKey","collapsibleId","messageId","attachmentFbid"],ignoreNulls:[]}},primaryKey:{fields:["threadKey","messageId","attachmentFbid"],ignoreNulls:[]}}})),Q=Object.freeze(babelHelpers["extends"]({},P,{messaging_privacy_settings:{autoIncrement:!1,defaults:{e2eeHdMediaEnabled:!1,e2eeXmaPreviewsDisabled:!1,readReceiptsDisabled:a.cast([0,0])},id:288,indexes:{},primaryKey:{fields:["userId"],ignoreNulls:[]}}})),R=Object.freeze(babelHelpers["extends"]({},Q,{media_staging:{autoIncrement:!1,defaults:{progress:0},id:161,indexes:{},primaryKey:{fields:["offlineAttachmentId"],ignoreNulls:[]}}})),S=Object.freeze(babelHelpers["extends"]({},R,{messages:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),displayedContentTypes:a.cast([0,1]),hasQuickReplies:!1,isAdminMessage:!1,isCollapsed:!1,isExpired:!1,isUnsent:!1,messageRenderingType:a.cast([0,0]),primarySortKey:a.cast([0,0]),quickReplyType:a.cast([0,0]),replyAttachmentType:a.cast([0,0]),replyStatus:a.cast([0,0]),secondarySortKey:a.cast([0,0]),sendStatus:a.cast([0,0]),sendStatusV2:a.cast([0,0]),textHasLinks:!1,timestampMs:a.cast([0,0]),transportKey:"FBBroker",unsentTimestampMs:a.cast([0,0]),viewFlags:a.cast([0,0])},id:12,indexes:{ephemeralExpirationTs:{fields:["ephemeralExpirationTs","messageId"],ignoreNulls:["ephemeralExpirationTs"]},messageDisplayOrder:{fields:["threadKey","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:[]},messageDisplayOrderAuthority:{fields:["threadKey","authorityLevel","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:[]},messageDisplayOrderOfflineThreadingId:{fields:["threadKey","primarySortKey","offlineThreadingId","messageId","isCollapsed"],ignoreNulls:[]},messageGroupId:{fields:["groupId","messageId"],ignoreNulls:["groupId"]},messageId:{fields:["messageId"],ignoreNulls:[]},messageSubthreadKey:{fields:["subthreadKey","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:["subthreadKey"]},optimistic:{fields:["offlineThreadingId"],ignoreNulls:[]},replySourceIdMessageID:{fields:["replySourceId","messageId"],ignoreNulls:["replySourceId"]},threadKeyPrimarySortKeySecondarySortKeyBotResponseId:{fields:["threadKey","primarySortKey","secondarySortKey","botResponseId","messageId"],ignoreNulls:["botResponseId"]}},primaryKey:{fields:["threadKey","timestampMs","messageId"],ignoreNulls:[]}}})),T=Object.freeze(babelHelpers["extends"]({},S,{messages:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),displayedContentTypes:a.cast([0,1]),hasQuickReplies:!1,isAdminMessage:!1,isCollapsed:!1,isExpired:!1,isUnsent:!1,messageRenderingType:a.cast([0,0]),primarySortKey:a.cast([0,0]),quickReplyType:a.cast([0,0]),replyAttachmentType:a.cast([0,0]),replyStatus:a.cast([0,0]),secondarySortKey:a.cast([0,0]),sendStatus:a.cast([0,0]),sendStatusV2:a.cast([0,0]),textHasLinks:!1,timestampMs:a.cast([0,0]),transportKey:"FBBroker",unsentTimestampMs:a.cast([0,0]),viewFlags:a.cast([0,0])},id:12,indexes:{ephemeralExpirationTs:{fields:["ephemeralExpirationTs","messageId"],ignoreNulls:["ephemeralExpirationTs"]},messageDisplayOrder:{fields:["threadKey","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:[]},messageDisplayOrderAuthority:{fields:["threadKey","authorityLevel","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:[]},messageDisplayOrderOfflineThreadingId:{fields:["threadKey","primarySortKey","offlineThreadingId","messageId","isCollapsed"],ignoreNulls:[]},messageGroupId:{fields:["groupId","messageId"],ignoreNulls:["groupId"]},messageId:{fields:["messageId"],ignoreNulls:[]},messageSubthreadKey:{fields:["subthreadKey","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:["subthreadKey"]},optimistic:{fields:["offlineThreadingId"],ignoreNulls:[]},replySourceIdMessageID:{fields:["replySourceId","messageId"],ignoreNulls:["replySourceId"]},threadKeyPrimarySortKeySecondarySortKeyBotResponseId:{fields:["threadKey","primarySortKey","secondarySortKey","botResponseId","messageId"],ignoreNulls:["botResponseId"]}},primaryKey:{fields:["threadKey","timestampMs","messageId"],ignoreNulls:[]}}})),U=Object.freeze(babelHelpers["extends"]({},T,{community_folders:{autoIncrement:!1,defaults:{capabilities:a.cast([0,0]),capabilities2:a.cast([0,0]),capabilities3:a.cast([0,0]),inviteStatus:a.cast([0,0]),notificationMutedUntil:a.cast([0,0])},id:162,indexes:{byFbGroupId:{fields:["fbGroupId","folderId"],ignoreNulls:[]}},primaryKey:{fields:["folderId"],ignoreNulls:[]}}})),V=Object.freeze(babelHelpers["extends"]({},U,{messages:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),displayedContentTypes:a.cast([0,1]),hasQuickReplies:!1,isAdminMessage:!1,isCollapsed:!1,isExpired:!1,isUnsent:!1,messageRenderingType:a.cast([0,0]),primarySortKey:a.cast([0,0]),quickReplyType:a.cast([0,0]),replyAttachmentType:a.cast([0,0]),replyStatus:a.cast([0,0]),secondarySortKey:a.cast([0,0]),sendStatus:a.cast([0,0]),sendStatusV2:a.cast([0,0]),textHasLinks:!1,timestampMs:a.cast([0,0]),transportKey:"FBBroker",unsentTimestampMs:a.cast([0,0]),viewFlags:a.cast([0,0])},id:12,indexes:{ephemeralExpirationTs:{fields:["ephemeralExpirationTs","messageId"],ignoreNulls:["ephemeralExpirationTs"]},messageDisplayOrder:{fields:["threadKey","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:[]},messageDisplayOrderAuthority:{fields:["threadKey","authorityLevel","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:[]},messageDisplayOrderOfflineThreadingId:{fields:["threadKey","primarySortKey","offlineThreadingId","messageId","isCollapsed"],ignoreNulls:[]},messageGroupId:{fields:["groupId","messageId"],ignoreNulls:["groupId"]},messageId:{fields:["messageId"],ignoreNulls:[]},messageSubthreadKey:{fields:["subthreadKey","primarySortKey","secondarySortKey","messageId","isCollapsed"],ignoreNulls:["subthreadKey"]},optimistic:{fields:["offlineThreadingId"],ignoreNulls:[]},replySourceIdMessageID:{fields:["replySourceId","messageId"],ignoreNulls:["replySourceId"]},threadKeyPrimarySortKeySecondarySortKeyBotResponseId:{fields:["threadKey","primarySortKey","secondarySortKey","botResponseId","messageId"],ignoreNulls:["botResponseId"]},timestampMs:{fields:["timestampMs","messageId"],ignoreNulls:[]}},primaryKey:{fields:["threadKey","timestampMs","messageId"],ignoreNulls:[]}}})),W=Object.freeze(babelHelpers["extends"]({},V,{ctx_ad_context:{autoIncrement:!1,defaults:{},id:337,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}}})),X=Object.freeze(babelHelpers["extends"]({},W,{server_search_results:{autoIncrement:!1,defaults:{},id:65,indexes:{},primaryKey:{fields:["query","resultId","globalIndex"],ignoreNulls:[]}}})),Y=Object.freeze(babelHelpers["extends"]({},X,{horizon_groups_key_mapping:{autoIncrement:!1,defaults:{},id:338,indexes:{},primaryKey:{fields:["threadKey","groupKey"],ignoreNulls:[]}}})),Z=Object.freeze(babelHelpers["extends"]({},Y,{ctx_ad_context:{autoIncrement:!1,defaults:{},id:337,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}}})),$=Object.freeze(babelHelpers["extends"]({},Z,{rtc_ongoing_calls_on_threads_v2:{autoIncrement:!1,defaults:{},id:23,indexes:{},primaryKey:{fields:["threadKey"],ignoreNulls:[]}},threads:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),capabilities:a.cast([0,0]),capabilities2:a.cast([0,0]),capabilities3:a.cast([0,0]),capabilities4:a.cast([0,0]),capabilities5:a.cast([0,0]),disableComposerInput:!1,hasPersistentMenu:!1,isAdminSnippet:!1,isCustomThreadPicture:!1,isDisappearingMode:!1,isHidden:!1,isReadReceiptsDisabled:!1,lastActivityTimestampMs:a.cast([0,0]),lastReadWatermarkTimestampMs:a.cast([0,0]),muteCallsExpireTimeMs:a.cast([0,0]),muteExpireTimeMs:a.cast([0,0]),muteMentionExpireTimeMs:a.cast([0,0]),ongoingCallState:a.cast([0,0]),readReceiptsDisabledV2:a.cast([0,0]),removeWatermarkTimestampMs:a.cast([0,0]),snippetHasEmoji:!1,threadInvitesEnabled:a.cast([0,0]),threadInvitesEnabledV2:a.cast([0,0]),typingIndicatorDisabled:a.cast([0,0]),unreadDisappearingMessageCount:a.cast([0,0]),unsendLimitMs:a.cast([0,0])},id:9,indexes:{clientThreadKey:{fields:["clientThreadKey","threadKey"],ignoreNulls:["clientThreadKey"]},lastActivityTimestampMs:{fields:["lastActivityTimestampMs","threadKey"],ignoreNulls:[]},parentThreadKeyLastActivityTimestampMs:{fields:["parentThreadKey","lastActivityTimestampMs","threadKey"],ignoreNulls:[]},secondaryParentThreadKeyLastActivityTimestampMs:{fields:["secondaryParentThreadKey","lastActivityTimestampMs","threadKey"],ignoreNulls:["secondaryParentThreadKey"]},syncGroupParentThreadKeyLastActivityTimestampMs:{fields:["syncGroup","parentThreadKey","lastActivityTimestampMs","threadKey"],ignoreNulls:[]},threadTypeLastActivityTimestampMs:{fields:["threadType","lastActivityTimestampMs","threadKey"],ignoreNulls:[]}},primaryKey:{fields:["threadKey"],ignoreNulls:[]}}})),ca=Object.freeze(babelHelpers["extends"]({},$,{horizon_party_members:{autoIncrement:!1,defaults:{},id:339,indexes:{},primaryKey:{fields:["partyKey","userId"],ignoreNulls:[]}}})),da=Object.freeze(babelHelpers["extends"]({},ca,{thread_theme_search_queries:{autoIncrement:!1,defaults:{},id:340,indexes:{},primaryKey:{fields:["query"],ignoreNulls:[]}},thread_theme_search_results:{autoIncrement:!1,defaults:{},id:341,indexes:{},primaryKey:{fields:["resultIndex","query"],ignoreNulls:[]}}})),ea=Object.freeze(babelHelpers["extends"]({},da,{client_media_status:{autoIncrement:!1,defaults:{},id:342,indexes:{},primaryKey:{fields:["id"],ignoreNulls:[]}}})),fa=Object.freeze(babelHelpers["extends"]({},ea,{secure_encrypted_backups_epochs:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0])},id:169,indexes:{fk_secure_encrypted_backups_client_state:{fields:["backupId","epochId"],ignoreNulls:[]}},primaryKey:{fields:["epochId"],ignoreNulls:[]}}})),ga=Object.freeze(babelHelpers["extends"]({},fa,{contacts:{autoIncrement:!1,defaults:{authorityLevel:a.cast([0,0]),blockedByViewerStatus:a.cast([0,0]),canViewerMessage:!0,capabilities2:a.cast([0,0]),contactReachabilityStatusType:a.cast([0,0]),contactType:a.cast([0,0]),contactTypeExact:a.cast([0,0]),friendshipStatus:a.cast([0,5]),gender:a.cast([0,0]),isEmployee:!1,isMemorialized:!1,isMessengerUser:!1,optimisticBlockedByViewerStatus:a.cast([0,0]),optimisticBlockedByViewerStatusTimestampMs:a.cast([0,0]),rank:0,restrictionType:a.cast([0,0]),waConnectStatus:a.cast([0,0])},id:7,indexes:{blockedByViewerStatusId:{fields:["blockedByViewerStatus","id"],ignoreNulls:[]}},primaryKey:{fields:["id"],ignoreNulls:[]}}})),ha=Object.freeze(babelHelpers["extends"]({},ga,{attachments:{autoIncrement:!1,defaults:{attachmentIndex:a.cast([0,0]),attachmentType:a.cast([0,0]),authorityLevel:a.cast([0,0]),hasMedia:!1,hasXma:!1,isSharable:!1,timestampMs:a.cast([0,0]),transportKey:"FBBroker"},id:16,indexes:{fk_messages:{fields:["threadKey","timestampMs","messageId","attachmentFbid"],ignoreNulls:[]},idx_attachments_collapsible_id:{fields:["threadKey","collapsibleId","messageId","attachmentFbid"],ignoreNulls:[]}},primaryKey:{fields:["threadKey","messageId","attachmentFbid"],ignoreNulls:[]}}})),ia=Object.freeze(babelHelpers["extends"]({},ha,{encrypted_backups_reenrollment_trigger:{autoIncrement:!0,defaults:{},id:343,indexes:{},primaryKey:{fields:["pk"],ignoreNulls:[]}}})),ja=Object.freeze(babelHelpers["extends"]({},ia,{attachments:{autoIncrement:!1,defaults:{attachmentIndex:a.cast([0,0]),attachmentType:a.cast([0,0]),authorityLevel:a.cast([0,0]),hasMedia:!1,hasXma:!1,isSharable:!1,timestampMs:a.cast([0,0]),transportKey:"FBBroker"},id:16,indexes:{fk_messages:{fields:["threadKey","timestampMs","messageId","attachmentFbid"],ignoreNulls:[]},idx_attachments_collapsible_id:{fields:["threadKey","collapsibleId","messageId","attachmentFbid"],ignoreNulls:[]}},primaryKey:{fields:["threadKey","messageId","attachmentFbid"],ignoreNulls:[]}}}));e=Object.freeze({afterUpgrade:d("LSDbV1.upgrade").afterUpgrade,revision:56,tables:ja,upgrade:{1:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(f),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),2:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(g),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),3:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(h),c=b.defaults;b=b.tableData;yield d("ReStoreIndicesMigration").runMigrationForIndicesIfNeeded(a,b,c,!1)});function c(b){return a.apply(this,arguments)}return c}(),4:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(i),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),5:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(j),c=b.defaults;b=b.tableData;yield d("ReStoreIndicesMigration").runMigrationForIndicesIfNeeded(a,b,c,!1);yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),6:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(k),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),7:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(l),c=b.defaults;b=b.tableData;yield d("ReStoreIndicesMigration").runMigrationForIndicesIfNeeded(a,b,c,!1);yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),8:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(m),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),9:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(n),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),10:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(o),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),11:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(p),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),12:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(q),c=b.defaults;b=b.tableData;yield d("ReStoreIndicesMigration").runMigrationForIndicesIfNeeded(a,b,c,!1);yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),13:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(r),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),14:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(s),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),15:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(t),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),16:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(u),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),17:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(v),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),18:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(w),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),19:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(x),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),20:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(y),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),21:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(z),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),22:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(A),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),23:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(B),c=b.defaults;b=b.tableData;yield d("ReStoreIndicesMigration").runMigrationForIndicesIfNeeded(a,b,c,!1);yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),24:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(C),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),25:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(D),c=b.defaults;b=b.tableData;yield d("ReStoreIndicesMigration").runMigrationForIndicesIfNeeded(a,b,c,!1);yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),26:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(E),c=b.defaults;b=b.tableData;yield d("ReStoreIndicesMigration").runMigrationForIndicesIfNeeded(a,b,c,!1);yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),27:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(F),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),28:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(G),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),29:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(H),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),30:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(I),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),31:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(J),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),32:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(K),c=b.defaults;b=b.tableData;yield d("ReStoreIndicesMigration").runMigrationForIndicesIfNeeded(a,b,c,!1)});function c(b){return a.apply(this,arguments)}return c}(),33:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(L),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),34:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(M),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),35:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(N),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),36:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(O),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),37:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(P),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),38:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(Q),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),39:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(R),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),40:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(S),c=b.defaults;b=b.tableData;yield d("ReStoreIndicesMigration").runMigrationForIndicesIfNeeded(a,b,c,!1)});function c(b){return a.apply(this,arguments)}return c}(),41:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(T),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),42:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(U),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),43:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(V),c=b.defaults;b=b.tableData;yield d("ReStoreIndicesMigration").runMigrationForIndicesIfNeeded(a,b,c,!1)});function c(b){return a.apply(this,arguments)}return c}(),44:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(W),c=b.defaults;b=b.tableData;yield d("ReStoreIndicesMigration").runMigrationForIndicesIfNeeded(a,b,c,!1);yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),45:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(X),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),46:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(Y),c=b.defaults;b=b.tableData;yield d("ReStoreIndicesMigration").runMigrationForIndicesIfNeeded(a,b,c,!1);yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),47:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(Z),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),48:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData($),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),49:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(ca),c=b.defaults;b=b.tableData;yield d("ReStoreIndicesMigration").runMigrationForIndicesIfNeeded(a,b,c,!1);yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),50:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(da),c=b.defaults;b=b.tableData;yield d("ReStoreIndicesMigration").runMigrationForIndicesIfNeeded(a,b,c,!1);yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),51:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(ea),c=b.defaults;b=b.tableData;yield d("ReStoreIndicesMigration").runMigrationForIndicesIfNeeded(a,b,c,!1);yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),52:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(fa),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),53:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(ga),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),54:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(ha),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),55:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(ia),c=b.defaults;b=b.tableData;yield d("ReStoreIndicesMigration").runMigrationForIndicesIfNeeded(a,b,c,!1);yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}(),56:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("ReStoreVersionedSchemaProviderUtil").getTableData(ja),c=b.defaults;b=b.tableData;yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(a.transaction,b,!1,c)});function c(b){return a.apply(this,arguments)}return c}()}});ba.LSDbV1=e}),98);
__d("ReStoreDbVersion",["FBLogger"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return a}function b(a){return a}function d(a,b){if(b in a)return a[b];throw c("FBLogger")("restore_db_migrations").mustfixThrow("Cannot find version: %s",b)}g.cast=a;g.uncast=b;g.castVersion=d}),98);
__d("LSDb",["LSDbV1","ReStoreDbVersion","justknobx"],(function(a,b,c,d,e,f,g){"use strict";a=Object.freeze({V1:d("ReStoreDbVersion").cast("V1")});e={targetVersion:d("ReStoreDbVersion").castVersion(a,"V"+((b=c("justknobx")._("2392"))!=null?b:0)),versions:{V1:d("LSDbV1").LSDbV1},legacyVersions:{},versionOrder:["V1"]};g.LSDbVersion=a;g.Versions=e}),98);
__d("LSDbForeignKeys",[],(function(a,b,c,d,e,f){"use strict";a={attachments:[{indexName:"fk_attachments",tableName:"attachment_items"},{indexName:"fk_attachments",tableName:"attachment_ctas"}],community_chat_poll_options:[{tableName:"community_chat_poll_votes"}],community_chat_polls:[{tableName:"community_chat_poll_options"},{tableName:"community_chat_poll_votes"}],data_trace_meta:[{indexName:"traceIdAddonId",tableName:"data_trace_addon"}],messages:[{indexName:"fk_messages",tableName:"attachments"},{indexName:"fk_messages",tableName:"reactions"}],pending_tasks:[{tableName:"encrypted_backup_restore_task_payload_context"},{indexName:"fk_pending_tasks",tableName:"pending_backups_context_v2"},{indexName:"fk_pending_tasks",tableName:"secure_recovery_code_data"},{indexName:"fk_pending_tasks",tableName:"secure_encrypted_backups_message_thread_id_context"},{tableName:"messages_optimistic_context"}],rooms:[{tableName:"room_participants"}],secure_encrypted_backups_client_state:[{indexName:"fk_secure_encrypted_backups_client_state",tableName:"secure_encrypted_backups_epochs"}],secure_encrypted_backups_devices:[{indexName:"fk_secure_encrypted_backups_device_supported_versions_device_id",tableName:"secure_encrypted_backups_device_supported_versions"}],threads:[{indexName:"threadKeyCtaId",tableName:"thread_nullstate_ctas"},{tableName:"messages"},{tableName:"attachments"},{tableName:"thread_nullstate"},{tableName:"messages_ranges_v2__generated"},{tableName:"attachments_ranges_v2__generated"},{tableName:"participants"},{indexName:"fk_threads",tableName:"mi_act_mapping_table"},{tableName:"group_invites"}]};b=a;f["default"]=b}),66);
__d("MAWDbVersionList",[],(function(a,b,c,d,e,f){"use strict";function a(a){return a}b={V21:21,V22:22,V23:23,V24:24,V25:25,V26:26,V27:27,V28:28,V29:29,V30:30,V31:31,V32:32,V33:33,V34:34,V35:35,V36:36,V37:37,V38:38,V39:39,V40:40,V41:41,V42:42,V43:43,V44:44,V45:45,V46:46,V47:47,V48:48,V49:49,V50:50,V51:51,V52:52,V53:53,V54:54,V55:55,V56:56,V57:57,V58:58,V59:59,V60:60,V61:61,V62:62,V63:63,V64:64,V65:65,V66:66,V67:67,V68:68,V69:69,V70:70,V71:71,V72:72,V73:73,V74:74,V75:75,V76:76,V77:77,V78:78,V79:79,V80:80,V81:81,V82:82,V83:83,V84:84,V85:85,V86:86,V87:87,V88:88,V89:89,V90:90,V91:91,V92:92,V93:93,V94:94,V95:95,V96:96,V97:97,V98:98,V99:99,V100:100,V101:101,V102:102,V103:103,V104:104,V105:105,V106:106,V107:107,V108:108,V109:109,V110:110,V111:111,V112:112,V113:113,V114:114,V115:115,V116:116,V117:117,V118:118,V119:119,V120:120,V121:121,V122:122,V123:123,V124:124,V125:125,V126:126};f.toVersion=a;f.VERSION=b}),66);
__d("MAWDbSchema",["MAWDbVersionList"],(function(a,b,c,d,e,f,g){"use strict";a=[{autoIncrement:!0,indexes:{externalId:{columns:["externalId"]}},name:"e2ee_appData",primaryKey:["appDataId"]},{autoIncrement:!1,indexes:{},name:"e2ee_appMeta",primaryKey:["key"]},{autoIncrement:!1,indexes:{},name:"e2ee_tasks",primaryKey:["taskName"]},{autoIncrement:!1,indexes:{},name:"e2ee_personalSenderKeyStatuses",primaryKey:["groupJid"]},{autoIncrement:!0,indexes:{altIndex:{columns:["altIndex"],unique:!1},externalId:{columns:["externalId"],unique:!1},messageDeleteTs:{columns:["messageDeleteTs"],unique:!1},messageExpirationTs:{columns:["messageExpirationTs"],unique:!1},msgId:{columns:["msgId"],unique:!1},quoteExternalId:{columns:["quoteExternalId"],unique:!1},thread:{columns:["thread"],unique:!1}},name:"e2ee_messages",primaryKey:["rowId"]},{autoIncrement:!0,indexes:{externalId:{columns:["externalId"],unique:!1},msgId:{columns:["msgId"],unique:!1},thread:{columns:["thread"],unique:!1}},name:"e2ee_unrenderedMessages",primaryKey:["rowId"]},{autoIncrement:!0,indexes:{externalIdWithType:{columns:["externalIdWithType"],unique:!1}},name:"e2ee_pendingStanzas",primaryKey:["rowId"]},{autoIncrement:!1,indexes:{threadId:{columns:["threadId"],unique:!1},userJid:{columns:["userJid"],unique:!1}},name:"e2ee_participants",primaryKey:["id"]},{autoIncrement:!1,indexes:{},name:"e2ee_receipts",primaryKey:["msgId"]},{autoIncrement:!0,indexes:{"*msgIds":{columns:["msgIds"],multiEntry_DO_NOT_USE:!0},hashedPlaintextHash:{columns:["hashedPlaintextHash"]}},name:"e2ee_media",primaryKey:["mediaId"]},{autoIncrement:!0,indexes:{externalId:{columns:["externalId"],unique:!1},reactionId:{columns:["reactionId"],unique:!1},reactToExternalId:{columns:["reactToExternalId"],unique:!1},reactToMsgId:{columns:["reactToMsgId"],unique:!1},threadJid:{columns:["threadJid"],unique:!1}},name:"e2ee_reactions",primaryKey:["rowId"]},{autoIncrement:!0,indexes:{hashedPlaintextHash:{columns:["hashedPlaintextHash"]}},name:"e2ee_chunk",primaryKey:["chunkId"]},{autoIncrement:!0,indexes:{jid:{columns:["jid"]},threadOrder:{columns:["threadOrder"],unique:!1}},name:"e2ee_threads",primaryKey:["chatId"]},{autoIncrement:!1,indexes:{},name:"e2ee_existingUsers",primaryKey:["id"]},{autoIncrement:!1,indexes:{},name:"e2ee_pendingReceipts",primaryKey:["id"]},{autoIncrement:!0,indexes:{version:{columns:["version"]}},name:"e2ee_browserEncryptionMeta",primaryKey:["id"]},{autoIncrement:!1,indexes:{},name:"e2ee_ftsBackloggedMessages",primaryKey:["rowId"]},{autoIncrement:!1,indexes:{},name:"e2ee_ftsEncryptionMeta",primaryKey:["key"]},{autoIncrement:!0,indexes:{"*prefixes":{columns:["prefixes"],multiEntry_DO_NOT_USE:!0,unique:!1},chatId:{columns:["chatId"],unique:!1},id:{columns:["id"],unique:!1}},name:"e2ee_ftsIndexV3",primaryKey:["ftsRowId"]},{autoIncrement:!1,indexes:{threadId:{columns:["threadId"],unique:!1}},name:"e2ee_groupInfo",primaryKey:["groupJid"]}];b=[{autoIncrement:!0,indexes:{altIndex:{columns:["altIndex"],unique:!1},externalId:{columns:["externalId"],unique:!1},messageDeleteTs:{columns:["messageDeleteTs"],unique:!1},messageExpirationTs:{columns:["messageExpirationTs"],unique:!1},msgId:{columns:["msgId"],unique:!1},quoteExternalId:{columns:["quoteExternalId"],unique:!1},thread:{columns:["thread"],unique:!1},unsendMsgContentDeleteTs:{columns:["unsendMsgContentDeleteTs"],unique:!1}},name:"e2ee_messages",primaryKey:["rowId"]}];c=[{autoIncrement:!0,indexes:{altIndex:{columns:["altIndex"],unique:!1},externalId:{columns:["externalId"],unique:!1},messageDeleteTs:{columns:["messageDeleteTs"],unique:!1},messageExpirationTs:{columns:["messageExpirationTs"],unique:!1},msgId:{columns:["msgId"],unique:!1},quoteExternalId:{columns:["quoteExternalId"],unique:!1},revokedExternalId:{columns:["revokedExternalId"],unique:!1},thread:{columns:["thread"],unique:!1},unsendMsgContentDeleteTs:{columns:["unsendMsgContentDeleteTs"],unique:!1}},name:"e2ee_messages",primaryKey:["rowId"]}];e=[{autoIncrement:!0,indexes:{},name:"e2ee_isDualSend",primaryKey:["rowId"]}];f=[{autoIncrement:!1,indexes:{},name:"e2ee_historySyncQRCodeSecretKey",primaryKey:["key"]}];var h=[{autoIncrement:!0,indexes:{externalId:{columns:["externalId"],unique:!1},messageDeleteForMeTs:{columns:["messageDeleteForMeTs"],unique:!1},msgId:{columns:["msgId"],unique:!1},thread:{columns:["thread"],unique:!1}},name:"e2ee_unrenderedMessages",primaryKey:["rowId"]}],i=[{autoIncrement:!0,indexes:{altIndex:{columns:["altIndex"],unique:!1},externalId:{columns:["externalId"],unique:!1},messageDeleteTs:{columns:["messageDeleteTs"],unique:!1},messageExpirationTs:{columns:["messageExpirationTs"],unique:!1},msgId:{columns:["msgId"],unique:!1},quoteExternalId:{columns:["quoteExternalId"],unique:!1},revokedExternalId:{columns:["revokedExternalId"],unique:!1},thread:{columns:["thread"],unique:!1},threadJid:{columns:["threadJid"],unique:!1},unsendMsgContentDeleteTs:{columns:["unsendMsgContentDeleteTs"],unique:!1}},name:"e2ee_messages",primaryKey:["rowId"]},{autoIncrement:!0,indexes:{externalId:{columns:["externalId"],unique:!1},messageDeleteForMeTs:{columns:["messageDeleteForMeTs"],unique:!1},msgId:{columns:["msgId"],unique:!1},thread:{columns:["thread"],unique:!1},threadJid:{columns:["threadJid"],unique:!1}},name:"e2ee_unrenderedMessages",primaryKey:["rowId"]}],j=[{autoIncrement:!1,indexes:{inviterJid:{columns:["inviterJid"],unique:!1}},name:"e2ee_groupInvites",primaryKey:["invitedParticipantId"]}],k=[{autoIncrement:!0,indexes:{deleteTs:{columns:["deleteTs"],unique:!1},externalIdWithType:{columns:["externalIdWithType"],unique:!1}},name:"e2ee_pendingStanzas",primaryKey:["rowId"]}],l=[{autoIncrement:!0,indexes:{"*msgIds":{columns:["msgIds"],multiEntry_DO_NOT_USE:!0},fbid:{columns:["fbid"]},hashedPlaintextHash:{columns:["hashedPlaintextHash"]},objectId:{columns:["objectId"]}},name:"e2ee_media",primaryKey:["mediaId"]}],m=[{autoIncrement:!0,indexes:{},name:"e2ee_dualSendMedia",primaryKey:["id"]}],n=[{autoIncrement:!0,indexes:{associatedMessageId:{columns:["associatedMessageId"],unique:!1},defaultPreviewMediaId:{columns:["defaultPreviewMediaId"],unique:!1},faviconMediaId:{columns:["faviconMediaId"],unique:!1},headerMediaId:{columns:["headerMediaId"],unique:!1},targetExpiringAtSec:{columns:["targetExpiringAtSec"],unique:!1}},name:"e2ee_xma",primaryKey:["xmaId"]}],o=[{autoIncrement:!1,indexes:{},name:"e2ee_historySyncQRCodeData",primaryKey:["rowId"]}],p=[{autoIncrement:!0,indexes:{associatedMessageId:{columns:["associatedMessageId"],unique:!1},defaultPreviewMediaId:{columns:["defaultPreviewMediaId"],unique:!1},externalId:{columns:["externalId"],unique:!1},faviconMediaId:{columns:["faviconMediaId"],unique:!1},headerMediaId:{columns:["headerMediaId"],unique:!1},targetExpiringAtSec:{columns:["targetExpiringAtSec"],unique:!1}},name:"e2ee_xma",primaryKey:["xmaId"]}],q=[{autoIncrement:!0,indexes:{},name:"e2ee_dyiBatch",primaryKey:["batchId"]}],r=[{autoIncrement:!1,indexes:{correspondingOpenThreadKey:{columns:["correspondingOpenThreadKey"],unique:!1},threadId:{columns:["threadId"],unique:!1}},name:"e2ee_groupInfo",primaryKey:["groupJid"]}],s=[{autoIncrement:!1,indexes:{},name:"e2ee_groupInvites",primaryKey:["invitedParticipantId","inviterJid"],removed:!0}],t=[{autoIncrement:!1,indexes:{inviterJid:{columns:["inviterJid"],unique:!1}},name:"e2ee_groupInvites",primaryKey:["invitedParticipantId","inviterJid"]}],u=[{autoIncrement:!0,indexes:{"[thread+sortOrderMs]":{columns:["thread","sortOrderMs"],unique:!1},altIndex:{columns:["altIndex"],unique:!1},externalId:{columns:["externalId"],unique:!1},messageDeleteTs:{columns:["messageDeleteTs"],unique:!1},messageExpirationTs:{columns:["messageExpirationTs"],unique:!1},msgId:{columns:["msgId"],unique:!1},quoteExternalId:{columns:["quoteExternalId"],unique:!1},revokedExternalId:{columns:["revokedExternalId"],unique:!1},thread:{columns:["thread"],unique:!1},threadJid:{columns:["threadJid"],unique:!1},unsendMsgContentDeleteTs:{columns:["unsendMsgContentDeleteTs"],unique:!1}},name:"e2ee_messages",primaryKey:["rowId"]},{autoIncrement:!0,indexes:{"[thread+sortOrderMs]":{columns:["thread","sortOrderMs"],unique:!1},externalId:{columns:["externalId"],unique:!1},messageDeleteForMeTs:{columns:["messageDeleteForMeTs"],unique:!1},msgId:{columns:["msgId"],unique:!1},thread:{columns:["thread"],unique:!1},threadJid:{columns:["threadJid"],unique:!1}},name:"e2ee_unrenderedMessages",primaryKey:["rowId"]}],v=[{autoIncrement:!1,indexes:{"[modelId+modelType+actionState]":{columns:["modelId","modelType","actionState"],unique:!1},action:{columns:["action"],unique:!1},actionState:{columns:["actionState"],unique:!1},collection:{columns:["collection"],unique:!1},indexMac:{columns:["indexMac"],unique:!1}},name:"e2ee_syncActions",primaryKey:["index"]}],w=[{autoIncrement:!1,indexes:{},name:"e2ee_missingKeys",primaryKey:["keyHex"]}],x=[{autoIncrement:!0,indexes:{action:{columns:["action"],unique:!1},collection:{columns:["collection"],unique:!1},index:{columns:["index"],unique:!1}},name:"e2ee_pendingMutations",primaryKey:["id"]}],y=[{autoIncrement:!0,indexes:{collection:{columns:["collection"]}},name:"e2ee_collectionVersions",primaryKey:["id"]}],z=[{autoIncrement:!0,indexes:{keyEpoch:{columns:["keyEpoch"],unique:!1},keyId:{columns:["keyId"]}},name:"e2ee_syncKeys",primaryKey:["id"]}],A=[],B=[{autoIncrement:!1,indexes:{externalId:{columns:["externalId"],unique:!1},lastRetryTs:{columns:["lastRetryTs"],unique:!1},traceId:{columns:["traceId"],unique:!1}},name:"e2ee_uploadRetryStatus",primaryKey:["externalId","lastRetryTs"]}],C=[{autoIncrement:!0,indexes:{jid:{columns:["jid"],unique:!1},stanzaId:{columns:["stanzaId"],unique:!1},type:{columns:["type"],unique:!1}},name:"e2ee_stanzaQueue",primaryKey:["stanzaQueueId"]}],D=[{autoIncrement:!0,indexes:{},name:"e2ee_deviceChangeAlerts",primaryKey:["deviceChangeAlertsId"]}],E=[{autoIncrement:!1,indexes:{},name:"e2ee_retroactiveBackupsState",primaryKey:["threadId"]}],F=[{autoIncrement:!0,indexes:{deduplicationKey:{columns:["deduplicationKey"],predicate:function(a){return a.deduplicationKey!=null}},jid:{columns:["jid"]},threadOrder:{columns:["threadOrder"],unique:!1}},name:"e2ee_threads",primaryKey:["chatId"]}],G=[{autoIncrement:!0,indexes:{mediaId:{columns:["mediaId"],unique:!1},msgId:{columns:["msgId"],unique:!1},objectId:{columns:["objectId"]}},name:"e2ee_mediaBackup",primaryKey:["mediaBackupId"]}],H=[{autoIncrement:!0,indexes:{authoritativeThreadKey:{columns:["authoritativeThreadKey"]},deduplicationKey:{columns:["deduplicationKey"],predicate:function(a){return a.deduplicationKey!=null}},jid:{columns:["jid"]},threadOrder:{columns:["threadOrder"],unique:!1}},name:"e2ee_threads",primaryKey:["chatId"]}],I=[{autoIncrement:!0,indexes:{"[jid+type]":{columns:["jid","type"],unique:!1},jid:{columns:["jid"],unique:!1},stanzaId:{columns:["stanzaId"],unique:!1},type:{columns:["type"],unique:!1}},name:"e2ee_stanzaQueue",primaryKey:["stanzaQueueId"]}],J=[{autoIncrement:!0,indexes:{originalMsgExternalId:{columns:["originalMsgExternalId"],unique:!1},threadJid:{columns:["threadJid"],unique:!1}},name:"e2ee_editMsgHistory",primaryKey:["editMsgHistoryId"]}],K=[{autoIncrement:!1,indexes:{},name:"e2ee_ftsBackloggedMessages",primaryKey:["rowId"],removed:!0},{autoIncrement:!1,indexes:{},name:"e2ee_ftsEncryptionMeta",primaryKey:["key"],removed:!0},{autoIncrement:!0,indexes:{},name:"e2ee_ftsIndexV3",primaryKey:["ftsRowId"],removed:!0}],L=[{autoIncrement:!0,indexes:{"*msgIds":{columns:["msgIds"],multiEntry_DO_NOT_USE:!0},fbid:{columns:["fbid"],unique:!1},hashedPlaintextHash:{columns:["hashedPlaintextHash"]},objectId:{columns:["objectId"],unique:!1}},name:"e2ee_media",primaryKey:["mediaId"]}],M=[{autoIncrement:!0,indexes:{},name:"e2ee_ebRestoreQueue",primaryKey:["queueId"]},{autoIncrement:!0,indexes:{},name:"e2ee_ebUploadQueue",primaryKey:["queueId"]}],N=[{autoIncrement:!0,indexes:{"[uploadStatus+uploadTsSec]":{columns:["uploadStatus","uploadTsSec"],unique:!1},uploadStatus:{columns:["uploadStatus"],unique:!1},uploadTsSec:{columns:["uploadTsSec"],unique:!1}},name:"e2ee_ebRestoreQueue",primaryKey:["queueId"]},{autoIncrement:!0,indexes:{"[uploadStatus+uploadTsSec]":{columns:["uploadStatus","uploadTsSec"],unique:!1},uploadStatus:{columns:["uploadStatus"],unique:!1},uploadTsSec:{columns:["uploadTsSec"],unique:!1}},name:"e2ee_ebUploadQueue",primaryKey:["queueId"]}],O=[{autoIncrement:!0,indexes:{"[thread+serverTs]":{columns:["thread","serverTs"],unique:!1},"[thread+sortOrderMs]":{columns:["thread","sortOrderMs"],unique:!1},altIndex:{columns:["altIndex"],unique:!1},externalId:{columns:["externalId"],unique:!1},messageDeleteTs:{columns:["messageDeleteTs"],unique:!1},messageExpirationTs:{columns:["messageExpirationTs"],unique:!1},msgId:{columns:["msgId"],unique:!1},quoteExternalId:{columns:["quoteExternalId"],unique:!1},revokedExternalId:{columns:["revokedExternalId"],unique:!1},thread:{columns:["thread"],unique:!1},threadJid:{columns:["threadJid"],unique:!1},unsendMsgContentDeleteTs:{columns:["unsendMsgContentDeleteTs"],unique:!1}},name:"e2ee_messages",primaryKey:["rowId"]}],P=[],Q=[{autoIncrement:!0,indexes:{},name:"e2ee_danglingQueue",primaryKey:["queueId"]}],R=[{autoIncrement:!0,indexes:{dbVersion:{columns:["dbVersion"],unique:!1}},name:"e2ee_staleQueue",primaryKey:["staleQueueId"]}],S=[{autoIncrement:!0,indexes:{"[offlineThreadingId+backupActionType]":{columns:["offlineThreadingId","backupActionType"],unique:!1},"[uploadStatus+uploadTsSec]":{columns:["uploadStatus","uploadTsSec"],unique:!1},backupActionType:{columns:["backupActionType"],unique:!1},msgIdKey:{columns:["msgIdKey"],unique:!1},offlineThreadingId:{columns:["offlineThreadingId"],unique:!1},uploadStatus:{columns:["uploadStatus"],unique:!1},uploadTsSec:{columns:["uploadTsSec"],unique:!1}},name:"e2ee_ebUploadQueue",primaryKey:["queueId"]}],T=[{autoIncrement:!1,indexes:{},name:"e2ee_ftsBackloggedMessages",primaryKey:["rowId"]},{autoIncrement:!1,indexes:{},name:"e2ee_ftsEncryptionMeta",primaryKey:["key"]},{autoIncrement:!0,indexes:{"*prefixes":{columns:["prefixes"],multiEntry_DO_NOT_USE:!0,unique:!1},chatId:{columns:["chatId"],unique:!1},id:{columns:["id"],unique:!1}},name:"e2ee_ftsIndexV3",primaryKey:["ftsRowId"]}],U=[{autoIncrement:!1,indexes:{"[threadJid+userJid]":{columns:["threadJid","userJid"],unique:!1},threadId:{columns:["threadId"],unique:!1},userJid:{columns:["userJid"],unique:!1}},name:"e2ee_participants",primaryKey:["id"]}],V=[{autoIncrement:!0,indexes:{},name:"e2ee_pendingMessageStanzaQueue",primaryKey:["pendingMessageStanzaQueueId"]}],W=[{autoIncrement:!0,indexes:{author:{columns:["author"],unique:!1},chat:{columns:["chat"],unique:!1},externalId:{columns:["externalId"],unique:!1}},name:"e2ee_deletedMessages",primaryKey:["rowId"]}],X=[{autoIncrement:!0,indexes:{threadJid:{columns:["threadJid"],unique:!1}},name:"e2ee_ebMsgRanges",primaryKey:["rangeId"]}],Y=[{autoIncrement:!1,indexes:{inviterJid:{columns:["inviterJid"],unique:!1},threadJid:{columns:["threadJid","inviteeJid"],unique:!1}},name:"e2ee_groupInvites",primaryKey:["invitedParticipantId","inviterJid"]}],Z=[{autoIncrement:!1,indexes:{waMsgId:{columns:["waMsgId"]}},name:"e2ee_receipts",primaryKey:["msgId"]}],$=[{autoIncrement:!0,indexes:{"[jid+priority]":{columns:["jid","priority"],unique:!1},"[jid+type]":{columns:["jid","type"],unique:!1},jid:{columns:["jid"],unique:!1},priority:{columns:["priority"],unique:!1},stanzaId:{columns:["stanzaId"],unique:!1},type:{columns:["type"],unique:!1}},name:"e2ee_stanzaQueue",primaryKey:["stanzaQueueId"]}],aa=[{autoIncrement:!0,indexes:{"[thread+serverTs]":{columns:["thread","serverTs"],unique:!1},"[thread+sortOrderMs]":{columns:["thread","sortOrderMs"],unique:!1},altIndex:{columns:["altIndex"],unique:!1},externalId:{columns:["externalId"],unique:!1},messageDeleteTs:{columns:["messageDeleteTs"],unique:!1},messageExpirationTs:{columns:["messageExpirationTs"],unique:!1},msgId:{columns:["msgId"],unique:!1},quoteExternalId:{columns:["quoteExternalId"],unique:!1},revokedExternalId:{columns:["revokedExternalId"],unique:!1},threadJid:{columns:["threadJid"],unique:!1},unsendMsgContentDeleteTs:{columns:["unsendMsgContentDeleteTs"],unique:!1}},name:"e2ee_messages",primaryKey:["rowId"]}],ba=[{autoIncrement:!0,indexes:{"[thread+serverTs]":{columns:["thread","serverTs"],predicate:function(a){return a.serverTs!=null},unique:!1},"[thread+sortOrderMs]":{columns:["thread","sortOrderMs"],unique:!1},"[threadJid+sortOrderMs]":{columns:["threadJid","sortOrderMs"],unique:!1},altIndex:{columns:["altIndex"],predicate:function(a){return a.altIndex!=null},unique:!1},externalId:{columns:["externalId"],unique:!1},messageDeleteTs:{columns:["messageDeleteTs"],predicate:function(a){return a.messageDeleteTs!=null},unique:!1},messageExpirationTs:{columns:["messageExpirationTs"],predicate:function(a){return a.messageExpirationTs!=null},unique:!1},msgId:{columns:["msgId"],unique:!1},quoteExternalId:{columns:["quoteExternalId"],predicate:function(a){return a.quoteExternalId!=null},unique:!1},revokedExternalId:{columns:["revokedExternalId"],predicate:function(a){return a.revokedExternalId!=null},unique:!1},threadJid:{columns:["threadJid"],unique:!1},unsendMsgContentDeleteTs:{columns:["unsendMsgContentDeleteTs"],predicate:function(a){return a.unsendMsgContentDeleteTs!=null},unique:!1}},name:"e2ee_messages",primaryKey:["rowId"]}],ca=[{autoIncrement:!1,dexieOnly_primaryKeyUnique:!0,indexes:{ts:{columns:["ts"],unique:!1}},name:"e2ee_sentBytesCache",primaryKey:["waMsgId"]}],da=[{autoIncrement:!0,indexes:{"[thread+serverTs]":{columns:["thread","serverTs"],predicate:function(a){return a.serverTs!=null},unique:!1},"[threadJid+sortOrderMs]":{columns:["threadJid","sortOrderMs"],unique:!1},altIndex:{columns:["altIndex"],predicate:function(a){return a.altIndex!=null},unique:!1},externalId:{columns:["externalId"],unique:!1},messageDeleteTs:{columns:["messageDeleteTs"],predicate:function(a){return a.messageDeleteTs!=null},unique:!1},messageExpirationTs:{columns:["messageExpirationTs"],predicate:function(a){return a.messageExpirationTs!=null},unique:!1},msgId:{columns:["msgId"],unique:!1},quoteExternalId:{columns:["quoteExternalId"],predicate:function(a){return a.quoteExternalId!=null},unique:!1},revokedExternalId:{columns:["revokedExternalId"],predicate:function(a){return a.revokedExternalId!=null},unique:!1},threadJid:{columns:["threadJid"],unique:!1},unsendMsgContentDeleteTs:{columns:["unsendMsgContentDeleteTs"],predicate:function(a){return a.unsendMsgContentDeleteTs!=null},unique:!1}},name:"e2ee_messages",primaryKey:["rowId"]}],ea=[{autoIncrement:!0,indexes:{"[originalMsgExternalId+threadJid]":{columns:["originalMsgExternalId","threadJid"],unique:!1}},name:"e2ee_editMsgHistory",primaryKey:["editMsgHistoryId"]}],fa=[{autoIncrement:!0,indexes:{deviceJid:{columns:["deviceJid"],unique:!1},displayOrder:{columns:["isArchived","ts"],unique:!1},isConfirmedActionTs:{columns:["isConfirmed","action","ts"],unique:!1},isNotifiedTs:{columns:["isNotified","ts"],unique:!1}},name:"e2ee_deviceChangeAlerts",primaryKey:["deviceChangeAlertsId"]},{autoIncrement:!1,indexes:{threadId:{columns:["threadId"],unique:!1}},name:"e2ee_groupInfo",primaryKey:["groupJid"]}],ga=[{autoIncrement:!0,indexes:{"[uploadStatus+uploadTsSec]":{columns:["uploadStatus","uploadTsSec"],unique:!1},backupActionType:{columns:["backupActionType"],unique:!1},msgIdKey:{columns:["msgIdKey"],unique:!1},uploadStatus:{columns:["uploadStatus"],unique:!1},uploadTsSec:{columns:["uploadTsSec"],unique:!1}},name:"e2ee_ebUploadQueue",primaryKey:["queueId"]}],ha=[{autoIncrement:!1,indexes:{},name:"e2ee_ephemeralSettings",primaryKey:["userJid"]}],ia=[{autoIncrement:!1,indexes:{ebClientRequired:{columns:["ebClientRequired","timestampMs"],unique:!1},restorable:{columns:["restorable","timestampMs"],unique:!1}},name:"e2ee_ebMessageRestoreTasks",primaryKey:["messageId"]}],ja=[{autoIncrement:!1,indexes:{},name:"e2ee_igMessageAuxiliaryInfo",primaryKey:["messageId"]}],ka=[{autoIncrement:!1,indexes:{plaintextHash:{columns:["plaintextHash"],unique:!1}},name:"e2ee_mediaKeys",primaryKey:["ciphertextHash"]}],la=[{autoIncrement:!0,indexes:{"[jid+priority]":{columns:["jid","priority"],unique:!1},jid:{columns:["jid"],unique:!1},priority:{columns:["priority"],unique:!1}},name:"e2ee_stanzaQueue",primaryKey:["stanzaQueueId"]}],ma=[{autoIncrement:!1,indexes:{"[threadJid+userJid]":{columns:["threadJid","userJid"],unique:!1},userJid:{columns:["userJid"],unique:!1}},name:"e2ee_participants",primaryKey:["id"]}],na=[{autoIncrement:!1,indexes:{},name:"e2ee_groupInfo",primaryKey:["groupJid"]}],oa=[{autoIncrement:!0,indexes:{},name:"e2ee_ebRestoreQueue",primaryKey:["queueId"]}],pa=[{autoIncrement:!1,indexes:{},name:"e2ee_ftsPurgeBacklog",primaryKey:["externalId"]},{autoIncrement:!1,indexes:{},name:"e2ee_ftsPurgeThreadBacklog",primaryKey:["chatJid"]}],qa=[{autoIncrement:!0,indexes:{"[threadJid+sortOrderMs]":{columns:["threadJid","sortOrderMs"],unique:!1},altIndex:{columns:["altIndex"],predicate:function(a){return a.altIndex!=null},unique:!1},externalId:{columns:["externalId"],unique:!1},messageDeleteTs:{columns:["messageDeleteTs"],predicate:function(a){return a.messageDeleteTs!=null},unique:!1},messageExpirationTs:{columns:["messageExpirationTs"],predicate:function(a){return a.messageExpirationTs!=null},unique:!1},msgId:{columns:["msgId"],unique:!1},quoteExternalId:{columns:["quoteExternalId"],predicate:function(a){return a.quoteExternalId!=null},unique:!1},revokedExternalId:{columns:["revokedExternalId"],predicate:function(a){return a.revokedExternalId!=null},unique:!1},threadJid:{columns:["threadJid"],unique:!1},unsendMsgContentDeleteTs:{columns:["unsendMsgContentDeleteTs"],predicate:function(a){return a.unsendMsgContentDeleteTs!=null},unique:!1}},name:"e2ee_messages",primaryKey:["rowId"]}],ra=[{autoIncrement:!0,indexes:{"*prefixes":{columns:["prefixes"],multiEntry_DO_NOT_USE:!0,unique:!1},chatId:{columns:["chatId"],unique:!1},id:{columns:["id"],unique:!1}},name:"e2ee_ftsIndexV3",primaryKey:["ftsRowId"],removed:!0}],sa=[{autoIncrement:!0,indexes:{fbid:{columns:["fbid"],unique:!1},hashedPlaintextHash:{columns:["hashedPlaintextHash"]},objectId:{columns:["objectId"],unique:!1}},name:"e2ee_media",primaryKey:["mediaId"]}],ta=[{autoIncrement:!1,indexes:{},name:"e2ee_retroactiveBackupsState",primaryKey:["threadId"],removed:!0}],ua=[{autoIncrement:!1,indexes:{},name:"e2ee_uploadRetryStatus",primaryKey:[],removed:!0}],va=[{autoIncrement:!1,indexes:{},name:"e2ee_receiverFetchInfo",primaryKey:["receiverFetchId"]}],wa=[{autoIncrement:!0,indexes:{"[threadJid+ts]":{columns:["threadJid","ts"],unique:!1},externalId:{columns:["externalId"],unique:!1},reactionId:{columns:["reactionId"],unique:!1},reactToExternalId:{columns:["reactToExternalId"],unique:!1},reactToMsgId:{columns:["reactToMsgId"],unique:!1},threadJid:{columns:["threadJid"],unique:!1},ts:{columns:["ts"],unique:!1}},name:"e2ee_reactions",primaryKey:["rowId"]}],xa=[{autoIncrement:!0,indexes:{"*msgIds":{columns:["msgIds"],multiEntry_DO_NOT_USE:!0},fbid:{columns:["fbid"],unique:!1},hashedPlaintextHash:{columns:["hashedPlaintextHash"]},objectId:{columns:["objectId"],unique:!1}},name:"e2ee_media",primaryKey:["mediaId"]}],ya=[{autoIncrement:!0,indexes:{"[threadJid+sortOrderMs]":{columns:["threadJid","sortOrderMs"],unique:!1},altIndex:{columns:["altIndex"],predicate:function(a){return a.altIndex!=null},unique:!1},externalId:{columns:["externalId"],unique:!1},messageDeleteTs:{columns:["messageDeleteTs"],predicate:function(a){return a.messageDeleteTs!=null},unique:!1},messageExpirationTs:{columns:["messageExpirationTs"],predicate:function(a){return a.messageExpirationTs!=null},unique:!1},msgId:{columns:["msgId"],unique:!1},quoteExpirationTs:{columns:["quoteExpirationTs"],predicate:function(a){return a.quoteExpirationTs!=null},unique:!1},quoteExternalId:{columns:["quoteExternalId"],predicate:function(a){return a.quoteExternalId!=null},unique:!1},revokedExternalId:{columns:["revokedExternalId"],predicate:function(a){return a.revokedExternalId!=null},unique:!1},threadJid:{columns:["threadJid"],unique:!1},unsendMsgContentDeleteTs:{columns:["unsendMsgContentDeleteTs"],predicate:function(a){return a.unsendMsgContentDeleteTs!=null},unique:!1}},name:"e2ee_messages",primaryKey:["rowId"]}],za=[{autoIncrement:!0,indexes:{"[threadJid+sortOrderMs]":{columns:["threadJid","sortOrderMs"],unique:!1},ack:{columns:["ack"],unique:!1},altIndex:{columns:["altIndex"],predicate:function(a){return a.altIndex!=null},unique:!1},externalId:{columns:["externalId"],unique:!1},messageDeleteTs:{columns:["messageDeleteTs"],predicate:function(a){return a.messageDeleteTs!=null},unique:!1},messageExpirationTs:{columns:["messageExpirationTs"],predicate:function(a){return a.messageExpirationTs!=null},unique:!1},msgId:{columns:["msgId"],unique:!1},quoteExpirationTs:{columns:["quoteExpirationTs"],predicate:function(a){return a.quoteExpirationTs!=null},unique:!1},quoteExternalId:{columns:["quoteExternalId"],predicate:function(a){return a.quoteExternalId!=null},unique:!1},revokedExternalId:{columns:["revokedExternalId"],predicate:function(a){return a.revokedExternalId!=null},unique:!1},threadJid:{columns:["threadJid"],unique:!1},unsendMsgContentDeleteTs:{columns:["unsendMsgContentDeleteTs"],predicate:function(a){return a.unsendMsgContentDeleteTs!=null},unique:!1}},name:"e2ee_messages",primaryKey:["rowId"]}],Aa=[{autoIncrement:!0,indexes:{"[threadJid+sortOrderMs]":{columns:["threadJid","sortOrderMs"],unique:!1},ack:{columns:["ack"],unique:!1},altIndex:{columns:["altIndex"],predicate:function(a){return a.altIndex!=null},unique:!1},externalId:{columns:["externalId"],unique:!1},messageDeleteTs:{columns:["messageDeleteTs"],predicate:function(a){return a.messageDeleteTs!=null},unique:!1},messageExpirationTs:{columns:["messageExpirationTs"],predicate:function(a){return a.messageExpirationTs!=null},unique:!1},msgId:{columns:["msgId"],unique:!1},protocolMsgId:{columns:["protocolMsgId"],unique:!0},quoteExpirationTs:{columns:["quoteExpirationTs"],predicate:function(a){return a.quoteExpirationTs!=null},unique:!1},quoteExternalId:{columns:["quoteExternalId"],predicate:function(a){return a.quoteExternalId!=null},unique:!1},revokedExternalId:{columns:["revokedExternalId"],predicate:function(a){return a.revokedExternalId!=null},unique:!1},threadJid:{columns:["threadJid"],unique:!1},unsendMsgContentDeleteTs:{columns:["unsendMsgContentDeleteTs"],predicate:function(a){return a.unsendMsgContentDeleteTs!=null},unique:!1}},name:"e2ee_messages",primaryKey:["rowId"]}],Ba=[{autoIncrement:!0,indexes:{version:{columns:["version"],unique:!0}},name:"e2ee_encryptionMetaV3",primaryKey:["id"]}],Ca=[{autoIncrement:!0,indexes:{"[threadJid+sortOrderMs]":{columns:["threadJid","sortOrderMs"],unique:!1},ack:{columns:["ack"],unique:!1},altIndex:{columns:["altIndex"],predicate:function(a){return a.altIndex!=null},unique:!1},externalId:{columns:["externalId"],unique:!1},messageDeleteTs:{columns:["messageDeleteTs"],predicate:function(a){return a.messageDeleteTs!=null},unique:!1},messageExpirationTs:{columns:["messageExpirationTs"],predicate:function(a){return a.messageExpirationTs!=null},unique:!1},msgId:{columns:["msgId"],unique:!1},protocolMsgId:{columns:["protocolMsgId"],unique:!0},quoteExpirationTs:{columns:["quoteExpirationTs"],predicate:function(a){return a.quoteExpirationTs!=null},unique:!1},quoteExternalId:{columns:["quoteExternalId"],predicate:function(a){return a.quoteExternalId!=null},unique:!1},revokedExternalId:{columns:["revokedExternalId"],predicate:function(a){return a.revokedExternalId!=null},unique:!1},threadJid:{columns:["threadJid"],unique:!1},unsendMsgContentDeleteTs:{columns:["unsendMsgContentDeleteTs"],predicate:function(a){return a.unsendMsgContentDeleteTs!=null},unique:!1}},name:"e2ee_messages_deduped",primaryKey:["rowId"]}],Da=[{autoIncrement:!1,indexes:{},name:"e2ee_poll",primaryKey:["chatJid","pollStanzaId"]}],Ea=[{autoIncrement:!0,indexes:{"[threadJid+pollStanzaId+sortOrderMs]":{columns:["threadJid","pollStanzaId","sortOrderMs"],predicate:function(a){return a.pollStanzaId!=null},unique:!1},"[threadJid+sortOrderMs]":{columns:["threadJid","sortOrderMs"],unique:!1},ack:{columns:["ack"],unique:!1},altIndex:{columns:["altIndex"],predicate:function(a){return a.altIndex!=null},unique:!1},externalId:{columns:["externalId"],unique:!1},messageDeleteTs:{columns:["messageDeleteTs"],predicate:function(a){return a.messageDeleteTs!=null},unique:!1},messageExpirationTs:{columns:["messageExpirationTs"],predicate:function(a){return a.messageExpirationTs!=null},unique:!1},msgId:{columns:["msgId"],unique:!1},protocolMsgId:{columns:["protocolMsgId"],unique:!0},quoteExpirationTs:{columns:["quoteExpirationTs"],predicate:function(a){return a.quoteExpirationTs!=null},unique:!1},quoteExternalId:{columns:["quoteExternalId"],predicate:function(a){return a.quoteExternalId!=null},unique:!1},revokedExternalId:{columns:["revokedExternalId"],predicate:function(a){return a.revokedExternalId!=null},unique:!1},threadJid:{columns:["threadJid"],unique:!1},unsendMsgContentDeleteTs:{columns:["unsendMsgContentDeleteTs"],predicate:function(a){return a.unsendMsgContentDeleteTs!=null},unique:!1}},name:"e2ee_messages",primaryKey:["rowId"]}],Fa=[{autoIncrement:!0,indexes:{"[threadJid+collapsibleId+sortOrderMs]":{columns:["threadJid","collapsibleId","sortOrderMs"],unique:!1},"[threadJid+pollStanzaId+sortOrderMs]":{columns:["threadJid","pollStanzaId","sortOrderMs"],predicate:function(a){return a.pollStanzaId!=null},unique:!1},"[threadJid+sortOrderMs]":{columns:["threadJid","sortOrderMs"],unique:!1},ack:{columns:["ack"],unique:!1},altIndex:{columns:["altIndex"],predicate:function(a){return a.altIndex!=null},unique:!1},externalId:{columns:["externalId"],unique:!1},messageDeleteTs:{columns:["messageDeleteTs"],predicate:function(a){return a.messageDeleteTs!=null},unique:!1},messageExpirationTs:{columns:["messageExpirationTs"],predicate:function(a){return a.messageExpirationTs!=null},unique:!1},msgId:{columns:["msgId"],unique:!1},protocolMsgId:{columns:["protocolMsgId"],unique:!0},quoteExpirationTs:{columns:["quoteExpirationTs"],predicate:function(a){return a.quoteExpirationTs!=null},unique:!1},quoteExternalId:{columns:["quoteExternalId"],predicate:function(a){return a.quoteExternalId!=null},unique:!1},revokedExternalId:{columns:["revokedExternalId"],predicate:function(a){return a.revokedExternalId!=null},unique:!1},threadJid:{columns:["threadJid"],unique:!1},unsendMsgContentDeleteTs:{columns:["unsendMsgContentDeleteTs"],predicate:function(a){return a.unsendMsgContentDeleteTs!=null},unique:!1}},name:"e2ee_messages",primaryKey:["rowId"]}],Ga=[{autoIncrement:!1,indexes:{},name:"e2ee_messages_deduped",primaryKey:[],removed:!0}];a=new Map([[(d=d("MAWDbVersionList")).VERSION.V21,a],[d.VERSION.V22,b],[d.VERSION.V23,c],[d.VERSION.V24,e],[d.VERSION.V25,f],[d.VERSION.V30,h],[d.VERSION.V31,i],[d.VERSION.V32,j],[d.VERSION.V33,k],[d.VERSION.V34,l],[d.VERSION.V35,m],[d.VERSION.V36,n],[d.VERSION.V37,o],[d.VERSION.V38,p],[d.VERSION.V39,q],[d.VERSION.V40,r],[d.VERSION.V41,s],[d.VERSION.V42,t],[d.VERSION.V45,u],[d.VERSION.V48,v],[d.VERSION.V49,w],[d.VERSION.V50,x],[d.VERSION.V51,y],[d.VERSION.V52,z],[d.VERSION.V57,A],[d.VERSION.V59,B],[d.VERSION.V61,C],[d.VERSION.V62,D],[d.VERSION.V63,E],[d.VERSION.V64,F],[d.VERSION.V65,G],[d.VERSION.V67,H],[d.VERSION.V69,I],[d.VERSION.V70,J],[d.VERSION.V73,K],[d.VERSION.V74,L],[d.VERSION.V75,M],[d.VERSION.V77,N],[d.VERSION.V78,O],[d.VERSION.V79,P],[d.VERSION.V80,Q],[d.VERSION.V81,R],[d.VERSION.V83,S],[d.VERSION.V84,T],[d.VERSION.V85,U],[d.VERSION.V86,V],[d.VERSION.V88,W],[d.VERSION.V89,X],[d.VERSION.V90,Y],[d.VERSION.V91,Z],[d.VERSION.V92,$],[d.VERSION.V93,aa],[d.VERSION.V94,ba],[d.VERSION.V95,ca],[d.VERSION.V96,da],[d.VERSION.V97,ea],[d.VERSION.V98,fa],[d.VERSION.V99,ga],[d.VERSION.V100,ha],[d.VERSION.V102,ia],[d.VERSION.V103,ja],[d.VERSION.V104,ka],[d.VERSION.V105,la],[d.VERSION.V106,ma],[d.VERSION.V107,na],[d.VERSION.V108,oa],[d.VERSION.V109,pa],[d.VERSION.V110,qa],[d.VERSION.V111,ra],[d.VERSION.V112,sa],[d.VERSION.V113,ta],[d.VERSION.V114,ua],[d.VERSION.V115,va],[d.VERSION.V116,wa],[d.VERSION.V117,xa],[d.VERSION.V118,ya],[d.VERSION.V119,za],[d.VERSION.V120,Aa],[d.VERSION.V121,Ba],[d.VERSION.V122,Ca],[d.VERSION.V123,Da],[d.VERSION.V124,Ea],[d.VERSION.V125,Fa],[d.VERSION.V126,Ga]]);b=["messages","unrenderedMessages","groupInfo","receipts","pendingReceipts","threads","participants","tasks","personalSenderKeyStatuses","appData","appMeta","chunk","mediaBackup","media","reactions","groupInvites","dyiBatch","syncActions","missingKeys","pendingMutations","collectionVersions","syncKeys","deviceChangeAlerts","xma","editMsgHistory","deletedMessages","ebMsgRanges","receiverFetchInfo","poll"];g.dbSchema=a;g.TABLES_TO_ENCRYPT=b}),98);
__d("MAWGetDbVersion",["MAWDbVersionList","justknobx","objectValues"],(function(a,b,c,d,e,f,g){"use strict";var h=null;function a(){var a;return(a=h)!=null?a:d("MAWDbVersionList").toVersion(c("justknobx")._("1716"))}function b(a){if(!c("justknobx")._("1495"))return;if(a==null){h=null;return h}if(a==="current"){h=null;return h}var b=Math.max.apply(Math,c("objectValues")(d("MAWDbVersionList").VERSION));if(a==="latest"){h=d("MAWDbVersionList").toVersion(b);return h}if(!Number.isInteger(a))return h;if(a<0){h=d("MAWDbVersionList").toVersion(b+a);return h}h=d("MAWDbVersionList").toVersion(a)}g.getArmadilloDbVersion=a;g.setArmadilloDbVersionForTest=b}),98);
__d("MAWDbSchema.restore",["MAWDbSchema","MAWGetDbVersion","objectEntries","sortBy"],(function(a,b,c,d,e,f,g){"use strict";a={e2ee_appData:!0,e2ee_appMeta:!0,e2ee_browserEncryptionMeta:!0,e2ee_chunk:!0,e2ee_collectionVersions:!0,e2ee_deletedMessages:!0,e2ee_deviceChangeAlerts:!0,e2ee_dyiBatch:!0,e2ee_ebMessageRestoreTasks:!0,e2ee_ebMsgRanges:!0,e2ee_ebUploadQueue:!0,e2ee_editMsgHistory:!0,e2ee_encryptionMetaV3:!0,e2ee_ephemeralSettings:!0,e2ee_existingUsers:!0,e2ee_ftsBackloggedMessages:!0,e2ee_ftsEncryptionMeta:!0,e2ee_ftsIndexV3:!0,e2ee_ftsPurgeBacklog:!0,e2ee_ftsPurgeThreadBacklog:!0,e2ee_groupInfo:!0,e2ee_groupInvites:!0,e2ee_historySyncQRCodeData:!0,e2ee_historySyncQRCodeSecretKey:!0,e2ee_igMessageAuxiliaryInfo:!0,e2ee_media:!0,e2ee_mediaBackup:!0,e2ee_messages:!0,e2ee_missingKeys:!0,e2ee_participants:!0,e2ee_pendingMessageStanzaQueue:!0,e2ee_pendingMutations:!0,e2ee_pendingReceipts:!0,e2ee_pendingStanzas:!0,e2ee_poll:!0,e2ee_reactions:!0,e2ee_receiverFetchInfo:!0,e2ee_syncActions:!0,e2ee_syncKeys:!0,e2ee_threads:!0,e2ee_unrenderedMessages:!0,e2ee_xma:!0};function h(a){var b=Object.fromEntries(c("objectEntries")(a.indexes).filter(function(a){a=a[1];return a.multiEntry_DO_NOT_USE!==!0}).map(function(b){var c=b[0];b=b[1];return[c,{columns:[].concat(b.columns,b.unique!==!1?[]:a.primaryKey),predicate:b.predicate}]}));b={auto_increment:a.autoIncrement===!0,indexes:b,name:a.name,primary_key:a.primaryKey};return b}var i=c("sortBy")(Array.from(d("MAWDbSchema").dbSchema.entries()),function(a){a=a[0];return a}).map(function(a){var b=a[0];a=a[1];return{schema:a,version:b}});b=function(a){return Array.from(i.filter(function(b){return b.version<=a&&b.schema!=null}).reduce(function(a,b){(b=b.schema)==null?void 0:b.forEach(function(b){if(b.removed===!0){a["delete"](b.name);return}a.set(b.name,h(b))});return a},new Map()).values())};e=b(d("MAWGetDbVersion").getArmadilloDbVersion());g.msgrDbStores=a;g.getSchemaForVersion=b;g.clientSchema=e}),98);
__d("LSDbSchema",["MAWDbSchema.restore"],(function(a,b,c,d,e,f){"use strict";a=importNamespace("MAWDbSchema.restore").clientSchema;f.clientSchema=a}),66);
__d("LSE2EEMetadataSyncGroupUtils",["gkx","isArmadillo","isInstamadillo","qex"],(function(a,b,c,d,e,f,g){"use strict";var h=c("isArmadillo")()||c("gkx")("23910")===!0&&!c("isInstamadillo")(),i=c("qex")._("923")===!0&&!c("isInstamadillo")();function a(){return h}function b(){return h?{groupId:95}:null}function d(){return i?{groupId:95}:null}g.shouldAlwaysResetE2EEMetadata=a;g.getE2EEMetadataSyncGroup=b;g.getE2EEMetadataSyncGroupForIGDSyncGroup=d}),98);
__d("LSTransactionLogger",["cr:8879"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=b("cr:8879")}),98);
__d("LSReStoreQplLogger",["LSTransactionLogger"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return{startTracking:function(b,d){var e=c("LSTransactionLogger")==null?void 0:c("LSTransactionLogger").startTracking(b,a,d);return{addAnnotations:function(b){c("LSTransactionLogger")==null?void 0:c("LSTransactionLogger").addAnnotations(a,e,b)},end:function(){c("LSTransactionLogger")==null?void 0:c("LSTransactionLogger").end(a,e)},fail:function(b){c("LSTransactionLogger")==null?void 0:c("LSTransactionLogger").fail(a,e,b)},mark:function(b){c("LSTransactionLogger")==null?void 0:c("LSTransactionLogger").mark(a,e,b)},recordLogicalWrite:function(){c("LSTransactionLogger")==null?void 0:c("LSTransactionLogger").recordLogicalWrite(e)}}}}}g.createQplLogger=a}),98);
__d("ReStoreCustomMigration",["ReStoreMigrateUtil","ReStorePersistedMetadata","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){return h.apply(this,arguments)}function h(){h=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var c,e=b.storeTx;b=b.transaction;if(a==null)return d("ReStoreMigrateUtil").ReStoreMigrateResult.NO_UPGRADE_NEEDED;var f=new(d("ReStorePersistedMetadata").ReStorePersistedCustomMigrationVersion)();c=(c=(yield f.read(e)))==null?void 0:c.customMigrationVersion;if(a.targetVersion===c)return d("ReStoreMigrateUtil").ReStoreMigrateResult.NO_UPGRADE_NEEDED;yield a.migration((c=c)!=null?c:0,b);f.write(e,{customMigrationVersion:a.targetVersion});return d("ReStoreMigrateUtil").ReStoreMigrateResult.UPGRADE_COMPLETE});return h.apply(this,arguments)}g.runCustomMigration=a}),98);
__d("ReStoreHooks",["PromiseOrValue"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){return{afterAdd:a.reduce(function(a,b){var c=b.onAfterAdd;return c?function(b){return(h||(h=d("PromiseOrValue"))).map(a(b),function(a){return c(babelHelpers.extends({},b,{value:a}))})}:a},function(a){a=a.value;return a}),afterEntriesNext:a.reduce(function(a,b){var c=b.onAfterEntriesNext;return c?function(b){return c({tableName:b.tableName,value:a(b)})}:a},function(a){a=a.value;return a}),afterGet:a.reduce(function(a,b){var c=b.onAfterGet;return c?function(b){return c({tableName:b.tableName,value:a(b)})}:a},function(a){a=a.value;return a}),afterPut:a.reduce(function(a,b){var c=b.onAfterPut;return c?function(b){return(h||(h=d("PromiseOrValue"))).map(a(b),function(a){return c(babelHelpers.extends({},b,{value:a}))})}:a},function(a){a=a.value;return a}),beforeAdd:a.reduce(function(a,b){var c=b.onBeforeAdd;return c?function(b){return(h||(h=d("PromiseOrValue"))).map(a(b),function(a){return c(babelHelpers.extends({},b,{value:a}))})}:a},function(a){a=a.value;return a}),beforeDelete:a.reduce(function(a,b){var c=b.onBeforeDelete;return c?function(b){return(h||(h=d("PromiseOrValue"))).map(a(b),function(a){return c(babelHelpers.extends({},b,{value:a}))})}:a},function(a){a=a.key;return a}),beforeNotify:a.reduce(function(a,b){var c=b.onBeforeNotify;return c?function(b){var d=a(b),e=d.newValue;d=d.prevValue;return c({newValue:e,prevValue:d,tableName:b.tableName})}:a},function(a){var b=a.newValue;a=a.prevValue;return{newValue:b,prevValue:a}}),beforePut:a.reduce(function(a,b){var c=b.onBeforePut;return c?function(b){return(h||(h=d("PromiseOrValue"))).map(a(b),function(a){return c(babelHelpers.extends({},b,{value:a}))})}:a},function(a){a=a.value;return a}),beforeUpsert:a.reduce(function(a,b){var c=b.onBeforeUpsert;return c?function(b){return(h||(h=d("PromiseOrValue"))).map(a(b),function(a){return c(babelHelpers.extends({},b,{value:a}))})}:a},function(a){a=a.value;return a})}}g.createHookManager=a}),98);
__d("ReStoreIndexeddbPersistenceInitFailure",[],(function(a,b,c,d,e,f){"use strict";var g="ReStoreIndexeddbPersistenceInitFailure";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b){var c;c=a.call(this,b)||this;c.message=b;c.name=g;return c}return b}(babelHelpers.wrapNativeSuper(Error));f.ERROR_NAME=g;f.ReStoreIndexeddbPersistenceInitFailure=a}),66);
__d("ReStorePersistence",["FBLogger","JSONStringifyBigIntSafe","ReStoreCommonUtils","ReStoreKeyComparer","ReStorePersistenceIds","ReStoreUtils","gkx","isPromise","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j={},k={gt:[!1,0],gte:[!0,0],lt:[!0,1],lte:[!1,1]},l=new Set(["encrypted_backups_virtual_devices","secure_encrypted_backups_recovery_code_status","device_metadata","secure_encrypted_backups_epochs","secure_encrypted_backups_client_state","encrypted_backups","experiences_shared_state"]),m=16,n=64,o=64,p=Math.floor(m/2),q=Math.floor(n/2),r=Math.floor(o/2);function s(b){if(b==null)return b;var c=Object.getOwnPropertyNames(b),a={},d,e;for(d=0;d<c.length;d++)e=b[c[d]],a[c[d]]=e;return a}function t(a,b){b===void 0&&(b=j);return a===b}function u(a,b,c){var e=0,f=a.length-1;while(e<=f){var g=e+f>>1;(c[0]?(i||(i=d("ReStoreKeyComparer"))).compareKey(a[g],b)<0:(i||(i=d("ReStoreKeyComparer"))).compareKey(a[g],b)<=0)?e=g+1:f=g-1}g=[e,f][c[1]];return g===a.length?-1:g}var v=function(){function a(a,b,c,d,e,f){this.$2=b,this.$3=c,this.$1=a,this.$4=d,this.$5=e,this.$6=f}var b=a.prototype;b.$7=function(a){return a==null?"root":"index("+a+")"};b.$8=function(a,b){return d("ReStoreUtils").getOrCreate(this.$1,b,function(){return{children:[].concat(a.children),isLeaf:!0,keys:[].concat(a.keys),nextId:a.nextId,prevId:a.prevId}})};b.$9=function(a,b){return d("ReStoreUtils").getOrCreate(this.$1,b,function(){return{children:[].concat(a.children),isLeaf:!1,keys:[].concat(a.keys)}})};b.$10=function(a,b){return d("ReStoreUtils").getOrCreate(this.$1,b,function(){return{headId:a.headId,rootId:a.rootId,tailId:a.tailId}})};b.$11=function(a,b){if(!a.length)return[0,0];var c=u(a,b,k.gte);if(c===-1)return[a.length,0];else if((i||(i=d("ReStoreKeyComparer"))).compareKey(a[c],b)===0)return[c,1];return[c,0]};b.$12=function*(a,b,d,e){var f=(yield* this.$13(a,b));f=f;b=b;var g=-1;while(!f.isLeaf){e==null?void 0:e.push([f,b,g]);if(f.keys==null){l.has(this.$2)&&(this.$4.logError==null?void 0:this.$4.logError(this.$2,this.$5,"dbCorruption",{}));throw c("FBLogger")("messenger_web").mustfixThrow("Encountered not leaf node without keys")}g=u(f.keys,d,k.gt);g===-1&&(g=f.keys.length);b=f.children[g];f=(yield* this.$13(a,b))}return[f,b,g]};b.$14=function*(a,b){this.$3();var d=this.$1.get(b);if(t(d))return void 0;if(d!==void 0)return d;d=this.$4.get(a,this.$2,b,this.$5);return(h||(h=c("isPromise")))(d)?yield d:d};b.$13=function*(a,b){a=(yield* this.$14(a,b));if(a===void 0){var d=this.$1.get(b);this.$4.logError==null?void 0:this.$4.logError(this.$2,this.$5,"dbCorruption",{deletedInThisTxn:t(d)?"true":"false",id:c("JSONStringifyBigIntSafe")(b)||""});throw c("FBLogger")("messenger_web").mustfixThrow("value not found")}return a};b.$15=function*(a,b){a=(a=(yield* this.$14(a,d("ReStorePersistenceIds").idForNextId)))!=null?a:0;this.$1.set("nextId",a+1);this.$1.set(a,b);return a};b.$16=function(a,b){this.$3(),this.$1.set(a,b)};b.incrementSeed=function*(a,b){a=(a=(yield* this.$14(a,"seed")))!=null?a:0;if(b===void 0){this.$1.set("seed",a+1);return a}b>=a&&this.$1.set("seed",b+1);return b};b.readData=function*(a,b){return yield* this.$14(a,b)};b.$17=function*(a,b,c){b=(yield* this.$14(a,this.$7(b)));if(b==null)return;a=(yield* this.$12(a,b.rootId,c));b=a[0];a=u(b.keys,c,k.gte);if(a===-1||(i||(i=d("ReStoreKeyComparer"))).compareKey(b.keys[a],c)!==0)return;return b.children[a]};b.tableGet=function*(a,b){b=(yield* this.$17(a,void 0,b));if(b===void 0)return;return yield* this.$18(a,b)};b.$18=function*(a,b){return Array.isArray(b)?b[0]:yield* this.$13(a,b)};b.$19=function*(a,b,d,e){if(Array.isArray(b)){var f=(yield* this.$17(a,void 0,b));if(f==null){c("gkx")("3570")&&this.$6(d,e,this.$2);throw c("FBLogger")("messenger_web").mustfixThrow("Encountered value in index %s but not found in table %s",d,this.$2)}return yield* this.$18(a,f)}return yield* this.$13(a,b)};b.indexGet=function*(a,b,c){var d=(yield* this.$17(a,b,c));if(d==null)return;return yield* this.$19(a,d,b,c)};b.getId=function*(a,b){a=(yield* this.$17(a,void 0,b));return Array.isArray(a)?b:a};b.tableSet=function*(a,b,c){var d,e=this.$4.shouldInline(this.$2,c),f=this.$7(),g=(yield* this.$14(a,f));if(g==null){var h;e||(d=(yield* this.$15(a,c)));h={children:[(h=d)!=null?h:[c]],isLeaf:!0,keys:[b]};h=(yield* this.$15(a,h));this.$16(f,{headId:h,rootId:h,tailId:h});return[(h=d)!=null?h:b,!1]}h=[];var i=(yield* this.$12(a,g.rootId,b,h)),k=i[0];i=i[1];var l=this.$11(k.keys,b),m=l[0];l=l[1];var n=!1;if(l===1){var o=k.children[m];if(!Array.isArray(o)){n=!0;if(!e){this.$16(o,c);return[o,!1]}else this.$1.set(o,j)}}o=this.$8(k,i);e||(d=(yield* this.$15(a,c)));l===0&&o.keys.splice(m,0,b);o.children.splice(m,l,(k=d)!=null?k:[c]);yield* this.$20(a,o,i,h,g,f,!0);return[(m=d)!=null?m:b,l===1&&!(e&&!n)]};b.indexSet=function*(a,b,c,d){b=this.$7(b);var e=(yield* this.$14(a,b));if(e==null){var f={children:[d],isLeaf:!0,keys:[c]};f=(yield* this.$15(a,f));this.$16(b,{headId:f,rootId:f,tailId:f});return}f=[];var g=(yield* this.$12(a,e.rootId,c,f)),h=g[0];g=g[1];var i=this.$11(h.keys,c),j=i[0];i=i[1];if(i===1&&h.children[j]===d)return;h=this.$8(h,g);h.keys.splice(j,i,c);h.children.splice(j,i,d);yield* this.$20(a,h,g,f,e,b,!1)};b.$20=function*(a,b,c,d,e,f,g){b=b;var h;c=c;for(;b.keys.length>=(b.isLeaf?g?m:n:o);b=h,c=i){var i;i=(i=d.pop())!=null?i:[];h=i[0];i=i[1];i!=null?h=this.$9(h,i):(h={children:[c],isLeaf:!1,keys:[]},this.$10(e,f).rootId=(yield* this.$15(a,h)));var j=Math.floor(b.keys.length/2),k=void 0;if(b.isLeaf){var l={children:b.children.slice(j),isLeaf:!0,keys:b.keys.slice(j),nextId:b.nextId,prevId:c},p=k=b.nextId=(yield* this.$15(a,l));l=l.nextId;l?this.$8(yield* this.$13(a,l),l).prevId=p:this.$10(e,f).tailId=k}else k=(yield* this.$15(a,{children:b.children.slice(j+1),isLeaf:!1,keys:b.keys.slice(j+1)}));l=this.$11(h.keys,b.keys[j]);p=l[0];l=l[1];h.keys.splice(p,l,b.keys[j]);h.children.splice(p+1,0,k);b.keys.length=j;b.children.length=j+(b.isLeaf?0:1)}};b.tableDelete=function*(a,b){a=(yield* this.$21(a,void 0,b));if(a==null)return!1;Array.isArray(a)||(this.$3(),this.$1.set(a,j));return!0};b.indexDelete=function*(a,b,c){return(yield* this.$21(a,b,c))!=null};b.$21=function*(a,b,e){var f=this.$7(b),g=(yield* this.$14(a,f));if(g==null)return;var h=[],m=(yield* this.$12(a,g.rootId,e,h)),n=m[0],o=m[1];m=m[2];var s=this.$8(n,o),t=u(n.keys,e,k.gte);if(t===-1||(i||(i=d("ReStoreKeyComparer"))).compareKey(n.keys[t],e)!==0)return;n=s.children[t];s.keys.splice(t,1);s.children.splice(t,1);e=s;t=o;s=m;var v;for(;e.keys.length<(e.isLeaf?b==null?p:q:r)&&h.length;x=[m,v,o],e=x[0],t=x[1],s=x[2],x){o=h.pop();m=o[0];v=o[1];o=o[2];m=this.$9(m,v);var w=[[{nodeId:m.children[s-1]},{node:e,nodeId:t},s-1],[{node:e,nodeId:t},{nodeId:m.children[s+1]},s]].filter(function(b){var a=b[0];b=b[1];return a.nodeId!==void 0&&b.nodeId!==void 0});for(var x of w){var y=x[0],z=x[1],A=x[2],B=y.node=y.node||(yield* this.$13(a,y.nodeId)),C=z.node=z.node||(yield* this.$13(a,z.nodeId)),D=e.isLeaf?b==null?p:q:r;if(B.keys.length+z.node.keys.length>=D*2){if(e.isLeaf){var E=this.$8(B,y.nodeId),F=this.$8(C,z.nodeId),G=[].concat(E.children,F.children),H=[].concat(B.keys,C.keys);E.children=(F.children=G).splice(0,D);E.keys=(F.keys=H).splice(0,D);m.keys[A]=H[0]}else{G=this.$9(B,y.nodeId);E=this.$9(C,z.nodeId);F=[].concat(G.children,E.children);H=[].concat(G.keys,[m.keys[A]],E.keys);G.children=(E.children=F).splice(0,D+1);m.keys[A]=H.splice(D,1)[0];G.keys=(E.keys=H).splice(0,D)}return n}}for(B of w){y=B[0];C=B[1];z=B[2];if(e.isLeaf){F=this.$8(y.node,y.nodeId);A=this.$8(C.node,C.nodeId);F.children=[].concat(F.children,A.children);F.keys=[].concat(F.keys,A.keys);A.keys.length=0;G=A.nextId;if(G){E=this.$8(yield* this.$13(a,G),G);E.prevId=y.nodeId}else this.$10(g,f).tailId=y.nodeId;F.nextId=A.nextId}else{H=this.$9(y.node,y.nodeId);D=this.$9(C.node,C.nodeId);H.children=[].concat(H.children,D.children);H.keys=[].concat(H.keys,[m.keys[z]],D.keys)}m.keys.splice(z,1);m.children.splice(z+1,1);this.$1.set(C.nodeId,j);break}}w=(yield* this.$13(a,g.rootId));if(!w.keys.length&&w.children.length){if(w.isLeaf){l.has(this.$2)&&(this.$4.logError==null?void 0:this.$4.logError(this.$2,this.$5,"dbCorruption",{}));throw c("FBLogger")("messenger_web").mustfixThrow("cannot be leaf")}this.$1.set(g.rootId,j);this.$10(g,f).rootId=w.children[0]}return n};b.btreeIterator=function(a,b,e){e=e===void 0?{}:e;var f=e.dir,g=f===void 0?"asc":f,h=babelHelpers.objectWithoutPropertiesLoose(e,["dir"]);function j(){if(g==="asc"){if(Object.prototype.hasOwnProperty.call(h,"gt"))return h.gt;else if(Object.prototype.hasOwnProperty.call(h,"gte"))return h.gte}else if(Object.prototype.hasOwnProperty.call(h,"lt"))return h.lt;else if(Object.prototype.hasOwnProperty.call(h,"lte"))return h.lte}function l(a){if(g==="asc")return Object.prototype.hasOwnProperty.call(h,"gt")?u(a.keys,h.gt,k.gt):Object.prototype.hasOwnProperty.call(h,"gte")?u(a.keys,h.gte,k.gte):0;else return Object.prototype.hasOwnProperty.call(h,"lt")?u(a.keys,h.lt,k.lt):Object.prototype.hasOwnProperty.call(h,"lte")?u(a.keys,h.lte,k.lte):a.keys.length-1}if(Object.prototype.hasOwnProperty.call(h,"gt")&&Object.prototype.hasOwnProperty.call(h,"gte"))throw c("FBLogger")("messenger_web").mustfixThrow("cannot specify both greater than and greater than or equal");if(Object.prototype.hasOwnProperty.call(h,"lt")&&Object.prototype.hasOwnProperty.call(h,"lte"))throw c("FBLogger")("messenger_web").mustfixThrow("cannot specify both less than and less than or equal");var m,n=this;return{next:function*(e){var f,o;if(m==null){var p=(yield* n.$14(a,n.$7(b)));if(p==null)return{done:!0};var q=j();if(q!==void 0){q=(yield* n.$12(a,p.rootId,q));f=q[0];q=q[1]}else if(g==="asc"){var r=[yield* n.$13(a,p.headId),p.headId];f=r[0];q=r[1]}else{r=[yield* n.$13(a,p.tailId),p.tailId];f=r[0];q=r[1]}p=l(f)}else{r=m;p=r.i;o=r.key;q=r.nodeId;f=(yield* n.$14(a,q));if(e!=null){if((i||(i=d("ReStoreKeyComparer"))).compareKey(e,o)===(g==="asc"?-1:1))throw c("FBLogger")("messenger_web").mustfixThrow("key must be ahead of current key");if(f==null||(i||(i=d("ReStoreKeyComparer"))).compareKey(e,f.keys[0])<0||(i||(i=d("ReStoreKeyComparer"))).compareKey(e,f.keys[f.keys.length-1])>0){r=(yield* n.$14(a,n.$7(b)));if(r==null)return{done:!0};r=(yield* n.$12(a,r.rootId,e));f=r[0];q=r[1]}p=u(f.keys,e,k[g==="asc"?"gte":"lte"])}else{if(f==null||p<0||p>=f.keys.length||(i||(i=d("ReStoreKeyComparer"))).compareKey(f.keys[p],o)!==0){if(f==null||!f.keys.length||(i||(i=d("ReStoreKeyComparer"))).compareKey(o,f.keys[0])<0||(i||(i=d("ReStoreKeyComparer"))).compareKey(o,f.keys[f.keys.length-1])>0){r=(yield* n.$14(a,n.$7(b)));if(r==null)return{done:!0};e=(yield* n.$12(a,r.rootId,o));f=e[0];q=e[1]}p=u(f.keys,o,k[g==="asc"?"lte":"gte"])}p+=g==="asc"?1:-1}}if(p<0||p>=f.keys.length)if(g==="asc"){if(f.nextId===void 0)return{done:!0};q=f.nextId;f=(yield* n.$13(a,q));p=0}else{if(f.prevId===void 0)return{done:!0};q=f.prevId;f=(yield* n.$13(a,q));p=f.keys.length-1}o=f.keys[p];m={i:p,key:o,node:f,nodeId:q};if(p<0||p>=f.keys.length)return{done:!0};r=g==="asc"?Object.prototype.hasOwnProperty.call(h,"lt")&&(i||(i=d("ReStoreKeyComparer"))).compareKey(o,h.lt)>=0||Object.prototype.hasOwnProperty.call(h,"lte")&&(i||(i=d("ReStoreKeyComparer"))).compareKey(o,h.lte)>0:Object.prototype.hasOwnProperty.call(h,"gt")&&(i||(i=d("ReStoreKeyComparer"))).compareKey(o,h.gt)<=0||Object.prototype.hasOwnProperty.call(h,"gte")&&(i||(i=d("ReStoreKeyComparer"))).compareKey(o,h.gte)<0;return r?{done:!0}:{done:!1,value:[f.keys[p],f.children[p]]}}}};b.tableEntries=function(a,b){b===void 0&&(b={});var c=this.btreeIterator(a,void 0,b),d=this;return{next:function*(b){b=(yield* c.next(b));if(b.done)return b;b=b.value;var e=b[0];b=b[1];b=(yield* d.$18(a,b));return{done:!1,value:[e,b]}}}};b.indexEntries=function(a,b,c){c===void 0&&(c={});var d=this.btreeIterator(a,b,c),e=this;return{next:function*(c){c=(yield* d.next(c));if(c.done)return c;c=c.value;var f=c[0];c=c[1];c=(yield* e.$19(a,c,b,f));return{done:!1,value:[f,c]}}}};b.keys=function(a,b,c){c===void 0&&(c={});var d=this.btreeIterator(a,b,c);return{next:function*(a){a=(yield* d.next(a));if(a.done)return a;a=a.value;a=a[0];return{done:!1,value:a}}}};b.clearIds=function*(a,b){this.$3();b=this.$7(b);var c=(yield* this.$14(a,b));this.$3();if(c==null)return;var d=(yield* this.$13(a,c.rootId));c=[{id:c.rootId,node:d}];while(c.length>0){d=c.shift();var e=d.id;d=d.node;if(!d.isLeaf)for(d of d.children){var f=(yield* this.$14(a,d));f!=null&&c.push({id:d,node:f})}this.$1.set(e,j)}this.$1.set(b,j)};b.readSeed=function*(a){return(a=(yield* this.$14(a,d("ReStorePersistenceIds").idForSeed)))!=null?a:0};b.remove=function(a,b){this.$3(),this.$1.set(b,j)};b.writeData=function(a,b,c){this.$16(b,s(c))};b.writeNewData=function*(a,b){return yield* this.$15(a,s(b))};return a}(),w=function(){function a(a,b,e,f,g){var h=this;this.$2=new Map();this.$6=!1;this.$3=function(){if(h.$6){f==null?void 0:f.mark("transaction_closed");throw c("FBLogger")("messenger_web").mustfixThrow("Transaction has closed")}e()};this.$4=b;this.$1=a;this.$5={get:function(a,c,e){var f=d("ReStoreUtils").getOrCreate(h.$1,c,function(){return new Map()});f=f.get(e);if(t(f))return void 0;return f!==void 0?f:b.get(a,c,e,"readwrite")},logError:b.logError,shouldInline:b.shouldInline};this.$7=g}var b=a.prototype;b.flush=function(){for(var a of this.$2){var b=a[0],c=a[1];b=d("ReStoreUtils").getOrCreate(this.$1,b,function(){return new Map()});for(c of c){var e=c[0],f=c[1];b.set(e,f)}}this.$6=!0};b.table=function(a){return new v(d("ReStoreUtils").getOrCreate(this.$2,a,function(){return new Map()}),a,this.$3,this.$5,"readwrite",this.$7)};return a}(),x=function(){function a(a,b,d,e){var f=this;this.$1=new Map();this.$2=!1;this.$7=function(){if(f.$2)throw c("FBLogger")("messenger_web").mustfixThrow("Transaction has closed: %s",f.$5)};this.$3=a;this.$4=b;this.types=a.types;this.$5=d;this.$6=e}var b=a.prototype;b.close=function(){this.$2=!0};b.flush=async function(a){a===void 0&&(a={upgrade:!1}),await this.$3.flush(this.$1,a),this.$2=!0};b.tablePermitsSynchronousIO=function(a){return(a=this.$3.permitsSynchronousIO==null?void 0:this.$3.permitsSynchronousIO(a))!=null?a:!1};b.table=function(a){return new v(d("ReStoreUtils").getOrCreate(this.$1,a,function(){return new Map()}),a,this.$7,this.$3,this.$4,this.$6)};b.createNested=function(a){return new w(this.$1,this.$3,this.$7,a,this.$6)};return a}();function a(a){function b(b,e,f){c("promiseDone")(async function(){await a.runExclusively(function(){return Promise.resolve([async function(){var c=new x(a,"readwrite",void 0,function(){});await d("ReStoreCommonUtils").gen(c.table(f).indexDelete(new WeakMap(),b,e));await c.flush()},"readwrite"])},"fixBrokenIndex")}())}return{clearCache:a.clearCache,close:a.close,createTransaction:function(c,d){return new x(a,c,d,b)},isClosed:a.isClosed||function(){return!1},isPersistenceSupported:a.isPersistenceSupported,logError:a.logError,runExclusively:a.runExclusively}}g.sentinelDeleted=j;g.isDeletedValue=t;g.ReStoreDbStoreTable=v;g.ReStoreDbStoreTransaction=x;g.createDbStore=a}),98);
__d("ReStoreTransactionDelay",["setTimeout"],(function(a,b,c,d,e,f,g){"use strict";async function a(a){a=a==="background"?new Promise(function(a){return c("setTimeout")(a,0)}):Promise.resolve();await a}g.default=a}),98);
__d("createReStoreSubscriptionManager",["ExecutionEnvironment","FBLogger","IntervalList","Promise","PromiseOrValue","ReStoreKeyComparer","ReStoreUtils","asyncToGeneratorRuntime","isPromise","nullthrows","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l;function a(a,e,f){var g=new Map(),m=new Map();function n(a,b){return(l||(l=d("ReStoreKeyComparer"))).compareValue(a,b)===0}function o(a,b,c,d){a=(a=z.get(a))!=null?a:[];var e=[];for(var f=0;f<b.length;f++){var g=b[f];for(var h=0;h<32&&g!==0;h++){if(g&1){var i=a[(f<<5)+h];if(!n(c[i],d[i])){e[f]=((i=e[f])!=null?i:0)|1<<h%32}}g=g>>>1}}return e}function p(a,b){for(var c=0;c<Math.max(a.length,b.length);c++)if((a[c]&b[c])!==0)return!0;return!1}function q(a,b,d,e){try{a=a(b,d,e);if((j||(j=c("isPromise")))(a))return a["catch"](function(a){c("FBLogger")("messenger_web").catching(a).mustfix("error in ReStore subscription async handler")});else return a}catch(a){c("FBLogger")("messenger_web").catching(a).mustfix("error in ReStore subscription handlers")}}function r(a,c,e,f,g){var i=Array.from(c(f));if(g.operation==="put"&&g.prevValue!=null){c=function(){var b=g.prevValue,c=[];for(var h of i){h[0];var j=h[1];j!=null&&j.forEach(function(a,b){c[b]=((b=c[b])!=null?b:0)|a})}var l=o(a,c,b,g.value);return{v:(k||(k=d("PromiseOrValue"))).all(i.map(function(a){var b=a[0];a=a[1];if(a==null||p(l,a))return q(b,f,g,e)}))}}();if(typeof c==="object")return c.v}return(h||(h=b("Promise"))).all(i.map(function(a){var b=a[0];a[1];return q(b,f,g,e)}))}function s(b){return c("nullthrows")((g.has(b)?g:g.set(b,[c("IntervalList")((l||(l=d("ReStoreKeyComparer"))).compareKey,a.tableNames[b].primaryKeyIds.length),new Map()])).get(b))}function t(b,e){var f=s(b);f[0];f=f[1];return c("nullthrows")((f.has(e)?f:f.set(e,c("IntervalList")((l||(l=d("ReStoreKeyComparer"))).compareKey,a.tableNames[b].indexes[e].length))).get(e))}function u(b,e,f,g,h,i){var j=function(a,c){return r(e,b.findIntersecting,i,a,c)},m=f==null?void 0:d("ReStoreUtils").searchKey(a.tableNames[e],f,h),n=g==null?void 0:d("ReStoreUtils").searchKey(a.tableNames[e],g,h);if(n==null&&m!=null)return j(m,{operation:"delete",prevValue:c("nullthrows")(f)});if(m==null&&n!=null)return j(n,{operation:"add",value:c("nullthrows")(g)});if(m!=null&&n!=null)return(l||(l=d("ReStoreKeyComparer"))).compareKey(m,n)===0?j(m,{operation:"put",prevValue:c("nullthrows")(f),value:c("nullthrows")(g)}):(k||(k=d("PromiseOrValue"))).map(j(m,{operation:"delete",prevValue:c("nullthrows")(f)}),function(){return j(n,{operation:"add",value:c("nullthrows")(g)})})}function v(a,b,c,e){var f=s(a),g=f[0],h=f[1];return(k||(k=d("PromiseOrValue"))).map(u(g,a,b,c,null,e),function(){return(k||(k=d("PromiseOrValue"))).all(Array.from(h.entries()).map(function(d){var f=d[0];d=d[1];return u(d,a,b,c,f,e)}).filter(function(a){return a!=null}))})}function w(){m.forEach(function(a){return a()})}function x(a,b){function d(){var d=new Map();try{for(var f of a){var g=f[0],h=f[1];e(b,g)&&d.set(g,h)}}catch(a){c("FBLogger")("messenger_web").catching(a).mustfix("Couldnot format changes to broadcast")}return Array.from(d)}if((i||(i=c("ExecutionEnvironment"))).isInBrowser&&f!=null){d=d();if(d.length===0)return;f.postMessage(b,d)}}(i||(i=c("ExecutionEnvironment"))).isInBrowser&&(f!=null&&f.onEventReceive("notifyTableV2",function(a){c("promiseDone")(b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var b=new WeakMap(),c=a.values();yield (k||(k=d("PromiseOrValue"))).loop(function(a){if(a.done)return{action:"break",value:void 0};a=a.value;var e=a[0];a=a[1];var f=a.values();return(k||(k=d("PromiseOrValue"))).map(k.loop(function(a){if(a.done)return{action:"break",value:void 0};a=a.value;var c=a[0];a=a[1];return(k||(k=d("PromiseOrValue"))).map(v(e,c,a,b),function(){return{action:"continue",value:f.next()}})},f.next()),function(){return{action:"continue",value:c.next()}})},c.next());w()})())}));var y=new Map(),z=new Map();function A(a,b){if(b==null)return null;var c=y.get(a);c==null&&(c=new Map(),y.set(a,c));var d=[];for(b of b){var e=c.get(b);if(e==null){e=c.size;c.set(b,e);var f=z.get(a);f==null&&(f=[],z.set(a,f));f.push(b)}f=e>>5;e=e&31;d[f]=((f=d[f])!=null?f:0)|1<<e}return d}return{dispose:function(){g.clear(),m.clear()},notifyCommit:w,notifyTableAndIndexSubscribers:v,notifyTableV2:x,subscribeIndex:function(a,c,d,e,f){var g=t(a,c),i=!1;c=function(a,c,e){return i?(h||(h=b("Promise"))).resolve():q(d,a,c,e)};var j=[c,A(a,f)];g.set(e,j);return function(){i=!0,g["delete"](j)}},subscribeTable:function(a,c,d,e){var f=s(a),g=f[0],i=!1;f=function(a,d,e){return i?(h||(h=b("Promise"))).resolve():q(c,a,d,e)};var j=[f,A(a,e)];g.set(d,j);return function(){i=!0,g["delete"](j)}},subscribeToCommit:function(a){m.set(a,a);return function(){m["delete"](a)}}}}g["default"]=a}),98);
__d("setImmediate",["TimeSlice","TimerStorage","setImmediateAcrossTransitions"],(function(a,b,c,d,e,f,g){function a(a){var b,d=function(){c("TimerStorage").unset(c("TimerStorage").IMMEDIATE,b);for(var d=arguments.length,e=new Array(d),f=0;f<d;f++)e[f]=arguments[f];Function.prototype.apply.call(a,this,e)};c("TimeSlice").copyGuardForWrapper(a,d);for(var e=arguments.length,f=new Array(e>1?e-1:0),g=1;g<e;g++)f[g-1]=arguments[g];b=c("setImmediateAcrossTransitions").apply(void 0,[d].concat(f));c("TimerStorage").set(c("TimerStorage").IMMEDIATE,b);return b}g["default"]=a}),98);
__d("ReStore",["ExecutionEnvironment","FBLogger","JSONStringifyBigIntSafe","ODS","PromiseOrValue","ReStoreCustomMigration","ReStoreHooks","ReStoreIndexeddbPersistenceInitFailure","ReStoreMigrateUtil","ReStoreOperationLock","ReStorePersistedMetadata","ReStorePersistence","ReStoreTable","ReStoreTransactionDelay","ReStoreUtils","clearTimeout","cr:1088","cr:6665","createReStoreSubscriptionManager","promiseDone","setImmediate","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,"ReStore transaction timed out")||this}return a}(babelHelpers.wrapNativeSuper(Error)),l={enableRejectingTransaction:!1,enableTransactionTimeout:!1,fbLoggerProject:"maw_db",odsCategoryID:600},m=12e4;function a(a){var e=a.persistence,f=a.hooks;f=f===void 0?[]:f;var g=a.customMigration,n=a.loggers,o=n===void 0?{logHistory:void 0,transactionQplLogger:void 0,userFlow:void 0}:n;n=a.config;var p=n===void 0?l:n,q=a.schema,r=o.logHistory,s=o.transactionQplLogger,t=o.userFlow,u=d("ReStorePersistence").createDbStore(e);n=e.tabTablesNotifier==null?void 0:e.tabTablesNotifier();var v=q.defaults,w=c("createReStoreSubscriptionManager")(q,e.shouldSync,n),x=d("ReStoreHooks").createHookManager(f),y=u.createTransaction("readonly"),z=e.types.includes("indexeddb"),A={background:{first:null,last:null},sync_script:{first:null,last:null},ui:{first:null,last:null}},B="<first_txn>";t==null?void 0:t.addPoint("migration_start");r==null?void 0:r.debug("restore migration start");var C=new(d("ReStoreOperationLock").WaitForPromiseLock)(e.types.includes("devtool")?Promise.resolve():u.runExclusively(function(){return Promise.resolve([async function(){t==null?void 0:t.addPoint("migration_execute_start");var a=new Map(),b=u.createTransaction("readwrite"),c=F(b,a);r==null?void 0:r.debug("migration transaction created");c={changedKeys:a,clearCache:E,hooksManager:x,loggers:o,storeTx:b,transaction:c};try{var e=await q.migrate(c);t==null?void 0:t.addPoint("migration_custom_start");c=await d("ReStoreCustomMigration").runCustomMigration(g,c);r==null?void 0:r.debug("migration custom end");t==null?void 0:t.addPoint("migration_custom_end");if(c===d("ReStoreMigrateUtil").ReStoreMigrateResult.NO_UPGRADE_NEEDED&&e===d("ReStoreMigrateUtil").ReStoreMigrateResult.NO_UPGRADE_NEEDED){b.close();r==null?void 0:r.debug("restore migration skipped");t==null?void 0:t.addPoint("migration_skipped");return}c=e!==d("ReStoreMigrateUtil").ReStoreMigrateResult.UPDATE_METADATA&&a.size!==0;await b.flush({upgrade:c});t==null?void 0:t.addPoint("migration_execute_end");r==null?void 0:r.debug("restore migration complete")}catch(a){b.close();throw a}t==null?void 0:t.addPoint("migration_end");r==null?void 0:r.debug("restore migration end")},"readwrite"])},"migration").catch(function(a){t==null?void 0:t.addAnnotations({string:{migration_error:a.message}});a instanceof d("ReStoreIndexeddbPersistenceInitFailure").ReStoreIndexeddbPersistenceInitFailure||(t==null?void 0:t.endFail("migration_error"));r==null?void 0:r.debug("restore migration error");c("FBLogger")("restore_init").catching(a).mustfix("migration failed");throw a}));async function D(a,b){var c=new WeakMap();await u.runExclusively(function(){return Promise.resolve([async function(){var e=new Map(),f=u.createTransaction("readwrite"),g=F(f,e),h=b.values();await (j||(j=d("PromiseOrValue"))).loop(function(b){if(b.done)return{action:"break",value:void 0};b=b.value;var e=b[0],f=b[1];return(j||(j=d("PromiseOrValue"))).map(j.map(f==null?g.table(a).delete(d("ReStoreUtils").searchKey(q.tableNames[a],e)):g.table(a).put(f),function(){return w.notifyTableAndIndexSubscribers(a,e,f,c)}),function(){return{action:"continue",value:h.next()}})},h.next());try{await f.flush({upgrade:!0})}catch(a){f.close();throw a}},"readwrite"])},"applyChangesToTable")}function E(){u.clearCache==null?void 0:u.clearCache()}function F(a,b,c){return d("ReStoreUtils").createReStoreTransaction(q,v,x,w,a,b,c)}var G;function H(a,b,c,d){return new Promise(function(e,f){p.enableRejectingTransaction&&(G=f);var g=function(){B=d;return a().then(e,f).finally(function(){return B=void 0})};A[b].last==null?A[b].first=A[b].last={value:{code:g,writemode:c}}:A[b].last=A[b].last.next={value:{code:g,writemode:c}}})}function I(a){return u.runExclusively(function(){var a=A.ui.first!=null?A.ui:A.sync_script.first!=null?A.sync_script:A.background;if(a.first==null){c("FBLogger")(p.fbLoggerProject).mustfix("Expected work is missing");return Promise.resolve([function(){return Promise.resolve({})},"readonly"])}var b=a.first;a.first===a.last&&(a.last=null);a.first=a.first.next;return Promise.resolve([b.value.code,b.value.writemode])},a)}async function J(a,b,d,e){var f;d===void 0&&(d="background");e===void 0&&(e={source:"unknown",type:"user_initiated"});var g,h=(f=arguments[4])!=null?f:"<unknown callsite>";h==="<unknown callsite>"&&e.type!=="test_env"&&c("FBLogger")(p.fbLoggerProject).info("Unknown callsite for runInTransaction, action source %s",e.source);e.type==="maw_ui_bridge"&&(h=h+":"+e.events.map(function(a){a=a.tag;return a}).join(","));try{g=await K(a,b,d,e,h)}catch(a){if(a instanceof k)throw c("FBLogger")(p.fbLoggerProject).blameToPreviousFrame().mustfixThrow("ReStore transaction took too long to execute: %s",h);throw a}return g}async function K(a,e,f,g,i){f===void 0&&(f="background");var k,l,n,o,q,r=s==null?void 0:s.startTracking(z,f);b("cr:1088")&&(k=Date.now());r==null?void 0:r.addAnnotations({string:{currentlyExecutingModuleAndLineNo:B,moduleAndLineNo:i}});async function t(){r==null?void 0:r.mark("execute_start");b("cr:1088")&&(l=Date.now());var e={ref:void 0},f=new Map(),m=u.createTransaction("readwrite",i),s;b("cr:1088")==null?void 0:b("cr:1088").trackingStoredProceduresForNewTransaction(g);b("cr:1088")==null?void 0:b("cr:1088").recordModuleName(i);try{var t=F(m,f,{qplFlow:r});(h||(h=c("ExecutionEnvironment"))).isInBrowser?s=await Promise.race([a(t),M(e,r,i)]):s=await a(t);e.ref!=null&&c("clearTimeout")(e.ref);r==null?void 0:r.mark("execute_end");r==null?void 0:r.mark("flush_start");await m.flush({qplFlow:r});r==null?void 0:r.mark("flush_end");b("cr:1088")&&(n=Date.now())}catch(a){e.ref!=null&&c("clearTimeout")(e.ref);m.close();r==null?void 0:r.fail(a);c("FBLogger")(p.fbLoggerProject).catching(a).mustfix("Transaction failed: %s",i);b("cr:1088")==null?void 0:b("cr:1088").recordRestoreTransaction({broadcast:o,complete:n,flush:q,queue:k,start:l},{changedKeys:f});throw a}finally{b("cr:1088")==null?void 0:b("cr:1088").stopTrackingStoredProcedures()}b("cr:1088")&&(q=Date.now());r==null?void 0:r.mark("broadcast_start");var v=new WeakMap();t=new Map();for(e of f){m=e[0];var y=e[1];m=JSON.parse(m);var z=m[0];m[1];m=t.get(z);m==null&&(m=[],t.set(z,m));var A=y[0];y=y[1];y=x.beforeNotify({newValue:y,prevValue:A,tableName:z});A=y.newValue;z=y.prevValue;m.push([z,A])}var B=t.entries();await (j||(j=d("PromiseOrValue"))).loop(function(a){if(a.done)return{action:"break",value:void 0};a=a.value;var b=a[0];a=a[1];var c=a.values();return(j||(j=d("PromiseOrValue"))).map(j.loop(function(a){if(a.done)return{action:"break",value:void 0};a=a.value;var e=a[0];a=a[1];return(j||(j=d("PromiseOrValue"))).map(w.notifyTableAndIndexSubscribers(b,e,a,v),function(){return{action:"continue",value:c.next()}})},c.next()),function(){return{action:"continue",value:B.next()}})},B.next());w.notifyTableV2(t,"notifyTableV2");w.notifyTableV2(t,"notifyInMemoryTable");w.notifyCommit();if(b("cr:1088")){o=Date.now();y={broadcast:o,complete:n,flush:q,queue:k,start:l};b("cr:1088").recordRestoreTransaction(y,{changedKeys:f})}r==null?void 0:r.mark("broadcast_end");r==null?void 0:r.mark("post_commit_start");c("setImmediate")(function(){r==null?void 0:r.mark("post_commit_end"),r==null?void 0:r.end()});return s}await c("ReStoreTransactionDelay")(f);t=H(t,f,e,i);r==null?void 0:r.mark("enqueued");r==null?void 0:r.addAnnotations({string:{actionSourceSource:g.source,actionSourceType:g.type},string_array:{actionSourceEvents:(f=g.events)==null?void 0:f.map(function(a){return a.tag}),actionSourceTaskNames:g.taskNames}});c("promiseDone")(I(i),function(){},function(a){G==null?void 0:G(a)});if(!(h||(h=c("ExecutionEnvironment"))).isInBrowser)return t;var v=c("setTimeout")(function(){var a;c("FBLogger")("messenger_web").mustfix("Transaction not resolved after %s ms. Source: %s; currently processing: %s. Native ops: %s. Executed SP: %s, isInWorker: %s",m,i,B,(a=c("JSONStringifyBigIntSafe")(b("cr:6665")==null?void 0:b("cr:6665").getCurrentlyExecutingNativeOps()))!=null?a:"<devtools not running>",(a=b("cr:1088")==null?void 0:(a=b("cr:1088").getStoredProcedureCallLog())==null?void 0:a.sp.map(function(a){return a.spName}).join(","))!=null?a:"<devtools not running>",(h||(h=c("ExecutionEnvironment"))).isInWorker)},m);return t.finally(function(){return c("clearTimeout")(v)})}(h||(h=c("ExecutionEnvironment"))).isInBrowser&&(n!=null&&n.onEventReceive("notifyInMemoryTable",function(a){c("promiseDone")(async function(){for(var b of a){var c=b[0],d=b[1];e.shouldApplySync("notifyInMemoryTable",c)&&await D(c,d)}w.notifyCommit()}())}));var L=Object.keys(q.tableNames).reduce(function(a,b){return babelHelpers.extends({},a,(a={},a[b]=new(d("ReStoreTable").ReStoreTable)(q.tableNames[b],x,C,y,w),a))},{});return{clearCacheIfSupported:E,closeDb:function(){var a=u;a instanceof Object&&a.close!==void 0&&a.close()},getCustomMigrationVersion:async function(){var a=await new(d("ReStorePersistedMetadata").ReStorePersistedCustomMigrationVersion)().read(u.createTransaction("readonly"));return a==null?void 0:a.customMigrationVersion},getTableData:function(){return q},isClosed:function(){return u.isClosed()},isPersistenceSupported:function(){var a=u;return a instanceof Object&&a.isPersistenceSupported!==void 0&&a.isPersistenceSupported instanceof Function?a.isPersistenceSupported():Promise.resolve(!0)},persistence:e,persistenceTypes:e.types,runInTransaction:J,subscribeToCommit:function(a){return w.subscribeToCommit(a)},subscriptionManager:w,table:function(a){return L[a]},tables:L,uniqueId:e.uniqueId};function M(a,e,f){return!p.enableTransactionTimeout?new Promise(function(){var e=1e4;a.ref=c("setTimeout")(function(){var a;c("FBLogger")(p.fbLoggerProject).mustfix("ReStore transaction took too long to execute, check it: %s. Executed SP: %s. isInWorker: %s.",f,b("cr:1088")==null?void 0:(a=b("cr:1088").getStoredProcedureCallLog())==null?void 0:a.sp.map(function(a){return a.spName}).join(","),(h||(h=c("ExecutionEnvironment"))).isInWorker);(i||(i=d("ODS"))).bumpEntityKey(p.odsCategoryID,"messenger.restore.transaction","timeout_"+(f||"").replace(/:/g,"_"),1)},e)}):new Promise(function(b,d){b=5e3;a.ref=c("setTimeout")(function(){e==null?void 0:e.mark("slow_transaction"),d(new k())},b)})}}g.default=a}),98);
__d("ReStoreInstrumentation",["JSONStringifyBigIntSafe"],(function(a,b,c,d,e,f,g){"use strict";var h=new Map();function a(a){h.set(a.uniqueId,{instance:a,persistence:a.persistence,tableNames:a.getTableData().tableNames});return a}function b(){return Array.from(h.entries()).map(function(a){var b=a[0];a=a[1];return{key:b,value:JSON.parse(c("JSONStringifyBigIntSafe")(a.tableNames))}})}g.instrument=a;g.getReStoreTables=b;g.reStoreInstancesMap=h}),98);
__d("LSReStoreWrapper",["LSReStoreQplLogger","MessengerLogHistory","ReStore","ReStoreInstrumentation","gkx","qpl"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,e,f,g){e===void 0&&(e=[]);f=d("ReStoreInstrumentation").instrument(c("ReStore")({config:{enableRejectingTransaction:c("gkx")("8566"),enableTransactionTimeout:c("gkx")("2947"),fbLoggerProject:"maw_db",odsCategoryID:3185},customMigration:f,hooks:e,loggers:{logHistory:d("MessengerLogHistory").getInstance("db_init"),transactionQplLogger:d("LSReStoreQplLogger").createQplLogger(c("qpl")._(25306500,"1488")),userFlow:g},persistence:a,schema:b}));return f}g.createLSReStore=a}),98);
__d("ReStoreVaulting",["cr:2075","cr:2151","cr:2203"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return b("cr:2075")!=null?b("cr:2075").vault(a):a}function c(a){return b("cr:2075")!=null?b("cr:2075").unvault(a):a}function d(a,c){return b("cr:2151")!=null&&b("cr:2203")!=null?b("cr:2151").unvaultDbRow(a,c,b("cr:2203").PERSISTED_DB_VAULT_DEFINITIONS):a}function e(a,c){return b("cr:2151")!=null&&b("cr:2203")!=null?b("cr:2151").vaultDbRow(a,c,b("cr:2203").PERSISTED_DB_VAULT_DEFINITIONS):a}g.maybeVault=a;g.maybeUnvault=c;g.maybeUnvaultDbRow=d;g.maybeVaultDbRow=e}),98);
__d("MAWLSVaultingHooks",["ReStoreVaulting"],(function(a,b,c,d,e,f,g){"use strict";a={onAfterEntriesNext:function(a){var b=a.tableName;a=a.value;return d("ReStoreVaulting").maybeVaultDbRow(a,b)},onAfterGet:function(a){var b=a.tableName;a=a.value;return d("ReStoreVaulting").maybeVaultDbRow(a,b)},onBeforeAdd:function(a){var b=a.tableName;a=a.value;return d("ReStoreVaulting").maybeUnvaultDbRow(a,b)},onBeforeNotify:function(a){var b=a.newValue,c=a.prevValue;a=a.tableName;return{newValue:d("ReStoreVaulting").maybeVaultDbRow(b,a),prevValue:d("ReStoreVaulting").maybeVaultDbRow(c,a)}},onBeforePut:function(a){var b=a.tableName;a=a.value;return d("ReStoreVaulting").maybeUnvaultDbRow(a,b)},onBeforeUpsert:function(a){var b=a.tableName;a=a.value;return d("ReStoreVaulting").maybeUnvaultDbRow(a,b)}};g["default"]=a}),98);
__d("createReStoreEphemeralPersistence",["FBLogger","Promise","ReStorePersistence","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(a,b,c){return(a.has(b)?a:a.set(b,c())).get(b)}function a(a,e){var f=(h||(h=b("Promise"))).resolve(),g=new Map();function j(a){return i(g,a,function(){return new Map()})}return{flush:function(a){a.forEach(function(a,b){var c=j(b);a.forEach(function(a,b){d("ReStorePersistence").isDeletedValue(a)?c["delete"](b):c.set(b,a)})});return(h||(h=b("Promise"))).resolve()},get:function(a,b,c){a=j(b);return a.get(c)},logError:function(a,b,d,e){if(d==="dbCorruption"){throw c("FBLogger")("messenger_web").mustfixThrow("Got unexpected undefined in edb, mode: %s, table: %s, id: %s, deletedInThisTxn: %s",b,a,(d=e==null?void 0:e.id)!=null?d:"",(b=e==null?void 0:e.deletedInThisTxn)!=null?b:"")}},permitsSynchronousIO:function(a){return!0},queueCommitWork:void 0,runExclusively:function(a){var c=e==null?void 0:e.startTracking(!1);c==null?void 0:c.addAnnotations({string:{source:"createReStoreEphemeralPersistence",type:"runExclusivelyPromiseChain"}});return new(h||(h=b("Promise")))(function(d,e){f=f.then(b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var b=(yield a()),f=b[0];b[1];try{d(yield f()),c==null?void 0:c.end()}catch(a){e(a),c==null?void 0:c.fail()}}))["catch"](function(a){e(a)})})},shouldApplySync:function(){return!1},shouldInline:function(a,b){return!0},shouldSync:function(){return!1},types:["ephemeral"],uniqueId:a}}g["default"]=a}),98);
__d("createLSReStoreEphemeralPersistence",["LSReStoreQplLogger","MAWCurrentUser","createReStoreEphemeralPersistence","qpl"],(function(a,b,c,d,e,f,g){"use strict";function a(){var a=d("MAWCurrentUser").getID();return c("createReStoreEphemeralPersistence")("LSEphemeral "+a,d("LSReStoreQplLogger").createQplLogger(c("qpl")._(25303045,"817")))}g.createLSReStoreEphemeralPersistence=a}),98);
__d("LSJSInMemoryStorage",["LSPlatformLsInitLog","LSReStoreWrapper","MAWLSVaultingHooks","createLSReStoreEphemeralPersistence"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return d("LSReStoreWrapper").createLSReStore(d("createLSReStoreEphemeralPersistence").createLSReStoreEphemeralPersistence(),a,[c("MAWLSVaultingHooks")],void 0,d("LSPlatformLsInitLog").lsInitLogger)}g["default"]=a}),98);
__d("LSMailboxInitialSyncCursor",["I64","LSPlatformMessengerSyncParams","LSPlatformWorkplaceSyncParams","gkx","qex"],(function(a,b,c,d,e,f,g){"use strict";var h;a=c("gkx")("23433")||c("gkx")("23434");b=c("gkx")("20836")?c("LSPlatformWorkplaceSyncParams").mailbox:c("LSPlatformMessengerSyncParams").mailbox;e=(h||(h=d("I64"))).of_int32(a&&(c("qex")._("422")===!0||c("qex")._("4331")===!0)?3:1);f=h.of_int32(a&&(c("qex")._("422")===!0||c("qex")._("4375")===!0)?3:1);d=h.of_int32(0);g.syncParams=b;g.taskChannel=e;g.syncChannel=f;g.lastSyncTimestampMs=d}),98);
__d("ReStoreVersionedSchemaProvider",["FBLogger","Promise","ReStoreDefaultValueMigration","ReStoreIndicesMigration","ReStoreMigrateUtil","ReStorePersistedMetadata","ReStoreVersionedSchemaProviderUtil","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h;a=function(){function a(a,b,c){this.defaults={};this.$1="";this.$1=a;this.$2=b;a=b.versions[b.targetVersion];b=d("ReStoreVersionedSchemaProviderUtil").getTableData(a.tables,c);a=b.defaults;c=b.tableData;this.tableNames=c.tableNames;this.tableIds=c.tableIds;this.defaults=a}var e=a.prototype;e.$3=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var c,e;(c=b.loggers.logHistory)==null?void 0:c.debug("["+this.$1+"][ReStoreVersionedSchemaProvider] Migrating from scratch...");(c=b.loggers.userFlow)==null?void 0:c.addPoint(this.$1+"_migration_versioned_from_scratch");c={tableIds:this.tableIds,tableNames:this.tableNames};yield d("ReStoreDefaultValueMigration").runMigrationForTableDefaultValuesIfNeeded(b.transaction,c,a,this.defaults);(e=b.loggers.logHistory)==null?void 0:e.debug("migration default values finished");yield d("ReStoreIndicesMigration").runMigrationForIndicesIfNeeded(b,c,this.defaults,a);(e=b.loggers.logHistory)==null?void 0:e.debug("migration indices finished");return a?d("ReStoreMigrateUtil").ReStoreMigrateResult.UPDATE_METADATA:d("ReStoreMigrateUtil").ReStoreMigrateResult.UPGRADE_COMPLETE});function c(b,c){return a.apply(this,arguments)}return c}();e.$4=function(a){var d=this,e=this.$2.versionOrder.indexOf(a);if(e===-1)throw c("FBLogger")("ReStoreVersionedSchemaProvider").mustfixThrow("[%s][ReStoreVersionedSchemaProvider] Cannot resolve version %s",this.$1,a);return(h||(h=b("Promise"))).all(this.$2.versionOrder.slice(e,this.$2.versionOrder.indexOf(this.$2.targetVersion)+1).map(function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){if(a in d.$2.versions)return{meta:d.$2.versions[a],version:a};else if(a in d.$2.legacyVersions)return{meta:yield d.$2.legacyVersions[a].load(),version:a};else throw c("FBLogger")("ReStoreVersionedSchemaProvider").mustfixThrow("[%s][ReStoreVersionedSchemaProvider] Cannot find version %s in either versions or legacyVersions",d.$1,a)});return function(b){return a.apply(this,arguments)}}()))};e.$5=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e){var f,g=this.$2.targetVersion,h=this.$2.versions[g].revision;(f=e.loggers)==null?void 0:(f=f.logHistory)==null?void 0:f.debug("["+this.$1+"][ReStoreVersionedSchemaProvider] currentVersion: "+a+"R"+b+", targetVersion: "+g+"R"+h);if(g===a&&h===b){(f=e.loggers)==null?void 0:(f=f.logHistory)==null?void 0:f.debug("["+this.$1+"][ReStoreVersionedSchemaProvider] DB already up to date, skipping migration");return d("ReStoreMigrateUtil").ReStoreMigrateResult.NO_UPGRADE_NEEDED}(f=e.loggers.logHistory)==null?void 0:f.debug("migration versioned incremental");(f=e.loggers.userFlow)==null?void 0:f.addPoint(this.$1+"_migration_versioned_incremental");f=(yield this.$4(a));if(f.length===0||b>h){c("FBLogger")("ReStoreVersionedSchemaProvider","migrate").mustfix("[%s][ReStoreVersionedSchemaProvider][DANGEROUS] Backward migration required from %sR%s to %sR%s, running #migrateFromScratch",this.$1,a,""+b,g,""+h);return this.$3(!1,e)}for(g of f){h=g.meta;f=g.version;for(var i=f===a?b+1:0;i<=h.revision;i++){var j;(j=e.loggers)==null?void 0:(j=j.logHistory)==null?void 0:j.debug("["+this.$1+"][ReStoreVersionedSchemaProvider] Migrating to "+f+"R"+i);(j=e.loggers.userFlow)==null?void 0:j.addPoint(this.$1+"_migration_versioned_"+f+"{$revision}_start");yield h.upgrade[i]==null?void 0:h.upgrade[i](e);yield h.afterUpgrade[i]==null?void 0:h.afterUpgrade[i](e.transaction);(j=e.loggers.userFlow)==null?void 0:j.addPoint(this.$1+"_migration_versioned_"+f+"{$revision}_end")}}return d("ReStoreMigrateUtil").ReStoreMigrateResult.UPGRADE_COMPLETE});function e(b,c,d){return a.apply(this,arguments)}return e}();e.$6=function(a,c,e,f,g){var i;(i=g.loggers.userFlow)==null?void 0:i.addPoint(this.$1+"_migration_versioned_hash_start");if(c===e&&a===f){(i=g.loggers.userFlow)==null?void 0:i.addAnnotations(d("ReStoreMigrateUtil").prefixAnnotations(this.$1,{string:{fromHashSchemaProvider:"no_changes"}}));(c=g.loggers)==null?void 0:(e=c.logHistory)==null?void 0:e.debug("["+this.$1+"][ReStoreVersionedSchemaProvider] Migrating from Hash schema provider, no changes are detected");return(h||(h=b("Promise"))).resolve(d("ReStoreMigrateUtil").ReStoreMigrateResult.UPDATE_METADATA)}else{(a=g.loggers.userFlow)==null?void 0:a.addAnnotations(d("ReStoreMigrateUtil").prefixAnnotations(this.$1,{string:{fromHashSchemaProvider:"changes"}}));(f=g.loggers)==null?void 0:(i=f.logHistory)==null?void 0:i.debug("["+this.$1+"][ReStoreVersionedSchemaProvider] Migrating from Hash schema provider, changed detected, calling #migrateFromScratch");return this.$3(!1,g)}};e.migrate=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b,c,e;(b=a.loggers.logHistory)==null?void 0:b.debug("["+this.$1+"][ReStoreVersionedSchemaProvider] Start migration");(b=a.loggers.userFlow)==null?void 0:b.addPoint(this.$1+"_migration_versioned_start");b=d("ReStorePersistedMetadata").ReStorePersistedDefaultValuesVersion.hash(this.defaults);var f=d("ReStorePersistedMetadata").ReStorePersistedSchemaVersion.hash({tableIds:this.tableIds,tableNames:this.tableNames}),g=new(d("ReStorePersistedMetadata").ReStorePersistedSchemaVersion)(this.$1),h=new(d("ReStorePersistedMetadata").ReStorePersistedDefaultValuesVersion)(this.$1),i=(yield g.read(a.storeTx));c=(c=(yield h.read(a.storeTx)))==null?void 0:c.defaultValuesVersion;var j=this.$2.versions[this.$2.targetVersion];(e=a.loggers.userFlow)==null?void 0:e.addAnnotations(d("ReStoreMigrateUtil").prefixAnnotations(this.$1,{"int":{defaultValuesVersion:b,schemaVersion:f,storeDefaultValuesVersion:c,storeRevision:i==null?void 0:i.revision,storeSchemaVersion:i==null?void 0:i.schemaVersion,targetRevision:j.revision},string:{schemaProviderType:"versioned",storeVersion:i==null?void 0:i.version,targetVersion:this.$2.targetVersion}}));(e=a.loggers.logHistory)==null?void 0:e.debug("storeSchemaVersion: "+((e=i==null?void 0:i.schemaVersion)!=null?e:"null"));(e=a.loggers.logHistory)==null?void 0:e.debug("storeDefaultValuesVersion: "+((e=c)!=null?e:"null"));e=(yield i!=null&&i.revision!=null&&i.version!=null?this.$5(i.version,i.revision,a):(i==null?void 0:i.schemaVersion)!=null?this.$6(c,i.schemaVersion,f,b,a):this.$3(!0,a));if(e!==d("ReStoreMigrateUtil").ReStoreMigrateResult.NO_UPGRADE_NEEDED){g.write(a.storeTx,{revision:j.revision,schemaVersion:f,version:this.$2.targetVersion});h.write(a.storeTx,{defaultValuesVersion:b});(c=a.loggers.logHistory)==null?void 0:c.debug("version is written")}(i=a.loggers.logHistory)==null?void 0:i.debug("["+this.$1+"][ReStoreVersionedSchemaProvider] End Migration. Result: "+e+", SchemaVersion: "+f+", Version: "+this.$2.targetVersion+", Revision: "+j.revision);(g=a.loggers.userFlow)==null?void 0:g.addPoint(this.$1+"_migration_versioned_end");return e});function c(b){return a.apply(this,arguments)}return c}();return a}();g["default"]=a}),98);
__d("LSVersionedSchemaProvider",["LSConstants","LSDb","LSDbForeignKeys","ReStoreVersionedSchemaProvider"],(function(a,b,c,d,e,f,g){"use strict";a=new(c("ReStoreVersionedSchemaProvider"))(d("LSConstants").LS_SCHEMA_NAME,d("LSDb").Versions,c("LSDbForeignKeys"));g.schemaProvider=a}),98);
__d("ReStoreHashSchemaProvider",["ReStoreHashMigration","ReStoreMetadata","ReStoreMigrateUtil","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b,c){var e=b.tableIds;b=b.tableNames;c===void 0&&(c={});this.$1=a;this.tableNames=babelHelpers["extends"]({},b,d("ReStoreMetadata").RESTORE_METADATA_TABLES);this.tableIds=e;this.defaults=(a=c)!=null?a:{}}var c=a.prototype;c.migrate=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b,c;(b=a.loggers.logHistory)==null?void 0:b.debug("["+this.$1+"][ReStoreHashSchemaProvider] Start migration");(b=a.loggers.userFlow)==null?void 0:b.addAnnotations(d("ReStoreMigrateUtil").prefixAnnotations(this.$1,{string:{schemaProviderType:"hash"}}));(b=a.loggers.userFlow)==null?void 0:b.addPoint(this.$1+"_migration_hash_start");b=(yield d("ReStoreHashMigration").runHashMigration(this.$1,{tableIds:this.tableIds,tableNames:this.tableNames},this.defaults,a));(c=a.loggers.logHistory)==null?void 0:c.debug("["+this.$1+"][ReStoreHashSchemaProvider] End migration. Result: "+b);(c=a.loggers.userFlow)==null?void 0:c.addPoint(this.$1+"_migration_hash_end");return b});function c(b){return a.apply(this,arguments)}return c}();return a}();g["default"]=a}),98);
__d("ReStoreSchemaProvider",["ReStoreMigrateUtil","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";function a(a,c){return{defaults:babelHelpers["extends"]({},a.defaults,c.defaults),migrate:function(){var e=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){var e=(yield a.migrate(b));b=(yield c.migrate(b));if(e===d("ReStoreMigrateUtil").ReStoreMigrateResult.UPGRADE_COMPLETE||b===d("ReStoreMigrateUtil").ReStoreMigrateResult.UPGRADE_COMPLETE)return d("ReStoreMigrateUtil").ReStoreMigrateResult.UPGRADE_COMPLETE;else if(e===d("ReStoreMigrateUtil").ReStoreMigrateResult.UPDATE_METADATA||b===d("ReStoreMigrateUtil").ReStoreMigrateResult.UPDATE_METADATA)return d("ReStoreMigrateUtil").ReStoreMigrateResult.UPDATE_METADATA;else return d("ReStoreMigrateUtil").ReStoreMigrateResult.NO_UPGRADE_NEEDED});function f(a){return e.apply(this,arguments)}return f}(),tableIds:babelHelpers["extends"]({},a.tableIds,c.tableIds),tableNames:babelHelpers["extends"]({},a.tableNames,c.tableNames)}}g.mergeSchemaProviders=a}),98);
__d("LSMetadata",["LSClientSchemaType","LSDbSchema","LSVersionedSchemaProvider","ReStoreHashSchemaProvider","ReStoreMetadata","ReStoreSchemaProvider"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return d("ReStoreSchemaProvider").mergeSchemaProviders(a,h)}var h=new(c("ReStoreHashSchemaProvider"))(d("LSClientSchemaType").MAW_SCHEMA_NAME,d("ReStoreMetadata").getBuildTableData({})([d("LSDbSchema").clientSchema]));b=a(d("LSVersionedSchemaProvider").schemaProvider);g.buildSchema=a;g.schema=b}),98);
__d("XPlatReactEventEmitter",["BaseEventEmitter"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("BaseEventEmitter")}),98);
__d("LSPlatformErrorChannel",["FBLogger","ODS","XPlatReactEventEmitter"],(function(a,b,c,d,e,f,g){"use strict";var h,i="onLSError",j=new(c("XPlatReactEventEmitter"))(),k=0,l=null;function a(a){l===null&&((h||(h=d("ODS"))).bumpEntityKey(3185,"lsplatform_error","first_error_emitted_to_"+k+"listeners"),k===0&&c("FBLogger")("messenger_web").mustfix("No listeners when emitting LS error %s",a.name)),l=a,j.emit(i,a)}function b(a){var b=j.addListener(i,a);k++;return function(){return b.remove()}}function e(){return l}function f(){j.removeAllListeners(i),l=null}f={TEST_ONLY_clear:f,emit:a,lastError:e,subscribe:b};g["default"]=f}),98);
__d("WAArrayBufferUtils",["Promise","err"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){var b=new ArrayBuffer(a.length);b=new Uint8Array(b);var c,d;for(c=0,d=a.length;c<d;c++)b[c]=a.charCodeAt(c);return b.buffer}function d(a,c){c===void 0&&(c=5e5);return new(h||(h=b("Promise")))(function(b,d){var e=a.length,f=new ArrayBuffer(e),g=new Uint8Array(f),h=0;setTimeout(i,0);function i(){var d=Math.min(h+c,e);while(h<d)g[h]=a.charCodeAt(h),h++;if(h===e){b(f);return}setTimeout(i,16)}})}function e(a){return String.fromCharCode.apply(null,new Uint8Array(a))}var i=8388607;function f(a,b){a=new Uint8Array(a);if(b>i)throw c("err")("Divisor is too big");var d=0;for(var e=0;e<a.length;++e)d=((d<<8)+a[e])%b;return d}function j(a){a=a.map(function(a){return new Uint8Array(a)});var b=a.reduce(function(a,b){return a+b.byteLength},0),c=new Uint8Array(b);a.reduce(function(a,b){c.set(b,a);return a+b.byteLength},0);return c.buffer.slice(c.byteOffset,c.byteOffset+c.byteLength)}function k(a,b){a=new Uint8Array(a);b=new Uint8Array(b);return l(a,b)}function l(a,b){if(a.byteLength!==b.byteLength)return!1;for(var c=0;c!==a.byteLength;c++)if(a[c]!==b[c])return!1;return!0}g.stringToArrayBuffer=a;g.largeStringToArrayBuffer=d;g.arrayBufferToString=e;g.arrayBufferMod=f;g.concatBuffers=j;g.arrayBuffersEqualUNSAFE=k;g.uint8ArraysEqualUNSAFE=l}),98);
/**
 * License: https://www.facebook.com/legal/license/OKBVmODmb-W/
 */
__d("tweetnacl-1.0.3",[],(function(a,b,c,d,e,f){"use strict";b={};var g={exports:b};function h(){(function(a){var b=function(a){var b,c=new Float64Array(16);if(a)for(b=0;b<a.length;b++)c[b]=a[b];return c},c=function(){throw new Error("no PRNG")},d=new Uint8Array(16),e=new Uint8Array(32);e[0]=9;var f=b(),g=b([1]),h=b([56129,1]),i=b([30883,4953,19914,30187,55467,16705,2637,112,59544,30585,16505,36039,65139,11119,27886,20995]),j=b([61785,9906,39828,60374,45398,33411,5274,224,53552,61171,33010,6542,64743,22239,55772,9222]),k=b([54554,36645,11616,51542,42930,38181,51040,26924,56412,64982,57905,49316,21502,52590,14035,8553]),l=b([26200,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214]),m=b([41136,18958,6951,50414,58488,44335,6150,12099,55207,15867,153,11085,57099,20417,9344,11139]);function n(a,b,c,d){a[b]=c>>24&255,a[b+1]=c>>16&255,a[b+2]=c>>8&255,a[b+3]=c&255,a[b+4]=d>>24&255,a[b+5]=d>>16&255,a[b+6]=d>>8&255,a[b+7]=d&255}function o(a,b,c,d,e){var f,g=0;for(f=0;f<e;f++)g|=a[b+f]^c[d+f];return(1&g-1>>>8)-1}function p(a,b,c,d){return o(a,b,c,d,16)}function q(a,b,c,d){return o(a,b,c,d,32)}function r(a,b,c,d){var e=d[0]&255|(d[1]&255)<<8|(d[2]&255)<<16|(d[3]&255)<<24,f=c[0]&255|(c[1]&255)<<8|(c[2]&255)<<16|(c[3]&255)<<24,g=c[4]&255|(c[5]&255)<<8|(c[6]&255)<<16|(c[7]&255)<<24,h=c[8]&255|(c[9]&255)<<8|(c[10]&255)<<16|(c[11]&255)<<24,i=c[12]&255|(c[13]&255)<<8|(c[14]&255)<<16|(c[15]&255)<<24,j=d[4]&255|(d[5]&255)<<8|(d[6]&255)<<16|(d[7]&255)<<24,k=b[0]&255|(b[1]&255)<<8|(b[2]&255)<<16|(b[3]&255)<<24,l=b[4]&255|(b[5]&255)<<8|(b[6]&255)<<16|(b[7]&255)<<24,m=b[8]&255|(b[9]&255)<<8|(b[10]&255)<<16|(b[11]&255)<<24;b=b[12]&255|(b[13]&255)<<8|(b[14]&255)<<16|(b[15]&255)<<24;var n=d[8]&255|(d[9]&255)<<8|(d[10]&255)<<16|(d[11]&255)<<24,o=c[16]&255|(c[17]&255)<<8|(c[18]&255)<<16|(c[19]&255)<<24,p=c[20]&255|(c[21]&255)<<8|(c[22]&255)<<16|(c[23]&255)<<24,q=c[24]&255|(c[25]&255)<<8|(c[26]&255)<<16|(c[27]&255)<<24;c=c[28]&255|(c[29]&255)<<8|(c[30]&255)<<16|(c[31]&255)<<24;d=d[12]&255|(d[13]&255)<<8|(d[14]&255)<<16|(d[15]&255)<<24;var r=e,s=f,t=g,u=h,v=i,w=j,x=k,y=l,z=m,A=b,B=n,C=o,D=p,E=q,F=c,G=d,H;for(var I=0;I<20;I+=2)H=r+D|0,v^=H<<7|H>>>32-7,H=v+r|0,z^=H<<9|H>>>32-9,H=z+v|0,D^=H<<13|H>>>32-13,H=D+z|0,r^=H<<18|H>>>32-18,H=w+s|0,A^=H<<7|H>>>32-7,H=A+w|0,E^=H<<9|H>>>32-9,H=E+A|0,s^=H<<13|H>>>32-13,H=s+E|0,w^=H<<18|H>>>32-18,H=B+x|0,F^=H<<7|H>>>32-7,H=F+B|0,t^=H<<9|H>>>32-9,H=t+F|0,x^=H<<13|H>>>32-13,H=x+t|0,B^=H<<18|H>>>32-18,H=G+C|0,u^=H<<7|H>>>32-7,H=u+G|0,y^=H<<9|H>>>32-9,H=y+u|0,C^=H<<13|H>>>32-13,H=C+y|0,G^=H<<18|H>>>32-18,H=r+u|0,s^=H<<7|H>>>32-7,H=s+r|0,t^=H<<9|H>>>32-9,H=t+s|0,u^=H<<13|H>>>32-13,H=u+t|0,r^=H<<18|H>>>32-18,H=w+v|0,x^=H<<7|H>>>32-7,H=x+w|0,y^=H<<9|H>>>32-9,H=y+x|0,v^=H<<13|H>>>32-13,H=v+y|0,w^=H<<18|H>>>32-18,H=B+A|0,C^=H<<7|H>>>32-7,H=C+B|0,z^=H<<9|H>>>32-9,H=z+C|0,A^=H<<13|H>>>32-13,H=A+z|0,B^=H<<18|H>>>32-18,H=G+F|0,D^=H<<7|H>>>32-7,H=D+G|0,E^=H<<9|H>>>32-9,H=E+D|0,F^=H<<13|H>>>32-13,H=F+E|0,G^=H<<18|H>>>32-18;r=r+e|0;s=s+f|0;t=t+g|0;u=u+h|0;v=v+i|0;w=w+j|0;x=x+k|0;y=y+l|0;z=z+m|0;A=A+b|0;B=B+n|0;C=C+o|0;D=D+p|0;E=E+q|0;F=F+c|0;G=G+d|0;a[0]=r>>>0&255;a[1]=r>>>8&255;a[2]=r>>>16&255;a[3]=r>>>24&255;a[4]=s>>>0&255;a[5]=s>>>8&255;a[6]=s>>>16&255;a[7]=s>>>24&255;a[8]=t>>>0&255;a[9]=t>>>8&255;a[10]=t>>>16&255;a[11]=t>>>24&255;a[12]=u>>>0&255;a[13]=u>>>8&255;a[14]=u>>>16&255;a[15]=u>>>24&255;a[16]=v>>>0&255;a[17]=v>>>8&255;a[18]=v>>>16&255;a[19]=v>>>24&255;a[20]=w>>>0&255;a[21]=w>>>8&255;a[22]=w>>>16&255;a[23]=w>>>24&255;a[24]=x>>>0&255;a[25]=x>>>8&255;a[26]=x>>>16&255;a[27]=x>>>24&255;a[28]=y>>>0&255;a[29]=y>>>8&255;a[30]=y>>>16&255;a[31]=y>>>24&255;a[32]=z>>>0&255;a[33]=z>>>8&255;a[34]=z>>>16&255;a[35]=z>>>24&255;a[36]=A>>>0&255;a[37]=A>>>8&255;a[38]=A>>>16&255;a[39]=A>>>24&255;a[40]=B>>>0&255;a[41]=B>>>8&255;a[42]=B>>>16&255;a[43]=B>>>24&255;a[44]=C>>>0&255;a[45]=C>>>8&255;a[46]=C>>>16&255;a[47]=C>>>24&255;a[48]=D>>>0&255;a[49]=D>>>8&255;a[50]=D>>>16&255;a[51]=D>>>24&255;a[52]=E>>>0&255;a[53]=E>>>8&255;a[54]=E>>>16&255;a[55]=E>>>24&255;a[56]=F>>>0&255;a[57]=F>>>8&255;a[58]=F>>>16&255;a[59]=F>>>24&255;a[60]=G>>>0&255;a[61]=G>>>8&255;a[62]=G>>>16&255;a[63]=G>>>24&255}function s(a,b,c,d){var e=d[0]&255|(d[1]&255)<<8|(d[2]&255)<<16|(d[3]&255)<<24,f=c[0]&255|(c[1]&255)<<8|(c[2]&255)<<16|(c[3]&255)<<24,g=c[4]&255|(c[5]&255)<<8|(c[6]&255)<<16|(c[7]&255)<<24,h=c[8]&255|(c[9]&255)<<8|(c[10]&255)<<16|(c[11]&255)<<24,i=c[12]&255|(c[13]&255)<<8|(c[14]&255)<<16|(c[15]&255)<<24,j=d[4]&255|(d[5]&255)<<8|(d[6]&255)<<16|(d[7]&255)<<24,k=b[0]&255|(b[1]&255)<<8|(b[2]&255)<<16|(b[3]&255)<<24,l=b[4]&255|(b[5]&255)<<8|(b[6]&255)<<16|(b[7]&255)<<24,m=b[8]&255|(b[9]&255)<<8|(b[10]&255)<<16|(b[11]&255)<<24;b=b[12]&255|(b[13]&255)<<8|(b[14]&255)<<16|(b[15]&255)<<24;var n=d[8]&255|(d[9]&255)<<8|(d[10]&255)<<16|(d[11]&255)<<24,o=c[16]&255|(c[17]&255)<<8|(c[18]&255)<<16|(c[19]&255)<<24,p=c[20]&255|(c[21]&255)<<8|(c[22]&255)<<16|(c[23]&255)<<24,q=c[24]&255|(c[25]&255)<<8|(c[26]&255)<<16|(c[27]&255)<<24;c=c[28]&255|(c[29]&255)<<8|(c[30]&255)<<16|(c[31]&255)<<24;d=d[12]&255|(d[13]&255)<<8|(d[14]&255)<<16|(d[15]&255)<<24;e=e;f=f;g=g;h=h;i=i;j=j;k=k;l=l;m=m;b=b;n=n;o=o;p=p;q=q;c=c;d=d;var r;for(var s=0;s<20;s+=2)r=e+p|0,i^=r<<7|r>>>32-7,r=i+e|0,m^=r<<9|r>>>32-9,r=m+i|0,p^=r<<13|r>>>32-13,r=p+m|0,e^=r<<18|r>>>32-18,r=j+f|0,b^=r<<7|r>>>32-7,r=b+j|0,q^=r<<9|r>>>32-9,r=q+b|0,f^=r<<13|r>>>32-13,r=f+q|0,j^=r<<18|r>>>32-18,r=n+k|0,c^=r<<7|r>>>32-7,r=c+n|0,g^=r<<9|r>>>32-9,r=g+c|0,k^=r<<13|r>>>32-13,r=k+g|0,n^=r<<18|r>>>32-18,r=d+o|0,h^=r<<7|r>>>32-7,r=h+d|0,l^=r<<9|r>>>32-9,r=l+h|0,o^=r<<13|r>>>32-13,r=o+l|0,d^=r<<18|r>>>32-18,r=e+h|0,f^=r<<7|r>>>32-7,r=f+e|0,g^=r<<9|r>>>32-9,r=g+f|0,h^=r<<13|r>>>32-13,r=h+g|0,e^=r<<18|r>>>32-18,r=j+i|0,k^=r<<7|r>>>32-7,r=k+j|0,l^=r<<9|r>>>32-9,r=l+k|0,i^=r<<13|r>>>32-13,r=i+l|0,j^=r<<18|r>>>32-18,r=n+b|0,o^=r<<7|r>>>32-7,r=o+n|0,m^=r<<9|r>>>32-9,r=m+o|0,b^=r<<13|r>>>32-13,r=b+m|0,n^=r<<18|r>>>32-18,r=d+c|0,p^=r<<7|r>>>32-7,r=p+d|0,q^=r<<9|r>>>32-9,r=q+p|0,c^=r<<13|r>>>32-13,r=c+q|0,d^=r<<18|r>>>32-18;a[0]=e>>>0&255;a[1]=e>>>8&255;a[2]=e>>>16&255;a[3]=e>>>24&255;a[4]=j>>>0&255;a[5]=j>>>8&255;a[6]=j>>>16&255;a[7]=j>>>24&255;a[8]=n>>>0&255;a[9]=n>>>8&255;a[10]=n>>>16&255;a[11]=n>>>24&255;a[12]=d>>>0&255;a[13]=d>>>8&255;a[14]=d>>>16&255;a[15]=d>>>24&255;a[16]=k>>>0&255;a[17]=k>>>8&255;a[18]=k>>>16&255;a[19]=k>>>24&255;a[20]=l>>>0&255;a[21]=l>>>8&255;a[22]=l>>>16&255;a[23]=l>>>24&255;a[24]=m>>>0&255;a[25]=m>>>8&255;a[26]=m>>>16&255;a[27]=m>>>24&255;a[28]=b>>>0&255;a[29]=b>>>8&255;a[30]=b>>>16&255;a[31]=b>>>24&255}function t(a,b,c,d){r(a,b,c,d)}function u(a,b,c,d){s(a,b,c,d)}var v=new Uint8Array([101,120,112,97,110,100,32,51,50,45,98,121,116,101,32,107]);function w(a,b,c,d,e,f,g){var h=new Uint8Array(16),i=new Uint8Array(64),j;for(j=0;j<16;j++)h[j]=0;for(j=0;j<8;j++)h[j]=f[j];while(e>=64){t(i,h,g,v);for(j=0;j<64;j++)a[b+j]=c[d+j]^i[j];f=1;for(j=8;j<16;j++)f=f+(h[j]&255)|0,h[j]=f&255,f>>>=8;e-=64;b+=64;d+=64}if(e>0){t(i,h,g,v);for(j=0;j<e;j++)a[b+j]=c[d+j]^i[j]}return 0}function x(a,b,c,d,e){var f=new Uint8Array(16),g=new Uint8Array(64),h;for(h=0;h<16;h++)f[h]=0;for(h=0;h<8;h++)f[h]=d[h];while(c>=64){t(g,f,e,v);for(h=0;h<64;h++)a[b+h]=g[h];d=1;for(h=8;h<16;h++)d=d+(f[h]&255)|0,f[h]=d&255,d>>>=8;c-=64;b+=64}if(c>0){t(g,f,e,v);for(h=0;h<c;h++)a[b+h]=g[h]}return 0}function y(a,b,c,d,e){var f=new Uint8Array(32);u(f,d,e,v);e=new Uint8Array(8);for(var g=0;g<8;g++)e[g]=d[g+16];return x(a,b,c,e,f)}function z(a,b,c,d,e,f,g){var h=new Uint8Array(32);u(h,f,g,v);g=new Uint8Array(8);for(var i=0;i<8;i++)g[i]=f[i+16];return w(a,b,c,d,e,g,h)}var A=function(a){this.buffer=new Uint8Array(16);this.r=new Uint16Array(10);this.h=new Uint16Array(10);this.pad=new Uint16Array(8);this.leftover=0;this.fin=0;var b,c;b=a[0]&255|(a[1]&255)<<8;this.r[0]=b&8191;c=a[2]&255|(a[3]&255)<<8;this.r[1]=(b>>>13|c<<3)&8191;b=a[4]&255|(a[5]&255)<<8;this.r[2]=(c>>>10|b<<6)&7939;c=a[6]&255|(a[7]&255)<<8;this.r[3]=(b>>>7|c<<9)&8191;b=a[8]&255|(a[9]&255)<<8;this.r[4]=(c>>>4|b<<12)&255;this.r[5]=b>>>1&8190;c=a[10]&255|(a[11]&255)<<8;this.r[6]=(b>>>14|c<<2)&8191;b=a[12]&255|(a[13]&255)<<8;this.r[7]=(c>>>11|b<<5)&8065;c=a[14]&255|(a[15]&255)<<8;this.r[8]=(b>>>8|c<<8)&8191;this.r[9]=c>>>5&127;this.pad[0]=a[16]&255|(a[17]&255)<<8;this.pad[1]=a[18]&255|(a[19]&255)<<8;this.pad[2]=a[20]&255|(a[21]&255)<<8;this.pad[3]=a[22]&255|(a[23]&255)<<8;this.pad[4]=a[24]&255|(a[25]&255)<<8;this.pad[5]=a[26]&255|(a[27]&255)<<8;this.pad[6]=a[28]&255|(a[29]&255)<<8;this.pad[7]=a[30]&255|(a[31]&255)<<8};A.prototype.blocks=function(a,b,c){var d=this.fin?0:1<<11,e,f,g,h,i,j,k,l,m,n,o,p=this.h[0],q=this.h[1],r=this.h[2],s=this.h[3],t=this.h[4],u=this.h[5],v=this.h[6],w=this.h[7],x=this.h[8],y=this.h[9],z=this.r[0],A=this.r[1],B=this.r[2],C=this.r[3],D=this.r[4],E=this.r[5],F=this.r[6],G=this.r[7],H=this.r[8],I=this.r[9];while(c>=16)e=a[b+0]&255|(a[b+1]&255)<<8,p+=e&8191,f=a[b+2]&255|(a[b+3]&255)<<8,q+=(e>>>13|f<<3)&8191,e=a[b+4]&255|(a[b+5]&255)<<8,r+=(f>>>10|e<<6)&8191,f=a[b+6]&255|(a[b+7]&255)<<8,s+=(e>>>7|f<<9)&8191,e=a[b+8]&255|(a[b+9]&255)<<8,t+=(f>>>4|e<<12)&8191,u+=e>>>1&8191,f=a[b+10]&255|(a[b+11]&255)<<8,v+=(e>>>14|f<<2)&8191,e=a[b+12]&255|(a[b+13]&255)<<8,w+=(f>>>11|e<<5)&8191,f=a[b+14]&255|(a[b+15]&255)<<8,x+=(e>>>8|f<<8)&8191,y+=f>>>5|d,e=0,f=e,f+=p*z,f+=q*(5*I),f+=r*(5*H),f+=s*(5*G),f+=t*(5*F),e=f>>>13,f&=8191,f+=u*(5*E),f+=v*(5*D),f+=w*(5*C),f+=x*(5*B),f+=y*(5*A),e+=f>>>13,f&=8191,g=e,g+=p*A,g+=q*z,g+=r*(5*I),g+=s*(5*H),g+=t*(5*G),e=g>>>13,g&=8191,g+=u*(5*F),g+=v*(5*E),g+=w*(5*D),g+=x*(5*C),g+=y*(5*B),e+=g>>>13,g&=8191,h=e,h+=p*B,h+=q*A,h+=r*z,h+=s*(5*I),h+=t*(5*H),e=h>>>13,h&=8191,h+=u*(5*G),h+=v*(5*F),h+=w*(5*E),h+=x*(5*D),h+=y*(5*C),e+=h>>>13,h&=8191,i=e,i+=p*C,i+=q*B,i+=r*A,i+=s*z,i+=t*(5*I),e=i>>>13,i&=8191,i+=u*(5*H),i+=v*(5*G),i+=w*(5*F),i+=x*(5*E),i+=y*(5*D),e+=i>>>13,i&=8191,j=e,j+=p*D,j+=q*C,j+=r*B,j+=s*A,j+=t*z,e=j>>>13,j&=8191,j+=u*(5*I),j+=v*(5*H),j+=w*(5*G),j+=x*(5*F),j+=y*(5*E),e+=j>>>13,j&=8191,k=e,k+=p*E,k+=q*D,k+=r*C,k+=s*B,k+=t*A,e=k>>>13,k&=8191,k+=u*z,k+=v*(5*I),k+=w*(5*H),k+=x*(5*G),k+=y*(5*F),e+=k>>>13,k&=8191,l=e,l+=p*F,l+=q*E,l+=r*D,l+=s*C,l+=t*B,e=l>>>13,l&=8191,l+=u*A,l+=v*z,l+=w*(5*I),l+=x*(5*H),l+=y*(5*G),e+=l>>>13,l&=8191,m=e,m+=p*G,m+=q*F,m+=r*E,m+=s*D,m+=t*C,e=m>>>13,m&=8191,m+=u*B,m+=v*A,m+=w*z,m+=x*(5*I),m+=y*(5*H),e+=m>>>13,m&=8191,n=e,n+=p*H,n+=q*G,n+=r*F,n+=s*E,n+=t*D,e=n>>>13,n&=8191,n+=u*C,n+=v*B,n+=w*A,n+=x*z,n+=y*(5*I),e+=n>>>13,n&=8191,o=e,o+=p*I,o+=q*H,o+=r*G,o+=s*F,o+=t*E,e=o>>>13,o&=8191,o+=u*D,o+=v*C,o+=w*B,o+=x*A,o+=y*z,e+=o>>>13,o&=8191,e=(e<<2)+e|0,e=e+f|0,f=e&8191,e=e>>>13,g+=e,p=f,q=g,r=h,s=i,t=j,u=k,v=l,w=m,x=n,y=o,b+=16,c-=16;this.h[0]=p;this.h[1]=q;this.h[2]=r;this.h[3]=s;this.h[4]=t;this.h[5]=u;this.h[6]=v;this.h[7]=w;this.h[8]=x;this.h[9]=y};A.prototype.finish=function(a,b){var c=new Uint16Array(10),d,e;if(this.leftover){e=this.leftover;this.buffer[e++]=1;for(;e<16;e++)this.buffer[e]=0;this.fin=1;this.blocks(this.buffer,0,16)}d=this.h[1]>>>13;this.h[1]&=8191;for(e=2;e<10;e++)this.h[e]+=d,d=this.h[e]>>>13,this.h[e]&=8191;this.h[0]+=d*5;d=this.h[0]>>>13;this.h[0]&=8191;this.h[1]+=d;d=this.h[1]>>>13;this.h[1]&=8191;this.h[2]+=d;c[0]=this.h[0]+5;d=c[0]>>>13;c[0]&=8191;for(e=1;e<10;e++)c[e]=this.h[e]+d,d=c[e]>>>13,c[e]&=8191;c[9]-=1<<13;d=(d^1)-1;for(e=0;e<10;e++)c[e]&=d;d=~d;for(e=0;e<10;e++)this.h[e]=this.h[e]&d|c[e];this.h[0]=(this.h[0]|this.h[1]<<13)&65535;this.h[1]=(this.h[1]>>>3|this.h[2]<<10)&65535;this.h[2]=(this.h[2]>>>6|this.h[3]<<7)&65535;this.h[3]=(this.h[3]>>>9|this.h[4]<<4)&65535;this.h[4]=(this.h[4]>>>12|this.h[5]<<1|this.h[6]<<14)&65535;this.h[5]=(this.h[6]>>>2|this.h[7]<<11)&65535;this.h[6]=(this.h[7]>>>5|this.h[8]<<8)&65535;this.h[7]=(this.h[8]>>>8|this.h[9]<<5)&65535;c=this.h[0]+this.pad[0];this.h[0]=c&65535;for(e=1;e<8;e++)c=(this.h[e]+this.pad[e]|0)+(c>>>16)|0,this.h[e]=c&65535;a[b+0]=this.h[0]>>>0&255;a[b+1]=this.h[0]>>>8&255;a[b+2]=this.h[1]>>>0&255;a[b+3]=this.h[1]>>>8&255;a[b+4]=this.h[2]>>>0&255;a[b+5]=this.h[2]>>>8&255;a[b+6]=this.h[3]>>>0&255;a[b+7]=this.h[3]>>>8&255;a[b+8]=this.h[4]>>>0&255;a[b+9]=this.h[4]>>>8&255;a[b+10]=this.h[5]>>>0&255;a[b+11]=this.h[5]>>>8&255;a[b+12]=this.h[6]>>>0&255;a[b+13]=this.h[6]>>>8&255;a[b+14]=this.h[7]>>>0&255;a[b+15]=this.h[7]>>>8&255};A.prototype.update=function(a,b,c){var d,e;if(this.leftover){e=16-this.leftover;e>c&&(e=c);for(d=0;d<e;d++)this.buffer[this.leftover+d]=a[b+d];c-=e;b+=e;this.leftover+=e;if(this.leftover<16)return;this.blocks(this.buffer,0,16);this.leftover=0}c>=16&&(e=c-c%16,this.blocks(a,b,e),b+=e,c-=e);if(c){for(d=0;d<c;d++)this.buffer[this.leftover+d]=a[b+d];this.leftover+=c}};function B(a,b,c,d,e,f){f=new A(f);f.update(c,d,e);f.finish(a,b);return 0}function C(a,b,c,d,e,f){var g=new Uint8Array(16);B(g,0,c,d,e,f);return p(a,b,g,0)}function D(a,b,c,d,e){if(c<32)return-1;z(a,0,b,0,c,d,e);B(a,16,a,32,c-32,a);for(b=0;b<16;b++)a[b]=0;return 0}function E(a,b,c,d,e){var f=new Uint8Array(32);if(c<32)return-1;y(f,0,32,d,e);if(C(b,16,b,32,c-32,f)!==0)return-1;z(a,0,b,0,c,d,e);for(f=0;f<32;f++)a[f]=0;return 0}function F(a,b){var c;for(c=0;c<16;c++)a[c]=b[c]|0}function G(a){var b,c,d=1;for(b=0;b<16;b++)c=a[b]+d+65535,d=Math.floor(c/65536),a[b]=c-d*65536;a[0]+=d-1+37*(d-1)}function H(a,b,c){var d;c=~(c-1);for(var e=0;e<16;e++)d=c&(a[e]^b[e]),a[e]^=d,b[e]^=d}function I(a,c){var d,e,f=b(),g=b();for(d=0;d<16;d++)g[d]=c[d];G(g);G(g);G(g);for(c=0;c<2;c++){f[0]=g[0]-65517;for(d=1;d<15;d++)f[d]=g[d]-65535-(f[d-1]>>16&1),f[d-1]&=65535;f[15]=g[15]-32767-(f[14]>>16&1);e=f[15]>>16&1;f[14]&=65535;H(g,f,1-e)}for(d=0;d<16;d++)a[2*d]=g[d]&255,a[2*d+1]=g[d]>>8}function J(a,b){var c=new Uint8Array(32),d=new Uint8Array(32);I(c,a);I(d,b);return q(c,0,d,0)}function K(a){var b=new Uint8Array(32);I(b,a);return b[0]&1}function L(a,b){var c;for(c=0;c<16;c++)a[c]=b[2*c]+(b[2*c+1]<<8);a[15]&=32767}function M(a,b,c){for(var d=0;d<16;d++)a[d]=b[d]+c[d]}function N(a,b,c){for(var d=0;d<16;d++)a[d]=b[d]-c[d]}function O(a,b,c){var d,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=c[0],K=c[1],L=c[2],M=c[3],N=c[4],O=c[5],P=c[6],Q=c[7],R=c[8],S=c[9],T=c[10],U=c[11],V=c[12],W=c[13],X=c[14];c=c[15];d=b[0];e+=d*J;f+=d*K;g+=d*L;h+=d*M;i+=d*N;j+=d*O;k+=d*P;l+=d*Q;m+=d*R;n+=d*S;o+=d*T;p+=d*U;q+=d*V;r+=d*W;s+=d*X;t+=d*c;d=b[1];f+=d*J;g+=d*K;h+=d*L;i+=d*M;j+=d*N;k+=d*O;l+=d*P;m+=d*Q;n+=d*R;o+=d*S;p+=d*T;q+=d*U;r+=d*V;s+=d*W;t+=d*X;u+=d*c;d=b[2];g+=d*J;h+=d*K;i+=d*L;j+=d*M;k+=d*N;l+=d*O;m+=d*P;n+=d*Q;o+=d*R;p+=d*S;q+=d*T;r+=d*U;s+=d*V;t+=d*W;u+=d*X;v+=d*c;d=b[3];h+=d*J;i+=d*K;j+=d*L;k+=d*M;l+=d*N;m+=d*O;n+=d*P;o+=d*Q;p+=d*R;q+=d*S;r+=d*T;s+=d*U;t+=d*V;u+=d*W;v+=d*X;w+=d*c;d=b[4];i+=d*J;j+=d*K;k+=d*L;l+=d*M;m+=d*N;n+=d*O;o+=d*P;p+=d*Q;q+=d*R;r+=d*S;s+=d*T;t+=d*U;u+=d*V;v+=d*W;w+=d*X;x+=d*c;d=b[5];j+=d*J;k+=d*K;l+=d*L;m+=d*M;n+=d*N;o+=d*O;p+=d*P;q+=d*Q;r+=d*R;s+=d*S;t+=d*T;u+=d*U;v+=d*V;w+=d*W;x+=d*X;y+=d*c;d=b[6];k+=d*J;l+=d*K;m+=d*L;n+=d*M;o+=d*N;p+=d*O;q+=d*P;r+=d*Q;s+=d*R;t+=d*S;u+=d*T;v+=d*U;w+=d*V;x+=d*W;y+=d*X;z+=d*c;d=b[7];l+=d*J;m+=d*K;n+=d*L;o+=d*M;p+=d*N;q+=d*O;r+=d*P;s+=d*Q;t+=d*R;u+=d*S;v+=d*T;w+=d*U;x+=d*V;y+=d*W;z+=d*X;A+=d*c;d=b[8];m+=d*J;n+=d*K;o+=d*L;p+=d*M;q+=d*N;r+=d*O;s+=d*P;t+=d*Q;u+=d*R;v+=d*S;w+=d*T;x+=d*U;y+=d*V;z+=d*W;A+=d*X;B+=d*c;d=b[9];n+=d*J;o+=d*K;p+=d*L;q+=d*M;r+=d*N;s+=d*O;t+=d*P;u+=d*Q;v+=d*R;w+=d*S;x+=d*T;y+=d*U;z+=d*V;A+=d*W;B+=d*X;C+=d*c;d=b[10];o+=d*J;p+=d*K;q+=d*L;r+=d*M;s+=d*N;t+=d*O;u+=d*P;v+=d*Q;w+=d*R;x+=d*S;y+=d*T;z+=d*U;A+=d*V;B+=d*W;C+=d*X;D+=d*c;d=b[11];p+=d*J;q+=d*K;r+=d*L;s+=d*M;t+=d*N;u+=d*O;v+=d*P;w+=d*Q;x+=d*R;y+=d*S;z+=d*T;A+=d*U;B+=d*V;C+=d*W;D+=d*X;E+=d*c;d=b[12];q+=d*J;r+=d*K;s+=d*L;t+=d*M;u+=d*N;v+=d*O;w+=d*P;x+=d*Q;y+=d*R;z+=d*S;A+=d*T;B+=d*U;C+=d*V;D+=d*W;E+=d*X;F+=d*c;d=b[13];r+=d*J;s+=d*K;t+=d*L;u+=d*M;v+=d*N;w+=d*O;x+=d*P;y+=d*Q;z+=d*R;A+=d*S;B+=d*T;C+=d*U;D+=d*V;E+=d*W;F+=d*X;G+=d*c;d=b[14];s+=d*J;t+=d*K;u+=d*L;v+=d*M;w+=d*N;x+=d*O;y+=d*P;z+=d*Q;A+=d*R;B+=d*S;C+=d*T;D+=d*U;E+=d*V;F+=d*W;G+=d*X;H+=d*c;d=b[15];t+=d*J;u+=d*K;v+=d*L;w+=d*M;x+=d*N;y+=d*O;z+=d*P;A+=d*Q;B+=d*R;C+=d*S;D+=d*T;E+=d*U;F+=d*V;G+=d*W;H+=d*X;I+=d*c;e+=38*u;f+=38*v;g+=38*w;h+=38*x;i+=38*y;j+=38*z;k+=38*A;l+=38*B;m+=38*C;n+=38*D;o+=38*E;p+=38*F;q+=38*G;r+=38*H;s+=38*I;b=1;d=e+b+65535;b=Math.floor(d/65536);e=d-b*65536;d=f+b+65535;b=Math.floor(d/65536);f=d-b*65536;d=g+b+65535;b=Math.floor(d/65536);g=d-b*65536;d=h+b+65535;b=Math.floor(d/65536);h=d-b*65536;d=i+b+65535;b=Math.floor(d/65536);i=d-b*65536;d=j+b+65535;b=Math.floor(d/65536);j=d-b*65536;d=k+b+65535;b=Math.floor(d/65536);k=d-b*65536;d=l+b+65535;b=Math.floor(d/65536);l=d-b*65536;d=m+b+65535;b=Math.floor(d/65536);m=d-b*65536;d=n+b+65535;b=Math.floor(d/65536);n=d-b*65536;d=o+b+65535;b=Math.floor(d/65536);o=d-b*65536;d=p+b+65535;b=Math.floor(d/65536);p=d-b*65536;d=q+b+65535;b=Math.floor(d/65536);q=d-b*65536;d=r+b+65535;b=Math.floor(d/65536);r=d-b*65536;d=s+b+65535;b=Math.floor(d/65536);s=d-b*65536;d=t+b+65535;b=Math.floor(d/65536);t=d-b*65536;e+=b-1+37*(b-1);b=1;d=e+b+65535;b=Math.floor(d/65536);e=d-b*65536;d=f+b+65535;b=Math.floor(d/65536);f=d-b*65536;d=g+b+65535;b=Math.floor(d/65536);g=d-b*65536;d=h+b+65535;b=Math.floor(d/65536);h=d-b*65536;d=i+b+65535;b=Math.floor(d/65536);i=d-b*65536;d=j+b+65535;b=Math.floor(d/65536);j=d-b*65536;d=k+b+65535;b=Math.floor(d/65536);k=d-b*65536;d=l+b+65535;b=Math.floor(d/65536);l=d-b*65536;d=m+b+65535;b=Math.floor(d/65536);m=d-b*65536;d=n+b+65535;b=Math.floor(d/65536);n=d-b*65536;d=o+b+65535;b=Math.floor(d/65536);o=d-b*65536;d=p+b+65535;b=Math.floor(d/65536);p=d-b*65536;d=q+b+65535;b=Math.floor(d/65536);q=d-b*65536;d=r+b+65535;b=Math.floor(d/65536);r=d-b*65536;d=s+b+65535;b=Math.floor(d/65536);s=d-b*65536;d=t+b+65535;b=Math.floor(d/65536);t=d-b*65536;e+=b-1+37*(b-1);a[0]=e;a[1]=f;a[2]=g;a[3]=h;a[4]=i;a[5]=j;a[6]=k;a[7]=l;a[8]=m;a[9]=n;a[10]=o;a[11]=p;a[12]=q;a[13]=r;a[14]=s;a[15]=t}function P(a,b){O(a,b,b)}function Q(a,c){var d=b(),e;for(e=0;e<16;e++)d[e]=c[e];for(e=253;e>=0;e--)P(d,d),e!==2&&e!==4&&O(d,d,c);for(e=0;e<16;e++)a[e]=d[e]}function R(a,c){var d=b(),e;for(e=0;e<16;e++)d[e]=c[e];for(e=250;e>=0;e--)P(d,d),e!==1&&O(d,d,c);for(e=0;e<16;e++)a[e]=d[e]}function S(a,c,d){var e=new Uint8Array(32),f=new Float64Array(80),g,i=b(),j=b(),k=b(),l=b(),m=b(),n=b();for(g=0;g<31;g++)e[g]=c[g];e[31]=c[31]&127|64;e[0]&=248;L(f,d);for(g=0;g<16;g++)j[g]=f[g],l[g]=i[g]=k[g]=0;i[0]=l[0]=1;for(g=254;g>=0;--g)c=e[g>>>3]>>>(g&7)&1,H(i,j,c),H(k,l,c),M(m,i,k),N(i,i,k),M(k,j,l),N(j,j,l),P(l,m),P(n,i),O(i,k,i),O(k,j,m),M(m,i,k),N(i,i,k),P(j,i),N(k,l,n),O(i,k,h),M(i,i,l),O(k,k,i),O(i,l,n),O(l,j,f),P(j,m),H(i,j,c),H(k,l,c);for(g=0;g<16;g++)f[g+16]=i[g],f[g+32]=k[g],f[g+48]=j[g],f[g+64]=l[g];d=f.subarray(32);c=f.subarray(16);Q(d,d);O(c,c,d);I(a,c);return 0}function T(a,b){return S(a,b,e)}function U(a,b){c(b,32);return T(a,b)}function V(a,b,c){var e=new Uint8Array(32);S(e,c,b);return u(a,d,e,v)}var W=D,X=E;function aa(a,b,c,d,e,f){var g=new Uint8Array(32);V(g,e,f);return W(a,b,c,d,g)}function ba(a,b,c,d,e,f){var g=new Uint8Array(32);V(g,e,f);return X(a,b,c,d,g)}var ca=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591];function da(a,b,c,d){var e=new Int32Array(16),f=new Int32Array(16),g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G=a[0],H=a[1],I=a[2],J=a[3],K=a[4],L=a[5],M=a[6],N=a[7],O=b[0],P=b[1],Q=b[2],R=b[3],S=b[4],T=b[5],U=b[6],V=b[7],W=0;while(d>=128){for(y=0;y<16;y++)z=8*y+W,e[y]=c[z+0]<<24|c[z+1]<<16|c[z+2]<<8|c[z+3],f[y]=c[z+4]<<24|c[z+5]<<16|c[z+6]<<8|c[z+7];for(y=0;y<80;y++){g=G;h=H;i=I;j=J;k=K;l=L;m=M;N;o=O;p=P;q=Q;r=R;s=S;t=T;u=U;V;A=N;B=V;C=B&65535;D=B>>>16;E=A&65535;F=A>>>16;A=(K>>>14|S<<32-14)^(K>>>18|S<<32-18)^(S>>>41-32|K<<32-(41-32));B=(S>>>14|K<<32-14)^(S>>>18|K<<32-18)^(K>>>41-32|S<<32-(41-32));C+=B&65535;D+=B>>>16;E+=A&65535;F+=A>>>16;A=K&L^~K&M;B=S&T^~S&U;C+=B&65535;D+=B>>>16;E+=A&65535;F+=A>>>16;A=ca[y*2];B=ca[y*2+1];C+=B&65535;D+=B>>>16;E+=A&65535;F+=A>>>16;A=e[y%16];B=f[y%16];C+=B&65535;D+=B>>>16;E+=A&65535;F+=A>>>16;D+=C>>>16;E+=D>>>16;F+=E>>>16;w=E&65535|F<<16;x=C&65535|D<<16;A=w;B=x;C=B&65535;D=B>>>16;E=A&65535;F=A>>>16;A=(G>>>28|O<<32-28)^(O>>>34-32|G<<32-(34-32))^(O>>>39-32|G<<32-(39-32));B=(O>>>28|G<<32-28)^(G>>>34-32|O<<32-(34-32))^(G>>>39-32|O<<32-(39-32));C+=B&65535;D+=B>>>16;E+=A&65535;F+=A>>>16;A=G&H^G&I^H&I;B=O&P^O&Q^P&Q;C+=B&65535;D+=B>>>16;E+=A&65535;F+=A>>>16;D+=C>>>16;E+=D>>>16;F+=E>>>16;n=E&65535|F<<16;v=C&65535|D<<16;A=j;B=r;C=B&65535;D=B>>>16;E=A&65535;F=A>>>16;A=w;B=x;C+=B&65535;D+=B>>>16;E+=A&65535;F+=A>>>16;D+=C>>>16;E+=D>>>16;F+=E>>>16;j=E&65535|F<<16;r=C&65535|D<<16;H=g;I=h;J=i;K=j;L=k;M=l;N=m;G=n;P=o;Q=p;R=q;S=r;T=s;U=t;V=u;O=v;if(y%16===15)for(z=0;z<16;z++)A=e[z],B=f[z],C=B&65535,D=B>>>16,E=A&65535,F=A>>>16,A=e[(z+9)%16],B=f[(z+9)%16],C+=B&65535,D+=B>>>16,E+=A&65535,F+=A>>>16,w=e[(z+1)%16],x=f[(z+1)%16],A=(w>>>1|x<<32-1)^(w>>>8|x<<32-8)^w>>>7,B=(x>>>1|w<<32-1)^(x>>>8|w<<32-8)^(x>>>7|w<<32-7),C+=B&65535,D+=B>>>16,E+=A&65535,F+=A>>>16,w=e[(z+14)%16],x=f[(z+14)%16],A=(w>>>19|x<<32-19)^(x>>>61-32|w<<32-(61-32))^w>>>6,B=(x>>>19|w<<32-19)^(w>>>61-32|x<<32-(61-32))^(x>>>6|w<<32-6),C+=B&65535,D+=B>>>16,E+=A&65535,F+=A>>>16,D+=C>>>16,E+=D>>>16,F+=E>>>16,e[z]=E&65535|F<<16,f[z]=C&65535|D<<16}A=G;B=O;C=B&65535;D=B>>>16;E=A&65535;F=A>>>16;A=a[0];B=b[0];C+=B&65535;D+=B>>>16;E+=A&65535;F+=A>>>16;D+=C>>>16;E+=D>>>16;F+=E>>>16;a[0]=G=E&65535|F<<16;b[0]=O=C&65535|D<<16;A=H;B=P;C=B&65535;D=B>>>16;E=A&65535;F=A>>>16;A=a[1];B=b[1];C+=B&65535;D+=B>>>16;E+=A&65535;F+=A>>>16;D+=C>>>16;E+=D>>>16;F+=E>>>16;a[1]=H=E&65535|F<<16;b[1]=P=C&65535|D<<16;A=I;B=Q;C=B&65535;D=B>>>16;E=A&65535;F=A>>>16;A=a[2];B=b[2];C+=B&65535;D+=B>>>16;E+=A&65535;F+=A>>>16;D+=C>>>16;E+=D>>>16;F+=E>>>16;a[2]=I=E&65535|F<<16;b[2]=Q=C&65535|D<<16;A=J;B=R;C=B&65535;D=B>>>16;E=A&65535;F=A>>>16;A=a[3];B=b[3];C+=B&65535;D+=B>>>16;E+=A&65535;F+=A>>>16;D+=C>>>16;E+=D>>>16;F+=E>>>16;a[3]=J=E&65535|F<<16;b[3]=R=C&65535|D<<16;A=K;B=S;C=B&65535;D=B>>>16;E=A&65535;F=A>>>16;A=a[4];B=b[4];C+=B&65535;D+=B>>>16;E+=A&65535;F+=A>>>16;D+=C>>>16;E+=D>>>16;F+=E>>>16;a[4]=K=E&65535|F<<16;b[4]=S=C&65535|D<<16;A=L;B=T;C=B&65535;D=B>>>16;E=A&65535;F=A>>>16;A=a[5];B=b[5];C+=B&65535;D+=B>>>16;E+=A&65535;F+=A>>>16;D+=C>>>16;E+=D>>>16;F+=E>>>16;a[5]=L=E&65535|F<<16;b[5]=T=C&65535|D<<16;A=M;B=U;C=B&65535;D=B>>>16;E=A&65535;F=A>>>16;A=a[6];B=b[6];C+=B&65535;D+=B>>>16;E+=A&65535;F+=A>>>16;D+=C>>>16;E+=D>>>16;F+=E>>>16;a[6]=M=E&65535|F<<16;b[6]=U=C&65535|D<<16;A=N;B=V;C=B&65535;D=B>>>16;E=A&65535;F=A>>>16;A=a[7];B=b[7];C+=B&65535;D+=B>>>16;E+=A&65535;F+=A>>>16;D+=C>>>16;E+=D>>>16;F+=E>>>16;a[7]=N=E&65535|F<<16;b[7]=V=C&65535|D<<16;W+=128;d-=128}return d}function Y(a,b,c){var d=new Int32Array(8),e=new Int32Array(8),f=new Uint8Array(256),g,h=c;d[0]=1779033703;d[1]=3144134277;d[2]=1013904242;d[3]=2773480762;d[4]=1359893119;d[5]=2600822924;d[6]=528734635;d[7]=1541459225;e[0]=4089235720;e[1]=2227873595;e[2]=4271175723;e[3]=1595750129;e[4]=2917565137;e[5]=725511199;e[6]=4215389547;e[7]=327033209;da(d,e,b,c);c%=128;for(g=0;g<c;g++)f[g]=b[h-c+g];f[c]=128;c=256-128*(c<112?1:0);f[c-9]=0;n(f,c-8,h/536870912|0,h<<3);da(d,e,f,c);for(g=0;g<8;g++)n(a,8*g,d[g],e[g]);return 0}function ea(a,c){var d=b(),e=b(),f=b(),g=b(),h=b(),i=b(),k=b(),l=b(),m=b();N(d,a[1],a[0]);N(m,c[1],c[0]);O(d,d,m);M(e,a[0],a[1]);M(m,c[0],c[1]);O(e,e,m);O(f,a[3],c[3]);O(f,f,j);O(g,a[2],c[2]);M(g,g,g);N(h,e,d);N(i,g,f);M(k,g,f);M(l,e,d);O(a[0],h,i);O(a[1],l,k);O(a[2],k,i);O(a[3],h,l)}function fa(a,b,c){var d;for(d=0;d<4;d++)H(a[d],b[d],c)}function ga(a,c){var d=b(),e=b(),f=b();Q(f,c[2]);O(d,c[0],f);O(e,c[1],f);I(a,e);a[31]^=K(d)<<7}function ha(a,b,c){var d,e;F(a[0],f);F(a[1],g);F(a[2],g);F(a[3],f);for(e=255;e>=0;--e)d=c[e/8|0]>>(e&7)&1,fa(a,b,d),ea(b,a),ea(a,a),fa(a,b,d)}function ia(a,c){var d=[b(),b(),b(),b()];F(d[0],k);F(d[1],l);F(d[2],g);O(d[3],k,l);ha(a,d,c)}function ja(a,d,e){var f=new Uint8Array(64),g=[b(),b(),b(),b()];e||c(d,32);Y(f,d,32);f[0]&=248;f[31]&=127;f[31]|=64;ia(g,f);ga(a,g);for(e=0;e<32;e++)d[e+32]=a[e];return 0}var ka=new Float64Array([237,211,245,92,26,99,18,88,214,156,247,162,222,249,222,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16]);function la(a,b){var c,d,e,f;for(d=63;d>=32;--d){c=0;for(e=d-32,f=d-12;e<f;++e)b[e]+=c-16*b[d]*ka[e-(d-32)],c=Math.floor((b[e]+128)/256),b[e]-=c*256;b[e]+=c;b[d]=0}c=0;for(e=0;e<32;e++)b[e]+=c-(b[31]>>4)*ka[e],c=b[e]>>8,b[e]&=255;for(e=0;e<32;e++)b[e]-=c*ka[e];for(d=0;d<32;d++)b[d+1]+=b[d]>>8,a[d]=b[d]&255}function ma(a){var b=new Float64Array(64),c;for(c=0;c<64;c++)b[c]=a[c];for(c=0;c<64;c++)a[c]=0;la(a,b)}function na(a,c,d,e){var f=new Uint8Array(64),g=new Uint8Array(64),h=new Uint8Array(64),i,j=new Float64Array(64),k=[b(),b(),b(),b()];Y(f,e,32);f[0]&=248;f[31]&=127;f[31]|=64;var l=d+64;for(i=0;i<d;i++)a[64+i]=c[i];for(i=0;i<32;i++)a[32+i]=f[32+i];Y(h,a.subarray(32),d+32);ma(h);ia(k,h);ga(a,k);for(i=32;i<64;i++)a[i]=e[i];Y(g,a,d+64);ma(g);for(i=0;i<64;i++)j[i]=0;for(i=0;i<32;i++)j[i]=h[i];for(i=0;i<32;i++)for(c=0;c<32;c++)j[i+c]+=g[i]*f[c];la(a.subarray(32),j);return l}function oa(a,c){var d=b(),e=b(),h=b(),j=b(),k=b(),l=b(),n=b();F(a[2],g);L(a[1],c);P(h,a[1]);O(j,h,i);N(h,h,a[2]);M(j,a[2],j);P(k,j);P(l,k);O(n,l,k);O(d,n,h);O(d,d,j);R(d,d);O(d,d,h);O(d,d,j);O(d,d,j);O(a[0],d,j);P(e,a[0]);O(e,e,j);J(e,h)&&O(a[0],a[0],m);P(e,a[0]);O(e,e,j);if(J(e,h))return-1;K(a[0])===c[31]>>7&&N(a[0],f,a[0]);O(a[3],a[0],a[1]);return 0}function pa(a,c,d,e){var f,g=new Uint8Array(32),h=new Uint8Array(64),i=[b(),b(),b(),b()],j=[b(),b(),b(),b()];if(d<64)return-1;if(oa(j,e))return-1;for(f=0;f<d;f++)a[f]=c[f];for(f=0;f<32;f++)a[f+32]=e[f];Y(h,a,d);ma(h);ha(i,j,h);ia(j,c.subarray(32));ea(i,j);ga(g,i);d-=64;if(q(c,0,g,0)){for(f=0;f<d;f++)a[f]=0;return-1}for(f=0;f<d;f++)a[f]=c[f+64];return d}var qa=32,ra=24,sa=32,ta=16,ua=32,va=32,wa=32,xa=32,ya=32,za=ra,Aa=sa,Ba=ta,Z=64,Ca=32,Da=64,Ea=32,Fa=64;a.lowlevel={crypto_core_hsalsa20:u,crypto_stream_xor:z,crypto_stream:y,crypto_stream_salsa20_xor:w,crypto_stream_salsa20:x,crypto_onetimeauth:B,crypto_onetimeauth_verify:C,crypto_verify_16:p,crypto_verify_32:q,crypto_secretbox:D,crypto_secretbox_open:E,crypto_scalarmult:S,crypto_scalarmult_base:T,crypto_box_beforenm:V,crypto_box_afternm:W,crypto_box:aa,crypto_box_open:ba,crypto_box_keypair:U,crypto_hash:Y,crypto_sign:na,crypto_sign_keypair:ja,crypto_sign_open:pa,crypto_secretbox_KEYBYTES:qa,crypto_secretbox_NONCEBYTES:ra,crypto_secretbox_ZEROBYTES:sa,crypto_secretbox_BOXZEROBYTES:ta,crypto_scalarmult_BYTES:ua,crypto_scalarmult_SCALARBYTES:va,crypto_box_PUBLICKEYBYTES:wa,crypto_box_SECRETKEYBYTES:xa,crypto_box_BEFORENMBYTES:ya,crypto_box_NONCEBYTES:za,crypto_box_ZEROBYTES:Aa,crypto_box_BOXZEROBYTES:Ba,crypto_sign_BYTES:Z,crypto_sign_PUBLICKEYBYTES:Ca,crypto_sign_SECRETKEYBYTES:Da,crypto_sign_SEEDBYTES:Ea,crypto_hash_BYTES:Fa,gf:b,D:i,L:ka,pack25519:I,unpack25519:L,M:O,A:M,S:P,Z:N,pow2523:R,add:ea,set25519:F,modL:la,scalarmult:ha,scalarbase:ia};function Ga(a,b){if(a.length!==qa)throw new Error("bad key size");if(b.length!==ra)throw new Error("bad nonce size")}function Ha(a,b){if(a.length!==wa)throw new Error("bad public key size");if(b.length!==xa)throw new Error("bad secret key size")}function $(){for(var a=0;a<arguments.length;a++)if(!(arguments[a]instanceof Uint8Array))throw new TypeError("unexpected type, use Uint8Array")}function Ia(a){for(var b=0;b<a.length;b++)a[b]=0}a.randomBytes=function(a){var b=new Uint8Array(a);c(b,a);return b};a.secretbox=function(a,b,c){$(a,b,c);Ga(c,b);var d=new Uint8Array(sa+a.length),e=new Uint8Array(d.length);for(var f=0;f<a.length;f++)d[f+sa]=a[f];D(e,d,d.length,b,c);return e.subarray(ta)};a.secretbox.open=function(a,b,c){$(a,b,c);Ga(c,b);var d=new Uint8Array(ta+a.length),e=new Uint8Array(d.length);for(var f=0;f<a.length;f++)d[f+ta]=a[f];if(d.length<32)return null;return E(e,d,d.length,b,c)!==0?null:e.subarray(sa)};a.secretbox.keyLength=qa;a.secretbox.nonceLength=ra;a.secretbox.overheadLength=ta;a.scalarMult=function(a,b){$(a,b);if(a.length!==va)throw new Error("bad n size");if(b.length!==ua)throw new Error("bad p size");var c=new Uint8Array(ua);S(c,a,b);return c};a.scalarMult.base=function(a){$(a);if(a.length!==va)throw new Error("bad n size");var b=new Uint8Array(ua);T(b,a);return b};a.scalarMult.scalarLength=va;a.scalarMult.groupElementLength=ua;a.box=function(b,c,d,e){d=a.box.before(d,e);return a.secretbox(b,c,d)};a.box.before=function(a,b){$(a,b);Ha(a,b);var c=new Uint8Array(ya);V(c,a,b);return c};a.box.after=a.secretbox;a.box.open=function(b,c,d,e){d=a.box.before(d,e);return a.secretbox.open(b,c,d)};a.box.open.after=a.secretbox.open;a.box.keyPair=function(){var a=new Uint8Array(wa),b=new Uint8Array(xa);U(a,b);return{publicKey:a,secretKey:b}};a.box.keyPair.fromSecretKey=function(a){$(a);if(a.length!==xa)throw new Error("bad secret key size");var b=new Uint8Array(wa);T(b,a);return{publicKey:b,secretKey:new Uint8Array(a)}};a.box.publicKeyLength=wa;a.box.secretKeyLength=xa;a.box.sharedKeyLength=ya;a.box.nonceLength=za;a.box.overheadLength=a.secretbox.overheadLength;a.sign=function(a,b){$(a,b);if(b.length!==Da)throw new Error("bad secret key size");var c=new Uint8Array(Z+a.length);na(c,a,a.length,b);return c};a.sign.open=function(a,b){$(a,b);if(b.length!==Ca)throw new Error("bad public key size");var c=new Uint8Array(a.length);a=pa(c,a,a.length,b);if(a<0)return null;b=new Uint8Array(a);for(a=0;a<b.length;a++)b[a]=c[a];return b};a.sign.detached=function(b,c){b=a.sign(b,c);c=new Uint8Array(Z);for(var d=0;d<c.length;d++)c[d]=b[d];return c};a.sign.detached.verify=function(a,b,c){$(a,b,c);if(b.length!==Z)throw new Error("bad signature size");if(c.length!==Ca)throw new Error("bad public key size");var d=new Uint8Array(Z+a.length),e=new Uint8Array(Z+a.length),f;for(f=0;f<Z;f++)d[f]=b[f];for(f=0;f<a.length;f++)d[f+Z]=a[f];return pa(e,d,d.length,c)>=0};a.sign.keyPair=function(){var a=new Uint8Array(Ca),b=new Uint8Array(Da);ja(a,b);return{publicKey:a,secretKey:b}};a.sign.keyPair.fromSecretKey=function(a){$(a);if(a.length!==Da)throw new Error("bad secret key size");var b=new Uint8Array(Ca);for(var c=0;c<b.length;c++)b[c]=a[32+c];return{publicKey:b,secretKey:new Uint8Array(a)}};a.sign.keyPair.fromSeed=function(a){$(a);if(a.length!==Ea)throw new Error("bad seed size");var b=new Uint8Array(Ca),c=new Uint8Array(Da);for(var d=0;d<32;d++)c[d]=a[d];ja(b,c,!0);return{publicKey:b,secretKey:c}};a.sign.publicKeyLength=Ca;a.sign.secretKeyLength=Da;a.sign.seedLength=Ea;a.sign.signatureLength=Z;a.hash=function(a){$(a);var b=new Uint8Array(Fa);Y(b,a,a.length);return b};a.hash.hashLength=Fa;a.verify=function(a,b){$(a,b);if(a.length===0||b.length===0)return!1;return a.length!==b.length?!1:o(a,0,b,0,a.length)===0?!0:!1};a.setPRNG=function(a){c=a};(function(){var b=typeof self!=="undefined"?self.crypto||self.msCrypto:null;if(b&&b.getRandomValues){var c=65536;a.setPRNG(function(a,d){var e,f=new Uint8Array(d);for(e=0;e<d;e+=c)b.getRandomValues(f.subarray(e,e+Math.min(d-e,c)));for(e=0;e<d;e++)a[e]=f[e];Ia(f)})}else b&&b.randomBytes&&a.setPRNG(function(a,c){var d,e=b.randomBytes(c);for(d=0;d<c;d++)a[d]=e[d];Ia(e)})})()})(typeof g!=="undefined"&&g.exports?g.exports:self.nacl=self.nacl||{})}var i=!1;function j(){i||(i=!0,h());return g.exports}function a(a){switch(a){case void 0:return j()}}e.exports=a}),null);
__d("tweetnacl",["tweetnacl-1.0.3"],(function(a,b,c,d,e,f){e.exports=b("tweetnacl-1.0.3")()}),null);
__d("WACryptoDependencies",["tweetnacl"],(function(a,b,c,d,e,f,g){"use strict";var h=self.crypto;function a(a){h=a;var b=65536;d("tweetnacl").setPRNG(function(c,d){var e,f=new Uint8Array(d);for(e=0;e<d;e+=b)a.getRandomValues(f.subarray(e,e+Math.min(d-e,b)));for(e=0;e<d;e++)c[e]=f[e];for(e=0;e<f.length;e++)f[e]=0})}function b(){return h}g.setCrypto=a;g.getCrypto=b}),98);
__d("WABase64",["WACryptoDependencies","WALogger","err"],(function(a,b,c,d,e,f,g){"use strict";function h(){var a=babelHelpers.taggedTemplateLiteralLoose(["Found unexpected character code while decoding B64 at index ",", length ",": ",""]);h=function(){return a};return a}var i=43,j=47,k=61,l=45,m=95,n=3e3,o="data:image/jpeg;base64,",p=function(a){return typeof a==="string"&&/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(a)};function q(a){return r(a,i,j,!0)}function a(a,b){b===void 0&&(b=!1);return r(a,l,m,b)}function r(a,b,c,d){a=Array.isArray(a)||a instanceof ArrayBuffer?new Uint8Array(a):a;if(a.length<=n)return s(a,b,c,d);else{var e=[];for(var f=0;f<a.length;f+=n)e.push(s(a.subarray(f,f+n),b,c,d));return e.join("")}}function s(a,b,c,d){var e=Math.ceil(a.length*4/3),f=4*Math.ceil(a.length/3),g=new Array(f);for(var h=0,i=0;h<f;h+=4,i+=3){var j=a[i]<<16|a[i+1]<<8|a[i+2];g[h]=j>>18;g[h+1]=j>>12&63;g[h+2]=j>>6&63;g[h+3]=j&63}for(j=0;j<e;j++){a=g[j];a<26?g[j]=65+a:a<52?g[j]=71+a:a<62?g[j]=a-4:a===62?g[j]=b:g[j]=c}for(h=e;h<f;h++)g[h]=61;i=String.fromCharCode.apply(String,g);return d?i:i.substring(0,e)}function t(a,b,c,e){var f=a.length,g=new Int32Array(f+f%4);for(var i=0;i<f;i++){var j=a.charCodeAt(i);if(65<=j&&j<=90)g[i]=j-65;else if(97<=j&&j<=122)g[i]=j-71;else if(48<=j&&j<=57)g[i]=j+4;else if(j===b)g[i]=62;else if(j===c)g[i]=63;else if(j===e){f=i;break}else{self.ERROR!=null&&d("WALogger").ERROR(h(),i,f,j);return null}}j=g.length/4;for(b=0,c=0;b<j;b++,c+=4)g[b]=g[c]<<18|g[c+1]<<12|g[c+2]<<6|g[c+3];e=Math.floor(f*3/4);i=new Uint8Array(e);a=0;b=0;for(;b+3<=e;a++,b+=3){j=g[a];i[b]=j>>16;i[b+1]=j>>8&255;i[b+2]=j&255}switch(e-b){case 2:i[b]=g[a]>>16;i[b+1]=g[a]>>8&255;break;case 1:i[b]=g[a]>>16}return i}function b(a){a=t(a,i,j,k);if(a)return a.buffer;else throw c("err")("Base64.decode given invalid string")}function e(a,b){b===void 0&&(b=!1);a=t(a,l,m,b?k:-1);if(a)return a.buffer;else throw c("err")("Base64.decode given invalid string")}function f(a){a=a instanceof ArrayBuffer?new Uint8Array(a):t(a,i,j,k);return a&&Array.from(a)}function u(a){return Math.floor(a.length*3/4)}function v(a){a=new Uint8Array(a);d("WACryptoDependencies").getCrypto().getRandomValues(a);return q(a)}g.BASE64_DATA_URL_SCHEME=o;g.isBase64=p;g.encodeB64=q;g.encodeB64UrlSafe=a;g.decodeB64=b;g.decodeB64UrlSafe=e;g.decodeB64ToJsArray=f;g.sizeWhenB64Decoded=u;g.randomBase64=v}),98);
__d("WABinary",["WAHex","err"],(function(a,b,c,d,e,f,g){"use strict";var h=65533,i=10,j=new Uint8Array(i),k=new Uint8Array(0);a=function(){function a(b,c){var d=this;b===void 0&&(b=k);c===void 0&&(c=!1);this.$1=new Uint8Array(0);this.$2=0;this.$4=0;this.write=function(){for(var b=0;b<arguments.length;b++){var c=b<0||arguments.length<=b?void 0:arguments[b];typeof c==="string"?d.writeString(c):typeof c==="number"?d.writeUint8(c):c instanceof a?d.writeBinary(c):c instanceof ArrayBuffer?d.writeBuffer(c):c instanceof Uint8Array&&d.writeByteArray(c)}};b instanceof ArrayBuffer?(this.$1=new Uint8Array(b),this.$2=this.$4=b.byteLength):b instanceof Uint8Array&&(this.$1=b,this.$2=this.$4=b.length);this.$5=0;this.$6=this.$3=0;this.$7=null;this.$8=c;this.$9=0;this.$10=0}var b=a.prototype;b.size=function(){return this.$2-this.$3};b.peek=function(a,b){this.$9++;var c=this.$3,d=this.$5;try{return a(this,b)}finally{this.$9--,this.$3=c-(this.$5-d)}};b.advance=function(a){this.$11(a)};b.readWithViewParser=function(a,b,c,d){return b(this.$12(),this.$11(a),a,c,d)};b.readWithBytesParser=function(a,b,c,d){return b(this.$1,this.$11(a),a,c,d)};b.readUint8=function(){return p(this,1,r,!1)};b.readInt8=function(){return p(this,1,r,!0)};b.readUint16=function(a){a===void 0&&(a=this.$8);return p(this,2,s,a)};b.readInt32=function(a){a===void 0&&(a=this.$8);return p(this,4,t,a)};b.readUint32=function(a){a===void 0&&(a=this.$8);return p(this,4,u,a)};b.readInt64=function(a){a===void 0&&(a=this.$8);return p(this,8,v,U,a)};b.readUint64=function(a){a===void 0&&(a=this.$8);return p(this,8,v,V,a)};b.readLong=function(a,b){b===void 0&&(b=this.$8);return p(this,8,v,a,b)};b.readFloat32=function(a){a===void 0&&(a=this.$8);return p(this,4,w,a)};b.readFloat64=function(a){a===void 0&&(a=this.$8);return p(this,8,x,a)};b.readVarInt=function(a){var b=this.size();b=q(this,0,y,b);return q(this,b,z,a)};b.readBuffer=function(a){a===void 0&&(a=this.size());return a===0?new ArrayBuffer(0):q(this,a,A)};b.readByteArray=function(a){a===void 0&&(a=this.size());return a===0?new Uint8Array(0):q(this,a,B)};b.readBinary=function(b,c){b===void 0&&(b=this.size());c===void 0&&(c=this.$8);if(b===0)return new a(void 0,c);b=q(this,b,B);return new a(b,c)};b.indexOf=function(a){if(a.length===0)return 0;var b=this.$1,c=this.$2,d=this.$3,e=0,f=d;for(var g=d;g<c;g++)if(b[g]===a[e]){e===0&&(f=g);e++;if(e===a.byteLength)return g-d-a.byteLength+1}else e>0&&(e=0,g=f);return-1};b.readString=function(a){return q(this,a,C)};b.ensureCapacity=function(a){this.$13(this.$3+a)};b.ensureAdditionalCapacity=function(a){this.$13(this.$4+a)};b.writeToView=function(a,b,c,d){var e=this.$14(a);return b(this.$12(),e,a,c,d)};b.writeToBytes=function(a,b,c,d){var e=this.$14(a);return b(this.$1,e,a,c,d)};b.writeUint8=function(a){S(a,0,256,"uint8"),E(this,1,F,a,!1)};b.writeInt8=function(a){S(a,-128,128,"signed int8"),E(this,1,F,a,!0)};b.writeUint16=function(a,b){b===void 0&&(b=this.$8),S(a,0,65536,"uint16"),D(this,2,G,a,b)};b.writeInt16=function(a,b){b===void 0&&(b=this.$8),S(a,-32768,32768,"signed int16"),D(this,2,H,a,b)};b.writeUint32=function(a,b){b===void 0&&(b=this.$8),S(a,0,4294967296,"uint32"),D(this,4,I,a,b)};b.writeInt32=function(a,b){b===void 0&&(b=this.$8),S(a,-2147483648,2147483648,"signed int32"),D(this,4,J,a,b)};b.writeUint64=function(a,b){b===void 0&&(b=this.$8),S(a,0,18446744073709552e3,"uint64"),D(this,8,K,a,b)};b.writeInt64=function(a,b){b===void 0&&(b=this.$8),S(a,-9223372036854776e3,9223372036854776e3,"signed int64"),D(this,8,K,a,b)};b.writeFloat32=function(a,b){b===void 0&&(b=this.$8),D(this,4,L,a,b)};b.writeFloat64=function(a,b){b===void 0&&(b=this.$8),D(this,8,M,a,b)};b.writeVarInt=function(a){S(a,-9223372036854776e3,9223372036854776e3,"varint (signed int64)");var b=a<0;a=b?-a:a;var c=Math.floor(a/4294967296);a=a-c*4294967296;b&&(c=~c,a===0?c++:a=-a);b=R(c,a);E(this,b,N,c,a)};b.writeVarIntFromHexLong=function(a){var b=d("WAHex").hexLongIsNegative(a);a=b?d("WAHex").negateHexLong(a):a;a=d("WAHex").hexLongToHex(a);var c=0,e=0;for(var f=0;f<d("WAHex").NUM_HEX_IN_LONG;f++)c=c<<4|e>>>28,e=e<<4|d("WAHex").hexAt(a,f);b&&(c=~c,e===0?c++:e=-e);a=R(c,e);E(this,a,N,c,e)};b.writeBinary=function(a){a=a.peek(function(a){return a.readByteArray()});if(a.length){var b=this.$14(a.length);this.$1.set(a,b)}};b.writeBuffer=function(a){this.writeByteArray(new Uint8Array(a))};b.writeByteArray=function(a){var b=this.$14(a.length);this.$1.set(a,b)};b.writeBufferView=function(a){this.writeByteArray(new Uint8Array(a.buffer,a.byteOffset,a.byteLength))};b.writeString=function(a){E(this,n(a),O,a)};b.writeHexLong=function(a,b){b===void 0&&(b=this.$8),D(this,8,P,a,b)};b.writeBytes=function(){for(var a=arguments.length,b=new Array(a),c=0;c<a;c++)b[c]=arguments[c];for(var d=0;d<b.length;d++)S(b[d],0,256,"byte");E(this,b.length,Q,b)};b.writeAtomically=function(a,b){this.$10++;var c=this.$4,d=this.$5;try{a=a(this,b);c=this.$4;d=this.$5;return a}finally{this.$10--,this.$4=c-(this.$5-d)}};b.writeWithVarIntLength=function(a,b){var c=this.$4;a=this.writeAtomically(a,b);b=this.$4;this.writeVarInt(b-c);var d=this.$4-b,e=this.$1;for(var f=0;f<d;f++)j[f]=e[b+f];for(f=b-1;f>=c;f--)e[f+d]=e[f];for(b=0;b<d;b++)e[c+b]=j[b];return a};a.build=function(){for(var b=arguments.length,c=new Array(b),d=0;d<b;d++)c[d]=arguments[d];var e=0;for(var f=0;f<c.length;f++){var g=c[f];typeof g==="string"?e+=n(g):typeof g==="number"?e++:g instanceof a?e+=g.size():g instanceof ArrayBuffer?e+=g.byteLength:g instanceof Uint8Array&&(e+=g.length)}var h=new a();h.ensureCapacity(e);h.write.apply(h,arguments);return h};b.$12=function(){return this.$7||(this.$7=new DataView(this.$1.buffer,this.$1.byteOffset))};b.$11=function(a){if(a<0)throw c("err")("ReadError: given negative number of bytes to read");var b=this.$3;a=b+a;if(a>this.$2)throw c("err")(b===this.$2?"ReadError: tried to read from depleted binary":"ReadError: tried to read beyond end of binary");this.$3=a;this.$9||(this.$6=a);return b};b.$13=function(a){var b=this.$1;if(a<=b.length)return a;else{var c=this.$6;a=a-c;var d=Math.max(a,2*(b.length-c),64);d=new Uint8Array(d);c?(d.set(b.subarray(c)),this.$5+=c,this.$3-=c,this.$2-=c,this.$4-=c,this.$6=0):d.set(b);this.$1=d;this.$7=null;return a}};b.$14=function(a){a=this.$13(this.$4+a);var b=this.$4;this.$4=a;this.$10||(this.$2=a);return b};return a}();var l="",m=0;function n(a){if(a===l)return m;var b=a.length,c=0;for(var d=0;d<b;d++){var e=a.charCodeAt(d);if(e<128)c++;else if(e<2048)c+=2;else if(e<55296||57344<=e&&e<=65535)c+=3;else if(55296<=e&&e<56320&&d+1!==b){e=a.charCodeAt(d+1);56320<=e&&e<57344?(d++,c+=4):c+=3}else c+=3}l=a;return m=c}function o(a,b,c){var d=b>>21;if(!a)return d===0;else{a=b&2097151;b=Boolean(a||c);return d===0||d===-1&&b}}function p(a,b,c,d,e){return a.readWithViewParser(b,c,d,e)}function q(a,b,c,d,e){return a.readWithBytesParser(b,c,d,e)}function r(a,b,c,d){return d?a.getInt8(b):a.getUint8(b)}function s(a,b,c,d){return a.getUint16(b,d)}function t(a,b,c,d){return a.getInt32(b,d)}function u(a,b,c,d){return a.getUint32(b,d)}function v(a,b,c,d,e){c=a.getInt32(e?b+4:b,e);a=a.getInt32(e?b:b+4,e);return d(c,a)}function w(a,b,c,d){return a.getFloat32(b,d)}function x(a,b,c,d){return a.getFloat64(b,d)}function y(a,b,d,e){d=Math.min(e,i);e=0;var f=128;while(e<d&&f&128)f=a[b+e++];if(e===i&&f>1)throw c("err")("ParseError: varint exceeds 64 bits");else if(f&128)return e+1;return e}function z(b,c,d,a){var e=0,f=0,g=d;d===i&&(g--,f=b[c+g]&1);for(d=g-1;d>=0;d--)e=e<<7|f>>>25,f=f<<7|b[c+d]&127;return a(e,f)}function A(a,b,c){b=b+a.byteOffset;a=a.buffer;return b===0&&c===a.byteLength?a:a.slice(b,b+c)}function B(a,b,c){return a.subarray(b,b+c)}function C(a,b,c){c=b+c;var d=[],e=null;for(b=b;b<c;b++){d.length>5e3&&(e||(e=[]),e.push(String.fromCharCode.apply(String,d)),d=[]);var f=a[b]|0;if((f&128)===0)d.push(f);else if((f&224)===192){var g=W(a,b+1,c);if(g){b++;g=(f&31)<<6|g&63;g>=128?d.push(g):d.push(h)}else d.push(h)}else if((f&240)===224){g=W(a,b+1,c);var i=W(a,b+2,c);if(g&&i){b+=2;i=(f&15)<<12|(g&63)<<6|i&63;i>=2048&&!(55296<=i&&i<57344)?d.push(i):d.push(h)}else g?(b++,d.push(h)):d.push(h)}else if((f&248)===240){i=W(a,b+1,c);g=W(a,b+2,c);var j=W(a,b+3,c);if(i&&g&&j){b+=3;f=(f&7)<<18|(i&63)<<12|(g&63)<<6|j&63;if(f>=65536&&f<=1114111){j=f-65536;d.push(55296|j>>10,56320|j&1023)}else d.push(h)}else i&&g?(b+=2,d.push(h)):i?(b++,d.push(h)):d.push(h)}else d.push(h)}f=String.fromCharCode.apply(String,d);if(e){e.push(f);return e.join("")}else return f}function D(a,b,c,d,e){return a.writeToView(b,c,d,e)}function E(a,b,c,d,e){return a.writeToBytes(b,c,d,e)}function F(a,b,c,d){a[b]=d}function G(a,b,c,d,e){a.setUint16(b,d,e)}function H(a,b,c,d,e){a.setInt16(b,d,e)}function I(a,b,c,d,e){a.setUint32(b,d,e)}function J(a,b,c,d,e){a.setInt32(b,d,e)}function K(a,b,c,d,e){c=d<0;d=c?-d:d;var f=Math.floor(d/4294967296);d=d-f*4294967296;c&&(f=~f,d===0?f++:d=-d);a.setUint32(e?b+4:b,f,e);a.setUint32(e?b:b+4,d,e)}function L(a,b,c,d,e){a.setFloat32(b,d,e)}function M(a,b,c,d,e){a.setFloat64(b,d,e)}function N(a,b,c,d,e){d=d;e=e;c=b+c-1;for(b=b;b<c;b++)a[b]=128|e&127,e=d<<25|e>>>7,d>>>=7;a[c]=e}function O(a,b,c,d){c=b;b=d.length;for(var e=0;e<b;e++){var f=d.charCodeAt(e);if(f<128)a[c++]=f;else if(f<2048)a[c++]=192|f>>6,a[c++]=128|f&63;else if(f<55296||57344<=f)a[c++]=224|f>>12,a[c++]=128|f>>6&63,a[c++]=128|f&63;else if(55296<=f&&f<56320&&e+1!==b){var g=d.charCodeAt(e+1);if(56320<=g&&g<57344){e++;f=65536+((f&1023)<<10|g&1023);a[c++]=240|f>>18;a[c++]=128|f>>12&63;a[c++]=128|f>>6&63;a[c++]=128|f&63}else a[c++]=239,a[c++]=191,a[c++]=189}else a[c++]=239,a[c++]=191,a[c++]=189}}function P(a,b,c,e,f){c=d("WAHex").hexLongIsNegative(e);e=d("WAHex").hexLongToHex(e);var g=0,h=0;for(var i=0;i<16;i++)g=g<<4|h>>>28,h=h<<4|d("WAHex").hexAt(e,i);c&&(g=~g,h===0?g++:h=-h);a.setUint32(f?b+4:b,g,f);a.setUint32(f?b:b+4,h,f)}function Q(a,b,c,d){for(var e=0;e<c;e++)a[b+e]=d[e]}function R(a,b){var c;a?(c=5,a=a>>>3):(c=1,a=b>>>7);while(a)c++,a>>>=7;return c}function S(a,b,d,e){if(typeof a!=="number"||a!==a||Math.floor(a)!==a||a<b||a>=d)throw c("err")(typeof a==="string"?'TyperError WriteError: string "'+a+'" is not a valid '+e:"TypeError WriteError: "+String(a)+" is not a valid "+e)}function T(a,b,d){var e=o(a,b,d),f;b>=0?f=b:f=a?b:4294967296+b;a=d>=0?d:4294967296+d;b=f*4294967296+a;if(!e)throw c("err")("ReadError: integer exceeded 53 bits ("+b+")");return b}function U(a,b){return T(!0,a,b)}function V(a,b){return T(!1,a,b)}function W(a,b,c){if(b>=c)return 0;c=a[b]|0;return(c&192)===128?c:0}g.Binary=a;g.numUtf8Bytes=n;g.longFitsInDouble=o;g.parseInt64OrThrow=U;g.parseUint64OrThrow=V}),98);
__d("WAByteArray",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){b=b;var c=new Uint8Array(a);for(a=a-1;a>=0;a--)c[a]=b&255,b>>>=8;return c}function b(a){return a.buffer.slice(a.byteOffset,a.byteLength+a.byteOffset)}function c(a,b){if(!a||!b)return!1;a=new Uint8Array(a);b=new Uint8Array(b);var c=a.length,d=b.length;if(c!==d)return!1;for(d=0;d<c;d++)if(a[d]!==b[d])return!1;return!0}f.intToBytes=a;f.uint8ArrayToBuffer=b;f.compareArrayBuffer=c}),66);
__d("WACryptoSha256Builder",["WABinary"],(function(a,b,c,d,e,f,g){"use strict";var h=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],i=64,j=4;a=function(){function a(){this.h0=0,this.h1=0,this.h2=0,this.h3=0,this.h4=0,this.h5=0,this.h6=0,this.h7=0,this.tail=new Uint8Array(0),this.size=0,this.reset()}var b=a.prototype;b.reset=function(){this.h0=1779033703,this.h1=3144134277,this.h2=1013904242,this.h3=2773480762,this.h4=1359893119,this.h5=2600822924,this.h6=528734635,this.h7=1541459225,this.tail=new Uint8Array(0),this.size=0};b.update=function(a){var b=new Uint8Array(this.tail.length+a.length);b.set(this.tail);b.set(a,this.tail.length);this.size+=a.length*8;while(b.length>=i){a=b.subarray(0,i);this.$1(a);b=b.slice(i)}this.tail=b;return this};b.$2=function(){var a=new Uint8Array(64);a.set(this.tail);a.set(new Uint8Array([128]),this.tail.length);if(this.tail.length+9>i){this.$1(a);var b=p(new Uint8Array(0),this.size);this.$1(b)}else{b=p(a,this.size);this.$1(b)}};b.finish=function(){this.$2();var a=new(d("WABinary").Binary)();a.writeUint32(this.h0);a.writeUint32(this.h1);a.writeUint32(this.h2);a.writeUint32(this.h3);a.writeUint32(this.h4);a.writeUint32(this.h5);a.writeUint32(this.h6);a.writeUint32(this.h7);this.reset();return a.readByteArray()};b.$1=function(a){var b=[];for(var c=0;c<16;c++){var d=a.subarray(c*j,j*(c+1));d=d[0]<<24|d[1]<<16|d[2]<<8|d[3];b.push(d)}for(d=16;d<64;d++){a=k(b[d-15]);c=l(b[d-2]);a=b[d-16]+(b[d-7]+a+c)>>>0;b.push(a)}c=this.h0;a=this.h1;d=this.h2;var e=this.h3,f=this.h4,g=this.h5,i=this.h6,n=this.h7;for(var o=0;o<64;o++){var p=m(f,6)^m(f,11)^m(f,25),q=f&g^~f&i,r=m(c,2)^m(c,13)^m(c,22),s=c&a^c&d^a&d;p=n+p+q+h[o]+b[o];q=r+s;n=i;i=g;g=f;f=e+p>>>0;e=d;d=a;a=c;c=p+q>>>0}this.h0=this.h0+c>>>0;this.h1=this.h1+a>>>0;this.h2=this.h2+d>>>0;this.h3=this.h3+e>>>0;this.h4=this.h4+f>>>0;this.h5=this.h5+g>>>0;this.h6=this.h6+i>>>0;this.h7=this.h7+n>>>0};return a}();function k(a){var b=m(a,7),c=m(a,18);a=n(a,3);return b^c^a}function l(a){var b=m(a,17),c=m(a,19);a=n(a,10);return b^c^a}function m(a,b){return a>>>b|a<<32-b}function n(a,b){return a>>>b}function o(a){return new Uint8Array(new Uint32Array([a]).buffer)}function p(a,b){var c=new Uint8Array(i);c.set(a);a=o(b);c.set(a.subarray(0,1),c.length-1);c.set(a.subarray(1,2),c.length-2);c.set(a.subarray(2,3),c.length-3);c.set(a.subarray(3,4),c.length-4);c.set(a.subarray(4,5),c.length-5);c.set(a.subarray(5,6),c.length-6);c.set(a.subarray(6,7),c.length-7);c.set(a.subarray(7,8),c.length-8);return c}g.Sha256Builder=a;g.sigma0=k;g.sigma1=l;g.rotateRight=m;g.shiftRight=n}),98);
__d("WACryptoSha256HmacBuilder",["WACryptoSha256Builder"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a){this.$1=new(d("WACryptoSha256Builder").Sha256Builder)(),this.$2=new Uint8Array(0),this.reset(a)}var b=a.prototype;b.reset=function(a){this.$2=new Uint8Array(64);a.length>64?this.$2.set(new(d("WACryptoSha256Builder").Sha256Builder)().update(a).finish(),0):this.$2.set(a,0);a=this.$2.map(function(a){return a^54});this.$1=new(d("WACryptoSha256Builder").Sha256Builder)().update(a)};b.update=function(a){this.$1.update(a);return this};b.finish=function(){var a=this.$2.map(function(a){return a^92}),b=this.$1.finish();return new(d("WACryptoSha256Builder").Sha256Builder)().update(a).update(b).finish()};return a}();g.Sha256HMacBuilder=a}),98);