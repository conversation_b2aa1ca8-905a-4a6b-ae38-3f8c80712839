;/*FB_PKG_DELIM*/

__d("CacheStorage",["ErrorGuard","ExecutionEnvironment","WebStorage","cr:6943","cr:8958","emptyFunction","err"],(function(a,b,c,d,e,f,g){var h,i,j,k="_@_",l="3b",m="CacheStorageVersion",n={length:0,getItem:a=c("emptyFunction"),setItem:a,clear:a,removeItem:a,key:a};d=function(){function a(a){this._store=a}var b=a.prototype;b.getStore=function(){return this._store};b.keys=function(){var a=[];for(var b=0;b<this._store.length;b++){var c=this._store.key(b);c!=null&&a.push(c)}return a};b.get=function(a){return this._store.getItem(a)};b.set=function(a,b){this._store.setItem(a,b)};b.remove=function(a){this._store.removeItem(a)};b.clear=function(){this._store.clear()};b.clearWithPrefix=function(a){a=a||"";var b=this.keys();for(var c=0;c<b.length;c++){var d=b[c];d!=null&&d.startsWith(a)&&this.remove(d)}};return a}();e=function(a){babelHelpers.inheritsLoose(b,a);function b(){var b;return a.call(this,(b=(h||(h=c("WebStorage"))).getLocalStorage())!=null?b:n)||this}b.available=function(){return!!(h||(h=c("WebStorage"))).getLocalStorage()};return b}(d);g=function(a){babelHelpers.inheritsLoose(b,a);function b(){var b;return a.call(this,(b=(h||(h=c("WebStorage"))).getSessionStorage())!=null?b:n)||this}b.available=function(){return!!(h||(h=c("WebStorage"))).getSessionStorage()};return b}(d);var o=function(){function a(){this._store={}}var b=a.prototype;b.getStore=function(){return this._store};b.keys=function(){return Object.keys(this._store)};b.get=function(a){return this._store[a]===void 0?null:this._store[a]};b.set=function(a,b){this._store[a]=b};b.remove=function(a){a in this._store&&delete this._store[a]};b.clear=function(){this._store={}};b.clearWithPrefix=function(a){a=a||"";var b=this.keys();for(var c=0;c<b.length;c++){var d=b[c];d.startsWith(a)&&this.remove(d)}};a.available=function(){return!0};return a}(),p={memory:o,localstorage:e,sessionstorage:g};a=function(){function a(a,d){this._changeCallbacks=[];this._key_prefix="_cs_";this._exception=null;d&&(this._key_prefix=d);a==="AUTO"||!a?d="memory":d=a;d&&(!p[d]||!p[d].available()?((i||(i=c("ExecutionEnvironment"))).canUseDOM,this._backend=new o()):this._backend=new p[d]());a=this.useBrowserStorage();a&&b("cr:6943").listen(window,"storage",this._onBrowserValueChanged.bind(this));d=a?this._backend.getStore().getItem(m):this._backend.getStore()[m];d!==l&&this.clearOwnKeys()}var d=a.prototype;d.useBrowserStorage=function(){return this._backend.getStore()===(h||(h=c("WebStorage"))).getLocalStorage()||this._backend.getStore()===(h||(h=c("WebStorage"))).getSessionStorage()};d.addValueChangeCallback=function(a){var b=this;this._changeCallbacks.push(a);return{remove:function(){b._changeCallbacks.slice(b._changeCallbacks.indexOf(a),1)}}};d._onBrowserValueChanged=function(a){this._changeCallbacks&&String(a.key).startsWith(this._key_prefix)&&this._changeCallbacks.forEach(function(b){b(a.key,a.oldValue,a.newValue)})};d.keys=function(){var a=this,b=[];(j||(j=c("ErrorGuard"))).guard(function(){if(a._backend){var c=a._backend.keys(),d=a._key_prefix.length;for(var e=0;e<c.length;e++)c[e].substr(0,d)==a._key_prefix&&b.push(c[e].substr(d))}},{name:"CacheStorage"})();return b};d.set=function(d,e,f){if(this._backend){if(this.useBrowserStorage()&&a._persistentWritesDisabled){this._exception=c("err")("writes disabled");return!1}var g;typeof e==="string"?g=k+e:!f?(g={__t:Date.now(),__v:e},g=b("cr:8958").stringify(g)):g=b("cr:8958").stringify(e);f=this._backend;e=this._key_prefix+d;d=!0;var h=null;while(d)try{h=null,f.set(e,g),d=!1}catch(a){h=a;var i=f.keys().length;this._evictCacheEntries();d=f.keys().length<i}if(h!==null){this._exception=h;return!1}else{this._exception=null;return!0}}this._exception=c("err")("no back end");return!1};d.getLastSetExceptionMessage=function(){return this._exception?this._exception.message:null};d.getLastSetException=function(){return this._exception};d.getStorageKeyCount=function(){var a=this._backend;return a?a.keys().length:0};d._evictCacheEntries=function(){var c=[],d=this._backend;d.keys().forEach(function(e){if(e===m)return;var g=d.get(e);if(g===void 0){d.remove(e);return}if(a._hasMagicPrefix(g))return;try{g=b("cr:8958").parse(g,f.id)}catch(a){d.remove(e);return}g&&g.__t!==void 0&&g.__v!==void 0&&c.push([e,g.__t])});c.sort(function(a,b){return a[1]-b[1]});for(var e=0;e<Math.ceil(c.length/2);e++)d.remove(c[e][0])};d.get=function(d,e){var g;if(this._backend){(j||(j=c("ErrorGuard"))).applyWithGuard(function(){g=this._backend.get(this._key_prefix+d)},this,[],{onError:function(){g=null},name:"CacheStorage:get"});if(g!=null)if(a._hasMagicPrefix(g))g=g.substr(k.length);else try{g=b("cr:8958").parse(g,f.id),g&&g.__t!==void 0&&g.__v!==void 0&&(g=g.__v)}catch(a){g=void 0}else g=void 0}g===void 0&&e!==void 0&&(g=e,this.set(d,g));return g};d.remove=function(a){this._backend&&(j||(j=c("ErrorGuard"))).applyWithGuard(this._backend.remove,this._backend,[this._key_prefix+a],{name:"CacheStorage:remove"})};d._setVersion=function(){var a=this;(j||(j=c("ErrorGuard"))).applyWithGuard(function(){a.useBrowserStorage()?a._backend.getStore().setItem(m,l):a._backend.getStore()[m]=l},this,[],{name:"CacheStorage:setVersion"})};d.clear=function(){this._backend&&((j||(j=c("ErrorGuard"))).applyWithGuard(this._backend.clear,this._backend,[],{name:"CacheStorage:clear"}),this._setVersion())};d.clearOwnKeys=function(){this._backend&&((j||(j=c("ErrorGuard"))).applyWithGuard(this._backend.clearWithPrefix,this._backend,[this._key_prefix],{name:"CacheStorage:clearOwnKeys"}),this._setVersion())};a.getAllStorageTypes=function(){return Object.keys(p)};a._hasMagicPrefix=function(a){return a.substr(0,k.length)===k};a.disablePersistentWrites=function(){a._persistentWritesDisabled=!0};return a}();a._persistentWritesDisabled=!1;f.exports=a}),34);
__d("CometSuspenseFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1828945");b=d("FalcoLoggerInternal").create("comet_suspense",a);e=b;g["default"]=e}),98);
__d("CrossWindowEventEmitter",["CacheStorage","EventEmitter","cr:8958"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(d,a);function d(d){var e;e=a.call(this)||this;e.$CrossWindowEventEmitter$p_2=d;e.$CrossWindowEventEmitter$p_1=new(c("CacheStorage"))("localstorage",d+":");e.$CrossWindowEventEmitter$p_1.addValueChangeCallback(function(c,d,f){d=c.split(":")[1];try{c=b("cr:8958").parse(f)}catch(a){c=void 0}if(c&&c.__v){(f=a.prototype.emit).call.apply(f,[babelHelpers.assertThisInitialized(e),d].concat(c.__v))}});return e}var e=d.prototype;e.emit=function(b){var c;for(var d=arguments.length,e=new Array(d>1?d-1:0),f=1;f<d;f++)e[f-1]=arguments[f];this.emitRemote.apply(this,[b].concat(e));(c=a.prototype.emit).call.apply(c,[this,b].concat(e))};e.emitRemote=function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];this.$CrossWindowEventEmitter$p_1.set(a,c)};return d}(c("EventEmitter"));g["default"]=a}),98);
__d("EventListenerImplForCacheStorage",["CometEventListener"],(function(a,b,c,d,e,f){"use strict";f["default"]=b("CometEventListener")}),66);
__d("IrisSubscribeCheckerUtils",[],(function(a,b,c,d,e,f){"use strict";function a(){if(typeof window!=="undefined"){var a;a=(a=window.location)==null?void 0:a.hostname;if(a==="m.facebook.com"||a==="mobile.facebook.com"||a==="mtouch.facebook.com")return!1}return!0}f.getIrisSubscribeCheckerInitialRunningState=a}),66);
__d("MqttEnvWebSocket",[],(function(a,b,c,d,e,f){"use strict";function a(a){return new WebSocket(a)}f.createWebSocket=a}),66);
__d("MqttEnv",["MqttEnvWebSocket","clearTimeout","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";b=Object.freeze({mqtt_waterfall_log_client_sampling:1,mqtt_ws_polling_enabled:3,mqtt_lp_use_fetch:9,mqtt_fast_lp:11,mqtt_lp_no_delay:12,mqtt_enable_publish_over_polling:13,mqttweb_global_connection_counter:15});e=function(){var a=b.prototype;a.random=function(){return this.$1!=null?this.$1():Math.random()};a.isUserLoggedInNow=function(){return this.$2!=null?this.$2():!0};a.setIsUserLoggedInNow=function(a){this.$2=a};a.clearTimeout=function(a){if(this.$3!=null){this.$3(a);return}c("clearTimeout")(a)};a.setTimeout=function(a,b){for(var d=arguments.length,e=new Array(d>2?d-2:0),f=2;f<d;f++)e[f-2]=arguments[f];return this.$4!=null?this.$4.apply(null,arguments):c("setTimeout").apply(null,arguments)};a.getLoggerInstance=function(){return this.$5!=null?this.$5():h.getInstance()};a.genGk=function(a){return this.$6!=null?this.$6(a):!1};a.createSocket=function(a,b){return this.$7!=null?this.$7(a,b):d("MqttEnvWebSocket").createWebSocket(a)};a.scheduleCallback=function(a){return this.$8!=null?this.$8(a):a()};a.scheduleLoggingCallback=function(a){return this.$9!=null?this.$9(a):a()};a.configRead=function(a,b){return this.$10!=null?this.$10(a,b):b};a.configWrite=function(a,b){this.$11!=null&&this.$11(a,b)};function b(){this.$1=null,this.$2=null,this.$3=null,this.$4=null,this.$5=null,this.$6=null,this.$7=null,this.$8=null,this.$9=null,this.$10=null,this.$11=null}a.initialize=function(a,b,c,d,e,f,g,h,i,j,k){this.$1=a,this.$2=b,this.$3=c,this.$4=d,this.$5=e,this.$6=f,this.$7=g,this.$8=h,this.$9=i,this.$10=j,this.$11=k};return b}();var h=function(){function a(){}a.getInstance=function(){return new a()};var b=a.prototype;b.setAppId=function(a){};b.eventLogConnect=function(a){};b.eventLogPull=function(a){};b.eventLogPullFinish=function(a){};b.eventLogDisconnect=function(a){};b.eventLogOutgoingPublish=function(a){};b.eventLogIncomingPublish=function(a){};b.eventLogPublishTimeout=function(a){};b.eventLogMiscellaneousError=function(a){};b.logIfLoggedOut=function(){};b.logError=function(a){};b.logErrorWarn=function(a){};b.logWarn=function(a){};b.debugTrace=function(a){};b.bumpCounter=function(a){};b.getBrowserConnectivity=function(){return!0};return a}(),i=new e();function a(a){i.setIsUserLoggedInNow(a)}g.MqttGkNames=b;g.Env=i;g.setIsUserLoggedInNow=a}),98);
__d("IrisSubscribeChecker",["IrisSubscribeCheckerUtils","MqttEnv"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a){this.$1=d("MqttEnv").Env.getLoggerInstance(),this.$2=null,this.$3=!1,this.$4=!1,this.$5=!1,this.$6=d("IrisSubscribeCheckerUtils").getIrisSubscribeCheckerInitialRunningState(),this.$7=0,this.$8=a}var b=a.prototype;b.start=function(){this.$6=!0};b.stop=function(){this.$6=!1,this.$9()};b.onConnectAttempt=function(){};b.onConnectFailure=function(){this.$9()};b.onConnected=function(){var a=this;this.$7++;this.$9();this.$3=!1;this.$4=!1;this.$5=!1;this.$6&&(this.$2=d("MqttEnv").Env.setTimeout(function(){a.$10()},8e3))};b.onConnectSuccess=function(){};b.onConnectionLost=function(){this.$9()};b.onConnectionDisconnect=function(){};b.onSubscribe=function(a){a==="/t_ms"&&(this.$3=!0)};b.onUnsubscribe=function(a){};b.onPublish=function(a){(a==="/messenger_sync_get_diffs"||a==="/messenger_sync_create_queue")&&(this.$4=!0,this.$3&&(this.$5=!0,this.$9()))};b.onMessage=function(a){};b.onWSFatal=function(){};b.$9=function(){this.$2&&(d("MqttEnv").Env.clearTimeout(this.$2),this.$2=null)};b.$10=function(){if(this.$4===!1){var a=this.$7==1?"no_iris_session_initialConnect":"no_iris_session";this.$1.bumpCounter(a);this.$8()?this.$1.bumpCounter(a+".withProvider"):this.$1.bumpCounter(a+".withoutProvider");this.$3===!0?this.$1.bumpCounter(a+".withTopicSubscribe"):this.$1.bumpCounter(a+".withoutTopicSubscribe")}this.$3===!1&&this.$1.bumpCounter("no_iris_topic_subscribe");this.$3===!0&&this.$4===!0&&this.$5===!1&&this.$1.bumpCounter("session_before_topic_subscribe")};return a}();g["default"]=a}),98);
__d("MqttGlobalStreamCounter",["CrossWindowEventEmitter","MqttEnv","uuidv4"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(){var a=this;this.isTabClosed=!1;this.otherTabs=new Map();this.thisTab={totalConnectionStreams:0};this.broadcastChannel=new(c("CrossWindowEventEmitter"))("MqttGlobalStreamCounter");this.broadcastChannel.addListener("count-updated",function(b){b=JSON.parse(String(b));var c=!1;a.otherTabs.has(b.key)||(c=!0);b.event==="DELETE"?a.otherTabs["delete"](b.key):b.event==="UPDATE"&&b.value!=null&&(a.otherTabs.set(b.key,b.value),c&&a.$1())});this.tabID=c("uuidv4")()}var b=a.prototype;b.streamRequested=function(){this.thisTab.totalConnectionStreams++,this.$1()};b.streamClosed=function(){this.thisTab.totalConnectionStreams>0&&(this.thisTab.totalConnectionStreams--,this.$1())};b.tabClosed=function(){if(this.isTabClosed)return;this.isTabClosed=!0;var a={key:this.tabID,event:"DELETE"};this.broadcastChannel.emitRemote("count-updated",JSON.stringify(a))};b.getGlobalState=function(){var a={totalConnectionStreams:this.thisTab.totalConnectionStreams};this.otherTabs.forEach(function(b){a.totalConnectionStreams+=b.totalConnectionStreams});return a};b.$1=function(){var a={key:this.tabID,event:"UPDATE",value:this.thisTab};this.broadcastChannel.emitRemote("count-updated",JSON.stringify(a))};b.isEnabled=function(){return!0};return a}();var h=d("MqttEnv").Env.genGk(d("MqttEnv").MqttGkNames.mqttweb_global_connection_counter)?new a():{streamRequested:function(){},streamClosed:function(){},tabClosed:function(){},getGlobalState:function(){return{totalConnectionStreams:-1}},isEnabled:function(){return!1}};b=function(){return h};g.getInstance=b}),98);
__d("MqttAnalyticsHook",["MqttEnv","MqttGlobalStreamCounter"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(){this.$1=d("MqttEnv").Env.getLoggerInstance(),this.$2=d("MqttGlobalStreamCounter").getInstance(),this.$3=0,this.$4=0,this.$5=0,this.$6=0,this.$1.bumpCounter("session_start"),d("MqttEnv").Env.isUserLoggedInNow()||this.$1.bumpCounter("session_start.logout")}var b=a.prototype;b.onConnectAttempt=function(){this.$1.bumpCounter("ws_connect_attempt"),this.$2.streamRequested()};b.onConnectFailure=function(){this.$4++,this.$1.bumpCounter("ws_connect_failure"),this.$1.debugTrace("connect","Connect failed existing streams count "+this.$2.getGlobalState().totalConnectionStreams),this.$2.streamClosed()};b.onConnected=function(){this.$1.bumpCounter("ws_connect_connected")};b.onConnectSuccess=function(){this.$3===0&&this.$1.bumpCounter("ws_connect_first_success"),this.$3++,this.$1.bumpCounter("ws_connect_success")};b.onConnectionLost=function(){this.$1.bumpCounter("ws_disconnect")};b.onConnectionDisconnect=function(){this.$2.streamClosed()};b.onSubscribe=function(a){};b.onUnsubscribe=function(a){};b.onPublish=function(a){this.$1.bumpCounter("ws_publish."+a)};b.onMessage=function(a){this.$1.bumpCounter("message_arrived."+a)};b.onWSFatal=function(){this.$1.bumpCounter("ws_fatal")};b.onPollRequestSent=function(){this.$1.bumpCounter("polling_request_send"),this.$2.streamRequested()};b.onPollRequestSuccess=function(){this.$1.bumpCounter("polling_request_succeed"),this.$5===0&&this.$1.bumpCounter("polling_first_success"),this.$5++};b.onPollResponse=function(a){this.$1.bumpCounter("lp.message_arrived."+a)};b.onPollFinish=function(){this.$1.bumpCounter("polling_request_finish"),this.$2.streamRequested()};b.onPollRequestFailed=function(a){this.$1.bumpCounter("polling_request_failed"),this.$1.bumpCounter("polling_request_failed_"+a),this.$6++,this.$1.debugTrace("PollRequest","Request failed existing streams count "+this.$2.getGlobalState().totalConnectionStreams),this.$2.streamClosed()};b.onPollShutdownAbort=function(){this.$2.streamClosed()};b.onTabClose=function(){this.$2.tabClosed()};return a}();g["default"]=a}),98);
__d("MqttConnectionHookCollection",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(){this.$1=new Set()}var b=a.prototype;b.addHook=function(a){this.$1.add(a)};b.removeHook=function(a){this.$1["delete"](a)};b.onConnectAttempt=function(){this.$1.forEach(function(a){a.onConnectAttempt==null?void 0:a.onConnectAttempt()})};b.onConnectFailure=function(){this.$1.forEach(function(a){a.onConnectFailure==null?void 0:a.onConnectFailure()})};b.onConnected=function(){this.$1.forEach(function(a){a.onConnected==null?void 0:a.onConnected()})};b.onConnectSuccess=function(){this.$1.forEach(function(a){a.onConnectSuccess==null?void 0:a.onConnectSuccess()})};b.onConnectionLost=function(){this.$1.forEach(function(a){a.onConnectionLost==null?void 0:a.onConnectionLost()})};b.onConnectionDisconnect=function(){this.$1.forEach(function(a){a.onConnectionDisconnect==null?void 0:a.onConnectionDisconnect()})};b.onSubscribe=function(a){this.$1.forEach(function(b){b.onSubscribe==null?void 0:b.onSubscribe(a)})};b.onUnsubscribe=function(a){this.$1.forEach(function(b){b.onUnsubscribe==null?void 0:b.onUnsubscribe(a)})};b.onPublish=function(a){this.$1.forEach(function(b){b.onPublish==null?void 0:b.onPublish(a)})};b.onMessage=function(a){this.$1.forEach(function(b){b.onMessage==null?void 0:b.onMessage(a)})};b.onWSFatal=function(){this.$1.forEach(function(a){a.onWSFatal==null?void 0:a.onWSFatal()})};return a}();f["default"]=a}),66);
__d("MqttProtocolUtils",["MqttEnv"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){if(a==null)return b;var c=new Uint8Array(a.length+b.length);c.set(a);c.set(b,a.length);return c}function b(a,b){b=b;var c=0,d=1,e;do{if(b===a.length)return null;e=a[b++];c+=(e&127)*d;d*=128}while((e&128)!==0);return{offset:b,value:c}}function c(a){a=a;var b=new Array(1);for(var c=0;c<4;c++){var d=a%128;a>>=7;if(a>0)b[c]=d|128;else{b[c]=d;break}}return b}function h(a,b,c){c=c;b[c++]=a>>8;b[c++]=a%256;return c}function e(a,b){return 256*a[b]+a[b+1]}function f(a){var b=0;for(var c=0,d=a.length;c<d;c++){var e=a.charCodeAt(c);e<128?b+=1:e<2048?b+=2:e>=55296&&e<=56319?(b+=4,c++):b+=3}return b}function i(a,b,c,d){d=h(b,c,d);j(a,c,d);return d+b}function j(a,b,c){c=c;for(var d=0,e=a.length;d<e;d++){var f=a.charCodeAt(d);f<128?b[c++]=f:f<2048?(b[c++]=192|f>>6,b[c++]=128|f&63):f<55296||f>=57344?(b[c++]=224|f>>12,b[c++]=128|f>>6&63,b[c++]=128|f&63):(f=65536+((f&1023)<<10|a.charCodeAt(++d)&1023),b[c++]=240|f>>18,b[c++]=128|f>>12&63,b[c++]=128|f>>6&63,b[c++]=128|f&63)}}function k(a,b,c){var d=[],e=b,f=0;while(e<b+c){var g=a[e++];if(g<128)d[f++]=String.fromCharCode(g);else if(g>191&&g<224){var h=a[e++];d[f++]=String.fromCharCode((g&31)<<6|h&63)}else if(g>239&&g<365){h=a[e++];var i=a[e++],j=a[e++];h=((g&7)<<18|(h&63)<<12|(i&63)<<6|j&63)-65536;d[f++]=String.fromCharCode(55296+(h>>10));d[f++]=String.fromCharCode(56320+(h&1023))}else{i=a[e++];j=a[e++];d[f++]=String.fromCharCode((g&15)<<12|(i&63)<<6|j&63)}}return d.join("")}var l=function(){function a(a,b,c,d){this.$1=a,this.$2=b,this.$5=c,this.$6=d,this.$4=!1}var b=a.prototype;b.$7=function(){var a=this;this.$4?(this.$4=!1,this.$5(),this.$3=d("MqttEnv").Env.setTimeout(function(){a.$7()},this.$2()*1e3)):this.$6()};b.reset=function(){var a=this;this.$4=!0;this.$3&&(d("MqttEnv").Env.clearTimeout(this.$3),this.$3=null);var b=this.$1()*1e3;b>0&&(this.$3=d("MqttEnv").Env.setTimeout(function(){a.$7()},b))};b.cancel=function(){this.$3&&(d("MqttEnv").Env.clearTimeout(this.$3),this.$3=null)};return a}();g.UTF8Length=f;g.convertStringToUTF8=j;g.concatBuffers=a;g.decodeMultiByteInt=b;g.convertUTF8ToString=k;g.encodeMultiByteInt=c;g.writeUInt16BE=h;g.readUInt16BE=e;g.writeString=i;g.Pinger=l}),98);
__d("MqttUtils",["MqttEnv"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){return h(a,"sid",b.toString())}function b(a,b){var c=a;b.forEach(function(a,b){c=h(c,b,a)});return c}function h(a,b,c){if(a.indexOf("?")>0)return a+"&"+b+"="+c;else return a+"?"+b+"="+c}function c(){return Math.floor(d("MqttEnv").Env.random()*Number.MAX_SAFE_INTEGER)}function i(a,b,c){var e=arguments.length>1?a.then(b,c):a;e.then(null,function(a){d("MqttEnv").Env.setTimeout(function(){if(a instanceof Error)throw a;else throw new Error("promiseDone")},0)})}function e(a,b,c,e){var f=!1;d("MqttEnv").Env.setTimeout(function(){f||(f=!0,c(new Error("promise timeout")))},e);i(a,function(a){f||(f=!0,b(a))},function(a){f||(f=!0,c(a))})}function f(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];var e=0;return a.replace(/%s/g,function(){return String(c[e++])})}g.endpointWithSessionId=a;g.endpointWithExtraParameters=b;g.endpointWithExtraParameter=h;g.generateSessionId=c;g.promiseDone=i;g.promiseDoneWithTimeout=e;g.sprintf=f}),98);
__d("MqttProtocolCodec",["MqttProtocolUtils","MqttUtils","err"],(function(a,b,c,d,e,f,g){"use strict";var h=Object.freeze({CONNECT:1,CONNACK:2,PUBLISH:3,PUBACK:4,SUBSCRIBE:8,SUBACK:9,UNSUBSCRIBE:10,UNSUBACK:11,PINGREQ:12,PINGRESP:13,DISCONNECT:14}),i=[0,6,77,81,73,115,100,112,3];function j(a,b){b=b;var e=b,f=a[b],g=f>>4;b+=1;var i=d("MqttProtocolUtils").decodeMultiByteInt(a,b);if(i==null)return{position:e,wireMessage:null};b=i.offset;i=b+i.value;if(i>a.length)return{position:e,wireMessage:null};var j;switch(g){case h.CONNACK:e=a[b++];e=!!(e&1);var o=a[b++];j=new m(e,o);break;case h.PUBLISH:e=f&15;o=e>>1&3;f=d("MqttProtocolUtils").readUInt16BE(a,b);b+=2;var r=d("MqttProtocolUtils").convertUTF8ToString(a,b,f);b+=f;f=null;o===1&&(f=d("MqttProtocolUtils").readUInt16BE(a,b),b+=2);var s=p.createWithBytes(a.subarray(b,i)),t=(e&1)===1;e=(e&8)===8;j=new q(r,s,o,f,t,e);break;case h.PINGREQ:j=new k("PINGREQ");break;case h.PINGRESP:j=new k("PINGRESP");break;case h.PUBACK:case h.UNSUBACK:r=d("MqttProtocolUtils").readUInt16BE(a,b);j=new n(g===h.PUBACK?"PUBACK":"UNSUBACK",r);break;case h.SUBACK:s=d("MqttProtocolUtils").readUInt16BE(a,b);b+=2;o=a.subarray(b,i);j=new l(s,o);break;default:throw c("err")(d("MqttUtils").sprintf("Invalid MQTT message type %s.",g))}return{position:i,wireMessage:j}}function a(a){var b=[],c=0;while(c<a.length){var d=j(a,c),e=d.wireMessage;c=d.position;if(e)b.push(e);else break}d=null;c<a.length&&(d=a.subarray(c));return{messages:b,remaining:d}}b=function(){function a(a){this.messageType=h[a]}var b=a.prototype;b.encode=function(){throw new TypeError("Cannot abstract class WireMessage")};return a}();var k=function(b){babelHelpers.inheritsLoose(a,b);function a(a){return b.call(this,a)||this}var c=a.prototype;c.encode=function(){var a=new ArrayBuffer(2),b=new Uint8Array(a);b[0]=(this.messageType&15)<<4;return a};return a}(b);e=function(a){babelHelpers.inheritsLoose(b,a);function b(){return a.call(this,"DISCONNECT")||this}var c=b.prototype;c.encode=function(){var a=(this.messageType&15)<<4,b=new ArrayBuffer(2),c=new Uint8Array(b);c[0]=a;c.set(d("MqttProtocolUtils").encodeMultiByteInt(0),1);return b};return b}(b);var l=function(b){babelHelpers.inheritsLoose(a,b);function a(a,c){var d;d=b.call(this,"SUBACK")||this;d.messageIdentifier=a;d.returnCode=c;return d}return a}(b),m=function(b){babelHelpers.inheritsLoose(a,b);function a(a,c){var d;d=b.call(this,"CONNACK")||this;d.sessionPresent=a;d.returnCode=c;return d}return a}(b),n=function(b){babelHelpers.inheritsLoose(a,b);function a(a,c){a=b.call(this,a)||this;a.messageIdentifier=c;return a}var c=a.prototype;c.encode=function(){var a=(this.messageType&15)<<4,b=2,c=d("MqttProtocolUtils").encodeMultiByteInt(b),e=c.length+1;b=new ArrayBuffer(b+e);var f=new Uint8Array(b);f[0]=a;f.set(c,1);e=d("MqttProtocolUtils").writeUInt16BE(this.messageIdentifier,f,e);return b};return a}(b);f=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c){var d;d=a.call(this,"CONNECT")||this;d.clientId=b;d.connectOptions=c;return d}var c=b.prototype;c.encode=function(){var a,b=(this.messageType&15)<<4,c=i.length+3;c+=(a=d("MqttProtocolUtils")).UTF8Length(this.clientId)+2;c+=a.UTF8Length(this.connectOptions.userName)+2;var e=a.encodeMultiByteInt(c);c=new ArrayBuffer(1+e.length+c);var f=new Uint8Array(c);f[0]=b;b=1;f.set(e,1);b+=e.length;f.set(i,b);b+=i.length;e=2|128;f[b++]=e;b=a.writeUInt16BE(this.connectOptions.getKeepAliveIntervalSeconds(),f,b);b=a.writeString(this.clientId,a.UTF8Length(this.clientId),f,b);b=a.writeString(this.connectOptions.userName,a.UTF8Length(this.connectOptions.userName),f,b);return c};return b}(b);var o=function(b){babelHelpers.inheritsLoose(a,b);function a(a,c,e,f){var g;g=b.call(this,a)||this;g.topic=c;if(e<0&&e>1||e===1&&f==null)throw new TypeError(d("MqttUtils").sprintf("Argument Invalid. qos: %s messageType: %s.",e,a));g.qos=e;g.messageIdentifier=f;return g}var c=a.prototype;c.encode=function(){var a=(this.messageType&15)<<4;a|=2;var b=d("MqttProtocolUtils").UTF8Length(this.topic),c=2+b+2;this.messageType===h.SUBSCRIBE&&(c+=1);var e=d("MqttProtocolUtils").encodeMultiByteInt(c);c=new ArrayBuffer(1+e.length+c);var f=new Uint8Array(c);f[0]=a;a=1;f.set(e,1);a+=e.length;this.messageIdentifier!=null&&(a=d("MqttProtocolUtils").writeUInt16BE(this.messageIdentifier,f,a));a=d("MqttProtocolUtils").writeString(this.topic,b,f,a);this.messageType===h.SUBSCRIBE&&this.qos!=null&&(f[a++]=this.qos);return c};return a}(b),p=function(){function a(a,b){this.payloadString=a,this.payloadBytes=b}a.createWithString=function(b){var c=new Uint8Array(new ArrayBuffer(d("MqttProtocolUtils").UTF8Length(b)));d("MqttProtocolUtils").convertStringToUTF8(b,c,0);return new a(b,c)};a.createWithBytes=function(b){var c=d("MqttProtocolUtils").convertUTF8ToString(b,0,b.length);return new a(c,b)};var b=a.prototype;b.string=function(){return this.payloadString};b.bytes=function(){return this.payloadBytes};return a}(),q=function(b){babelHelpers.inheritsLoose(a,b);function a(a,c,d,e,f,g){var h;h=b.call(this,"PUBLISH")||this;h.topic=a;h.payloadMessage=c;h.qos=d;h.messageIdentifier=e;h.retained=f!=null?f:!1;h.duplicate=g!=null?g:!1;if(h.qos===1&&h.messageIdentifier==null)throw new TypeError("Argument Invalid. messageIdentifier: null and qos: 1");return h}var c=a.prototype;c.encode=function(){var a=(this.messageType&15)<<4;this.duplicate&&(a|=8);a=a|=this.qos<<1;this.retained&&a!=1;var b=d("MqttProtocolUtils").UTF8Length(this.topic),c=b+2,e=this.qos===0?0:2;c+=e;e=this.payloadMessage.bytes();c+=e.byteLength;var f=d("MqttProtocolUtils").encodeMultiByteInt(c);c=new ArrayBuffer(1+f.length+c);var g=new Uint8Array(c);g[0]=a;g.set(f,1);a=1+f.length;a=d("MqttProtocolUtils").writeString(this.topic,b,g,a);this.qos!==0&&this.messageIdentifier!=null&&(a=d("MqttProtocolUtils").writeUInt16BE(this.messageIdentifier,g,a));g.set(e,a);return c};return a}(b),r=p.createWithString,s=p.createWithBytes;b={Base:b,ConnAck:m,Connect:f,Disconnect:e,Ping:k,PubAckUnsubAck:n,Publish:q,Subscription:o};g.MESSAGE_TYPE=h;g.decodeMessage=j;g.decodeByteMessages=a;g.Message=p;g.createMessageWithString=r;g.createMessageWithBytes=s;g.WireMessage=b}),98);
__d("MqttTypes",[],(function(a,b,c,d,e,f){"use strict";a=function(a,b,c){this.errorCode=a,this.errorName=b,this.errorMessage=c};b=Object.freeze({APP_DISCONNECT:new a(7,"APP_DISCONNECT","Disconnect initiated by app"),BROWSER_CLOSE:new a(11,"BROWSER_CLOSE","Browser closed"),CONNACK_FAILURE:new a(5,"CONNACK_FAILURE","Connection failure due to connack"),CONNECT_TIMEOUT:new a(4,"CONNECT_TIMEOUT","Connect timed out"),INVALID_DATA_TYPE:new a(3,"INVALID_DATA_TYPE","Received non-arraybuffer from socket."),PING_TIMEOUT:new a(6,"PING_TIMEOUT","Ping timeout"),RECONNECT:new a(10,"RECONNECT","Reconnecting"),SERVER_DISCONNECT:new a(8,"SERVER_DISCONNECT","Disconnect message sent my server"),SOCKET_CLOSE:new a(9,"SOCKET_CLOSE","Socket connection closed"),SOCKET_ERROR:new a(1,"SOCKET_ERROR","Socket error"),SOCKET_MESSAGE:new a(2,"SOCKET_MESSAGE","Unable to parse invalid socket message")});c=function(a,b){this.mqttError=a,this.connAck=b};d=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,d){d===void 0&&(d=null);c=a.call(this,c)||this;c.isRecoverable=b;c.originalError=d;return c}return b}(babelHelpers.wrapNativeSuper(Error));f.MqttError=a;f.MqttErrors=b;f.ConnectFailure=c;f.MqttChannelError=d}),66);
__d("MqttProtocolClient",["ErrorSerializer","MqttEnv","MqttProtocolCodec","MqttProtocolUtils","MqttTypes","err","getErrorSafe"],(function(a,b,c,d,e,f,g){"use strict";var h=20,i=6e4;a=function(){function a(a){var b=this;this.$12=function(a,c,e){c===void 0&&(c=null);b.$9.bumpCounter("protocol.debug.disconnect.internal."+a.errorName);b.$9.bumpCounter("protocol.debug.disconnect.internal");var f=b.$5,g=b.$2,h=g.onConnectFailure,i=g.onConnectionLost;b.setConnected(!1);b.$14();f?d("MqttEnv").Env.scheduleCallback(function(){i(a,c)}):d("MqttEnv").Env.scheduleCallback(function(){h(b.$11,new(d("MqttTypes").ConnectFailure)(a,e!=null?e:-1),c)})};this.$13=function(a){var c=b.$6;if(c==null){b.$9.bumpCounter("protocol.socket_send.failed.socket_null.<message type>");return 0}if(c.readyState!==c.OPEN){b.$9.bumpCounter("protocol.socket_send.failed.socket_not_open.<message type>");return 0}a=a.encode();var d=a.byteLength;c.send(a);return d};this.$3=a;this.$2={getKeepAliveIntervalSeconds:function(){return 10},getKeepAliveTimeoutSeconds:function(){return 10},ignoreSubProtocol:!1,mqttVersion:3,onConnectFailure:function(a,b,c){},onConnection:function(){},onConnectionLost:function(a,b){},onConnectSuccess:function(a){},onMessageArrived:function(a,b,c){},onMessageDelivered:function(a){},userName:""};this.$1="mqttwsclient";this.$4=0;this.$5=!1;this.$9=d("MqttEnv").Env.getLoggerInstance();this.$11=0}var b=a.prototype;b.connect=function(a){var b=this;if(this.$5)throw c("err")("Invalid state: connect - already connected");this.$2=a;this.setConnected(!1);this.$7!=null&&(d("MqttEnv").Env.clearTimeout(this.$7),this.$7=null);this.$7=d("MqttEnv").Env.setTimeout(function(){b.$9.bumpCounter("protocol.error.connect.timeout"),b.$12(d("MqttTypes").MqttErrors.CONNECT_TIMEOUT)},h*1e3);this.$6=d("MqttEnv").Env.createSocket(this.$3);this.$6.binaryType="arraybuffer";this.$6.onopen=function(){b.setConnected(!0),b.$9.debugTrace("Socket-Open","MQTTProtocolClient Socket Open"),b.$11=b.$13(new(d("MqttProtocolCodec").WireMessage.Connect)(b.$1,a)),a.onConnection()};this.$6.onmessage=function(a){a=a.data;if(!(a instanceof ArrayBuffer)){b.$9.bumpCounter("protocol.error.onmessage.type");b.$12(d("MqttTypes").MqttErrors.INVALID_DATA_TYPE);return}try{a=new Uint8Array(a);b.$10!=null&&(a=d("MqttProtocolUtils").concatBuffers(b.$10,a),b.$9.bumpCounter("protocol.debug.usingMessagesBuffer"),delete b.$10,b.$10=null);a=d("MqttProtocolCodec").decodeByteMessages(a);var c=a.messages;b.$10=a.remaining;for(a=0;a<c.length;a++)b.handleMessage(c[a])}catch(a){b.$10=null,b.$9.logError(a,d("MqttTypes").MqttErrors.SOCKET_MESSAGE.errorMessage),b.$9.bumpCounter("protocol.error.onmessage.parse"),b.$12(d("MqttTypes").MqttErrors.SOCKET_MESSAGE,a.message)}};this.$6.onerror=function(a){b.$9.bumpCounter("protocol.error.socket");b.$9.debugTrace("Socket-Error","MQTTProtocolClient Socket Error");a=typeof a==="object"&&a!=null&&"error"in a&&!("message"in a)?a.error:a;b.$12(d("MqttTypes").MqttErrors.SOCKET_ERROR,c("ErrorSerializer").toReadableMessage(c("getErrorSafe")(a)))};this.$6.onclose=function(a){b.$9.bumpCounter("protocol.socket.close"),a.wasClean||b.$9.debugTrace("Socket-Unclean-Close","MQTTProtocolClient error code: "+a.code+" reason: "+a.reason),b.$12(d("MqttTypes").MqttErrors.SOCKET_CLOSE,a.code+" : "+a.reason)};this.$8!=null&&this.$8.cancel();this.$8=new(d("MqttProtocolUtils").Pinger)(a.getKeepAliveIntervalSeconds,a.getKeepAliveTimeoutSeconds,this.$13.bind(this,new(d("MqttProtocolCodec").WireMessage.Ping)("PINGREQ")),function(a,c){return b.$12(d("MqttTypes").MqttErrors.PING_TIMEOUT,a,c)})};b.$14=function(){var a=this;this.setConnected(!1);this.$8!=null&&this.$8.cancel();this.$7!=null&&(d("MqttEnv").Env.clearTimeout(this.$7),this.$7=null);this.$6!=null&&(this.$6.onopen=function(b){a.$9.debugTrace("Socket Open After Teardown","Socket opening after teardown")},this.$6.onmessage=function(a){},this.$6.onerror=function(a){},this.$6.onclose=function(b){a.$9.debugTrace("Socket Close After Teardown","code: "+b.code+", reason: "+b.reason)},this.$6.close(),this.$6=null);this.$2.onConnectSuccess=function(a){};this.$2.onConnectFailure=function(a,b){};this.$2.onConnection=function(){};this.$2.onConnectionLost=function(a){};this.$2.onMessageArrived=function(a,b,c){};this.$2.onMessageDelivered=function(a){}};b.disconnect=function(a){if(this.$6==null||this.$6.readyState!==this.$6.OPEN||!this.$5){this.$14();return}this.$13(new(d("MqttProtocolCodec").WireMessage.Disconnect)());this.$9.bumpCounter("protocol.debug.disconnect");this.$12(a)};b.isConnected=function(){return this.$5};b.setConnected=function(a){this.$5=a};b.subscribe=function(a){if(!this.$5){this.$9.bumpCounter("protocol.error.subscribe.notconnected");throw c("err")("Invalid state: subscribe - not connected")}this.$9.bumpCounter("protocol.subscribe."+a);a=new(d("MqttProtocolCodec").WireMessage.Subscription)("SUBSCRIBE",a,0,this.$15());this.$13(a)};b.unsubscribe=function(a){if(!this.$5){this.$9.bumpCounter("protocol.error.unsubscribe.notconnected");throw c("err")("Invalid state: unsubscribe - not connected")}this.$9.bumpCounter("protocol.unsubscribe."+a);a=new(d("MqttProtocolCodec").WireMessage.Subscription)("UNSUBSCRIBE",a,0,this.$15());this.$13(a)};b.publish=function(a,b,c){return this.$16(a,d("MqttProtocolCodec").createMessageWithString(b),c)};b.publishBinary=function(a,b,c){return this.$16(a,d("MqttProtocolCodec").createMessageWithBytes(b),c)};b.$16=function(a,b,c){this.$5||this.$9.bumpCounter("protocol.error.publish.notconnected");this.$9.bumpCounter("protocol.publish."+a);var e=this.$15();a=new(d("MqttProtocolCodec").WireMessage.Publish)(a,b,c,e);this.$13(a);return e};b.$15=function(){++this.$4===i&&(this.$4=1);return this.$4};b.handleMessage=function(a){var b=this;switch(a.messageType){case d("MqttProtocolCodec").MESSAGE_TYPE.CONNACK:this.$7!=null&&(d("MqttEnv").Env.clearTimeout(this.$7),this.$7=null);if(a instanceof d("MqttProtocolCodec").WireMessage.ConnAck){var e=a;if(e.returnCode!==0){this.$9.bumpCounter("protocol.error.connack.invalidreturncode");this.setConnected(!1);this.$12(d("MqttTypes").MqttErrors.CONNACK_FAILURE,"connack code="+e.returnCode,e.returnCode);return}d("MqttEnv").Env.scheduleCallback(function(){b.$2.onConnectSuccess(b.$11)});this.$8!=null&&this.$8.reset()}break;case d("MqttProtocolCodec").MESSAGE_TYPE.PUBLISH:if(a instanceof d("MqttProtocolCodec").WireMessage.Publish){var f=a;d("MqttEnv").Env.scheduleCallback(function(){b.$2.onMessageArrived(f.topic,f.payloadMessage,f.qos)});e=f.messageIdentifier;this.$9.bumpCounter("protocol.publish.received");if(f.qos===1&&e!=null){e=new(d("MqttProtocolCodec").WireMessage.PubAckUnsubAck)("PUBACK",e);this.$13(e)}}break;case d("MqttProtocolCodec").MESSAGE_TYPE.PUBACK:if(a instanceof d("MqttProtocolCodec").WireMessage.PubAckUnsubAck){e=a;var g=e.messageIdentifier;this.$9.bumpCounter("protocol.puback.received");d("MqttEnv").Env.scheduleCallback(function(){b.$2.onMessageDelivered(g)})}break;case d("MqttProtocolCodec").MESSAGE_TYPE.PINGRESP:this.$8!=null&&this.$8.reset();break;case d("MqttProtocolCodec").MESSAGE_TYPE.DISCONNECT:this.$12(d("MqttTypes").MqttErrors.SERVER_DISCONNECT);break;case d("MqttProtocolCodec").MESSAGE_TYPE.SUBACK:this.$9.bumpCounter("protocol.suback.received");break;case d("MqttProtocolCodec").MESSAGE_TYPE.UNSUBACK:this.$9.bumpCounter("protocol.unsuback.received");break;default:this.$9.logError(c("err")("MqttProtocolClient: Received unhandled message type: "+a.messageType),"Received unhandled message type");this.$9.bumpCounter("protocol.error.handlemessage.unsupportedtype");break}};return a}();g["default"]=a}),98);
__d("MqttPublishListener",[],(function(a,b,c,d,e,f){"use strict";a=Object.freeze({NOT_CONNECTED:"NOT_CONNECTED",PUBLISH_ERROR:"PUBLISH_ERROR",QUEUED:"QUEUED",SENT:"SENT",ACKED:"ACKED",NOT_ACKED:"NOT_ACKED"});f.MqttPublishEvent=a}),66);
__d("MqttUserNameUtils",[],(function(a,b,c,d,e,f){"use strict";function a(a){return a!=null?a:typeof document==="object"&&document&&document.hasFocus&&document.hasFocus()}function b(){return navigator.userAgent}f.getMqttForegroundStatus=a;f.getUserAgent=b}),66);
__d("MqttUserName",["MqttUserNameUtils"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b,c,e,f,g,h,i,j,k,l,m){h===void 0&&(h=null),i===void 0&&(i=""),j===void 0&&(j="websocket"),k===void 0&&(k=null),l===void 0&&(l=null),m===void 0&&(m=null),this.$1=a,this.$2=b,this.$3=c,this.$4=e,this.$5=f,this.$6=g,this.$7=d("MqttUserNameUtils").getMqttForegroundStatus(),this.$8=h,this.$9=i,this.$10=j,this.$11=k,this.$12=l,this.$13=m}var b=a.prototype;b.gen=function(a,b,c,e){e===void 0&&(e=[]);var f=d("MqttUserNameUtils").getMqttForegroundStatus(this.$7);c=c.map(function(a){a={isBase64Publish:!1,messageId:a.messageId,payload:a.payload,qos:a.qos,topic:a.topic};typeof a.payload!=="string"&&(a.payload=btoa(String.fromCharCode.apply(null,a.payload)),a.isBase64Publish=!0);return a});f={a:d("MqttUserNameUtils").getUserAgent(),asi:this.$13,aid:this.$5,aids:this.$12!=null?Object.fromEntries(this.$12):null,chat_on:this.$6,cp:this.$3,ct:this.$10,d:this.$4,dc:"",ecp:this.$2,fg:f,gas:this.$8,mqtt_sid:"",no_auto_fg:!0,p:this.$11,pack:e,php_override:this.$9,pm:c,s:a,st:b,u:this.$1};return JSON.stringify(f)};b.setForegroundState=function(a){this.$7=a};b.setChatVisibility=function(a){this.$6=a};b.getEndpointCapabilities=function(){return this.$2};b.getDeviceId=function(){return this.$4};b.setEndpointCapabilities=function(a){this.$2=a};b.getIsGuestAuthStringPresent=function(){return this.$8!==null};return a}();g["default"]=a}),98);
__d("MqttConnection",["MqttConnectionHookCollection","MqttEnv","MqttProtocolClient","MqttPublishListener","MqttTypes","MqttUserName","MqttUtils","Promise","err"],(function(a,b,c,d,e,f,g){"use strict";var h,i=15,j=15,k=1,l=1,m=64,n="publish",o="subscribe",p="unsubscribe",q=function(a){},r=18e4,s=5*1e3,t=15,u=21;a=function(){function a(){var a=this;this.$38=function(){return a.$20};this.$39=function(){return a.$21};this.$8=!1;this.$10=d("MqttEnv").Env.getLoggerInstance();this.$11="Disconnected";this.$17=new Set();this.$24=new Map();this.$12=0;this.$13=0;this.$14=0;this.$15=0;this.$16=0;this.$8=!1;this.$5="";this.$6=new(c("MqttUserName"))("",0,1,"",0,!0);this.$9=0;this.$18=0;this.$19=!1;this.$7=null;var b=function(){};this.$1=b;this.$2=b;this.$3=b;this.$25=!1;this.$26=!1;this.$27=new(c("MqttConnectionHookCollection"))();this.$4=function(){return[]};this.$20=i;this.$21=j;this.$22=null;this.$23=0}var e=a.prototype;e.run=function(a){var b=this,c=a.endpoint,d=a.extraConnectMessageProvider,e=a.mqttUserName,f=a.onJSError,g=a.onMessageReceived,h=a.onStateChange;a=a.subscribedTopics;if(this.$8){this.$10.debugTrace("run","Run called while in running state.");return}this.$1=h;this.$3=g;this.$5=c;this.$6=e;this.$8=!0;this.$18=Date.now();this.$12=0;this.$13=0;this.$2=f||q;a&&a.forEach(function(a){return b.$17.add(a)});this.$4=d;this.$20=i;this.$21=j;this.$28()};e.shutdown=function(a){this.$29(a),this.$30("shutdown"),this.$8=!1,this.$10.debugTrace("shutdown","Shutdown")};e.subscribe=function(a){this.$17.add(a);this.ensureConnected(o)&&this.$31(a);return!0};e.publish=function(a,b,c,d){return this.$32(a,b,c,d)};e.publishBinary=function(a,b,c,d){return this.$32(a,b,c,d)};e.$32=function(a,e,f,g){var i={reject:function(a){},resolve:function(){}};g!=null&&(i.listener=g);var j=new(h||(h=b("Promise")))(function(a,b){i.resolve=a,i.reject=b}),k=this.ensureConnected(n);!k?(g==null?void 0:g.onEvent(d("MqttPublishListener").MqttPublishEvent.NOT_CONNECTED),i.reject(c("err")("Client disconnected"))):this.$33(a,e,f,i);return j};e.unsubscribe=function(a){this.$17["delete"](a);this.ensureConnected(p)&&this.$34(a);return!0};e.addHook=function(a){this.$27.addHook(a)};e.removeHook=function(a){this.$27.removeHook(a)};e.isRunning=function(){return this.$8};e.getSessionId=function(){return this.$9};e.hasFatal=function(){return this.$25};e.hasConnectSuccess=function(){return this.$26};e.canPublish=function(){return this.ensureConnected("canPublish")};e.ensureConnected=function(a){return!this.$8||this.$7==null||!this.$7.isConnected()?!1:!0};e.connectionState=function(){return this.$11};e.mqttStateFromConnectionState=function(a){switch(a){case"Connecting":case"TransportConnected":return"Connecting";case"Connected":return"Connected";case"Disconnected":return"Disconnected"}throw c("err")("Unknown state "+a)};e.testOnlyGetSubscribedTopics=function(){return this.$17};e.getDisconnectCount=function(){return this.$23};e.$29=function(a){this.$8&&this.$7!=null&&(this.$35("Disconnected"),this.$7!=null&&(this.$7.disconnect(a),this.$27.onConnectionDisconnect()),this.$7=null)};e.$36=function(a){if(this.$11==="Connected"&&a==="Disconnected"&&this.$18===0){this.$18=Date.now();return}if(a==="Connecting"&&this.$18!==0&&!this.$19){this.$10.bumpCounter("protocol.reconnectstarted");this.$19=!0;return}if(a==="Connected"&&this.$18!==0){a=Date.now()-this.$18;switch(Math.floor(a/3e4)){case 0:this.$10.bumpCounter("protocol.reconnectedwithin30s");break;case 1:this.$10.bumpCounter("protocol.reconnectedwithin60s");break;case 2:this.$10.bumpCounter("protocol.reconnectedwithin90s");break;case 3:this.$10.bumpCounter("protocol.reconnectedwithin120s");break;default:this.$10.bumpCounter("protocol.reconnectedmorethan120s");break}this.$18=0;this.$19=!1;return}};e.$35=function(a){a!==this.$11&&(this.$10.debugTrace("MQTTConnection-updateState","State changed to: "+a),this.$36(a),this.$11=a,this.$1(a))};e.$28=function(){var a=this;if(!this.$8)return;this.$11!=="Disconnected"&&this.$10.debugTrace("Reconnect","Current State not disconnected: "+this.$11);this.$35("Connecting");var b=Date.now(),e=this.$10.getBrowserConnectivity();this.$9=d("MqttUtils").generateSessionId();var f=Array.from(this.$17),g=d("MqttUtils").endpointWithSessionId(this.$5,this.$9);g=d("MqttUtils").endpointWithExtraParameter(g,"cid",this.$6.getDeviceId());try{this.$7=new(c("MqttProtocolClient"))(g);var h=this.$37(),i=h.map(function(a){return a.topic});g=this.$6.gen(this.$9,f,h);this.$7!=null&&(this.$13+=1,this.$7.connect({getKeepAliveIntervalSeconds:this.$38,getKeepAliveTimeoutSeconds:this.$39,ignoreSubProtocol:!0,mqttVersion:3,onConnectFailure:function(c,d,g){return a.$40(d,b,a.$18,f,i,e,c,g)},onConnection:function(){return a.$41(h,f)},onConnectionLost:function(b,c){return a.$42(b,c)},onConnectSuccess:function(c){return a.$43(b,a.$18,f,i,e,c)},onMessageArrived:function(b,c,d){return a.$44(b,c,d)},onMessageDelivered:function(b){return a.$45(b)},userName:g}),this.$10.bumpCounter("protocol.connectattempt"),this.$6.getIsGuestAuthStringPresent()&&this.$10.bumpCounter("guestAuthentication.connectattempt"),this.$27.onConnectAttempt())}catch(a){this.$10.bumpCounter("js_error_in_init_exception"),this.$35("Disconnected"),this.$46(!1,!1,b,this.$18,f,[],e,null,14,"init error - "+a.message),a&&this.$10.logErrorWarn(a,"ws_js_error"),this.$27.onWSFatal(),this.$47(new(d("MqttTypes").MqttChannelError)(!1,"ws_js_error",a),"client_init")}};e.$37=function(){var a=this.$4(),b=65536;return a.map(function(a){a.messageId=b--;return a})};e.$31=function(a){try{if(this.$7!=null){this.$7.subscribe(a);this.$10.debugTrace("#doSubscribe","Subscribing to "+a);this.$27.onSubscribe(a);this.$10.bumpCounter("protocol.subscribe");return!0}}catch(b){this.$10.logError(b,"Exception subscribing"),this.$10.bumpCounter("subscribe_exception."+a),this.$10.bumpCounter("protocol.subscribe.error")}return!1};e.$34=function(a){try{if(this.$7!=null){this.$7.unsubscribe(a);this.$10.debugTrace("#doUnsubscribe","Unsubscribing to "+a);this.$27.onUnsubscribe(a);this.$10.bumpCounter("protocol.unsubscribe");return!0}}catch(b){this.$10.logError(b,"Exception unsubscribing"),this.$10.bumpCounter("unsubscribe_exception."+a),this.$10.bumpCounter("protocol.unsubscribe.error")}return!1};e.$33=function(a,b,c,e){if(this.$7!=null)try{var f;typeof b==="string"?f=this.$7.publish(a,b,c):f=this.$7.publishBinary(a,b,c);this.$10.bumpCounter("protocol.publish");b=f!=null?f:"null";this.$10.debugTrace("#doPublish","publish "+a+" with messageId:"+b+" qos:"+c);this.$27.onPublish(a);(b=e.listener)==null?void 0:b.onEvent(d("MqttPublishListener").MqttPublishEvent.SENT);f!=null?(this.$10.bumpCounter("publish.ack_expected"),this.$24.set(f,e)):e.resolve();return!0}catch(b){this.$10.logError(b,"Exception publishing");this.$10.bumpCounter("publish_exception."+a);e.reject(b);this.$10.bumpCounter("protocol.publish.error");return!1}else return!1};e.$48=function(a){var b=this;a===void 0&&(a=null);if(!this.$8)return;this.$29(d("MqttTypes").MqttErrors.RECONNECT);this.$30("reconnect");this.$49();this.$9=-1;this.$16=0;a=a;a==null&&(a=k*Math.pow(2,this.$12));a=Math.max(a,l);a=Math.min(a,m);a=a*(1e3+d("MqttEnv").Env.random()*200);this.$10.debugTrace("#scheduleReconnect","Reconnect in "+a+" ms");this.$22=d("MqttEnv").Env.setTimeout(function(){b.$28()},a);this.$12+=1};e.$49=function(){var a=Date.now()-this.$16,b=this.$16!==0&&a>s,c=this.$12>t;(b||c)&&(this.$12=0);this.$16!==0&&a<=s&&this.$10.bumpCounter("short_lived_session");c&&this.$10.bumpCounter("connection_attempt_limit")};e.$46=function(a,b,c,d,e,f,g,h,i,j){a?this.$14++:this.$15++;var k=Date.now();this.$10.eventLogConnect({ackReceived:b,attemptNumber:this.$13,connectionState:this.mqttStateFromConnectionState(this.$11),connectionStatus:a,disconnectCount:this.$23,duration:k-c,errorCode:i,errorMessage:j,failTotal:this.$15,hostname:this.$5,messageSizeBytes:h,osConnectivity:g,publishes:f,sessionID:this.$9,subscribedTopics:e,successTotal:this.$14,total_duration:k-d});a&&(this.$13=0)};e.$40=function(a,b,c,d,e,f,g,h){var i=a.mqttError;h=h!=null?i.errorMessage+" - "+h:i.errorMessage;this.$10.debugTrace("connect","Connect failed "+h);this.$10.bumpCounter("protocol.onconnectfailure");this.$6.getIsGuestAuthStringPresent()&&this.$10.bumpCounter("guestAuthentication.onconnectfailure");this.$27.onConnectFailure();this.$46(!1,a.connAck!==-1,b,c,d,e,f,g,i.errorCode,h);if(a.connAck!=null){this.$10.bumpCounter("protocol.connect_failure."+a.connAck);if(a.connAck===u){this.$48(r);return}}this.$48()};e.$41=function(a,b){var c=this;this.$10.bumpCounter("protocol.onconnection");this.$10.debugTrace("Connect","Socket established");this.$27.onConnected();this.$6.getIsGuestAuthStringPresent()&&this.$10.bumpCounter("guestAuthentication.onconnection");b.forEach(function(a){c.$27.onSubscribe(a)});a.forEach(function(a){c.$27.onPublish(a.topic)});this.$35("TransportConnected")};e.$43=function(a,b,c,d,e,f){this.$10.bumpCounter("protocol.onconnectsuccess"),this.$10.debugTrace("connect","Connect success"),this.$6.getIsGuestAuthStringPresent()&&this.$10.bumpCounter("guestAuthentication.onconnectsucess"),this.$27.onConnectSuccess(),this.$26=!0,this.$35("Connected"),this.$46(!0,!0,a,b,c,d,e,f),this.$50(c),this.$16=Date.now()};e.$42=function(a,b){this.$10.bumpCounter("protocol.onconnectionlost");this.$23++;if(a.errorCode){b=b!=null?a.errorMessage+" - "+b:a.errorMessage;this.$10.eventLogDisconnect({disconnectCount:this.$23,duration:Date.now()-this.$16,errorCode:a.errorCode,errorMessage:b,sessionID:this.$9})}this.$10.debugTrace("connect","connection lost");this.$27.onConnectionLost();this.$6.getIsGuestAuthStringPresent()&&this.$10.bumpCounter("guestAuthentication.onconnectionlost");this.$48()};e.$44=function(a,b,c){this.$10.bumpCounter("protocol.onmessagearrived");this.$6.getIsGuestAuthStringPresent()&&this.$10.bumpCounter("guestAuthentication.onmessagearrived");this.$10.debugTrace("onMessageArrived","Message received on "+a);this.$27.onMessage(a);try{this.$3(a,b,c)}catch(b){this.$10.logError(b,"Listener threw error"),this.$10.bumpCounter("listener_error."+a)}};e.$45=function(a){this.$10.bumpCounter("protocol.onmessagedelivered");var b=a!=null?a:"null";this.$10.debugTrace("onMessageDelivered","Delivered Message {ID: "+b+"}");if(a==null)return;b=this.$24.get(a);if(b==null){this.$10.bumpCounter("protocol.message_with_unknown_id");return}this.$24["delete"](a);(a=b.listener)==null?void 0:a.onEvent(d("MqttPublishListener").MqttPublishEvent.ACKED);b.resolve();this.$10.bumpCounter("publish.ack_received")};e.$30=function(a){this.$10.bumpCounter("protocol.fail_all_unacked_publishes."+a),this.$24.forEach(function(b,e,f){(e=b.listener)==null?void 0:e.onEvent(d("MqttPublishListener").MqttPublishEvent.NOT_ACKED);b.reject(c("err")(a))}),this.$24.clear()};e.$50=function(a){var b=this,c=new Set(a);c.forEach(function(a){b.$17.has(a)||b.unsubscribe(a)});a=new Set(this.$17);a.forEach(function(a){c.has(a)||b.subscribe(a)})};e.$47=function(a,b){try{this.$10.bumpCounter("js_error_in_init");this.$10.bumpCounter(b+".error");this.$25=!0;var c=a?a.message:"error";this.$10.debugTrace("onError",b+": "+c);this.$2(a)}catch(a){this.$10.bumpCounter("js_error_in_error_logging"),this.$10.logError(a,"JS error while trying to log previous error")}};e.setRunning_OnlyForTests=function(a){this.$8=a};return a}();g["default"]=a}),98);
__d("MqttWebSocketUtils",[],(function(a,b,c,d,e,f){"use strict";function b(){return"WebSocket"in a&&a.WebSocket!=null&&"CLOSING"in a.WebSocket.prototype}f.hasWSSupport=b}),66);
__d("isFastRefreshEnabledForCurrentDomain",[],(function(a,b,c,d,e,f){"use strict";function a(){return!1}f["default"]=a}),66);
__d("MqttChannel",["ChannelClientID","IrisSubscribeChecker","MetaConfig","MqttAnalyticsHook","MqttConnection","MqttEnv","MqttPublishListener","MqttTypes","MqttUserName","MqttUtils","MqttWebSocketUtils","Promise","Run","err","isFastRefreshEnabledForCurrentDomain","promiseDone","requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h,i=29125;a=function(){function a(a){var b=this,e=a.endpoint,f=a.pollingEndpoint,g=a.userFbid,h=a.appId,i=a.initialSubscribedTopics,j=a.capabilities,k=a.clientCapabilities,l=a.chatVisibility;l=l===void 0?!0:l;var m=a.guestAuthString;m=m===void 0?null:m;var n=a.phpOverride;n=n===void 0?"":n;var o=a.clientType;o=o===void 0?"websocket":o;var p=a.deviceId;p=p===void 0?c("ChannelClientID").getID():p;var q=a.pageId;q=q===void 0?null:q;var r=a.assetIds;r=r===void 0?null:r;a=a.appSpecificInfo;a=a===void 0?null:a;this.$11=d("MqttEnv").Env.getLoggerInstance();this.$11.setAppId(h);this.$12=new(c("MqttUserName"))(g,j,k,p,h,l,m,n,o,q,r,a);this.$1=e;this.$2=f;this.$5="Disconnected";this.$7="LPInactive";this.$6="Disconnected";this.$3=[];this.$4=new Set();this.$8=new Map();this.$9=new Map();this.$10=new(c("MqttConnection"))();this.$13=null;this.$16=new(c("MqttAnalyticsHook"))();this.$15=new Map();this.$14=[];if(!d("MqttEnv").Env.isUserLoggedInNow()&&!c("isFastRefreshEnabledForCurrentDomain")()&&(m==null||m=="")){this.$11.bumpCounter("logged_out_init");return}this.$10.addHook(this.$16);this.$10.addHook(new(c("IrisSubscribeChecker"))(function(){return b.$4.size>0}));this.$17(i);this.$18()}var e=a.prototype;e.$17=function(a){var b=this;if(this.$10.isRunning()){this.$19("run","Connection started calling run again");return}if(d("MqttEnv").Env.genGk(d("MqttEnv").MqttGkNames.mqtt_ws_polling_enabled))if(c("MetaConfig")._("15")){this.$19("MqttChannel","Websocket disabled, will do long polling only");return}else if(!d("MqttWebSocketUtils").hasWSSupport()){this.$19("MqttChannel","Websocket Unavailable, will do long polling only");this.$11.bumpCounter("ws_unavailable_polling");return}this.$10.run({endpoint:this.$1,extraConnectMessageProvider:function(){return b.$20()},mqttUserName:this.$12,onJSError:function(a){b.$21(a)},onMessageReceived:function(a,c,d){b.$22(a,c,d)},onStateChange:function(a){b.$23(a)},subscribedTopics:a});this.$24()};e.$24=function(){var a=this;d("Run").onUnload(function(){a.$25(d("MqttTypes").MqttErrors.BROWSER_CLOSE)})};e.shutdown=function(){this.$25(d("MqttTypes").MqttErrors.APP_DISCONNECT)};e.$25=function(a){this.$10&&this.$10.shutdown(a),this.$13&&this.$13.shutdown(),this.$16.onTabClose()};e.publish=function(a,b,c){c===void 0&&(c={qos:1,skipBuffer:!1});return this.$26(a,b,c)};e.publishBinary=function(a,b,c){c===void 0&&(c={qos:1,skipBuffer:!1});return this.$26(a,b,c)};e.$26=function(a,b,c){c===void 0&&(c={qos:1,skipBuffer:!1});var e;c.qos===0?e=this.$27(a,b,c.listener):e=this.$28(a,b,c);d("MqttUtils").promiseDone(e,function(){},function(a){(a=c.listener)==null?void 0:a.onEvent(d("MqttPublishListener").MqttPublishEvent.PUBLISH_ERROR)});return e};e.$27=function(a,b,c){return this.$29(a,b,0,c,1,null)};e.$28=function(a,c,e){var f={reject:function(a){},resolve:function(){}},g=new(h||(h=b("Promise")))(function(a,b){f.resolve=a,f.reject=b}),i=d("MqttEnv").Env.random();c={ack:f,attempt:0,options:e,payload:c,publishToken:i,startTime:Date.now(),timeoutId:null,topic:a};if(e.skipBuffer)if(this.$10.connectionState()==="Connecting"){this.$14.push(c);(a=e.listener)==null?void 0:a.onEvent(d("MqttPublishListener").MqttPublishEvent.QUEUED)}else this.$30(c);else{c.timeoutId=this.$31(i);this.$15.set(i,c);(a=e.listener)==null?void 0:a.onEvent(d("MqttPublishListener").MqttPublishEvent.QUEUED);this.$32(c)}return g};e.$30=function(a){a.attempt+=1,d("MqttUtils").promiseDone(this.$29(a.topic,a.payload,a.options.qos,a.options.listener,a.attempt,a.startTime),function(){a.ack.resolve()},function(b){a.ack.reject(b)})};e.$32=function(a){var b=this;a.attempt+=1;d("MqttUtils").promiseDone(this.$29(a.topic,a.payload,a.options.qos,a.options.listener,a.attempt,a.startTime),function(){return b.$33(a)},function(a){})};e.$29=function(a,b,e,f,g,h){var i=this,j,k,l=this.getConnectionState(),m=Date.now();!this.$10.canPublish()&&this.$13&&this.$13.canPublish()?(typeof b==="string"?j=this.$13.publish(a,b,e):j=this.$13.publishBinary(a,b,e),k="lp",f!=null&&(j=j.then(function(){return f.onEvent(d("MqttPublishListener").MqttPublishEvent.SENT)})),this.$11.bumpCounter("try_publish_lp")):(typeof b==="string"?j=this.$10.publish(a,b,e,f):j=this.$10.publishBinary(a,b,e,f),k="ws",this.$11.bumpCounter("try_publish_ws"));c("promiseDone")(j,function(){i.$11.eventLogOutgoingPublish({attempt:g,connectionState:l,disconnectCount:i.$10.getDisconnectCount(),errorMessage:null,firstAttemptStartTime:h,payloadSizeBytes:b.length*2,protocol:k,qos:e,sessionID:i.$10.getSessionId(),success:!0,thisAttemptStartTime:m,topic:a})},function(c){i.$11.eventLogOutgoingPublish({attempt:g,connectionState:l,disconnectCount:i.$10.getDisconnectCount(),errorMessage:c!=null?c.toString():null,firstAttemptStartTime:h,payloadSizeBytes:b.length*2,protocol:k,qos:e,sessionID:i.$10.getSessionId(),success:!1,thisAttemptStartTime:m,topic:a})});return j};e.subscribe=function(a,b){var c=this;this.$10.subscribe(a);var d=this.$8.get(a);!d?(d=[b],this.$8.set(a,d)):d.push(b);return function(){var d=c.$8.get(a)||[];d=d.filter(function(a){return a!==b});c.$8.set(a,d);d.length===0&&c.unsubscribeAll(a)}};e.subscribeBinary=function(a,b){var c=this;this.$10.subscribe(a);var d=this.$9.get(a);!d?(d=[b],this.$9.set(a,d)):d.push(b);return function(){var d=c.$9.get(a)||[];d=d.filter(function(a){return a!==b});c.$9.set(a,d);d.length===0&&c.unsubscribeAll(a)}};e.subscribeChannelEvents=function(a){var b=this;this.$3.push(a);return function(){b.unsubscribeChannelEvents(a)}};e.unsubscribeChannelEvents=function(a){a=this.$3.indexOf(a);a>-1&&this.$3.splice(a,1)};e.registerExtraConnectPayloadProvider=function(a){var b=this;this.$19("MqttChannel","registerExtraConnectPayloadProvider called");this.$4.add(a);if(this.$10.isRunning()){a=a.getPublishMessages();a.forEach(function(a){typeof a.payload==="string"?b.publish(a.topic,a.payload,{qos:a.qos,skipBuffer:!0}):b.publishBinary(a.topic,a.payload,{qos:a.qos,skipBuffer:!0})})}};e.unregisterExtraConnectPayloadProvider=function(a){this.$4["delete"](a)};e.unsubscribeAll=function(a){this.$10.unsubscribe(a),this.$8["delete"](a),this.$9["delete"](a)};e.getConnectionState=function(){return this.$5};e.getLongPollingStatus=function(){return this.$7};e.getEndpoint=function(){return this.$1};e.addHook=function(a){this.$10.addHook(a)};e.removeHook=function(a){this.$10.removeHook(a)};e.testOnlyMessageReceived=function(a,b){this.$22(a,b,-1)};e.$34=function(a){var b=this.$15.get(a);b!=null&&b.timeoutId!=null&&d("MqttEnv").Env.clearTimeout(b.timeoutId);this.$15["delete"](a)};e.$31=function(a){var b=this;return d("MqttEnv").Env.setTimeout(function(a){var d=b.$15.get(a);if(!d)return;var e=d.topic;b.$11.bumpCounter("publish_timeout."+e);b.$11.debugTrace("publish_timeout","Timeout publishing topic: "+e+" publishToken: "+a);b.$34(a);d.ack.reject(c("err")("Publish Timed Out"))},i,a)};e.$33=function(a){var b=a.publishToken,c=this.$15.get(b);if(!c)return;this.$34(b);a.ack.resolve();this.$11.debugTrace("publish_success","Topic: "+a.topic+" publishToken: "+a.publishToken);this.$11.bumpCounter("publish_success."+a.topic)};e.$22=function(a,b,c){var d=this.$8.get(a);this.$11.eventLogIncomingPublish({connectionState:this.getConnectionState(),disconnectCount:this.$10.getDisconnectCount(),payloadSizeBytes:b.payloadString.length*2,qos:c,sessionID:this.$10.getSessionId(),topic:a});if(!d)this.$11.debugTrace("_onMessageReceived",a+" being dropped, no listeners");else for(d of d)try{d(b.payloadString)}catch(a){this.$11.logError(a,"Listener exception"),this.$11.bumpCounter("listener_error")}d=this.$9.get(a);this.$11.eventLogIncomingPublish({connectionState:this.getConnectionState(),disconnectCount:this.$10.getDisconnectCount(),payloadSizeBytes:b.payloadBytes.length,qos:c,sessionID:this.$10.getSessionId(),topic:a});if(!d)this.$11.debugTrace("_onMessageReceived",a+" being dropped, no binary listeners");else for(c of d)try{c(b.payloadBytes)}catch(a){this.$11.logError(a,"Binary Listener exception"),this.$11.bumpCounter("listener_error")}};e.$21=function(a){if(d("MqttEnv").Env.genGk(d("MqttEnv").MqttGkNames.mqtt_ws_polling_enabled)){a.isRecoverable?this.$11.bumpCounter("recoverable_error_skipped"):this.$11.bumpCounter("unrecoverable_error_skipped");return}a.isRecoverable?this.$11.bumpCounter("recoverable_error_not_skipped"):this.$11.bumpCounter("unrecoverable_error_not_skipped");this.$35(a)};e.$36=function(a){this.$35(a)};e.$35=function(a){for(var b of this.$3)b.onJSError&&b.onJSError(a)};e.$37=function(){var a=this;this.$14.forEach(function(b){a.$30(b),a.$11.bumpCounter("publish_from_temp_buffer."+b.topic)});this.$14=[];this.$15.forEach(function(b,c,d){a.$32(b),a.$11.bumpCounter("publish_from_buffer."+b.topic)})};e.$23=function(a){a==="Connecting"?(this.$14.forEach(function(a){a.ack.reject(c("err")("Client Reconnecting - "+a.topic))}),this.$14=[]):a==="TransportConnected"&&this.$37(),this.$19("_changeState","Connection state = "+a),this.$38(a,this.$7)};e.$39=function(a,b){this.$13&&this.$13.canPublish()&&this.$37(),this.$19("_changeLPStatus","LP status = "+a+", LP Request status = "+b),this.$38(this.$6,a)};e.$38=function(a,b){var c=this.$10.mqttStateFromConnectionState(a);this.$13&&this.$13.canPublish()&&(c="Connected");this.$6=a;(c!==this.$5||b!=this.$7)&&(this.$5=c,this.$7=b,this.$40(c))};e.$40=function(a){for(var b of this.$3)b.onMQTTStateChanged(a)};e.$20=function(){var a=this,b=[];this.$4.forEach(function(c){try{c=c.getPublishMessages();Array.prototype.push.apply(b,c)}catch(b){a.$11.logError(b,"ConnectPayload provider exception"),a.$11.bumpCounter("connectPayloadProvider_error")}});return b};e.$18=function(){var a=this;d("MqttEnv").Env.genGk(d("MqttEnv").MqttGkNames.mqtt_ws_polling_enabled)&&this.$2&&this.$2!=""&&c("requireDeferred")("MqttLongPollingRunner").__setRef("MqttChannel").onReady(function(b){b=new b(a.$2,a.$12,a.$10.hasFatal(),a.$10.hasConnectSuccess(),function(b,c,d){return a.$22(b,c,d)},function(){var b=Array.from(a.$8.keys()),c=Array.from(a.$9.keys());return Array.from(new Set(b.concat(c)))},function(){return a.$20()},function(b){a.$36(b)},function(b,c){a.$39(b,c)});b.start();a.$10.addHook(b);b.addHook(a.$16);a.$13=b;a.$11.debugTrace("MqttChannel","longPollingRunner loaded")})};e.$19=function(a,b){this.$11.debugTrace(a,"Mqtt channel: "+b)};e.setForegroundState=function(a){this.$12&&this.$12.setForegroundState(a)};e.setChatVisibility=function(a){this.$12&&this.$12.setChatVisibility(a)};e.getEndpointCapabilities=function(){return this.$12.getEndpointCapabilities()};e.setEndpointCapabilities=function(a){this.$12.setEndpointCapabilities(a)};e.getConnection_OnlyForTests=function(){return this.$10};return a}();g["default"]=a}),98);
__d("MqttConfig",["MqttWebConfig"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("MqttWebConfig")}),98);
__d("MqttEnvConfigStorage",["WebStorage"],(function(a,b,c,d,e,f,g){var h,i="mqtt:";function a(a,b){var d=(h||(h=c("WebStorage"))).getLocalStorage();if(d){d=d.getItem(i+a);if(d!=null)return d}return b}function b(a,b){var d=(h||(h=c("WebStorage"))).getLocalStorage();d&&(b==null?d.removeItem(i+a):(h||(h=c("WebStorage"))).setItemGuarded(d,i+a,b))}g.configRead=a;g.configWrite=b}),98);
__d("MqttUnifiedClientConnectFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1744057");b=d("FalcoLoggerInternal").create("mqtt_unified_client_connect",a);e=b;g["default"]=e}),98);
__d("MqttUnifiedClientDisconnectFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1744058");b=d("FalcoLoggerInternal").create("mqtt_unified_client_disconnect",a);e=b;g["default"]=e}),98);
__d("MqttUnifiedClientIncomingPublishFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1744059");b=d("FalcoLoggerInternal").create("mqtt_unified_client_incoming_publish",a);e=b;g["default"]=e}),98);
__d("MqttUnifiedClientOutgoingPublishFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1744060");b=d("FalcoLoggerInternal").create("mqtt_unified_client_outgoing_publish",a);e=b;g["default"]=e}),98);
__d("MqttWsClientTypedLoggerLite",["generateLiteTypedLogger"],(function(a,b,c,d,e,f){"use strict";e.exports=b("generateLiteTypedLogger")("logger:MqttWsClientLoggerConfig")}),null);
__d("MqttLogger",["ChannelClientID","FBLogger","LogHistory","MetaConfig","MqttEnv","MqttGlobalStreamCounter","MqttUnifiedClientConnectFalcoEvent","MqttUnifiedClientDisconnectFalcoEvent","MqttUnifiedClientIncomingPublishFalcoEvent","MqttUnifiedClientOutgoingPublishFalcoEvent","MqttWsClientTypedLoggerLite","NetworkStatus","ODS","Random"],(function(a,b,c,d,e,f,g){"use strict";var h,i=c("MetaConfig")._("99"),j="mqtt_client",k=100,l="WEBSOCKET",m=typeof window!=="undefined"?window:self,n=null,o={CLIENT_ERROR:"mqtt_client_error",CONNECT:"mqtt_client_connect",DISCONNECT:"mqtt_client_disconnect",PUBLISH:"mqtt_client_publish",PUBLISH_TIMEOUT:"mqtt_qos1_publish_timeout",SOCKET_DISCONNECT:"mqtt_protocol_error"},p={CONNECT:"connect",DISCONNECT:"disconnect",INCOMING_PUBLISH:"incoming_publish",OUTGOING_PUBLISH:"outgoing_publish"};a=function(){a.getInstance=function(b){n||(n=new a(b));return n};function a(a){this.$1=d("LogHistory").getInstance(j),this.$2=0,this.$3=Date.now(),this.$4=c("ChannelClientID").getID(),this.$7(),this.$5=d("MqttGlobalStreamCounter").getInstance(),this.$5.isEnabled()||(this.$5=null),this.$6=a}var b=a.prototype;b.setAppId=function(a){this.$2===0&&(this.$2=a)};b.eventLogConnect=function(a){var b=a.ackReceived,d=a.attemptNumber,e=a.connectionState,f=a.connectionStatus,g=a.disconnectCount,h=a.duration,i=a.errorMessage,j=a.failTotal,k=a.hostname,m=a.messageSizeBytes,n=a.osConnectivity,q=a.sessionID,r=a.successTotal;a=a.total_duration;var s=f?"success":"failed";this.bumpCounter(o.CONNECT+"."+s);s=r/(r+j);r=n&&this.getBrowserConnectivity();var t={acked:b,attempt_number:d.toString(),client_type:l,connection_state:e,disconnect_count:g.toString(),duration:h.toString(),error:i,event_type:p.CONNECT,extra_data:{connect_success_rate:s.toString(),device_id:this.$4,hostname:k,connectionStatus:f.toString(),exposure:(j=this.$6)!=null?j:""},os_connectivity:r,payload_size:m==null?void 0:m.toString(),session_id:q.toString(),total_duration:a.toString(),ws_count:(n=this.$5)==null?void 0:n.getGlobalState().totalConnectionStreams.toString()};this.$8(function(){c("MqttUnifiedClientConnectFalcoEvent").log(function(){return t})})};b.eventLogPull=function(a){var b=a.duration,c=a.errorMessage,e=a.hostname,f=a.pullEventName,g=a.sessionID;a=a.status;a={connection_status:a,device_id:this.$4,duration:b,error_message:c,hostname:e,href:m.location.hostname,logged_in:d("MqttEnv").Env.isUserLoggedInNow(),session_id:g};this.$9(f,a)};b.eventLogPullFinish=function(a){var b=a.duration,c=a.errorMessage,e=a.publishReceived,f=a.publishSent,g=a.pullEventName;a=a.sessionID;b={device_id:this.$4,duration:b,error_message:c,href:m.location.hostname,logged_in:d("MqttEnv").Env.isUserLoggedInNow(),publish_received:e,publish_sent:f,session_id:a};this.$9(g,b)};b.eventLogDisconnect=function(a){var b=a.disconnectCount,d=a.duration,e=a.errorMessage;a=a.sessionID;this.bumpCounter(o.DISCONNECT);var f={client_type:l,connection_state:"Disconnected",disconnect_count:b.toString(),duration:d.toString(),error:e,event_type:p.DISCONNECT,extra_data:{device_id:this.$4,exposure:(b=this.$6)!=null?b:""},os_connectivity:this.getBrowserConnectivity(),session_id:a.toString(),ws_count:(d=this.$5)==null?void 0:d.getGlobalState().totalConnectionStreams.toString()};this.$8(function(){c("MqttUnifiedClientDisconnectFalcoEvent").log(function(){return f})})};b.eventLogOutgoingPublish=function(a){var b=a.attempt,d=a.connectionState,e=a.disconnectCount,f=a.errorMessage,g=a.firstAttemptStartTime,h=a.payloadSizeBytes,i=a.protocol,j=a.qos,k=a.sessionID,m=a.success,n=a.thisAttemptStartTime;a=a.topic;this.bumpCounter(o.PUBLISH+"."+a);var q=Date.now(),r={acked:j===1?m:null,attempt_number:b.toString(),client_type:l,connection_state:d,disconnect_count:e.toString(),duration:(q-n).toString(),error:f,event_type:p.OUTGOING_PUBLISH,extra_data:{device_id:this.$4,protocol:i},os_connectivity:this.getBrowserConnectivity(),payload_size:h.toString(),qos:j.toString(),session_id:k.toString(),topic:a,total_duration:g!=null?(q-g).toString():null};this.$8(function(){c("MqttUnifiedClientIncomingPublishFalcoEvent").log(function(){return r})})};b.eventLogIncomingPublish=function(a){var b=a.connectionState,d=a.disconnectCount,e=a.payloadSizeBytes,f=a.qos,g=a.sessionID;a=a.topic;var h={client_type:l,connection_state:b,disconnect_count:d.toString(),event_type:p.INCOMING_PUBLISH,extra_data:{device_id:this.$4},os_connectivity:this.getBrowserConnectivity(),payload_size:e.toString(),qos:f.toString(),session_id:g.toString(),topic:a};b=a=="/webrtc"||a=="/rtc_multi";this.$10(function(){c("MqttUnifiedClientOutgoingPublishFalcoEvent").log(function(){return h})},50,b)};b.logError=function(a,b){d("MqttEnv").Env.scheduleLoggingCallback(function(){try{c("FBLogger")(j).catching(a).mustfix(b)}catch(a){}})};b.logErrorWarn=function(a,b){d("MqttEnv").Env.scheduleLoggingCallback(function(){try{c("FBLogger")(j).catching(a).warn(b)}catch(a){}})};b.logWarn=function(a,b){var c=this;d("MqttEnv").Env.scheduleLoggingCallback(function(){try{c.$1.warn(a,b)}catch(a){}})};b.debugTrace=function(a,b){var c=this;d("MqttEnv").Env.scheduleLoggingCallback(function(){try{c.$1.debug(a,b)}catch(a){}})};b.bumpCounter=function(a){var b=this;if(!d("Random").coinflip(k))return;d("MqttEnv").Env.scheduleLoggingCallback(function(){b.$2!==0&&(h||(h=d("ODS"))).bumpEntityKey(2966,"mqtt_ws_client",b.$2+"."+a,k),(h||(h=d("ODS"))).bumpEntityKey(2966,"mqtt_ws_client",a,k)})};b.$9=function(a,b,e){var f=this;d("MqttEnv").Env.scheduleLoggingCallback(function(){b.event_type=a;b.app_id=f.$2;b.online=f.$11();var d=JSON.stringify(b);f.$1.log(a,d,{weight:e});a!==o.DISCONNECT&&c("MqttWsClientTypedLoggerLite").log(b)})};b.$10=function(a,b,c){i&&b!==0?c=c||d("Random").coinflip(b):c=!0;c&&d("MqttEnv").Env.scheduleCallback(a)};b.$8=function(a){this.$10(a,0,!1)};b.$11=function(){return m.navigator&&m.navigator.onLine!==void 0?m.navigator.onLine:!1};b.getBrowserConnectivity=function(){return c("NetworkStatus").isOnline()};b.$7=function(){var a=this;if(m.navigator&&m.navigator.onLine!==void 0){var b=function(b){b=b.online;b||a.bumpCounter("browser_disconnect")};c("NetworkStatus").onChange(b)}};return a}();g["default"]=a}),98);
__d("MqttEnvGk",["MqttEnv","MqttLogger","gkx"],(function(a,b,c,d,e,f,g){"use strict";function a(a){switch(a){case d("MqttEnv").MqttGkNames.mqtt_waterfall_log_client_sampling:return c("gkx")("21119");case d("MqttEnv").MqttGkNames.mqtt_ws_polling_enabled:return!0;case d("MqttEnv").MqttGkNames.mqtt_lp_use_fetch:return c("gkx")("21120");case d("MqttEnv").MqttGkNames.mqtt_fast_lp:return c("gkx")("21121");case d("MqttEnv").MqttGkNames.mqtt_lp_no_delay:return c("gkx")("21122");case d("MqttEnv").MqttGkNames.mqtt_enable_publish_over_polling:return c("gkx")("21123");case d("MqttEnv").MqttGkNames.mqttweb_global_connection_counter:return c("gkx")("21124");default:c("MqttLogger").getInstance().logError(new Error("unknown gk"),"Unknown GK value "+a);return!1}}g["default"]=a}),98);
__d("MqttEnvInitializer",["CurrentMessengerUser","MqttEnv","MqttEnvConfigStorage","MqttEnvGk","MqttLogger","Random","clearTimeout","setTimeoutAcrossTransitions"],(function(a,b,c,d,e,f,g){"use strict";function a(a){d("MqttEnv").Env.initialize(c("Random").random,d("CurrentMessengerUser").isLoggedInNow,c("clearTimeout"),c("setTimeoutAcrossTransitions"),function(){return c("MqttLogger").getInstance()},c("MqttEnvGk"),(a=a)!=null?a:null,null,null,d("MqttEnvConfigStorage").configRead,d("MqttEnvConfigStorage").configWrite)}b={genGk:c("MqttEnvGk"),initialize:a};g["default"]=b}),98);
__d("FBMqttChannel",["MqttChannel","MqttConfig","MqttEnvInitializer"],(function(a,b,c,d,e,f,g){"use strict";c("MqttEnvInitializer").initialize();b=new(c("MqttChannel"))({appId:(a=c("MqttConfig")).appID,capabilities:a.capabilities,chatVisibility:!1,clientCapabilities:a.clientCapabilities,endpoint:a.endpoint,initialSubscribedTopics:a.subscribedTopics,phpOverride:a.hostNameOverride,pollingEndpoint:a.pollingEndpoint,userFbid:a.fbid});g["default"]=b}),98);
__d("FbtLogging",["cr:1094907","cr:8828"],(function(a,b,c,d,e,f,g){"use strict";a=b("cr:1094907")==null?void 0:b("cr:1094907").logImpression;c=b("cr:8828")==null?void 0:b("cr:8828").logImpressionV2;g.logImpression=a;g.logImpressionV2=c}),98);
__d("IntlQtEventFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1848815");b=d("FalcoLoggerInternal").create("intl_qt_event",a);e=b;g["default"]=e}),98);
__d("MarauderLogger",["Banzai","CacheStorage","MarauderConfig"],(function(a,b,c,d,e,f){var g="client_event",h="navigation",i=18e4,j="marauder",k="marauder_last_event_time",l="marauder_last_session_id",m={},n=[],o=!1,p=null,q=null,r=null,s=0,t,u,v=!1,w=null;function a(){F().set(k,x())}b("Banzai").subscribe(b("Banzai").SHUTDOWN,a);function x(){t=t||F().get(k)||0;return t}function y(){v||(u=F().get(l),v=!0);var a=Date.now();(!u||a-i>x())&&(u=a.toString(16)+"-"+(~~(Math.random()*16777215)).toString(16),F().set(l,u));return u}function z(){return{user_agent:window.navigator.userAgent,screen_height:window.screen.availHeight,screen_width:window.screen.availWidth,density:window.screen.devicePixelRatio||null,platform:window.navigator.platform||null,locale:window.navigator.language||null}}function A(){return{locale:navigator.language}}function B(a,b,c,d,e,f,g){var h=g!=null&&g!=0?g:Date.now();t=g!=null&&g!=0?Date.now():h;g=b!=null&&b!=""?b:p;return{name:a,time:h/1e3,module:g,obj_type:d,obj_id:e,uuid:f,extra:c}}function C(a,b,c){return B("content",null,{flags:b},null,null,a,c)}function D(a){var b=window.__mrdr;if(b)for(var c in b){var d=b[c];if(d[3]!==0){delete b[c];if(c==="1")if(r!==null)c=r;else continue;a.push(C(c,1,d[1]));a.push(C(c,2,d[2]));a.push(C(c,3,d[3]))}}}function E(a,c){D(a);if(a.length===0)return;o&&a.push(B("counters",null,m));var d=b("Banzai").BASIC;c==="vital"&&(d=b("Banzai").VITAL);var e=b("MarauderConfig").gk_enabled;s===0&&e&&(a.push(B("device_status",null,A())),d={delay:5e3});c==="signal"&&(d={signal:!0});e&&Math.random()<.01&&a.push(B("device_info",null,z()));if(r!==null)for(c=0;c<a.length;c++){e=a[c];(e.uuid===null||e.uuid===void 0)&&(e.uuid=r)}e={app_ver:b("MarauderConfig").app_version,data:a,device_id:void 0,log_type:g,seq:s++,session_id:y()};c=F().get("device_id");c&&(e.device_id=c);m={};o=!1;b("Banzai").post(j,e,d)}function F(){w||(w=new(b("CacheStorage"))("localstorage",""));return w}function c(a){m[a]||(m[a]=0),m[a]++,o=!0}function G(b,a,c,d,f,g,h,i){E([B(b,a,c,d,f,g,h)],i)}function H(a,b){p!==b&&(n.push(B(h,p,{dest_module:b,source_url:q,destination_url:a})),p=b,q=a)}function d(a,b){p!==b&&(r=null,H(a,b))}function f(a,b,c){G(b?"show_module":"hide_module",a,c)}function I(a){p=a}function J(){return p}function K(a){r===null&&(r=a,a!==null&&(E(n),n=[]))}e.exports={count:c,log:G,navigateTo:d,navigateWithinSession:H,toggleModule:f,setUUID:K,setNavigationModule:I,getNavigationModule:J}}),null);
__d("WebStorageCleanupReason",[],(function(a,b,c,d,e,f){"use strict";var g=null;function a(){return g}function b(a){g=a}f.getLastCleanupReason=a;f.setLastCleanupReason=b}),66);
__d("bumpVultureJSHash",["ODS"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){(h||(h=d("ODS"))).bumpEntityKey(7506,"vulture_js",a,b)}g["default"]=a}),98);