;/*FB_PKG_DELIM*/

__d("PolarisClearSearchHistoryButtonMutation_instagramRelayOperation",[],(function(a,b,c,d,e,f){e.exports="29507187725596697"}),null);
__d("PolarisClearSearchHistoryButtonMutation.graphql",["PolarisClearSearchHistoryButtonMutation_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a=function(){var a=[{alias:null,args:[{kind:"Literal",name:"_request_data",value:{}}],concreteType:"XDTEmptyRecord",kind:"LinkedField",name:"xdt_api__v1__fbsearch__clear_search_history",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"__typename",storageKey:null}],storageKey:"xdt_api__v1__fbsearch__clear_search_history(_request_data:{})"}];return{fragment:{argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisClearSearchHistoryButtonMutation",selections:a,type:"Mutation",abstractKey:null},kind:"Request",operation:{argumentDefinitions:[],kind:"Operation",name:"PolarisClearSearchHistoryButtonMutation",selections:a},params:{id:b("PolarisClearSearchHistoryButtonMutation_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__fbsearch__clear_search_history"]},name:"PolarisClearSearchHistoryButtonMutation",operationKind:"mutation",text:null}}}();e.exports=a}),null);
__d("PolarisClearSearchHistoryButton.next.react",["ClearSearchHistoryFalcoEvent","CometRelay","IGDSBox.react","IGDSButton.react","IGDSTextVariants.react","PolarisAccessDataStrings","PolarisClearSearchHistoryButtonMutation.graphql","PolarisConfirmDialog.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react")),k=i.useState,l=h!==void 0?h:h=b("PolarisClearSearchHistoryButtonMutation.graphql");function a(a){var b=d("react-compiler-runtime").c(17),e=a.clearSearchHistoryDescription;a=a.clearSearchHistoryText;var f=k(!1),g=f[0],h=f[1];f=d("CometRelay").useMutation(l);var i=f[0];b[0]===Symbol["for"]("react.memo_cache_sentinel")?(f=function(){h(!0)},b[0]=f):f=b[0];f=f;var o;b[1]!==i?(o=function(){c("ClearSearchHistoryFalcoEvent").log(n);var a=m;i({optimisticUpdater:a,updater:a,variables:{}});h(!1)},b[1]=i,b[2]=o):o=b[2];o=o;var p;b[3]===Symbol["for"]("react.memo_cache_sentinel")?(p=function(){h(!1)},b[3]=p):p=b[3];p=p;b[4]!==a?(f=j.jsx(c("IGDSButton.react"),{display:"block",label:a,onClick:f,variant:"primary_link"}),b[4]=a,b[5]=f):f=b[5];a=f;b[6]!==e?(f=j.jsx(d("IGDSTextVariants.react").IGDSTextBody,{color:"secondaryText",zeroMargin:!0,children:e}),b[6]=e,b[7]=f):f=b[7];var q;b[8]!==a||b[9]!==f?(q=j.jsxs(c("IGDSBox.react"),{alignItems:"start",direction:"column",marginBottom:6,position:"relative",children:[a,f]}),b[8]=a,b[9]=f,b[10]=q):q=b[10];f=q;b[11]!==o||b[12]!==g?(q=g&&j.jsx(c("PolarisConfirmDialog.react"),{body:d("PolarisAccessDataStrings").ACCESS_DATA_CLEAR_SEARCH_DIALOG_BODY_V2,cancelLabel:d("PolarisAccessDataStrings").ACCESS_DATA_CLEAR_SEARCH_DIALOG_CANCEL_BUTTON_TEXT_V2,confirmLabel:d("PolarisAccessDataStrings").ACCESS_DATA_CLEAR_SEARCH_DIALOG_CONFIRM_BUTTON_TEXT_V2,destructiveConfirm:!0,onClose:p,onConfirm:o,title:d("PolarisAccessDataStrings").ACCESS_DATA_CLEAR_SEARCH_DIALOG_HEADER_V2}),b[11]=o,b[12]=g,b[13]=q):q=b[13];p=e!==void 0?f:a;b[14]!==q||b[15]!==p?(o=j.jsxs(j.Fragment,{children:[q,p]}),b[14]=q,b[15]=p,b[16]=o):o=b[16];return o}function m(a){(a=a.getRoot().getLinkedRecord("xdt_api__v1__fbsearch__recent_searches_connection"))==null?void 0:a.setLinkedRecords([],"recent")}function n(){return{pigeon_reserved_keyword_module:"clear_search_history_page"}}g["default"]=a}),98);
__d("PolarisFeedSidebarGlimmer.react",["IGDSBox.react","IGDSListItemPlaceholder.react","PolarisFeedEmptyGlimmer.react","PolarisFeedSidebarLayout.react","PolarisFooter.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(){var a=d("react-compiler-runtime").c(3),b,e;a[0]===Symbol["for"]("react.memo_cache_sentinel")?(b=i.jsx(c("PolarisFooter.react"),{variant:"sidebar"}),e=i.jsx(c("IGDSListItemPlaceholder.react"),{fullWidth:!0,index:0,size:"large"}),a[0]=b,a[1]=e):(b=a[0],e=a[1]);a[2]===Symbol["for"]("react.memo_cache_sentinel")?(b=i.jsx(c("PolarisFeedSidebarLayout.react"),{footer:b,header:e,suggestedUserList:i.jsx(c("IGDSBox.react"),{marginBottom:6,width:"100%",children:i.jsx(c("PolarisFeedEmptyGlimmer.react"),{})})}),a[2]=b):b=a[2];return b}g["default"]=a}),98);
__d("PolarisFeedSidebarWrapperDeferred.react",["CometPlaceholder.react","PolarisFeedSidebarGlimmer.react","deferredLoadComponent","react","react-compiler-runtime","requireDeferredForDisplay"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j=c("deferredLoadComponent")(c("requireDeferredForDisplay")("PolarisFeedSidebarWrapper.react").__setRef("PolarisFeedSidebarWrapperDeferred.react"));function a(a){var b=d("react-compiler-runtime").c(3);a=a.suggestedUserListQueryRef;var e;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(e=i.jsx(c("PolarisFeedSidebarGlimmer.react"),{}),b[0]=e):e=b[0];b[1]!==a?(e=i.jsx(c("CometPlaceholder.react"),{fallback:e,children:i.jsx(j,{suggestedUserListQueryRef:a})}),b[1]=a,b[2]=e):e=b[2];return e}g["default"]=a}),98);
__d("PolarisListWithKeyCommands.react",["fbt","CometComponentWithKeyCommands.react","CometKeys","react"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=i||(i=d("react"));b=i;var k=b.useEffect,l=b.useMemo,m=b.useRef;function a(a){var b=a.children,d=a.dependencies,e=a.selector;a=babelHelpers.objectWithoutPropertiesLoose(a,["children","dependencies","selector"]);var f=m(),g=m(),i=l(function(){return[{command:{key:c("CometKeys").DOWN},description:h._(/*BTDS*/"Navigate to next item"),handler:function(){var a;a=(a=g.current)==null?void 0:a.querySelectorAll(e);if(!(a==null?void 0:a.length))return;f.current=f.current==null?0:Math.min((a==null?void 0:a.length)-1,f.current+1);a==null?void 0:(a=a[f.current])==null?void 0:a.focus()},triggerFromInputs:!0},{command:{key:c("CometKeys").UP},description:h._(/*BTDS*/"Navigate to previous item"),handler:function(){var a;a=(a=g.current)==null?void 0:a.querySelectorAll(e);if(f.current==null||!(a==null?void 0:a.length))return;f.current=Math.max(0,f.current-1);a==null?void 0:(a=a[f.current])==null?void 0:a.focus()},triggerFromInputs:!0}]},d);k(function(){f.current=void 0},d);return j.jsx(c("CometComponentWithKeyCommands.react"),{commandConfigs:i,children:j.jsx("div",babelHelpers["extends"]({},a,{ref:g,children:b}))})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),226);
__d("PolarisNonProfiledSearchContentGatedFragment.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisNonProfiledSearchContentGatedFragment",selections:[{args:null,kind:"FragmentSpread",name:"PolarisNonProfiledSearchContentFragment"}],type:"XDTNonProfiledSerpResponse",abstractKey:null};e.exports=a}),null);
__d("PolarisNonProfiledSearchContentGated.react",["CometRelay","PolarisNonProfiledSearchContentGatedFragment.graphql","cr:7654","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function a(a){var c=d("react-compiler-runtime").c(3),e=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisNonProfiledSearchContentGatedFragment.graphql"),a.fragmentKey),f;c[0]!==e||c[1]!==a?(f=b("cr:7654")?j.jsx(b("cr:7654"),babelHelpers["extends"]({},a,{fragmentKey:e})):null,c[0]=e,c[1]=a,c[2]=f):f=c[2];return f}g["default"]=a}),98);
__d("PolarisSearchBoxInactiveState.next.react",["IGDSBox.react","IGDSSearchPanoOutlineIcon.react","PolarisGenericStrings","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(8),e=a.caption;a=a.onClick;var f;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(f={className:"x1hmx34t x1lq5wgf xgqcy7u x30kzoy x9jhf4c x9f619 x1roi4f4 x1ed109x x78zum5 xl565be x5yr21d x17qophe xexx8yu xv54qhq x18d9i69 xf7dkkf x10l6tqk x13vifvy xh8yej3 xhtitgo"},b[0]=f):f=b[0];var g,h;b[1]===Symbol["for"]("react.memo_cache_sentinel")?(g=i.jsx("div",babelHelpers["extends"]({className:"xbmvrgn x1n2onr6"},{children:i.jsx(c("IGDSSearchPanoOutlineIcon.react"),{alt:d("PolarisGenericStrings").SEARCH_TEXT,color:"ig-secondary-icon",size:16})})),h={className:"x972fbf x10w94by x1qhh985 x14e42zd x1rg5ohu x19qstwj xdj266r x14z9mp xat24cr x1lziwak x1f0l55g x6ikm8r x10wlt62 xexx8yu xyri2b x18d9i69 x1c1uobl xlyipyv xuxw1ft"},b[1]=g,b[2]=h):(g=b[1],h=b[2]);b[3]!==e?(g=i.jsxs(c("IGDSBox.react"),{alignItems:"center",direction:"row",display:"flex",children:[g,i.jsx("span",babelHelpers["extends"]({},h,{children:e}))]}),b[3]=e,b[4]=g):g=b[4];b[5]!==a||b[6]!==g?(h=i.jsx("div",babelHelpers["extends"]({},f,{onClick:a,role:"button",tabIndex:"0",children:g})),b[5]=a,b[6]=g,b[7]=h):h=b[7];return h}g["default"]=a}),98);
__d("PolarisSearchBox.next.react",["IGDSBox.react","IGDSButton.react","IGDSSpinner.react","PolarisActiveSearchContext.react","PolarisGenericStrings","PolarisIGCorePressable.react","PolarisRoutes","PolarisSearchBoxInactiveState.next.react","PolarisSearchBoxTextInput.react","PolarisSearchConstants","PolarisSearchContext.react","PolarisSearchLogger","PolarisSearchResultDisplayTypes","PolarisSearchStrings","browserHistory_DO_NOT_USE","polarisLogAction","react","react-compiler-runtime","useCometRouterDispatcher","useDebounced","usePolarisIsOnExplorePage","usePolarisIsOnFeedPage"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));b=h;b.useCallback;var j=b.useContext,k=b.useState,l=250;function a(a){var b=d("react-compiler-runtime").c(70),e=a.analyticsContext,f=a.children,g=a.hideResults,h=a.isActive,n=a.isAutoFocused,o=a.loading,p=a.onSearch,q=a.placeholder,r=a.setPendingQuery,s=a.useBottomMargin;a=a.useHistory;g=g===void 0?!1:g;n=n===void 0?!1:n;q=q===void 0?d("PolarisSearchStrings").SEARCH_PLACEHOLDER_TEXT:q;s=s===void 0?!0:s;var t=a===void 0?!0:a;a=c("usePolarisIsOnFeedPage")();var u=c("usePolarisIsOnExplorePage")(),v=k(!1),w=v[0],x=v[1];v=k(!1);var y=v[0],z=v[1];v=k(!1);var A=v[0],B=v[1];v=j(d("PolarisActiveSearchContext.react").PolarisActiveSearchContext);var C=v.discoverToken,D=v.query,E=v.rankToken;v=v.resultDisplayType;var F=v===c("PolarisSearchResultDisplayTypes").Page,G=k(D),H=G[0],I=G[1];b[0]!==e||b[1]!==p||b[2]!==E||b[3]!==r?(G=function(a){if(a==="@"||a==="#")return;r(a);p(d("PolarisSearchConstants").SEARCH_CONTEXT.BLENDED,a,(a=E)!=null?a:"");c("polarisLogAction")("search",{rankToken:E,source:e})},b[0]=e,b[1]=p,b[2]=E,b[3]=r,b[4]=G):G=b[4];G=G;var J=c("useDebounced")(G,l);b[5]!==A||b[6]!==h?(G=function(){return h===!0||A},b[5]=A,b[6]=h,b[7]=G):G=b[7];G=G;var K=c("useCometRouterDispatcher")(),L=m,M;b[8]!==J?(M=function(a){I(a.target.value),J(a.target.value)},b[8]=J,b[9]=M):M=b[9];M=M;var N;b[10]!==e||b[11]!==C||b[12]!==A||b[13]!==h||b[14]!==E||b[15]!==t?(N=function(){A||(c("polarisLogAction")("searchBoxFocus",{rankToken:E,source:e}),d("PolarisSearchLogger").logSearchSessionInitiated({searchSessionID:C})),t===!0&&h!==!0&&d("browserHistory_DO_NOT_USE").browserHistory.push(d("PolarisRoutes").DISCOVER_SEARCH_PATH),B(!0)},b[10]=e,b[11]=C,b[12]=A,b[13]=h,b[14]=E,b[15]=t,b[16]=N):N=b[16];N=N;var O;b[17]===Symbol["for"]("react.memo_cache_sentinel")?(O=function(){x(!0)},b[17]=O):O=b[17];O=O;var P;b[18]===Symbol["for"]("react.memo_cache_sentinel")?(P=function(){B(!1),z(!0)},b[18]=P):P=b[18];var Q=P;b[19]!==F||b[20]!==r?(P=function(){!F?B(!1):x(!0),r(""),I("")},b[19]=F,b[20]=r,b[21]=P):P=b[21];P=P;var R;b[22]!==K?(R=function(){c("polarisLogAction")("searchBoxCancel"),K==null?void 0:K.goBack()},b[22]=K,b[23]=R):R=b[23];R=R;var S;b[24]===Symbol["for"]("react.memo_cache_sentinel")?(S=function(){return i.createElement("div",babelHelpers["extends"]({className:"x1qjc9v5 x972fbf x10w94by x1qhh985 x14e42zd x1ey2m1c x9f619 x78zum5 xdt5ytf x2lah0s xln7xf2 xk390pu xds687c x17qophe xdj266r x14z9mp xat24cr x1lziwak xexx8yu xyri2b x18d9i69 x1c1uobl xixxii4 x13vifvy x11njtxf"},{key:"active_modal_background",onClick:Q,role:"dialog"}))},b[24]=S):S=b[24];S=S;D=D||q;b[25]!==G||b[26]!==D?(S=G()?S():i.jsx(c("PolarisSearchBoxInactiveState.next.react"),{caption:D,onClick:O}),b[25]=G,b[26]=D,b[27]=S):S=b[27];O=S;S=(D=j(d("PolarisSearchContext.react").PolarisSearchContext))==null?void 0:D.placement;D=S===d("PolarisSearchContext.react").SearchPlacement.SidebarSearchPanel;S=(!a||D)&&!F;b[28]!==g||b[29]!==F||b[30]!==u?(a={0:{},4:{className:"xvbhtw8 x9f619 x78zum5 x1q0g3np xdd8jsf x1y1aw1k xf159sx xwib8y2 xmzvs34 xh8yej3 x1vjfegm"},2:{className:"x1ixjvfu x1q0q8m5 xso031l"},6:{className:"xvbhtw8 x9f619 x78zum5 x1q0g3np xdd8jsf x1y1aw1k xf159sx xwib8y2 xmzvs34 xh8yej3 x1vjfegm x1ixjvfu x1q0q8m5 xso031l"},1:{className:"xixxii4"},5:{className:"xvbhtw8 x9f619 x78zum5 x1q0g3np xdd8jsf x1y1aw1k xf159sx xwib8y2 xmzvs34 xh8yej3 x1vjfegm xixxii4"},3:{className:"x1ixjvfu x1q0q8m5 xso031l xixxii4"},7:{className:"xvbhtw8 x9f619 x78zum5 x1q0g3np xdd8jsf x1y1aw1k xf159sx xwib8y2 xmzvs34 xh8yej3 x1vjfegm x1ixjvfu x1q0q8m5 xso031l xixxii4"}}[!!F<<2|!!(F&&!g)<<1|!!(F&&u)<<0],b[28]=g,b[29]=F,b[30]=u,b[31]=a):a=b[31];b[32]!==F||b[33]!==S||b[34]!==s?(D={0:{className:"x6s0dn4 x78zum5 xdt5ytf x1c4vz4f xs83m0k xc9qbxq xjoudau x1n2onr6 xnf1dy1"},4:{className:"x6s0dn4 x78zum5 xdt5ytf x1iyjqo2 x1n2onr6"},2:{className:"xjoudau x6s0dn4 x78zum5 xdt5ytf x1c4vz4f xs83m0k xrf2nzk x1n2onr6 xh8yej3"},6:{className:"x6s0dn4 x78zum5 xdt5ytf x1c4vz4f xs83m0k xrf2nzk x1n2onr6 xh8yej3"},1:{className:"x6s0dn4 x78zum5 xdt5ytf x1c4vz4f xs83m0k xc9qbxq xjoudau x1n2onr6 xnf1dy1 x1hq5gj4"},5:{className:"x6s0dn4 x78zum5 xdt5ytf x1iyjqo2 x1n2onr6 x1hq5gj4"},3:{className:"xjoudau x6s0dn4 x78zum5 xdt5ytf x1c4vz4f xs83m0k xrf2nzk x1n2onr6 xh8yej3 x1hq5gj4"},7:{className:"x6s0dn4 x78zum5 xdt5ytf x1c4vz4f xs83m0k xrf2nzk x1n2onr6 xh8yej3 x1hq5gj4"}}[!!F<<2|!!S<<1|!!(S&&s)<<0],b[32]=F,b[33]=S,b[34]=s,b[35]=D):D=b[35];b[36]!==y||b[37]!==M||b[38]!==N||b[39]!==w||b[40]!==n||b[41]!==H||b[42]!==q?(u=i.jsx(c("PolarisSearchBoxTextInput.react"),{autoFocus:n,backgroundClick:y,inactiveClick:w,onChange:M,onFocus:N,onKeyDown:L,placeholder:q,setBackgroundClick:z,setInactiveClick:x,value:H}),b[36]=y,b[37]=M,b[38]=N,b[39]=w,b[40]=n,b[41]=H,b[42]=q,b[43]=u):u=b[43];b[44]!==G||b[45]!==o?(S=o&&G()&&i.jsx("div",babelHelpers["extends"]({className:"x1k3u6ij xehw8m1 x10l6tqk xwa60dl x1cb1t30 xzkaem6"},{children:i.jsx(c("IGDSSpinner.react"),{size:"small"})})),b[44]=G,b[45]=o,b[46]=S):S=b[46];b[47]!==P||b[48]!==G||b[49]!==o?(s=!o&&G()&&i.jsx(c("PolarisIGCorePressable.react"),{className:"x1k3u6ij x10l6tqk xwa60dl x1cb1t30 xzkaem6 xo3uz88 xk334sl xuobv7d xr13hgf xiy17q3 x9p3b3b x1qx5ct2 xw4jnvo",label:d("PolarisSearchStrings").CLEAR_SEARCH_BOX_TEXT,onPress:P}),b[47]=P,b[48]=G,b[49]=o,b[50]=s):s=b[50];b[51]!==f||b[52]!==G||b[53]!==v?(L=v===c("PolarisSearchResultDisplayTypes").Popover&&G()&&i.jsx("div",babelHelpers["extends"]({className:"x10l6tqk x1ym02ve x1vjfegm"},{children:f})),b[51]=f,b[52]=G,b[53]=v,b[54]=L):L=b[54];b[55]!==O||b[56]!==D||b[57]!==u||b[58]!==S||b[59]!==s||b[60]!==L?(y=i.jsxs("div",babelHelpers["extends"]({},D,{children:[u,O,S,s,L]})),b[55]=O,b[56]=D,b[57]=u,b[58]=S,b[59]=s,b[60]=L,b[61]=y):y=b[61];b[62]!==R||b[63]!==g||b[64]!==F?(M=F&&g===!1&&i.jsx(c("IGDSBox.react"),{flex:"shrink",justifyContent:"center",marginStart:3,position:"relative",children:i.jsx(c("IGDSButton.react"),{display:"block",label:d("PolarisGenericStrings").CANCEL_TEXT,onClick:R,variant:"secondary_link"})}),b[62]=R,b[63]=g,b[64]=F,b[65]=M):M=b[65];b[66]!==a||b[67]!==y||b[68]!==M?(N=i.jsxs("div",babelHelpers["extends"]({},a,{children:[y,M]})),b[66]=a,b[67]=y,b[68]=M,b[69]=N):N=b[69];return N}function m(){}g["default"]=a}),98);
__d("PolarisSearchBoxContainerQuery_instagramRelayOperation",[],(function(a,b,c,d,e,f){e.exports="9810106072436236"}),null);
__d("PolarisSearchBoxContainerQuery.graphql",["PolarisSearchBoxContainerQuery_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a=function(){var a={defaultValue:null,kind:"LocalArgument",name:"data"},c={defaultValue:null,kind:"LocalArgument",name:"hasQuery"},d={defaultValue:null,kind:"LocalArgument",name:"query"},e={alias:null,args:null,kind:"ScalarField",name:"position",storageKey:null},f={alias:null,args:null,kind:"ScalarField",name:"name",storageKey:null},g={alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},h={alias:null,args:null,concreteType:"XDTInformModule",kind:"LinkedField",name:"inform_module",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"category_id",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"category_name",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"inform_module_behavior",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"action_link",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"title_text",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"body_text",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"action_text",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"see_results_button_text",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"request_token",storageKey:null}],storageKey:null},i={alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},j=[{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_verified",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"ai_agent_owner_username",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"full_name",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"search_social_context",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"unseen_count",storageKey:null},i,{alias:null,args:null,kind:"ScalarField",name:"live_broadcast_visibility",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"live_broadcast_id",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"profile_pic_url",storageKey:null},{alias:null,args:null,concreteType:"XDTProfilePicUrlInfo",kind:"LinkedField",name:"hd_profile_pic_url_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"url",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_unpublished",storageKey:null},g];return{fragment:{argumentDefinitions:[a,c,d],kind:"Fragment",metadata:null,name:"PolarisSearchBoxContainerQuery",selections:[{args:null,kind:"FragmentSpread",name:"PolarisSearchBoxContainer_topsearch"},{args:null,kind:"FragmentSpread",name:"PolarisSearchBoxContainer_nonprofiledsearch"}],type:"Query",abstractKey:null},kind:"Request",operation:{argumentDefinitions:[a,d,c],kind:"Operation",name:"PolarisSearchBoxContainerQuery",selections:[{condition:"hasQuery",kind:"Condition",passingValue:!0,selections:[{alias:null,args:[{kind:"Variable",name:"_request_data",variableName:"data"}],concreteType:"XDTTopsearchResponse",kind:"LinkedField",name:"xdt_api__v1__fbsearch__topsearch_connection",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTSeeMoreKeywordItems",kind:"LinkedField",name:"see_more",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"preview_number",storageKey:null},{alias:null,args:null,concreteType:"XDTRankedKeywordItem",kind:"LinkedField",name:"list",plural:!0,selections:[e,{alias:null,args:null,concreteType:"XDTSearchKeywordResultDict",kind:"LinkedField",name:"keyword",plural:!1,selections:[f,g],storageKey:null}],storageKey:null}],storageKey:null},h,{alias:null,args:null,concreteType:"XDTRankedHashtagItem",kind:"LinkedField",name:"hashtags",plural:!0,selections:[e,{alias:null,args:null,concreteType:"XDTTagSearchResultItemDict",kind:"LinkedField",name:"hashtag",plural:!1,selections:[f,{alias:null,args:null,kind:"ScalarField",name:"media_count",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"search_result_subtitle",storageKey:null},g],storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTRankedLocationItem",kind:"LinkedField",name:"places",plural:!0,selections:[e,{alias:null,args:null,concreteType:"XDTSearchLocationResultDict",kind:"LinkedField",name:"place",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTLocationDict",kind:"LinkedField",name:"location",plural:!1,selections:[i,f,{alias:null,args:null,kind:"ScalarField",name:"facebook_places_id",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"subtitle",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"title",storageKey:null}],storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTRankedUser",kind:"LinkedField",name:"users",plural:!0,selections:[e,{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:j,storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"rank_token",storageKey:null}],storageKey:null},{alias:null,args:[{kind:"Variable",name:"query",variableName:"query"}],concreteType:"XDTNonProfiledSerpResponse",kind:"LinkedField",name:"xdt_api__v1__fbsearch__non_profiled_serp",plural:!1,selections:[h,{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"users",plural:!0,selections:j,storageKey:null}],storageKey:null}]}]},params:{id:b("PolarisSearchBoxContainerQuery_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__fbsearch__topsearch_connection","xdt_api__v1__fbsearch__non_profiled_serp"]},name:"PolarisSearchBoxContainerQuery",operationKind:"query",text:null}}}();e.exports=a}),null);
__d("PolarisSearchBoxNonProfiledRefetchableQuery_instagramRelayOperation",[],(function(a,b,c,d,e,f){e.exports="9827860787299500"}),null);
__d("PolarisSearchBoxNonProfiledRefetchableQuery.graphql",["PolarisSearchBoxNonProfiledRefetchableQuery_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a=function(){var a=[{defaultValue:null,kind:"LocalArgument",name:"hasQuery"},{defaultValue:null,kind:"LocalArgument",name:"query"}];return{fragment:{argumentDefinitions:a,kind:"Fragment",metadata:null,name:"PolarisSearchBoxNonProfiledRefetchableQuery",selections:[{args:null,kind:"FragmentSpread",name:"PolarisSearchBoxContainer_nonprofiledsearch"}],type:"Query",abstractKey:null},kind:"Request",operation:{argumentDefinitions:a,kind:"Operation",name:"PolarisSearchBoxNonProfiledRefetchableQuery",selections:[{condition:"hasQuery",kind:"Condition",passingValue:!0,selections:[{alias:null,args:[{kind:"Variable",name:"query",variableName:"query"}],concreteType:"XDTNonProfiledSerpResponse",kind:"LinkedField",name:"xdt_api__v1__fbsearch__non_profiled_serp",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTInformModule",kind:"LinkedField",name:"inform_module",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"category_id",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"category_name",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"inform_module_behavior",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"action_link",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"title_text",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"body_text",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"action_text",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"see_results_button_text",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"request_token",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"users",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_verified",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"ai_agent_owner_username",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"full_name",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"search_social_context",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"unseen_count",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"live_broadcast_visibility",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"live_broadcast_id",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"profile_pic_url",storageKey:null},{alias:null,args:null,concreteType:"XDTProfilePicUrlInfo",kind:"LinkedField",name:"hd_profile_pic_url_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"url",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_unpublished",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null}],storageKey:null}],storageKey:null}]}]},params:{id:b("PolarisSearchBoxNonProfiledRefetchableQuery_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__fbsearch__non_profiled_serp"]},name:"PolarisSearchBoxNonProfiledRefetchableQuery",operationKind:"query",text:null}}}();e.exports=a}),null);
__d("PolarisSearchBoxContainer_nonprofiledsearch.graphql",["PolarisSearchBoxNonProfiledRefetchableQuery.graphql"],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[{kind:"RootArgument",name:"hasQuery"},{kind:"RootArgument",name:"query"}],kind:"Fragment",metadata:{refetch:{connection:null,fragmentPathInResult:[],operation:b("PolarisSearchBoxNonProfiledRefetchableQuery.graphql")}},name:"PolarisSearchBoxContainer_nonprofiledsearch",selections:[{condition:"hasQuery",kind:"Condition",passingValue:!0,selections:[{alias:null,args:[{kind:"Variable",name:"query",variableName:"query"}],concreteType:"XDTNonProfiledSerpResponse",kind:"LinkedField",name:"xdt_api__v1__fbsearch__non_profiled_serp",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisNonProfiledSearchContentGatedFragment"},{args:null,kind:"FragmentSpread",name:"usePolarisNonProfiledSearchResultsData_items"},{args:null,kind:"FragmentSpread",name:"usePolarisSearchViewportLogTopsearch_nonprofiled"}],storageKey:null}]}],type:"Query",abstractKey:null};e.exports=a}),null);
__d("PolarisSearchBoxRefetchableQuery_instagramRelayOperation",[],(function(a,b,c,d,e,f){e.exports="9523870587735596"}),null);
__d("PolarisSearchBoxRefetchableQuery.graphql",["PolarisSearchBoxRefetchableQuery_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a=function(){var a=[{defaultValue:null,kind:"LocalArgument",name:"data"},{defaultValue:null,kind:"LocalArgument",name:"hasQuery"}],c={alias:null,args:null,kind:"ScalarField",name:"position",storageKey:null},d={alias:null,args:null,kind:"ScalarField",name:"name",storageKey:null},e={alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},f={alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null};return{fragment:{argumentDefinitions:a,kind:"Fragment",metadata:null,name:"PolarisSearchBoxRefetchableQuery",selections:[{args:null,kind:"FragmentSpread",name:"PolarisSearchBoxContainer_topsearch"}],type:"Query",abstractKey:null},kind:"Request",operation:{argumentDefinitions:a,kind:"Operation",name:"PolarisSearchBoxRefetchableQuery",selections:[{condition:"hasQuery",kind:"Condition",passingValue:!0,selections:[{alias:null,args:[{kind:"Variable",name:"_request_data",variableName:"data"}],concreteType:"XDTTopsearchResponse",kind:"LinkedField",name:"xdt_api__v1__fbsearch__topsearch_connection",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTSeeMoreKeywordItems",kind:"LinkedField",name:"see_more",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"preview_number",storageKey:null},{alias:null,args:null,concreteType:"XDTRankedKeywordItem",kind:"LinkedField",name:"list",plural:!0,selections:[c,{alias:null,args:null,concreteType:"XDTSearchKeywordResultDict",kind:"LinkedField",name:"keyword",plural:!1,selections:[d,e],storageKey:null}],storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTInformModule",kind:"LinkedField",name:"inform_module",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"category_id",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"category_name",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"inform_module_behavior",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"action_link",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"title_text",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"body_text",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"action_text",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"see_results_button_text",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"request_token",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTRankedHashtagItem",kind:"LinkedField",name:"hashtags",plural:!0,selections:[c,{alias:null,args:null,concreteType:"XDTTagSearchResultItemDict",kind:"LinkedField",name:"hashtag",plural:!1,selections:[d,{alias:null,args:null,kind:"ScalarField",name:"media_count",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"search_result_subtitle",storageKey:null},e],storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTRankedLocationItem",kind:"LinkedField",name:"places",plural:!0,selections:[c,{alias:null,args:null,concreteType:"XDTSearchLocationResultDict",kind:"LinkedField",name:"place",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTLocationDict",kind:"LinkedField",name:"location",plural:!1,selections:[f,d,{alias:null,args:null,kind:"ScalarField",name:"facebook_places_id",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"subtitle",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"title",storageKey:null}],storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTRankedUser",kind:"LinkedField",name:"users",plural:!0,selections:[c,{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_verified",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"ai_agent_owner_username",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"full_name",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"search_social_context",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"unseen_count",storageKey:null},f,{alias:null,args:null,kind:"ScalarField",name:"live_broadcast_visibility",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"live_broadcast_id",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"profile_pic_url",storageKey:null},{alias:null,args:null,concreteType:"XDTProfilePicUrlInfo",kind:"LinkedField",name:"hd_profile_pic_url_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"url",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_unpublished",storageKey:null},e],storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"rank_token",storageKey:null}],storageKey:null}]}]},params:{id:b("PolarisSearchBoxRefetchableQuery_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__fbsearch__topsearch_connection"]},name:"PolarisSearchBoxRefetchableQuery",operationKind:"query",text:null}}}();e.exports=a}),null);
__d("PolarisSearchBoxContainer_topsearch.graphql",["PolarisSearchBoxRefetchableQuery.graphql"],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[{kind:"RootArgument",name:"data"},{kind:"RootArgument",name:"hasQuery"}],kind:"Fragment",metadata:{refetch:{connection:null,fragmentPathInResult:[],operation:b("PolarisSearchBoxRefetchableQuery.graphql")}},name:"PolarisSearchBoxContainer_topsearch",selections:[{condition:"hasQuery",kind:"Condition",passingValue:!0,selections:[{alias:null,args:[{kind:"Variable",name:"_request_data",variableName:"data"}],concreteType:"XDTTopsearchResponse",kind:"LinkedField",name:"xdt_api__v1__fbsearch__topsearch_connection",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisSearchContentFragment"},{args:null,kind:"FragmentSpread",name:"usePolarisSearchResultsLogData_items"},{args:null,kind:"FragmentSpread",name:"usePolarisSearchViewportLogTopsearch_topsearch"},{alias:null,args:null,kind:"ScalarField",name:"rank_token",storageKey:null}],storageKey:null}]}],type:"Query",abstractKey:null};e.exports=a}),null);
__d("PolarisSearchContentFragment.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisSearchContentFragment",selections:[{args:null,kind:"FragmentSpread",name:"PolarisSearchResultsListFragment"}],type:"XDTTopsearchResponse",abstractKey:null};e.exports=a}),null);
__d("PolarisSearchNullStateQuery_instagramRelayOperation",[],(function(a,b,c,d,e,f){e.exports="9775140959235855"}),null);
__d("PolarisSearchNullStateQuery.graphql",["PolarisSearchNullStateQuery_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a=function(){var a={alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},c={alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},d={alias:null,args:null,kind:"ScalarField",name:"name",storageKey:null};return{fragment:{argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisSearchNullStateQuery",selections:[{alias:null,args:null,concreteType:"XDTRecentSearches",kind:"LinkedField",name:"xdt_api__v1__fbsearch__recent_searches_connection",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisSearchNullStateSection_items"}],storageKey:null}],type:"Query",abstractKey:null},kind:"Request",operation:{argumentDefinitions:[],kind:"Operation",name:"PolarisSearchNullStateQuery",selections:[{alias:null,args:null,concreteType:"XDTRecentSearches",kind:"LinkedField",name:"xdt_api__v1__fbsearch__recent_searches_connection",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTIgRecentSearchesItem",kind:"LinkedField",name:"recent",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_verified",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"ai_agent_owner_username",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"full_name",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"search_social_context",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"unseen_count",storageKey:null},a,{alias:null,args:null,kind:"ScalarField",name:"live_broadcast_visibility",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"live_broadcast_id",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"profile_pic_url",storageKey:null},{alias:null,args:null,concreteType:"XDTProfilePicUrlInfo",kind:"LinkedField",name:"hd_profile_pic_url_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"url",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_unpublished",storageKey:null},c],storageKey:null},{alias:null,args:null,concreteType:"XDTTagSearchResultItemDict",kind:"LinkedField",name:"hashtag",plural:!1,selections:[d,{alias:null,args:null,kind:"ScalarField",name:"media_count",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"search_result_subtitle",storageKey:null},c],storageKey:null},{alias:null,args:null,concreteType:"XDTSearchLocationResultDict",kind:"LinkedField",name:"place",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTLocationDict",kind:"LinkedField",name:"location",plural:!1,selections:[a,d,{alias:null,args:null,kind:"ScalarField",name:"facebook_places_id",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"subtitle",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"title",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTKeywordDict",kind:"LinkedField",name:"keyword",plural:!1,selections:[d,c],storageKey:null}],storageKey:null}],storageKey:null}]},params:{id:b("PolarisSearchNullStateQuery_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__fbsearch__recent_searches_connection"]},name:"PolarisSearchNullStateQuery",operationKind:"query",text:null}}}();e.exports=a}),null);
__d("PolarisSearchNullStateSectionDismissItemMutation.graphql",[],(function(a,b,c,d,e,f){"use strict";a=function(){var a=[{defaultValue:null,kind:"LocalArgument",name:"input"}],b=[{alias:null,args:[{kind:"Variable",name:"_request_data",variableName:"input"}],concreteType:"XDTEmptyRecord",kind:"LinkedField",name:"xdt_api__v1__fbsearch__hide_search_entities",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"__typename",storageKey:null}],storageKey:null}];return{fragment:{argumentDefinitions:a,kind:"Fragment",metadata:null,name:"PolarisSearchNullStateSectionDismissItemMutation",selections:b,type:"Mutation",abstractKey:null},kind:"Request",operation:{argumentDefinitions:a,kind:"Operation",name:"PolarisSearchNullStateSectionDismissItemMutation",selections:b},params:{id:"9472844426143921",metadata:{},name:"PolarisSearchNullStateSectionDismissItemMutation",operationKind:"mutation",text:null}}}();e.exports=a}),null);
__d("PolarisSearchNullStateSection_items.graphql",[],(function(a,b,c,d,e,f){"use strict";a=function(){var a={kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},b={alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null};return{argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisSearchNullStateSection_items",selections:[{alias:null,args:null,concreteType:"XDTIgRecentSearchesItem",kind:"LinkedField",name:"recent",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisSearchResultUserItemFragment_user"},a],storageKey:null},{alias:null,args:null,concreteType:"XDTTagSearchResultItemDict",kind:"LinkedField",name:"hashtag",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisSearchResultHashtagItemFragment_hashtag"},{kind:"RequiredField",field:b,action:"THROW"}],storageKey:null},{alias:null,args:null,concreteType:"XDTSearchLocationResultDict",kind:"LinkedField",name:"place",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisSearchResultPlaceItemFragment_location"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTLocationDict",kind:"LinkedField",name:"location",plural:!1,selections:[a],storageKey:null},action:"THROW"}],storageKey:null},{alias:null,args:null,concreteType:"XDTKeywordDict",kind:"LinkedField",name:"keyword",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"name",storageKey:null},b],storageKey:null}],storageKey:null},{args:null,kind:"FragmentSpread",name:"usePolarisSearchViewportLogRecentSearches_recentSearches"}],type:"XDTRecentSearches",abstractKey:null}}();e.exports=a}),null);
__d("PolarisSearchViewportLogger_recentSearches.graphql",[],(function(a,b,c,d,e,f){"use strict";a={kind:"InlineDataFragment",name:"PolarisSearchViewportLogger_recentSearches"};e.exports=a}),null);
__d("PolarisSearchViewportLogger",["CometRelay","PolarisIsLoggedIn","PolarisSearchLogger","PolarisSearchViewportLogger_recentSearches.graphql","SearchViewportViewFalcoEvent","err"],(function(a,b,c,d,e,f,g){"use strict";var h,i=function(){a.merge=function(b,c){return new a(b.resultList.concat(c.resultList),b.typeList.concat(c.typeList),b.positionList.concat(c.positionList))};function a(a,b,c){this.resultList=a,this.typeList=b,this.positionList=c}return a}();a=function(){a.getInstance=function(){a.$1||(a.$1=new a());return a.$1};function a(){if(a.$1)throw c("err")("PolarisSearchViewportLogger instance already exists.");a.$1=this;this.$2="";this.$3="default";this.$6="";this.$4=new i([],[],[]);this.$5=new i([],[],[])}var e=a.prototype;e.reset=function(a,b,c){if(a===this.$2&&b===this.$3&&c===this.$6)return;this.$2=a;this.$3=b;this.$6=c};e.applyResults=function(a,b,c){this.$4=new i(a,b,c)};e.applyRecentResults=function(a){var c=this;a=d("CometRelay").readInlineData(h!==void 0?h:h=b("PolarisSearchViewportLogger_recentSearches.graphql"),a);a=a.recent;this.$5=new i([],[],[]);a==null?void 0:a.forEach(function(a,b){var e;if(a.user!=null&&a.user.pk!=null)c.$5.resultList.push(a.user.pk),c.$5.typeList.push(d("PolarisSearchLogger").TYPE_MAP.USER_RESULT);else if(a.hashtag!=null)c.$5.resultList.push(a.hashtag.id),c.$5.typeList.push(d("PolarisSearchLogger").TYPE_MAP.HASHTAG_RESULT);else if(a.place!=null&&((e=a.place.location)==null?void 0:e.pk)!=null)c.$5.resultList.push(a.place.location.pk),c.$5.typeList.push(d("PolarisSearchLogger").TYPE_MAP.PLACE_RESULT);else if(a.keyword!=null){e=(e=a.keyword.id)!=null?e:a.keyword.name;c.$5.resultList.push(String(e));c.$5.typeList.push(d("PolarisSearchLogger").TYPE_MAP.KEYWORD_RESULT)}c.$5.positionList.push(b)})};e.log=function(){var a=this;if(!d("PolarisIsLoggedIn").isLoggedIn())return;var b=i.merge(this.$5,this.$4);c("SearchViewportViewFalcoEvent").log(function(){return{pigeon_reserved_keyword_module:d("PolarisSearchLogger").getContainerModule(a.$3),query_text:a.$2,results_list:b.resultList,results_position_list:b.positionList.map(function(a){return a.toString()}),results_source_list:[],results_type_list:b.typeList,search_session_id:a.$6}})};return a}();e=a.getInstance();f=e;g["default"]=f}),98);
__d("usePolarisSearchViewportLogRecentSearches_recentSearches.graphql",[],(function(a,b,c,d,e,f){"use strict";a=function(){var a=[{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null}],b={alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null};return{argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisSearchViewportLogRecentSearches_recentSearches",selections:[{kind:"InlineDataFragmentSpread",name:"PolarisSearchViewportLogger_recentSearches",selections:[{alias:null,args:null,concreteType:"XDTIgRecentSearchesItem",kind:"LinkedField",name:"recent",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:a,storageKey:null},{alias:null,args:null,concreteType:"XDTTagSearchResultItemDict",kind:"LinkedField",name:"hashtag",plural:!1,selections:[b],storageKey:null},{alias:null,args:null,concreteType:"XDTSearchLocationResultDict",kind:"LinkedField",name:"place",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTLocationDict",kind:"LinkedField",name:"location",plural:!1,selections:a,storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTKeywordDict",kind:"LinkedField",name:"keyword",plural:!1,selections:[b,{alias:null,args:null,kind:"ScalarField",name:"name",storageKey:null}],storageKey:null}],storageKey:null}],args:null,argumentDefinitions:[]}],type:"XDTRecentSearches",abstractKey:null}}();e.exports=a}),null);
__d("usePolarisSearchViewportLogRecentSearches",["CometRelay","PolarisActiveSearchContext.react","PolarisIsLoggedIn","PolarisSearchViewportLogger","react","react-compiler-runtime","usePolarisSearchViewportLogRecentSearches_recentSearches.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i;e=i||d("react");var j=e.useContext,k=e.useEffect;function a(a,e){var f=d("react-compiler-runtime").c(8),g=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisSearchViewportLogRecentSearches_recentSearches.graphql"),a);a=j(d("PolarisActiveSearchContext.react").PolarisActiveSearchContext);var i=a.discoverToken,l=a.tab,m;f[0]!==i||f[1]!==e||f[2]!==l?(a=function(){c("PolarisSearchViewportLogger").reset(e,l,i)},m=[i,e,l],f[0]=i,f[1]=e,f[2]=l,f[3]=a,f[4]=m):(a=f[3],m=f[4]);k(a,m);f[5]!==g?(a=function(){if(!d("PolarisIsLoggedIn").isLoggedIn())return;c("PolarisSearchViewportLogger").applyRecentResults(g)},m=[g],f[5]=g,f[6]=a,f[7]=m):(a=f[6],m=f[7]);k(a,m)}g["default"]=a}),98);
__d("PolarisSearchNullStateSection.next.react",["fbt","CometRelay","IGDSSpinner.react","IGDSTextVariants.react","PolarisClearSearchHistoryButton.next.react","PolarisReactRedux.react","PolarisSearchActions","PolarisSearchConstants","PolarisSearchContext.react","PolarisSearchLoggingHelper","PolarisSearchNullStateSectionDismissItemMutation.graphql","PolarisSearchNullStateSection_items.graphql","PolarisSearchResultHashtagItem.next.react","PolarisSearchResultKeywordItem.next.react","PolarisSearchResultPlaceItem.next.react","PolarisSearchResultUserItem.next.react","PolarisUA","react","react-compiler-runtime","usePolarisSearchViewportLogRecentSearches"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k,l=k||(k=d("react")),m=k.useContext,n=i!==void 0?i:i=b("PolarisSearchNullStateSectionDismissItemMutation.graphql");function a(a){var e,f,g=d("react-compiler-runtime").c(31),i=a.fragmentKey,k=a.loading;a=a.title;e=(e=m(d("PolarisSearchContext.react").PolarisSearchContext))==null?void 0:e.placement;var o=d("PolarisReactRedux.react").useDispatch();i=d("CometRelay").useFragment(j!==void 0?j:j=b("PolarisSearchNullStateSection_items.graphql"),i);var p=d("CometRelay").useMutation(n),q=p[0];c("usePolarisSearchViewportLogRecentSearches")(i,"");g[0]!==o?(p=function(){o(d("PolarisSearchActions").resetSelectedSearchResult())},g[0]=o,g[1]=p):p=g[1];p=p;if(d("PolarisUA").isMobile()&&((f=i.recent)==null?void 0:f.length)===0)return null;g[2]!==q?(f=function(a,b,c){a.preventDefault();a=function(a){var e;e=(e=(e=a.getRoot().getLinkedRecord("xdt_api__v1__fbsearch__recent_searches_connection"))==null?void 0:e.getLinkedRecords("recent"))!=null?e:[];e=e==null?void 0:e.filter(function(a){a=a==null?void 0:a.getLinkedRecord(c);switch(c){case d("PolarisSearchConstants").RECENT_SEARCH_TYPES.USER:return(a==null?void 0:a.getValue("pk"))!==b;case d("PolarisSearchConstants").RECENT_SEARCH_TYPES.HASHTAG:return(a==null?void 0:a.getValue("id"))!==b;case d("PolarisSearchConstants").RECENT_SEARCH_TYPES.LOCATION:var e;return(a==null?void 0:(e=a.getLinkedRecord("location"))==null?void 0:e.getValue("facebook_places_id"))!==b;case d("PolarisSearchConstants").RECENT_SEARCH_TYPES.KEYWORD:return(a==null?void 0:a.getValue("name"))!==b}});(a=a.getRoot().getLinkedRecord("xdt_api__v1__fbsearch__recent_searches_connection"))==null?void 0:a.setLinkedRecords([].concat(e),"recent")};b!=null&&q({optimisticUpdater:a,updater:a,variables:{input:{hashtag:c===d("PolarisSearchConstants").RECENT_SEARCH_TYPES.HASHTAG?[b]:[],keyword_names:c===d("PolarisSearchConstants").RECENT_SEARCH_TYPES.KEYWORD?JSON.stringify([b]):void 0,place:c===d("PolarisSearchConstants").RECENT_SEARCH_TYPES.LOCATION?[b]:[],section:"recent",user:c===d("PolarisSearchConstants").RECENT_SEARCH_TYPES.USER?[b]:[]}}})},g[2]=q,g[3]=f):f=g[3];var r=f;if(k){g[4]===Symbol["for"]("react.memo_cache_sentinel")?(f={className:"x6s0dn4 x972fbf x10w94by x1qhh985 x14e42zd x9f619 x78zum5 xdt5ytf x1iyjqo2 x2lah0s xln7xf2 xk390pu xl56j7k xdj266r x14z9mp xat24cr x1lziwak xexx8yu xyri2b x18d9i69 x1c1uobl x1n2onr6 x11njtxf"},g[4]=f):f=g[4];g[5]===Symbol["for"]("react.memo_cache_sentinel")?(f=l.jsx("div",babelHelpers["extends"]({},f,{"data-testid":void 0,children:l.jsx(c("IGDSSpinner.react"),{position:"absolute",size:"small"})})),g[5]=f):f=g[5];f=f}else{var s;if(((s=i.recent)==null?void 0:s.length)===0){g[6]===Symbol["for"]("react.memo_cache_sentinel")?(s={className:"x6s0dn4 x972fbf x10w94by x1qhh985 x14e42zd x9f619 x78zum5 xdt5ytf x1iyjqo2 x2lah0s xln7xf2 xk390pu xl56j7k xdj266r x14z9mp xat24cr x1lziwak xexx8yu xyri2b x18d9i69 x1c1uobl x1n2onr6 x11njtxf"},g[6]=s):s=g[6];g[7]===Symbol["for"]("react.memo_cache_sentinel")?(s=l.jsx("div",babelHelpers["extends"]({},s,{"data-testid":void 0,children:l.jsx(d("IGDSTextVariants.react").IGDSTextBodyEmphasized,{color:"secondaryText",textAlign:"center",children:h._(/*BTDS*/"No recent searches.")})})),g[7]=s):s=g[7];f=s}else{g[8]===Symbol["for"]("react.memo_cache_sentinel")?(s={className:"x972fbf x10w94by x1qhh985 x14e42zd xln7xf2 xk390pu x1xmf6yo x14z9mp x1e56ztr x1lziwak xexx8yu xyri2b x18d9i69 x1c1uobl x11njtxf"},g[8]=s):s=g[8];if(g[9]!==i.recent||g[10]!==r){var t;t=(t=i.recent)==null?void 0:t.map(function(a,b){if(a.user!=null)return l.jsx(c("PolarisSearchResultUserItem.next.react"),{context:d("PolarisSearchLoggingHelper").SearchResultContextTypes.SEARCH,fragmentKey:a.user,onDismiss:r,position:b},"null_state_section_user_"+a.user.pk);else if(a.hashtag!=null)return l.jsx(c("PolarisSearchResultHashtagItem.next.react"),{context:d("PolarisSearchLoggingHelper").SearchResultContextTypes.SEARCH,fragmentKey:a.hashtag,onDismiss:r,position:b},"null_state_section_hashtag_"+a.hashtag.id);else if(a.place!=null)return l.jsx(c("PolarisSearchResultPlaceItem.next.react"),{context:d("PolarisSearchLoggingHelper").SearchResultContextTypes.SEARCH,fragmentKey:a.place,onDismiss:r,position:b},"null_state_section_place_"+a.place.location.pk);else if(a.keyword!=null){var e;e=(e=(e=a.keyword)==null?void 0:e.name)!=null?e:"";return l.jsx(c("PolarisSearchResultKeywordItem.next.react"),{id:a.keyword.id!=null?String(a.keyword.id):void 0,name:e,onDismiss:r,position:b},"null_state_section_keyword_"+e)}});g[9]=i.recent;g[10]=r;g[11]=t}else t=g[11];g[12]!==p||g[13]!==t?(s=l.jsx("ul",babelHelpers["extends"]({},s,{"data-testid":void 0,onMouseLeave:p,children:t})),g[12]=p,g[13]=t,g[14]=s):s=g[14];f=s}}g[15]===Symbol["for"]("react.memo_cache_sentinel")?(p={className:"x78zum5 xdt5ytf x1iyjqo2 x5yr21d x1odjw0f x1n2onr6 xh8yej3"},g[15]=p):p=g[15];g[16]!==e?(t={0:{className:"x6s0dn4 x78zum5 x1q0g3np x1qughib xyqm7xq x1ys307a x1iorvi4"},1:{className:"x6s0dn4 x78zum5 x1q0g3np x1qughib x1iorvi4 x1k70j0n xpwdb9g x1e56ztr xefazk8"}}[!!(e===d("PolarisSearchContext.react").SearchPlacement.SidebarSearchPanel)<<0],g[16]=e,g[17]=t):t=g[17];g[18]!==k||g[19]!==a?(s=!k&&l.jsx(d("IGDSTextVariants.react").IGDSTextSection,{zeroMargin:!0,children:a}),g[18]=k,g[19]=a,g[20]=s):s=g[20];g[21]!==i.recent||g[22]!==k?(e=i.recent&&i.recent.length>0&&!k&&l.jsx(c("PolarisClearSearchHistoryButton.next.react"),{clearSearchHistoryText:h._(/*BTDS*/"Clear all")}),g[21]=i.recent,g[22]=k,g[23]=e):e=g[23];g[24]!==t||g[25]!==s||g[26]!==e?(a=l.jsxs("div",babelHelpers["extends"]({},t,{children:[s,e]})),g[24]=t,g[25]=s,g[26]=e,g[27]=a):a=g[27];g[28]!==f||g[29]!==a?(i=l.jsxs("div",babelHelpers["extends"]({},p,{children:[a,f]})),g[28]=f,g[29]=a,g[30]=i):i=g[30];return i}g["default"]=a}),226);
__d("PolarisSearchNullState.next.react",["fbt","CometRelay","PolarisSearchNullStateQuery.graphql","PolarisSearchNullStateSection.next.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k=j||d("react"),l=h._(/*BTDS*/"Recent");function a(a){var e=d("react-compiler-runtime").c(3);a=a.loading;var f=d("CometRelay").useLazyLoadQuery(i!==void 0?i:i=b("PolarisSearchNullStateQuery.graphql"),{}),g;e[0]!==f.xdt_api__v1__fbsearch__recent_searches_connection||e[1]!==a?(g=k.jsx(c("PolarisSearchNullStateSection.next.react"),{fragmentKey:f.xdt_api__v1__fbsearch__recent_searches_connection,loading:a,title:l},"recent-searches-null-state-section"),e[0]=f.xdt_api__v1__fbsearch__recent_searches_connection,e[1]=a,e[2]=g):g=e[2];return g}g["default"]=a}),226);
__d("PolarisSearchContent.next.react",["CometPlaceholder.react","CometRelay","PolarisActiveSearchContext.react","PolarisSearchContentFragment.graphql","PolarisSearchNullState.next.react","PolarisSearchResultDisplayTypes","PolarisSearchResultsListWrapper.next.react","PolarisSearchResultsLoadingState.next.react","deferredLoadComponent","react","react-compiler-runtime","requireDeferredForDisplay"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react")),k=i.useContext,l=c("deferredLoadComponent")(c("requireDeferredForDisplay")("PolarisSearchResultsList.next.react").__setRef("PolarisSearchContent.next.react"));function a(a){var e=d("react-compiler-runtime").c(6),f=a.fragmentKey,g=a.isLoading,i=a.pendingQuery;a=a.searchSessionId;f=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisSearchContentFragment.graphql"),f);var m=k(d("PolarisActiveSearchContext.react").PolarisActiveSearchContext);m=m.resultDisplayType;m=m===c("PolarisSearchResultDisplayTypes").Panel;var n;e[0]!==f||e[1]!==m||e[2]!==g||e[3]!==i||e[4]!==a?(n=i===""?j.jsx(c("CometPlaceholder.react"),{fallback:j.jsx(c("PolarisSearchResultsListWrapper.next.react"),{children:j.jsx(c("PolarisSearchResultsLoadingState.next.react"),{includeHeaders:!0,paddingX:m?6:4})}),children:j.jsx(c("PolarisSearchResultsListWrapper.next.react"),{children:j.jsx(c("PolarisSearchNullState.next.react"),{loading:g})})}):j.jsx(c("CometPlaceholder.react"),{fallback:j.jsx(c("PolarisSearchResultsListWrapper.next.react"),{children:j.jsx(c("PolarisSearchResultsLoadingState.next.react"),{paddingX:m?6:4})}),children:j.jsx(c("PolarisSearchResultsListWrapper.next.react"),{children:j.jsx(l,{fragmentKey:f,isLoading:g,query:i,searchSessionId:a})})}),e[0]=f,e[1]=m,e[2]=g,e[3]=i,e[4]=a,e[5]=n):n=e[5];return n}g["default"]=a}),98);
__d("PolarisSearchResultsPopover.next.react",["IGDSPopover.react","PolarisSearchResultsListWrapper.next.react","PolarisSearchResultsLoadingState.next.react","PolarisSuspenseWithErrorBoundary.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(4);a=a.children;var e;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(e={className:"x1qjc9v5 x972fbf x10w94by x1qhh985 x14e42zd x9f619 x78zum5 xdt5ytf x2lah0s xk390pu xg7h5cd x1n2onr6 x11njtxf x1ko0cyj"},b[0]=e):e=b[0];var f;b[1]===Symbol["for"]("react.memo_cache_sentinel")?(f=i.jsx(c("PolarisSearchResultsListWrapper.next.react"),{children:i.jsx(c("PolarisSearchResultsLoadingState.next.react"),{paddingX:4})}),b[1]=f):f=b[1];b[2]!==a?(e=i.jsx(c("IGDSPopover.react"),{popoverContent:i.jsx("div",babelHelpers["extends"]({},e,{children:i.jsx(c("PolarisSuspenseWithErrorBoundary.react"),{loadingRenderer:f,children:a})})),popoverName:"searchResultsPopover"}),b[2]=a,b[3]=e):e=b[3];return e}g["default"]=a}),98);
__d("PolarisSearchTabsGated.react",["cr:7690","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var c=d("react-compiler-runtime").c(2),e;c[0]!==a?(e=b("cr:7690")?i.jsx(b("cr:7690"),babelHelpers["extends"]({},a)):null,c[0]=a,c[1]=e):e=c[1];return e}g["default"]=a}),98);
__d("isNullish",[],(function(a,b,c,d,e,f){"use strict";function a(a){return a===void 0||a===null}f["default"]=a}),66);
__d("shallowArrayEqual",["isNullish"],(function(a,b,c,d,e,f,g){function a(a,b){if(a===b)return!0;if(c("isNullish")(a)||c("isNullish")(b)||a.length!==b.length)return!1;for(var d=0,e=a.length;d<e;d++)if(a[d]!==b[d])return!1;return!0}g["default"]=a}),98);
__d("useDelayedBooleanState",["clearTimeout","react","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");var i=b.useEffect,j=b.useLayoutEffect,k=b.useRef,l=b.useState,m=50;function a(a,b){b===void 0&&(b=m);var d=l(!1),e=d[0],f=d[1],g=k(null);j(function(){c("clearTimeout")(g.current),g.current=null,a?g.current=c("setTimeout")(function(){f(!0),g.current=null},b):f(!1)},[a,b]);i(function(){return function(){return c("clearTimeout")(g.current)}},[]);return e}g["default"]=a}),98);
__d("usePolarisNonProfiledSearchResultsData_items.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisNonProfiledSearchResultsData_items",selections:[{alias:null,args:null,concreteType:"XDTInformModule",kind:"LinkedField",name:"inform_module",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"category_id",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"users",plural:!0,selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"}],storageKey:null}],type:"XDTNonProfiledSerpResponse",abstractKey:null};e.exports=a}),null);
__d("usePolarisNonProfiledSearchResultsData",["CometRelay","PolarisSearchLogger","react-compiler-runtime","usePolarisNonProfiledSearchResultsData_items.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){var c=d("react-compiler-runtime").c(3);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisNonProfiledSearchResultsData_items.graphql"),a);var e,f;if(c[0]!==a){f=Symbol["for"]("react.early_return_sentinel");bb0:{var g,i=[],j=[],k=[];if(a==null){f={resultsList:i,resultsPositionList:k,resultsTypeList:j};break bb0}a.inform_module!=null&&(i.push(String(a.inform_module.category_id)),j.push(d("PolarisSearchLogger").TYPE_MAP.INFORM_MODULE_RESULT),k.push(0));(g=a.users)==null?void 0:g.forEach(function(a,b){i.push(a.pk),j.push(d("PolarisSearchLogger").TYPE_MAP.USER_RESULT),k.push(b)});e={resultsList:i,resultsPositionList:k,resultsTypeList:j}}c[0]=a;c[1]=e;c[2]=f}else e=c[1],f=c[2];return f!==Symbol["for"]("react.early_return_sentinel")?f:e}g["default"]=a}),98);
__d("usePolarisSearchResultsLogData_items.graphql",[],(function(a,b,c,d,e,f){"use strict";a=function(){var a={alias:null,args:null,kind:"ScalarField",name:"position",storageKey:null},b={alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},c={kind:"RequiredField",field:a,action:"THROW"},d=[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"}];return{argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisSearchResultsLogData_items",selections:[{alias:null,args:null,concreteType:"XDTInformModule",kind:"LinkedField",name:"inform_module",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"category_id",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTSeeMoreKeywordItems",kind:"LinkedField",name:"see_more",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTRankedKeywordItem",kind:"LinkedField",name:"list",plural:!0,selections:[a,{alias:null,args:null,concreteType:"XDTSearchKeywordResultDict",kind:"LinkedField",name:"keyword",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"name",storageKey:null},b],storageKey:null}],storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTRankedHashtagItem",kind:"LinkedField",name:"hashtags",plural:!0,selections:[c,{alias:null,args:null,concreteType:"XDTTagSearchResultItemDict",kind:"LinkedField",name:"hashtag",plural:!1,selections:[{kind:"RequiredField",field:b,action:"THROW"}],storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTRankedLocationItem",kind:"LinkedField",name:"places",plural:!0,selections:[c,{alias:null,args:null,concreteType:"XDTSearchLocationResultDict",kind:"LinkedField",name:"place",plural:!1,selections:[{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTLocationDict",kind:"LinkedField",name:"location",plural:!1,selections:d,storageKey:null},action:"THROW"}],storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTRankedUser",kind:"LinkedField",name:"users",plural:!0,selections:[c,{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:d,storageKey:null},action:"THROW"}],storageKey:null}],type:"XDTTopsearchResponse",abstractKey:null}}();e.exports=a}),null);
__d("usePolarisSearchResultsLogData",["CometRelay","PolarisSearchLogger","react-compiler-runtime","usePolarisSearchResultsLogData_items.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){var c=d("react-compiler-runtime").c(3);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisSearchResultsLogData_items.graphql"),a);var e,f;if(c[0]!==a){f=Symbol["for"]("react.early_return_sentinel");bb0:{var g,i=[],j=[],k=[];if(a==null){f={resultsList:i,resultsPositionList:k,resultsTypeList:j};break bb0}a.inform_module!=null&&(i.push(String(a.inform_module.category_id)),j.push(d("PolarisSearchLogger").TYPE_MAP.INFORM_MODULE_RESULT));(g=a.hashtags)==null?void 0:g.forEach(function(a){i.push(a.hashtag.id),j.push(d("PolarisSearchLogger").TYPE_MAP.HASHTAG_RESULT),k.push(a.position)});(g=a.users)==null?void 0:g.forEach(function(a){i.push(a.user.pk),j.push(d("PolarisSearchLogger").TYPE_MAP.USER_RESULT),k.push(a.position)});(g=a.places)==null?void 0:g.forEach(function(a){i.push(a.place.location.pk),j.push(d("PolarisSearchLogger").TYPE_MAP.PLACE_RESULT),k.push(a.position)});(g=a.see_more)==null?void 0:g.list.forEach(function(a){var b;b=(b=a.keyword.id)!=null?b:a.keyword.name;i.push(String(b));j.push(d("PolarisSearchLogger").TYPE_MAP.KEYWORD_RESULT);k.push(a.position)});e={resultsList:i,resultsPositionList:k,resultsTypeList:j}}c[0]=a;c[1]=e;c[2]=f}else e=c[1],f=c[2];return f!==Symbol["for"]("react.early_return_sentinel")?f:e}g["default"]=a}),98);
__d("usePolarisSearchViewportLogTopsearch_nonprofiled.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisSearchViewportLogTopsearch_nonprofiled",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisNonProfiledSearchResultsData_items"}],type:"XDTNonProfiledSerpResponse",abstractKey:null};e.exports=a}),null);
__d("usePolarisSearchViewportLogTopsearch_topsearch.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisSearchViewportLogTopsearch_topsearch",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisSearchResultsLogData_items"}],type:"XDTTopsearchResponse",abstractKey:null};e.exports=a}),null);
__d("usePolarisSearchViewportLogTopsearch",["CometRelay","PolarisIsLoggedIn","PolarisSearchConstants","PolarisSearchViewportLogger","react","react-compiler-runtime","usePolarisNonProfiledSearchResultsData","usePolarisSearchResultsLogData","usePolarisSearchViewportLogTopsearch_nonprofiled.graphql","usePolarisSearchViewportLogTopsearch_topsearch.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=(j||d("react")).useEffect;function a(a,e,f,g,j,m){g=d("react-compiler-runtime").c(20);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisSearchViewportLogTopsearch_topsearch.graphql"),a);e=d("CometRelay").useFragment(i!==void 0?i:i=b("usePolarisSearchViewportLogTopsearch_nonprofiled.graphql"),e);var n,o;g[0]!==j||g[1]!==f||g[2]!==m?(n=function(){c("PolarisSearchViewportLogger").reset(f,m,j)},o=[j,f,m],g[0]=j,g[1]=f,g[2]=m,g[3]=n,g[4]=o):(n=g[3],o=g[4]);k(n,o);g[5]===Symbol["for"]("react.memo_cache_sentinel")?(n=[],g[5]=n):n=g[5];k(l,n);o=c("usePolarisSearchResultsLogData")(a);var p=o.resultsList;n=o.resultsPositionList;var q=o.resultsTypeList;g[6]!==n?(a=n===void 0?[]:n,g[6]=n,g[7]=a):a=g[7];var r=a;o=c("usePolarisNonProfiledSearchResultsData")(e);var s=o.resultsList;n=o.resultsPositionList;var t=o.resultsTypeList;g[8]!==n?(a=n===void 0?[]:n,g[8]=n,g[9]=a):a=g[9];var u=a;g[10]!==s||g[11]!==u||g[12]!==t||g[13]!==p||g[14]!==r||g[15]!==q||g[16]!==f||g[17]!==m?(e=function(){var a=m===d("PolarisSearchConstants").SearchTabConstants["default"]?p:s,b=m===d("PolarisSearchConstants").SearchTabConstants["default"]?q:t,e=m===d("PolarisSearchConstants").SearchTabConstants["default"]?r:u;if(!d("PolarisIsLoggedIn").isLoggedIn()||f==="")return;c("PolarisSearchViewportLogger").applyResults(a,b,e)},o=[s,u,t,p,r,q,f,m],g[10]=s,g[11]=u,g[12]=t,g[13]=p,g[14]=r,g[15]=q,g[16]=f,g[17]=m,g[18]=e,g[19]=o):(e=g[18],o=g[19]);k(e,o)}function l(){return m}function m(){c("PolarisSearchViewportLogger").log()}g["default"]=a}),98);
__d("PolarisSearchBoxContainer.next.react",["CometRelay","FBLogger","IGDSBox.react","IGDSDivider.react","PolarisActiveSearchContext.react","PolarisListWithKeyCommands.react","PolarisNonProfiledSearchContentGated.react","PolarisSearchBox.next.react","PolarisSearchBoxContainerQuery.graphql","PolarisSearchBoxContainer_nonprofiledsearch.graphql","PolarisSearchBoxContainer_topsearch.graphql","PolarisSearchConstants","PolarisSearchContent.next.react","PolarisSearchLogger","PolarisSearchResultDisplayTypes","PolarisSearchResultsPopover.next.react","PolarisSearchTabsGated.react","polarisLogAction","react","shallowArrayEqual","useDelayedBooleanState","usePolarisNonProfiledSearchResultsData","usePolarisSearchResultsLogData","usePolarisSearchViewportLogTopsearch","usePolarisSelector","useStableValue"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=k||(k=d("react"));e=k;var m=e.useCallback,n=e.useEffect,o=e.useRef,p=e.useState,q=e.useTransition,r={searchBar:{display:"x78zum5",height:"xdd8jsf",order:"x1g77sc7",$$css:!0}},s=4,t=100;function a(a){var e,f=a.analyticsContext,g=a.hideResults;g=g===void 0?!1:g;var k=a.isAutoFocused,u=a.placeholder;a=a.resultDisplayType;a=a===void 0?c("PolarisSearchResultDisplayTypes").Panel:a;var v=q(),w=v[0],x=v[1];v=c("useDelayedBooleanState")(w,t);w=p("");var y=w[0];w=w[1];var z=c("usePolarisSelector")(function(a){return a.discoverChaining.token}),A=d("CometRelay").useLazyLoadQuery(h!==void 0?h:h=b("PolarisSearchBoxContainerQuery.graphql"),{data:{context:d("PolarisSearchConstants").SEARCH_CONTEXT.BLENDED,include_reel:String(!0),search_session_id:z,search_surface:"web_top_search"},hasQuery:!1}),B=d("CometRelay").useRefetchableFragment(i!==void 0?i:i=b("PolarisSearchBoxContainer_topsearch.graphql"),A),C=B[0],D=B[1];B=d("CometRelay").useRefetchableFragment(j!==void 0?j:j=b("PolarisSearchBoxContainer_nonprofiledsearch.graphql"),A);A=B[0];var E=B[1];B=c("usePolarisSearchResultsLogData")(C==null?void 0:C.xdt_api__v1__fbsearch__topsearch_connection);var F=B.resultsList;B=B.resultsTypeList;var G=d("useStableValue").useStableValue({resultsList:F,resultsTypeList:B},function(a,b){return c("shallowArrayEqual")(a.resultsList,b.resultsList)});F=c("usePolarisNonProfiledSearchResultsData")(A==null?void 0:A.xdt_api__v1__fbsearch__non_profiled_serp);B=F.resultsList;F=F.resultsTypeList;var H=d("useStableValue").useStableValue({resultsList:B,resultsTypeList:F},function(a,b){return c("shallowArrayEqual")(a.resultsList,b.resultsList)});B=p("");var I=B[0],J=B[1];F=p("");var K=F[0],L=F[1],M=o(d("PolarisSearchConstants").SearchTabConstants["default"]);B=function(a){M.current=a};y===""&&(M.current=d("PolarisSearchConstants").SearchTabConstants["default"]);F=y!=="";var N=m(function(a,b,e){c("polarisLogAction")("searchAttempt"),x(function(){if(M.current===d("PolarisSearchConstants").SearchTabConstants["default"]){if(I===b)return;D({data:{context:a,include_reel:String(!0),query:b,rank_token:e,search_session_id:z,search_surface:"web_top_search"},hasQuery:!0},{onComplete:function(a){J(b),a!=null?(c("polarisLogAction")("searchFailure"),c("FBLogger")("ig_web").catching(a).warn("Search query failed")):c("polarisLogAction")("searchSuccess")}})}else{if(K===b)return;E({hasQuery:!0,query:b},{onComplete:function(a){L(b),a!=null?(c("polarisLogAction")("searchFailure"),c("FBLogger")("ig_web").catching(a).warn("Search query failed")):c("polarisLogAction")("searchSuccess")}})}})},[K,I,D,E]);c("usePolarisSearchViewportLogTopsearch")(C==null?void 0:C.xdt_api__v1__fbsearch__topsearch_connection,A==null?void 0:A.xdt_api__v1__fbsearch__non_profiled_serp,y,(e=C==null?void 0:(e=C.xdt_api__v1__fbsearch__topsearch_connection)==null?void 0:e.rank_token)!=null?e:"",z,M.current);n(function(){if(I==="")return;d("PolarisSearchLogger").logSearchResultsByList({analyticsContext:f,queryText:I,resultsList:G.resultsList,resultsTypeList:G.resultsTypeList,searchSessionID:z,tab:d("PolarisSearchConstants").SearchTabConstants["default"]})},[G]);n(function(){if(K==="")return;d("PolarisSearchLogger").logSearchResultsByList({analyticsContext:f,queryText:K,resultsList:H.resultsList,resultsTypeList:H.resultsTypeList,searchSessionID:z,tab:d("PolarisSearchConstants").SearchTabConstants.notPersonalized})},[H]);e=c("PolarisNonProfiledSearchContentGated.react")==null||y===""||M.current===d("PolarisSearchConstants").SearchTabConstants["default"]?l.jsx(c("PolarisSearchContent.next.react"),{fragmentKey:C==null?void 0:C.xdt_api__v1__fbsearch__topsearch_connection,isLoading:v,pendingQuery:y,searchSessionId:z}):l.jsx(c("PolarisNonProfiledSearchContentGated.react"),{fragmentKey:A==null?void 0:A.xdt_api__v1__fbsearch__non_profiled_serp,isLoading:v,pendingQuery:y,searchSessionId:z});A=a===c("PolarisSearchResultDisplayTypes").Popover;var O=a===c("PolarisSearchResultDisplayTypes").Page,P=a===c("PolarisSearchResultDisplayTypes").Panel;k=l.jsx(c("PolarisSearchBox.next.react"),{analyticsContext:f,hideResults:g,isAutoFocused:k,loading:v,onSearch:N,placeholder:u,setPendingQuery:w,useBottomMargin:!(c("PolarisSearchTabsGated.react")!=null&&F),useHistory:O,children:A?l.jsxs(c("PolarisSearchResultsPopover.next.react"),{children:[c("PolarisSearchTabsGated.react")!=null&&F&&l.jsx(c("PolarisSearchTabsGated.react"),{onSearch:N}),e]}):void 0});return l.jsx(c("PolarisListWithKeyCommands.react"),{className:"x78zum5 xdt5ytf x5yr21d",dependencies:[G,H,M.current],selector:'a[tabindex="0"]',children:l.jsxs(d("PolarisActiveSearchContext.react").PolarisActiveSearchContextProvider,{discoverToken:z,query:y,rankToken:(u=C==null?void 0:(v=C.xdt_api__v1__fbsearch__topsearch_connection)==null?void 0:v.rank_token)!=null?u:"",resultDisplayType:a,setTab:B,tab:M.current,children:[l.jsx(c("IGDSBox.react"),{marginEnd:P?s:0,marginStart:P?s:0,position:"relative",xstyle:O&&r.searchBar,children:k}),!A&&!g&&l.jsxs(l.Fragment,{children:[c("PolarisSearchTabsGated.react")!=null&&F?l.jsx(c("PolarisSearchTabsGated.react"),{onSearch:N}):O?null:l.jsx(c("IGDSDivider.react"),{}),e]})]})})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);