;/*FB_PKG_DELIM*/

__d("BanzaiConsts",[],(function(a,b,c,d,e,f){a={SEND:"Banzai:SEND",OK:"Banzai:OK",ERROR:"Banzai:ERROR",SHUTDOWN:"Banzai:SHUTDOWN",BASIC:"basic",VITAL:"vital",BASIC_WAIT:6e4,BASIC_WAIT_COMET:2e3,VITAL_WAIT:1e3,BATCH_SIZE_LIMIT:64e3,EXPIRY:864e5,BATCH_TIMEOUT:1e4,LAST_STORAGE_FLUSH:"banzai:last_storage_flush",STORAGE_FLUSH_INTERVAL:12*60*6e4,ENSURE_LAZY_QUEUE_FLUSH_TIMEOUT:3e4,POST_READY:0,POST_INFLIGHT:1,POST_SENT:2};b=a;f["default"]=b}),66);
__d("BanzaiUtils",["BanzaiConsts","FBLogger","cr:1172","cr:9985","cr:9986"],(function(a,b,c,d,e,f){"use strict";var g,h={canSend:function(a){return a[2]>=b("cr:9985")()-(g||(g=b("BanzaiConsts"))).EXPIRY},filterPost:function(a,c,d,e){if(e.overlimit)return!0;if(!e.sendMinimumOnePost&&a[4]+e.currentSize>(g||(g=b("BanzaiConsts"))).BATCH_SIZE_LIMIT)return!0;var f=a.__meta;if(f.status!=null&&f.status>=(g||(g=b("BanzaiConsts"))).POST_SENT||!h.canSend(a))return!1;if(f.status!=null&&f.status>=(g||(g=b("BanzaiConsts"))).POST_INFLIGHT)return!0;var i=f.compress!=null?f.compress:!0,j=(f.webSessionId!=null?f.webSessionId:"null")+(f.userID!=null?f.userID:"null")+(f.appID!=null?f.appID:"null")+(i?"compress":""),k=e.wadMap.get(j);k||(k={app_id:f.appID,needs_compression:i,posts:[],user:f.userID,webSessionId:f.webSessionId},e.wadMap.set(j,k),c.push(k));f.status=(g||(g=b("BanzaiConsts"))).POST_INFLIGHT;Array.isArray(k.posts)?k.posts.push(a):b("FBLogger")("banzai").mustfix("Posts were a string instead of array");d.push(a);e.currentSize+=a[4];e.currentSize>=(g||(g=b("BanzaiConsts"))).BATCH_SIZE_LIMIT&&(e.overlimit=!0);return e.keepRetryable&&Boolean(f.retry)},resetPostStatus:function(a){a.__meta.status=(g||(g=b("BanzaiConsts"))).POST_READY},retryPost:function(a,c,d){var e=a;e.__meta.status=(g||(g=b("BanzaiConsts"))).POST_READY;e[3]=(e[3]||0)+1;e.__meta.retry!==!0&&c>=400&&c<600&&d.push(a)},wrapData:function(a,c,d,e,f){d=[a,c,d,0,(a=f)!=null?a:c?JSON.stringify(c).length:0];d.__meta={appID:b("cr:9986").getAppID(),retry:e===!0,status:(g||(g=b("BanzaiConsts"))).POST_READY,userID:b("cr:9986").getPossiblyNonFacebookUserID(),webSessionId:b("cr:1172").getId()};return d}};e.exports=h}),null);
__d("NavigationMetrics",["cr:6016"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:6016")}),98);
__d("cancelIdleCallback",["cr:7384"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:7384")}),98);
__d("IdleCallbackImplementation",["performanceNow","requestAnimationFramePolyfill"],(function(a,b,c,d,e,f,g){var h,i=[],j=0,k=0,l=-1,m=!1,n=1e3/60,o=2;function p(a){return a}function q(a){return a}function b(b,c){var d=k++;i[d]=b;s();if(c!=null&&c.timeout>0){var e=p(d);a.setTimeout(function(){return y(e)},c.timeout)}return p(d)}function r(a){a=q(a);i[a]=null}function s(){m||(m=!0,c("requestAnimationFramePolyfill")(function(a){m=!1,u((h||(h=c("performanceNow")))()-a)}))}function t(a){var b=n-o;if(a<b)return b-a;a=a%n;if(a>b||a<o)return 0;else return b-a}function u(a){var b=(h||(h=c("performanceNow")))();if(b>l){a=t(a);if(a>0){b=b+a;x(b);l=b}}v()&&s()}function v(){return j<i.length}function w(){while(v()){var a=i[j];j++;if(a)return a}return null}function x(a){var b;while((h||(h=c("performanceNow")))()<a&&(b=w()))b(new z(a))}function y(a){var b=q(a);b=i[b];b&&(r(a),b(new z(null)))}var z=function(){function a(a){this.didTimeout=a==null,this.$1=a}var b=a.prototype;b.timeRemaining=function(){var a=this.$1;if(a!=null){var b=(h||(h=c("performanceNow")))();if(b<a)return a-b}return 0};return a}();g.requestIdleCallback=b;g.cancelIdleCallback=r}),98);
__d("requestIdleCallbackAcrossTransitions",["IdleCallbackImplementation","TimeSlice"],(function(a,b,c,d,e,f,g){var h=a.requestIdleCallback||d("IdleCallbackImplementation").requestIdleCallback;function b(b,d){b=c("TimeSlice").guard(b,"requestIdleCallback",{propagationType:c("TimeSlice").PropagationType.CONTINUATION,registerCallStack:!0});return h.call(a,b,d)}g["default"]=b}),98);
__d("SetIdleTimeoutAcrossTransitions",["NavigationMetrics","cancelIdleCallback","clearTimeout","nullthrows","requestIdleCallbackAcrossTransitions","setTimeoutAcrossTransitions"],(function(a,b,c,d,e,f,g){"use strict";var h=!1,i=new Map();function b(a,b){if(h){var d=c("setTimeoutAcrossTransitions")(function(){var b=c("requestIdleCallbackAcrossTransitions")(function(){a(),i["delete"](b)});i.set(d,b)},b);return d}else return c("setTimeoutAcrossTransitions")(a,b)}function d(a){c("clearTimeout")(a),i.has(a)&&(c("cancelIdleCallback")(c("nullthrows")(i.get(a))),i["delete"](a))}c("NavigationMetrics").addRetroactiveListener(c("NavigationMetrics").Events.EVENT_OCCURRED,function(b,c){c.event==="all_pagelets_loaded"&&(h=!!a.requestIdleCallback)});g.start=b;g.clear=d}),98);
__d("BanzaiStorage",["BanzaiConsts","BanzaiUtils","CurrentUser","SetIdleTimeoutAcrossTransitions","WebSession","WebStorage","WebStorageMutex","cr:8958","isInIframe","performanceAbsoluteNow"],(function(a,b,c,d,e,f){"use strict";var g,h,i,j="bz:",k=b("isInIframe")(),l,m=!1,n=null;function o(){var a="check_quota";try{var b=p();if(!b)return!1;b.setItem(a,a);b.removeItem(a);return!0}catch(a){return!1}}function p(){m||(m=!0,l=(g||(g=b("WebStorage"))).getLocalStorage());return l}a={flush:function(a){if(k)return;var c=p();if(c){n==null&&(n=parseInt(c.getItem((h||(h=b("BanzaiConsts"))).LAST_STORAGE_FLUSH),10));var d=n&&(i||(i=b("performanceAbsoluteNow")))()-n>=(h||(h=b("BanzaiConsts"))).STORAGE_FLUSH_INTERVAL;d&&a();(d||!n)&&(n=(i||(i=b("performanceAbsoluteNow")))(),(g||(g=b("WebStorage"))).setItemGuarded(c,(h||(h=b("BanzaiConsts"))).LAST_STORAGE_FLUSH,n.toString()))}},restore:function(a){if(k)return;var c=p();if(!c)return;var d=function(d){var e=[];for(var f=0;f<c.length;f++){var g=c.key(f);typeof g==="string"&&g.indexOf(j)===0&&g.indexOf("bz:__")!==0&&e.push(g)}e.forEach(function(d){var e=c.getItem(d);c.removeItem(d);if(e==null||e==="")return;d=b("cr:8958").parse(e);d.forEach(function(c){if(!c)return;var d=c.__meta=c.pop(),e=b("BanzaiUtils").canSend(c);if(!e)return;e=b("CurrentUser").getPossiblyNonFacebookUserID();(d.userID===e||e==="0")&&(b("BanzaiUtils").resetPostStatus(c),a(c))})});d&&d.unlock()};o()?new(b("WebStorageMutex"))("banzai").lock(d):b("SetIdleTimeoutAcrossTransitions").start(d,0)},store:function(a){if(k)return;var c=p(),d=a.filter(function(a){return a.__meta.status!==(h||(h=b("BanzaiConsts"))).POST_SENT});if(!c||d.length<=0)return;d=d.map(function(a){return[a[0],a[1],a[2],a[3]||0,a[4],a.__meta]});a.splice(0,a.length);(g||(g=b("WebStorage"))).setItemGuarded(c,j+b("WebSession").getId()+"."+(i||(i=b("performanceAbsoluteNow")))(),b("cr:8958").stringify(d))}};e.exports=a}),null);
__d("QueryString",[],(function(a,b,c,d,e,f){function g(a){var b=[];Object.keys(a).sort().forEach(function(c){var d=a[c];if(d===void 0)return;if(d===null){b.push(c);return}b.push(encodeURIComponent(c)+"="+encodeURIComponent(String(d)))});return b.join("&")}function a(a,b){b===void 0&&(b=!1);var c={};if(a==="")return c;a=a.split("&");for(var d=0;d<a.length;d++){var e=a[d].split("=",2),f=decodeURIComponent(e[0]);if(b&&Object.prototype.hasOwnProperty.call(c,f))throw new URIError("Duplicate key: "+f);c[f]=e.length===2?decodeURIComponent(e[1]):null}return c}function b(a,b){return a+(a.indexOf("?")!==-1?"&":"?")+(typeof b==="string"?b:g(b))}c={encode:g,decode:a,appendToUrl:b};f["default"]=c}),66);
__d("once",[],(function(a,b,c,d,e,f){"use strict";function a(a){var b=g(a);for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&(b[c]=a[c]);return b}function g(a){var b=a,c;a=function(){if(b){for(var a=arguments.length,d=new Array(a),e=0;e<a;e++)d[e]=arguments[e];c=b.apply(this,d);b=null}return c};return a}f["default"]=a}),66);
__d("BanzaiAdapterComet",["BanzaiConfig","BanzaiConsts","BanzaiStorage","BaseEventEmitter","ExecutionEnvironment","FBLogger","HasteBitMapName","JSScheduler","NetworkStatus","QueryString","Run","SiteData","StaticSiteData","URI","UserAgent","ZeroRewrites","getAsyncHeaders","getAsyncParams","gkx","justknobx","objectValues","once","unrecoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=[],m=new(c("BaseEventEmitter"))(),n="/ajax/bz",o="POST",p={cleanup:function(){var a=l;l=[];a.forEach(function(a){a.readyState<4&&a.abort()})},config:c("BanzaiConfig"),getEndPointUrl:function(a){var b=c("getAsyncParams")(o);c("objectValues")(c("HasteBitMapName")).forEach(function(a){return delete b[a]});delete b[c("StaticSiteData").jsmod_key];b.ph=c("SiteData").push_phase;a=n;c("justknobx")._("55")&&c("gkx")("23403")?a="/a/bz":c("gkx")("23404")?a="/ajax/bnzai":c("gkx")("4070")&&(a="/a/fl");a=c("QueryString").appendToUrl(a,b);if(a.length>2e3)throw c("unrecoverableViolation")("url is too long: ${url}","comet_infra");return a},getStorage:function(){return c("BanzaiStorage")},inform:function(a){Array.isArray(a)?a.forEach(function(a){return m.emit(a)}):m.emit(a)},isOkToSendViaBeacon:function(){return!1},onUnload:function(a){d("Run").onAfterUnload(a)},preferredCompressionMethod:c("once")(function(){return"deflate"}),readyToSend:function(){return c("UserAgent").isBrowser("IE <= 8")||navigator.onLine},send:function(a,b,d,e){var f,g=p.getEndPointUrl(!1);g=c("ZeroRewrites").rewriteURI(new(h||(h=c("URI")))(g));(i||(i=c("ExecutionEnvironment"))).isInWorker&&(g=g.getQualifiedURI());var m=c("ZeroRewrites").getTransportBuilderForURI(g)();m.open(o,g.toString(),!0);c("justknobx")._("2233")&&Object.entries(c("getAsyncHeaders")(g)).forEach(function(a){var b=a[0];a=a[1];m.setRequestHeader(b,a)});e===!0?m.onreadystatechange=function(){if(m.readyState>=4){var a=l.indexOf(m);a>=0&&l.splice(a,1);try{a=m.status}catch(b){a=0}a===200?(b&&b(),c("NetworkStatus").reportSuccess()):(d&&d(a),c("NetworkStatus").reportError())}}:m.onreadystatechange=function(){(j||(j=c("JSScheduler"))).scheduleNormalPriCallback(function(){if(m.readyState>=4){var a=l.indexOf(m);a>=0&&l.splice(a,1);try{a=m.status}catch(b){a=0}a===200?(b&&b(),c("NetworkStatus").reportSuccess(),p.inform((k||(k=c("BanzaiConsts"))).OK)):(d&&d(a),c("NetworkStatus").reportError(),p.inform((k||(k=c("BanzaiConsts"))).ERROR))}})};l.push(m);c("NetworkStatus").isOnline()?m.send(a):f=c("NetworkStatus").onChange(function(b){b=b.online;b&&(m.send(a),f.remove())})},setHooks:function(){},setUnloadHook:function(a){d("Run").onAfterUnload(a._unload)},subscribe:function(a,b){if(Array.isArray(a)){var c=[];a.forEach(function(a){return c.push(m.addListener(a,b))});return{remove:function(){c.forEach(function(a){return a.remove()})}}}else return m.addListener(a,b)},useBeacon:!1,wrapInTimeSlice:function(a,b){c("FBLogger")("banzai").mustfix("wrapInTimeSlice is not implemented");return function(){}}};a=p;g["default"]=a}),98);
/**
 * License: https://www.facebook.com/legal/license/WRsJ32R7YJG/
 */
__d("SnappyCompress",[],(function(a,b,c,d,e,f){"use strict";function g(){return typeof process==="object"&&(typeof process.versions==="object"&&typeof process.versions.node!=="undefined")?!0:!1}function h(a){return a instanceof Uint8Array&&(!g()||!Buffer.isBuffer(a))}function i(a){return a instanceof ArrayBuffer}function j(a){return!g()?!1:Buffer.isBuffer(a)}var k="Argument compressed must be type of ArrayBuffer, Buffer, or Uint8Array";function a(a){if(!h(a)&&!i(a)&&!j(a))throw new TypeError(k);var b=!1,c=!1;h(a)?b=!0:i(a)&&(c=!0,a=new Uint8Array(a));a=new A(a);var d=a.readUncompressedLength();if(d===-1)throw new Error("Invalid Snappy bitstream");if(b){b=new Uint8Array(d);if(!a.uncompressToBuffer(b))throw new Error("Invalid Snappy bitstream")}else if(c){b=new ArrayBuffer(d);c=new Uint8Array(b);if(!a.uncompressToBuffer(c))throw new Error("Invalid Snappy bitstream")}else{b=Buffer.alloc(d);if(!a.uncompressToBuffer(b))throw new Error("Invalid Snappy bitstream")}return b}function b(a){if(!h(a)&&!i(a)&&!j(a))throw new TypeError(k);var b=!1,c=!1;h(a)?b=!0:i(a)&&(c=!0,a=new Uint8Array(a));a=new x(a);var d=a.maxCompressedLength(),e,f,g;b?(e=new Uint8Array(d),g=a.compressToBuffer(e)):c?(e=new ArrayBuffer(d),f=new Uint8Array(e),g=a.compressToBuffer(f)):(e=Buffer.alloc(d),g=a.compressToBuffer(e));if(!e.slice){f=new Uint8Array(Array.prototype.slice.call(e,0,g));if(b)return f;else if(c)return f.buffer;else throw new Error("not implemented")}return e.slice(0,g)}c=16;var l=1<<c,m=14,n=new Array(m+1);function o(a,b){return a*506832829>>>b}function p(a,b){return a[b]+(a[b+1]<<8)+(a[b+2]<<16)+(a[b+3]<<24)}function q(a,b,c){return a[b]===a[c]&&a[b+1]===a[c+1]&&a[b+2]===a[c+2]&&a[b+3]===a[c+3]}function r(a,b,c,d,e){var f;for(f=0;f<e;f++)c[d+f]=a[b+f]}function s(a,b,c,d,e){c<=60?(d[e]=c-1<<2,e+=1):c<256?(d[e]=60<<2,d[e+1]=c-1,e+=2):(d[e]=61<<2,d[e+1]=c-1&255,d[e+2]=c-1>>>8,e+=3);r(a,b,d,e,c);return e+c}function t(a,b,c,d){if(d<12&&c<2048){a[b]=1+(d-4<<2)+(c>>>8<<5);a[b+1]=c&255;return b+2}else{a[b]=2+(d-1<<2);a[b+1]=c&255;a[b+2]=c>>>8;return b+3}}function u(a,b,c,d){while(d>=68)b=t(a,b,c,64),d-=64;d>64&&(b=t(a,b,c,60),d-=60);return t(a,b,c,d)}function v(a,b,c,d,e){var f=1;while(1<<f<=c&&f<=m)f+=1;f-=1;var g=32-f;typeof n[f]==="undefined"&&(n[f]=new Uint16Array(1<<f));f=n[f];var h;for(h=0;h<f.length;h++)f[h]=0;h=b+c;var i=b,j=b,k,l,r,t,v,w=!0,x=15;if(c>=x){c=h-x;b+=1;x=o(p(a,b),g);while(w){t=32;l=b;do{b=l;k=x;v=t>>>5;t+=1;l=b+v;if(b>c){w=!1;break}x=o(p(a,l),g);r=i+f[k];f[k]=b-i}while(!q(a,b,r));if(!w)break;e=s(a,j,b-j,d,e);do{v=b;k=4;while(b+k<h&&a[b+k]===a[r+k])k+=1;b+=k;l=v-r;e=u(d,e,l,k);j=b;if(b>=c){w=!1;break}t=o(p(a,b-1),g);f[t]=b-1-i;v=o(p(a,b),g);r=i+f[v];f[v]=b-i}while(q(a,b,r));if(!w)break;b+=1;x=o(p(a,b),g)}}j<h&&(e=s(a,j,h-j,d,e));return e}function w(a,b,c){do b[c]=a&127,a=a>>>7,a>0&&(b[c]+=128),c+=1;while(a>0);return c}function x(a){this.array=a}x.prototype.maxCompressedLength=function(){var a=this.array.length;return 32+a+Math.floor(a/6)};x.prototype.compressToBuffer=function(a){var b=this.array,c=b.length,d=0,e=0,f;e=w(c,a,e);while(d<c)f=Math.min(c-d,l),e=v(b,d,f,a,e),d+=f;return e};var y=[0,255,65535,16777215,4294967295];function r(a,b,c,d,e){var f;for(f=0;f<e;f++)c[d+f]=a[b+f]}function z(a,b,c,d){var e;for(e=0;e<d;e++)a[b+e]=a[b-c+e]}function A(a){this.array=a,this.pos=0}A.prototype.readUncompressedLength=function(){var a=0,b=0,c,d;while(b<32&&this.pos<this.array.length){c=this.array[this.pos];this.pos+=1;d=c&127;if(d<<b>>>b!==d)return-1;a|=d<<b;if(c<128)return a;b+=7}return-1};A.prototype.uncompressToBuffer=function(a){var b=this.array,c=b.length,d=this.pos,e=0,f,g,h,i;while(d<b.length){f=b[d];d+=1;if((f&3)===0){g=(f>>>2)+1;if(g>60){if(d+3>=c)return!1;h=g-60;g=b[d]+(b[d+1]<<8)+(b[d+2]<<16)+(b[d+3]<<24);g=(g&y[h])+1;d+=h}if(d+g>c)return!1;r(b,d,a,e,g);d+=g;e+=g}else{switch(f&3){case 1:g=(f>>>2&7)+4;i=b[d]+(f>>>5<<8);d+=1;break;case 2:if(d+1>=c)return!1;g=(f>>>2)+1;i=b[d]+(b[d+1]<<8);d+=2;break;case 3:if(d+3>=c)return!1;g=(f>>>2)+1;i=b[d]+(b[d+1]<<8)+(b[d+2]<<16)+(b[d+3]<<24);d+=4;break;default:break}if(i===0||i>e)return!1;z(a,e,i,g);e+=g}}return!0};e.exports.uncompress=a;e.exports.compress=b}),null);
__d("SnappyCompressUtil",["SnappyCompress","gkx"],(function(a,b,c,d,e,f,g){"use strict";var h=a.Uint8Array,i=a.btoa,j=a.TextEncoder;function k(a){if(a==null||i==null)return null;var b=null;try{b=c("SnappyCompress").compress(a)}catch(a){return null}a="";for(var d=0;d<b.length;d++)a+=String.fromCharCode(b[d]);return i(a)}function l(a){if(a==null||i==null)return null;var b=null;try{b=c("SnappyCompress").compress(a)}catch(a){return null}a=Array.from(b,function(a){return String.fromCharCode(a)}).join("");return i(a)}var m=k,n=!1;function b(a){n||(m=c("gkx")("4737")?l:k,n=!0);return m(a)}var o={compressUint8ArrayToSnappy:b,compressStringToSnappy:function(b){if(h==null||i==null)return null;var c=new a.Uint8Array(b.length);for(var d=0;d<b.length;d++){var e=b.charCodeAt(d);if(e>127)return null;c[d]=e}return o.compressUint8ArrayToSnappy(c)},compressStringToSnappyBinary:function(a){if(h==null)return null;var b=null;if(j!=null)b=new j().encode(a);else{b=new h(a.length);for(var d=0;d<a.length;d++){var e=a.charCodeAt(d);if(e>127)return null;b[d]=e}}e=null;try{e=c("SnappyCompress").compress(b)}catch(a){return null}return e}};f.exports=o}),34);
__d("BanzaiCompressionUtils",["FBLogger","Promise","SnappyCompressUtil","once","performanceNow"],(function(a,b,c,d,e,f){"use strict";var g,h,i=b("once")(function(){if(a.CompressionStream==null)return!1;if(a.Response==null)return!1;try{new a.CompressionStream("deflate")}catch(a){return!1}return!0}),j={compressWad:function(a,c){if(a.needs_compression!==!0){delete a.needs_compression;return}if(c==="deflate"){j.compressWad(a,"snappy");return}var d=(g||(g=b("performanceNow")))(),e=JSON.stringify(a.posts),f;switch(c){case"snappy":f=b("SnappyCompressUtil").compressStringToSnappyBinary(e);break;case"snappy_base64":f=b("SnappyCompressUtil").compressStringToSnappy(e);break;default:break}f!=null&&f.length<e.length?(a.posts=f,a.compression=c,a.snappy_ms=Math.ceil((g||(g=b("performanceNow")))()-d),a.snappy_ms<0&&b("FBLogger")("BanzaiCompressionUtils").warn("Expected positive snappy_ms but got %s",a.snappy_ms)):a.compression="";delete a.needs_compression},compressWadAsync:function(c,d){if(d!=="deflate"){j.compressWad(c,"snappy");return(h||(h=b("Promise"))).resolve()}if(!i())return j.compressWadAsync(c,"snappy");var e=(g||(g=b("performanceNow")))(),f=JSON.stringify(c.posts),k=new Response(f).body;if(!k){c.compression="";delete c.needs_compression;return(h||(h=b("Promise"))).resolve()}k=k.pipeThrough(new a.CompressionStream("deflate"));return new Response(k).arrayBuffer().then(function(a){a.byteLength<f.length?(c.posts=new Uint8Array(a),c.compression=d,c.snappy_ms=Math.ceil((g||(g=b("performanceNow")))()-e),c.snappy_ms<0&&b("FBLogger")("BanzaiCompressionUtils").warn("Expected positive snappy_ms but got %s",c.snappy_ms)):c.compression="",delete c.needs_compression})["catch"](function(){c.compression="",delete c.needs_compression})},outOfBandsPosts:function(a){var b=0,c={};for(a of a){var d=a.compression==="snappy"||a.compression==="deflate";if(d){d=new Blob([a.posts],{type:"application/octet-stream"});a.posts=String(b);c["post_"+String(b)]=d;b++}}return c}};e.exports=j}),null);
__d("setTimeoutCometLoggingPriWithFallback",["cr:1268629"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=b("cr:1268629")}),98);
__d("setTimeoutCometSpeculativeWithFallback",["cr:1268630"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=b("cr:1268630")}),98);
__d("BanzaiComet",["BanzaiAdapterComet","BanzaiCompressionUtils","BanzaiConsts","BanzaiLazyQueue","BanzaiUtils","CurrentAppID","CurrentUser","ErrorGuard","ExecutionEnvironment","FBLogger","Promise","Run","Visibility","WebSession","clearTimeout","performanceAbsoluteNow","recoverableViolation","setInterval","setTimeout","setTimeoutCometLoggingPriWithFallback","setTimeoutCometSpeculativeWithFallback"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l,m={basic:[],vital:[]},n=[],o={basic:null,vital:null},p={basic:null,vital:null},q=new Map(),r,s=null,t={_expiredBatchMap:function(){var a=(i||(i=c("performanceAbsoluteNow")))();for(var b of q.entries()){var d=b[1];if(d.expiryTime<=a){var e=d.posts[0];e=(e=e.__meta.priority)!=null?e:(j||(j=c("BanzaiConsts"))).BASIC;(e=t._getPostBuffer(e)).push.apply(e,d.posts);q["delete"](b[0])}}q.size>0&&(r=c("setTimeout")(t._expiredBatchMap,(j||(j=c("BanzaiConsts"))).BATCH_TIMEOUT))},_flushBatchMap:function(){c("clearTimeout")(r);r=null;for(var a of q.values()){var b=a.posts[0];b=(b=b.__meta.priority)!=null?b:(j||(j=c("BanzaiConsts"))).BASIC;(b=t._getPostBuffer(b)).push.apply(b,a.posts)}q.clear()},_flushLazyQueue:function(){c("BanzaiLazyQueue").flushQueue().forEach(function(a){return t.post.apply(t,a)})},_gatherWadsAndPostsFromBuffer:function(a,b,d,e,f,g){var h={currentSize:0,keepRetryable:d,overlimit:!1,sendMinimumOnePost:g,wadMap:new Map()};d=f[e].filter(function(d){return c("BanzaiUtils").filterPost(d,a,b,h)});!h.overlimit&&e==="vital"&&(f.basic=f.basic.filter(function(d){return c("BanzaiUtils").filterPost(d,a,b,h)}));return d},_getPostBuffer:function(a){return a==null?m.basic:m[a]||[]},_handleBatchPost:function(a,b,d){if(d==null)return!1;var e=a[2],f=a[0],g=q.get(f);if(g!=null&&g.expiryTime<=e){(b=t._getPostBuffer(b)).push.apply(b,g.posts);q["delete"](f);return!1}if(g!=null&&g.expiryTime>e){g.posts.push(a);return!0}b={expiryTime:e+d,posts:[a]};q.set(f,b);r||(r=c("setTimeout")(t._expiredBatchMap,(j||(j=c("BanzaiConsts"))).BATCH_TIMEOUT));return!0},_handlePostPreflightChecks:function(a,b,d){if(t.adapter.config.disabled===!0)return!0;if(!(k||(k=c("ExecutionEnvironment"))).canUseDOM&&!(k||(k=c("ExecutionEnvironment"))).isInWorker)return!0;if(c("BanzaiAdapterComet").config.disabled===!0)return!0;b=c("BanzaiAdapterComet").config.blacklist;return b!=null&&typeof b.indexOf==="function"&&b.indexOf(a)!==-1?!0:!1},_handleSignalPost:function(a,b,e){if(!e)return!1;var f=a;f.__meta.status=(j||(j=c("BanzaiConsts"))).POST_INFLIGHT;e=[{app_id:d("CurrentAppID").getAppID(),posts:[a],trigger:a[0],user:c("CurrentUser").getPossiblyNonFacebookUserID(),webSessionId:d("WebSession").getId()}];c("BanzaiAdapterComet").send(t._prepForTransit(e),function(){f.__meta.status=(j||(j=c("BanzaiConsts"))).POST_SENT,f.__meta.callback!=null&&f.__meta.callback()},function(d){c("BanzaiUtils").retryPost(a,d,m[b])},!0);return!f.__meta.retry},_initialize:function(){var a=[(j||(j=c("BanzaiConsts"))).VITAL,j.BASIC];if((k||(k=c("ExecutionEnvironment"))).canUseDOM){t.isEnabled("comet_flush_lazy_queue")&&c("setInterval")(function(){t._flushLazyQueue()},(j||(j=c("BanzaiConsts"))).ENSURE_LAZY_QUEUE_FLUSH_TIMEOUT);if(c("Visibility").isSupported()){var b;(b=c("Visibility")).addListener(b.HIDDEN,function(){t._flushLazyQueue(),a.forEach(function(a){t._getPostBuffer(a).length>0&&t._tryToSendViaBeacon(a)}),t._store()});b.addListener(b.VISIBLE,function(){t._flushLazyQueue(),a.forEach(function(a){t._tryToSendViaBeacon(a)}),t._restore()})}else t.adapter.setHooks(t);d("Run").onBeforeUnload(function(){t._flushLazyQueue(),t._flushBatchMap(),t._sendBeacon((j||(j=c("BanzaiConsts"))).VITAL),t._sendBeacon(j.BASIC)},!1);t.adapter.setUnloadHook(t);d("Run").onAfterLoad(function(){t._restore()})}else(k||(k=c("ExecutionEnvironment"))).isInWorker&&self.addEventListener("force-flush-logs",function(){t.flush(),t._flushLazyQueue(),t._flushBatchMap()})},_isShutdown:!1,_prepForTransit:function(a){var b=new FormData();b.append("ts",String(Date.now()));var d=c("BanzaiCompressionUtils").outOfBandsPosts(a);Object.keys(d).forEach(function(a){b.append(a,d[a])});b.append("q",JSON.stringify(a));return b},_prepWadForTransit:function(a){c("BanzaiCompressionUtils").compressWad(a,c("BanzaiAdapterComet").preferredCompressionMethod())},_prepWadForTransitAsync:function(a){return c("BanzaiCompressionUtils").compressWadAsync(a,c("BanzaiAdapterComet").preferredCompressionMethod())},_restore:function(){var a=function(a){var b=a.__meta;b=b.priority===(j||(j=c("BanzaiConsts"))).VITAL?(j||(j=c("BanzaiConsts"))).VITAL:(j||(j=c("BanzaiConsts"))).BASIC;t._getPostBuffer(b).push(a)},b=c("BanzaiAdapterComet").getStorage();(l||(l=c("ErrorGuard"))).applyWithGuard(b.restore,b,[a]);t._schedule((j||(j=c("BanzaiConsts"))).VITAL_WAIT,j.VITAL)},_schedule:function(a,b){if(b==null)return!1;var d=function(){p[b]=null,o[b]=null,t._sendWithCallbacks(b,null,null)},e=(i||(i=c("performanceAbsoluteNow")))()+a;if(o[b]==null||e<o[b]){o[b]=e;p[b]!==null&&c("clearTimeout")(p[b]);b===(j||(j=c("BanzaiConsts"))).VITAL?p.vital=c("setTimeoutCometLoggingPriWithFallback")(d,a):p.basic=c("setTimeoutCometSpeculativeWithFallback")(d,a);return!0}return!1},_sendBeacon:function(a){t._getPostBuffer(a).length>0&&t._tryToSendViaBeacon(a)},_sendWithCallbacks:function(a,d,e){m[a].length>0&&t._schedule(a==="vital"?(j||(j=c("BanzaiConsts"))).VITAL_WAIT:(j||(j=c("BanzaiConsts"))).BASIC_WAIT_COMET,a);if(!c("BanzaiAdapterComet").readyToSend()){e&&e();return}var f=c("BanzaiAdapterComet").getStorage();(l||(l=c("ErrorGuard"))).applyWithGuard(f.flush,f,[t._restore]);c("BanzaiAdapterComet").inform((j||(j=c("BanzaiConsts"))).SEND);var g=[],i=[];m[a]=t._gatherWadsAndPostsFromBuffer(g,i,!0,a,m,!0);if(g.length<=0){c("BanzaiAdapterComet").inform((j||(j=c("BanzaiConsts"))).OK);d&&d();return}g[0].trigger=s;s=null;g.forEach(function(a){return a.send_method="ajax"});n.push.apply(n,i);(h||(h=b("Promise"))).all(g.map(t._prepWadForTransitAsync))["finally"](function(){if(t._isShutdown)return;i.forEach(function(a){a=n.indexOf(a);if(a===-1){c("recoverableViolation")("inflight post not found in inPreparationPosts","comet_infra");return}n.splice(a,1)});c("BanzaiAdapterComet").send(t._prepForTransit(g),function(){i.forEach(function(a){a=a;a.__meta.status=(j||(j=c("BanzaiConsts"))).POST_SENT;typeof a.__meta.callback==="function"&&a.__meta.callback()}),d&&d()},function(b){i.forEach(function(d){c("BanzaiUtils").retryPost(d,b,m[a])}),t._store(),e&&e()})})},_store:function(){var a=c("BanzaiAdapterComet").getStorage();(l||(l=c("ErrorGuard"))).applyWithGuard(a.store,a,[m[(j||(j=c("BanzaiConsts"))).VITAL]]);l.applyWithGuard(a.store,a,[m[j.BASIC]])},_testState:function(){return{postBuffer:m.basic,triggerRoute:s}},_tryToSendViaBeacon:function(b){if(!(navigator&&navigator.sendBeacon))return!1;var d=!0,e=[],f=[];m[b]=t._gatherWadsAndPostsFromBuffer(e,f,!1,b,m,!1);if(e.length<=0)return!1;e.forEach(function(a){return a.send_method="beacon"});e.map(t._prepWadForTransit);e=t._prepForTransit(e);var g=t.adapter.getEndPointUrl(!0);g=a.navigator.sendBeacon(g,e);g||(d=!1,f.forEach(function(a){c("BanzaiUtils").resetPostStatus(a),t._getPostBuffer(b).push(a)}));return d},_unload:function(){t._flushLazyQueue(),t._flushBatchMap(),c("BanzaiAdapterComet").cleanup(),c("BanzaiAdapterComet").inform((j||(j=c("BanzaiConsts"))).SHUTDOWN),t._isShutdown=!0,n.forEach(function(a){var b=a;b=b.__meta.priority;c("BanzaiUtils").retryPost(a,444,t._getPostBuffer((a=b)!=null?a:(j||(j=c("BanzaiConsts"))).VITAL))}),t._sendBeacon(j.VITAL),t._sendBeacon(j.BASIC),t._store()},_validateRouteAndSize:function(a,b){a||c("FBLogger")("banzai").blameToPreviousFrame().blameToPreviousFrame().mustfix("BanzaiComet.post called without specifying a route");return((a=JSON.stringify(b))!=null?a:"").length},BASIC:{delay:(j||(j=c("BanzaiConsts"))).BASIC_WAIT},BASIC_WAIT:j.BASIC_WAIT,ERROR:j.ERROR,EXPIRY:void 0,OK:j.OK,SEND:j.SEND,SHUTDOWN:j.SHUTDOWN,VITAL:{delay:j.VITAL_WAIT},VITAL_WAIT:j.VITAL_WAIT,adapter:c("BanzaiAdapterComet"),canUseNavigatorBeacon:function(){return!!(navigator&&navigator.sendBeacon&&c("BanzaiAdapterComet").isOkToSendViaBeacon())},flush:function(a,b){t.flushHelper((j||(j=c("BanzaiConsts"))).VITAL,a,b),t.flushHelper(j.BASIC,a,b)},flushHelper:function(a,b,d){o[a]=null,p[a]!==null&&(c("clearTimeout")(p[a]),p[a]=null),t._sendWithCallbacks(a,b,d)},isEnabled:function(a){return!!(c("BanzaiAdapterComet").config.gks&&c("BanzaiAdapterComet").config.gks[a])},post:function(a,b,d){var e;t._flushLazyQueue();if(t._handlePostPreflightChecks(a,b,d))return;var f=a.split(":");if((c("BanzaiAdapterComet").config.known_routes||[]).indexOf(f[0])===-1){c("BanzaiAdapterComet").config.should_log_unknown_routes===!0&&c("FBLogger")("banzai").blameToPreviousFrame().mustfix("Attempted to post to invalid Banzai route '"+a+"'. This call site should be cleaned up.");if(c("BanzaiAdapterComet").config.should_drop_unknown_routes===!0)return}f=t._validateRouteAndSize(a,b);d=d||{};b=c("BanzaiUtils").wrapData(a,b,(i||(i=c("performanceAbsoluteNow")))(),d.retry,f);f=b;d.callback&&(f.__meta.callback=d.callback);d.compress!=null&&(f.__meta.compress=d.compress);e=(e=d.delay)!=null?e:(j||(j=c("BanzaiConsts"))).BASIC_WAIT_COMET;var g=e>(j||(j=c("BanzaiConsts"))).VITAL_WAIT?(j||(j=c("BanzaiConsts"))).BASIC:(j||(j=c("BanzaiConsts"))).VITAL;f.__meta.priority=g;if(t._handleSignalPost(b,g,(f=d.signal)!=null?f:!1))return;if(t._handleBatchPost(b,g,d.batch))return;t._getPostBuffer(g).push(b);(t._schedule(e,g)||s==null)&&(s=a)},postsCount:new Map(),subscribe:c("BanzaiAdapterComet").subscribe};t._initialize();e=t;g["default"]=e}),98);
__d("BanzaiWWW",["cr:1642797"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:1642797")}),98);
__d("BigPipeInstance",[],(function(a,b,c,d,e,f){"use strict";var g=null;a={Events:{init:"BigPipe/init",tti:"tti_bigpipe",displayed:"all_pagelets_displayed",loaded:"all_pagelets_loaded"},setCurrentInstance_DO_NOT_USE:function(a){g=a},getCurrentInstance:function(){return g}};e.exports=a}),null);
__d("EventProfilerSham",[],(function(a,b,c,d,e,f){a={__wrapEventListenHandler:function(a){return a},tagCurrentActiveInteractionsAs:function(a){},setCurrentAdAccountId:function(a){},setAdsConfig:function(a){}};b=a;f["default"]=b}),66);
__d("InlineFbtResult",["cr:1183579"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:1183579")}),98);
__d("InlineFbtResultImplComet",["FbtHooks","FbtReactUtil","FbtResultBase","react","recoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function k(a){var b=a.content,d=a.hash,e=a.inlineMode;a=a.translation;d==null&&c("recoverableViolation")('Fbt string hash should not be null for translated string "'+a+'" '+("[inlineMode="+e+"]"),"internationalization");return j.jsx("span",{"data-intl-hash":d,"data-intl-translation":a,"data-intl-trid":"",children:b})}k.displayName=k.name+" [from "+f.id+"]";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,e,f,g){var i;i=a.call(this,b,(h||(h=c("FbtHooks"))).getErrorListener({hash:g,translation:f}))||this;i.$$typeof=d("FbtReactUtil").REACT_ELEMENT_TYPE;i.key=null;i.ref=null;i.type=k;i.props={content:b,hash:g,inlineMode:e,translation:f};return i}return b}(c("FbtResultBase"));g["default"]=a}),98);
__d("IntlCLDRNumberType01",["IntlVariations"],(function(a,b,c,d,e,f,g){"use strict";a={getVariation:function(a){return c("IntlVariations").NUMBER_OTHER}};b=a;g["default"]=b}),98);
__d("IntlCLDRNumberType02",["IntlVariations"],(function(a,b,c,d,e,f,g){"use strict";a={getVariation:function(a){if(a===0||a===1)return c("IntlVariations").NUMBER_ONE;else return c("IntlVariations").NUMBER_OTHER}};b=a;g["default"]=b}),98);
__d("IntlCLDRNumberType05",["IntlVariations"],(function(a,b,c,d,e,f,g){"use strict";a={getVariation:function(a){if(a===1)return c("IntlVariations").NUMBER_ONE;else return c("IntlVariations").NUMBER_OTHER}};b=a;g["default"]=b}),98);
__d("NavigationMetricsCore",["mixInEventEmitter","pageID"],(function(a,b,c,d,e,f,g){var h={NAVIGATION_DONE:"NAVIGATION_DONE",EVENT_OCCURRED:"EVENT_OCCURRED"},i={tti:"tti",e2e:"e2e",all_pagelets_loaded:"all_pagelets_loaded",all_pagelets_displayed:"all_pagelets_displayed"},j=0,k={},l=function(){function a(){this.eventTimings={tti:null,e2e:null,all_pagelets_loaded:null,all_pagelets_displayed:null},this.lid=c("pageID")+":"+j++,this.extras={}}var b=a.prototype;b.getLID=function(){return this.lid};b.setRequestStart=function(a){this.start=a;return this};b.setTTI=function(a){this.eventTimings.tti=a;this.$1(i.tti,a);return this};b.setE2E=function(a){this.eventTimings.e2e=a;this.$1(i.e2e,a);return this};b.setExtra=function(a,b){this.extras[a]=b;return this};b.setDisplayDone=function(a){this.eventTimings.all_pagelets_displayed=a;this.setExtra("all_pagelets_displayed",a);this.$1(i.all_pagelets_displayed,a);return this};b.setAllPageletsLoaded=function(a){this.eventTimings.all_pagelets_loaded=a;this.setExtra("all_pagelets_loaded",a);this.$1(i.all_pagelets_loaded,a);return this};b.setServerLID=function(a){this.serverLID=a;return this};b.$1=function(a,b){var c={};k!=null&&this.serverLID!=null&&k[this.serverLID]!=null&&(c=k[this.serverLID]);c=babelHelpers["extends"]({},c,{event:a,timestamp:b});m.emitAndHold(h.EVENT_OCCURRED,this.serverLID,c);return this};b.doneNavigation=function(){var a=babelHelpers["extends"]({start:this.start,extras:this.extras},this.eventTimings);if(this.serverLID&&k[this.serverLID]){var b=this.serverLID;Object.assign(a,k[b]);delete k[b]}m.emitAndHold(h.NAVIGATION_DONE,this.lid,a)};return a}(),m={Events:h,postPagelet:function(a,b,c){},siteInit:function(a){a(l)},setPage:function(a){if(!a.serverLID)return;k[a.serverLID]={page:a.page,pageType:a.page_type,pageURI:a.page_uri,serverLID:a.serverLID}},getFullPageLoadLid:function(){throw new Error("getFullPageLoadLid is not implemented on this site")}};c("mixInEventEmitter")(m,h);a=m;g["default"]=a}),98);
__d("PageEvents",[],(function(a,b,c,d,e,f){a=Object.freeze({NATIVE_ONLOAD:"onload/onload",BIGPIPE_ONLOAD:"onload/onload_callback",AJAXPIPE_ONLOAD:"ajaxpipe/onload_callback",NATIVE_DOMREADY:"onload/dom_content_ready",BIGPIPE_DOMREADY:"onload/domcontent_callback",AJAXPIPE_DOMREADY:"ajaxpipe/domcontent_callback",NATIVE_ONBEFOREUNLOAD:"onload/beforeunload",NATIVE_ONUNLOAD:"onload/unload",AJAXPIPE_ONUNLOAD:"onload/exit",AJAXPIPE_SEND:"ajaxpipe/send",AJAXPIPE_FIRST_RESPONSE:"ajaxpipe/first_response",AJAXPIPE_ONBEFORECLEARCANVAS:"ajaxpipe/onbeforeclearcanvas"});f["default"]=a}),66);
__d("NavigationMetricsWWW",["Arbiter","BigPipeInstance","NavigationMetricsCore","PageEvents","performance"],(function(a,b,c,d,e,f,g){var h,i="0";c("NavigationMetricsCore").getFullPageLoadLid=function(){return i};c("NavigationMetricsCore").siteInit(function(a){var b=new a(),e=!0;c("Arbiter").subscribe(d("BigPipeInstance").Events.init,function(f,g){var h=e?b:new a();e&&(i=g.lid);e=!1;h.setServerLID(g.lid);f=g.arbiter;f.subscribe(d("BigPipeInstance").Events.tti,function(a,b){a=b.ts;h.setTTI(a)});f.subscribe(c("PageEvents").AJAXPIPE_SEND,function(a,b){a=b.ts;h.setRequestStart(a)});f.subscribe(c("PageEvents").AJAXPIPE_ONLOAD,function(a,b){a=b.ts;h.setE2E(a).doneNavigation()});f.subscribe(d("BigPipeInstance").Events.displayed,function(a,b){a=b.ts;h.setDisplayDone(a)});f.subscribe(d("BigPipeInstance").Events.loaded,function(a,b){a=b.ts;h.setAllPageletsLoaded(a)})});c("Arbiter").subscribe(c("PageEvents").BIGPIPE_ONLOAD,function(a,d){a=d.ts;e=!1;b.setRequestStart((h||(h=c("performance"))).timing&&(h||(h=c("performance"))).timing.navigationStart).setE2E(a).doneNavigation()})});g["default"]=c("NavigationMetricsCore")}),98);
__d("ReactFeatureFlags",["gkx","qex"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;a=c("gkx")("11557");b=c("gkx")("11685");d=!0;e=c("gkx")("10839");f=c("gkx")("21072")||((f=c("qex")._("104"))!=null?f:!1);h=(h=c("qex")._("128"))!=null?h:250;i=(i=c("qex")._("344"))!=null?i:5e3;j=(j=c("qex")._("388"))!=null?j:5e3;var k=!1,l=!1,m=c("gkx")("21063"),n=c("gkx")("9861"),o=!0,p=!0,q=c("gkx")("10850"),r=!1,s=!1,t=c("gkx")("11370"),u=!0,v=c("gkx")("33056"),w=!1;c=!q&&c("gkx")("21069")||c("gkx")("10211");g.alwaysThrottleRetries=a;g.enableNoCloningMemoCache=b;g.enableObjectFiber=d;g.enableHiddenSubtreeInsertionEffectCleanup=e;g.enableRetryLaneExpiration=f;g.syncLaneExpirationMs=h;g.transitionLaneExpirationMs=i;g.retryLaneExpirationMs=j;g.enableScrollEndPolyfill=k;g.enableInfiniteRenderLoopDetection=l;g.enableTrustedTypesIntegration=m;g.enableRenderableContext=n;g.enableFragmentRefs=o;g.enableViewTransition=p;g.enableComponentPerformanceTrack=q;g.enableTransitionTracing=r;g.renameElementSymbol=s;g.disableDefaultPropsExceptForClasses=t;g.enableDO_NOT_USE_disableStrictPassiveEffect=u;g.favorSafetyOverHydrationPerf=v;g.disableSchedulerTimeoutInWorkLoop=w;g.enableSchedulingProfiler=c}),98);
__d("React-prod.classic",["ReactFeatureFlags"],(function(a,b,c,d,e,f){"use strict";var g=(b=b("ReactFeatureFlags")).disableDefaultPropsExceptForClasses,h=b.enableRenderableContext,i=b.enableTransitionTracing,j=b.renameElementSymbol,k=b.enableViewTransition;b=Symbol["for"]("react.element");var l=j?Symbol["for"]("react.transitional.element"):b,m=Symbol["for"]("react.portal");j=Symbol["for"]("react.fragment");b=Symbol["for"]("react.strict_mode");var n=Symbol["for"]("react.profiler"),o=Symbol["for"]("react.provider"),p=Symbol["for"]("react.consumer"),q=Symbol["for"]("react.context"),r=Symbol["for"]("react.forward_ref"),s=Symbol["for"]("react.suspense"),t=Symbol["for"]("react.suspense_list"),u=Symbol["for"]("react.memo"),v=Symbol["for"]("react.lazy"),w=Symbol["for"]("react.scope"),x=Symbol["for"]("react.activity"),y=Symbol["for"]("react.legacy_hidden"),z=Symbol["for"]("react.tracing_marker"),A=Symbol["for"]("react.view_transition"),B=typeof Symbol==="function"?Symbol.iterator:"@@iterator";function C(a){if(null===a||"object"!==typeof a)return null;a=B&&a[B]||a["@@iterator"];return"function"===typeof a?a:null}var D={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},E=Object.assign,F={};function a(a,b,c){this.props=a,this.context=b,this.refs=F,this.updater=c||D}a.prototype.isReactComponent={};a.prototype.setState=function(a,b){if("object"!==typeof a&&"function"!==typeof a&&null!=a)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,a,b,"setState")};a.prototype.forceUpdate=function(a){this.updater.enqueueForceUpdate(this,a,"forceUpdate")};function c(){}c.prototype=a.prototype;function d(a,b,c){this.props=a,this.context=b,this.refs=F,this.updater=c||D}c=d.prototype=new c();c.constructor=d;E(c,a.prototype);c.isPureReactComponent=!0;var G=Array.isArray;function H(){}var I={H:null,A:null,T:null,S:null},J=Object.prototype.hasOwnProperty;function K(a,b,c,d,e,f){c=f.ref;return{$$typeof:l,type:a,key:b,ref:void 0!==c?c:null,props:f}}function e(a,b,c){var d=null;void 0!==c&&(d=""+c);void 0!==b.key&&(d=""+b.key);if("key"in b){c={};for(var e in b)"key"!==e&&(c[e]=b[e])}else c=b;if(!g&&a&&a.defaultProps){b=a.defaultProps;for(e in b)void 0===c[e]&&(c[e]=b[e])}return K(a,d,void 0,void 0,null,c)}function L(a,b){return K(a.type,b,void 0,void 0,void 0,a.props)}function M(a){return"object"===typeof a&&null!==a&&a.$$typeof===l}function N(a){var b={"=":"=0",":":"=2"};return"$"+a.replace(/[=:]/g,function(a){return b[a]})}var O=/\/+/g;function P(a,b){return"object"===typeof a&&null!==a&&null!=a.key?N(""+a.key):b.toString(36)}function Q(a){switch(a.status){case"fulfilled":return a.value;case"rejected":throw a.reason;default:switch("string"===typeof a.status?a.then(H,H):(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)})),a.status){case"fulfilled":return a.value;case"rejected":throw a.reason}}throw a}function R(a,b,c,d,e){var f=typeof a;("undefined"===f||"boolean"===f)&&(a=null);var g=!1;if(null===a)g=!0;else switch(f){case"bigint":case"string":case"number":g=!0;break;case"object":switch(a.$$typeof){case l:case m:g=!0;break;case v:return g=a._init,R(g(a._payload),b,c,d,e)}}if(g)return e=e(a),g=""===d?"."+P(a,0):d,G(e)?(c="",null!=g&&(c=g.replace(O,"$&/")+"/"),R(e,b,c,"",function(a){return a})):null!=e&&(M(e)&&(e=L(e,c+(null==e.key||a&&a.key===e.key?"":(""+e.key).replace(O,"$&/")+"/")+g)),b.push(e)),1;g=0;var h=""===d?".":d+":";if(G(a))for(var i=0;i<a.length;i++)d=a[i],f=h+P(d,i),g+=R(d,b,c,f,e);else if(i=C(a),"function"===typeof i)for(a=i.call(a),i=0;!(d=a.next()).done;)d=d.value,f=h+P(d,i++),g+=R(d,b,c,f,e);else if("object"===f){if("function"===typeof a.then)return R(Q(a),b,c,d,e);b=String(a);throw Error("Objects are not valid as a React child (found: "+("[object Object]"===b?"object with keys {"+Object.keys(a).join(", ")+"}":b)+"). If you meant to render a collection of children, use an array instead.")}return g}function S(a,b,c){if(null==a)return a;var d=[],e=0;R(a,d,"","",function(a){return b.call(c,a,e++)});return d}function T(a){if(-1===a._status){var b=a._result;b=b();b.then(function(b){(0===a._status||-1===a._status)&&(a._status=1,a._result=b)},function(b){(0===a._status||-1===a._status)&&(a._status=2,a._result=b)});-1===a._status&&(a._status=0,a._result=b)}if(1===a._status)return a._result["default"];throw a._result}function U(a){return I.H.useMemoCache(a)}var V="function"===typeof reportError?reportError:function(a){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var b=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof a&&null!==a&&"string"===typeof a.message?String(a.message):String(a),error:a});if(!window.dispatchEvent(b))return}else if("object"===typeof process&&"function"===typeof process.emit){process.emit("uncaughtException",a);return}};function W(a,b){var c=I.T,d={};k&&(d.types=null!==c?c.types:null);i&&(d.name=void 0!==b&&void 0!==b.name?b.name:null,d.startTime=-1);I.T=d;try{b=a();a=I.S;null!==a&&a(d,b);"object"===typeof b&&null!==b&&"function"===typeof b.then&&b.then(H,V)}catch(a){V(a)}finally{null!==c&&null!==d.types&&(c.types=d.types),I.T=c}}function X(a){if(k){var b=I.T;if(null!==b){var c=b.types;null===c?b.types=[a]:-1===c.indexOf(a)&&c.push(a)}else W(X.bind(null,a))}}c={__proto__:null,c:U};f.Children={map:S,forEach:function(a,b,c){S(a,function(){b.apply(this,arguments)},c)},count:function(a){var b=0;S(a,function(){b++});return b},toArray:function(a){return S(a,function(a){return a})||[]},only:function(a){if(!M(a))throw Error("React.Children.only expected to receive a single React element child.");return a}};f.Component=a;f.Fragment=j;f.Profiler=n;f.PureComponent=d;f.StrictMode=b;f.Suspense=s;f.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=I;f.__COMPILER_RUNTIME=c;f.act=function(){throw Error("act(...) is not supported in production builds of React.")};f.c=U;f.cache=function(a){return function(){return a.apply(null,arguments)}};f.captureOwnerStack=void 0;f.cloneElement=function(a,b,c){if(null===a||void 0===a)throw Error("The argument must be a React element, but you passed "+a+".");var d=E({},a.props),e=a.key,f=void 0;if(null!=b){void 0!==b.ref&&(f=void 0);void 0!==b.key&&(e=""+b.key);if(!g&&a.type&&a.type.defaultProps)var h=a.type.defaultProps;for(i in b)!J.call(b,i)||"key"===i||"__self"===i||"__source"===i||"ref"===i&&void 0===b.ref||(d[i]=g||void 0!==b[i]||void 0===h?b[i]:h[i])}var i=arguments.length-2;if(1===i)d.children=c;else if(1<i){h=Array(i);for(var j=0;j<i;j++)h[j]=arguments[j+2];d.children=h}return K(a.type,e,void 0,void 0,f,d)};f.createContext=function(a){a={$$typeof:q,_currentValue:a,_currentValue2:a,_threadCount:0,Provider:null,Consumer:null};h?(a.Provider=a,a.Consumer={$$typeof:p,_context:a}):(a.Provider={$$typeof:o,_context:a},a.Consumer=a);return a};f.createElement=function(a,b,c){var d,e={},f=null;if(null!=b)for(d in void 0!==b.key&&(f=""+b.key),b)J.call(b,d)&&"key"!==d&&"__self"!==d&&"__source"!==d&&(e[d]=b[d]);var g=arguments.length-2;if(1===g)e.children=c;else if(1<g){for(var h=Array(g),i=0;i<g;i++)h[i]=arguments[i+2];e.children=h}if(a&&a.defaultProps)for(d in g=a.defaultProps,g)void 0===e[d]&&(e[d]=g[d]);return K(a,f,void 0,void 0,null,e)};f.createRef=function(){return{current:null}};f.experimental_useEffectEvent=function(a){return I.H.useEffectEvent(a)};f.forwardRef=function(a){return{$$typeof:r,render:a}};f.isValidElement=M;f.jsx=e;f.jsxDEV=void 0;f.jsxs=e;f.lazy=function(a){return{$$typeof:v,_payload:{_status:-1,_result:a},_init:T}};f.memo=function(a,b){return{$$typeof:u,type:a,compare:void 0===b?null:b}};f.startTransition=W;f.unstable_Activity=x;f.unstable_LegacyHidden=y;f.unstable_Scope=w;f.unstable_SuspenseList=t;f.unstable_TracingMarker=z;f.unstable_ViewTransition=A;f.unstable_addTransitionType=X;f.unstable_getCacheForType=function(a){var b=I.A;return b?b.getCacheForType(a):a()};f.unstable_useCacheRefresh=function(){return I.H.useCacheRefresh()};f.unstable_useMemoCache=U;f.use=function(a){return I.H.use(a)};f.useActionState=function(a,b,c){return I.H.useActionState(a,b,c)};f.useCallback=function(a,b){return I.H.useCallback(a,b)};f.useContext=function(a){return I.H.useContext(a)};f.useDebugValue=function(){};f.useDeferredValue=function(a,b){return I.H.useDeferredValue(a,b)};f.useEffect=function(a,b){return I.H.useEffect(a,b)};f.useId=function(){return I.H.useId()};f.useImperativeHandle=function(a,b,c){return I.H.useImperativeHandle(a,b,c)};f.useInsertionEffect=function(a,b){return I.H.useInsertionEffect(a,b)};f.useLayoutEffect=function(a,b){return I.H.useLayoutEffect(a,b)};f.useMemo=function(a,b){return I.H.useMemo(a,b)};f.useOptimistic=function(a,b){return I.H.useOptimistic(a,b)};f.useReducer=function(a,b,c){return I.H.useReducer(a,b,c)};f.useRef=function(a){return I.H.useRef(a)};f.useState=function(a){return I.H.useState(a)};f.useSyncExternalStore=function(a,b,c){return I.H.useSyncExternalStore(a,b,c)};f.useTransition=function(){return I.H.useTransition()};f.version="19.2.0-www-classic-d1772728-20250606"}),null);
__d("React.classic",["cr:1292365"],(function(a,b,c,d,e,f){e.exports=b("cr:1292365")}),null);
__d("TransAppInlineMode",[],(function(a,b,c,d,e,f){a=Object.freeze({STRING_MANAGER:"STRING_MANAGER",TRANSLATION:"TRANSLATION",APPROVE:"APPROVE",REPORT:"REPORT",NO_INLINE:"NO_INLINE"});f["default"]=a}),66);
__d("cancelIdleCallbackComet",["IdleCallbackImplementation"],(function(a,b,c,d,e,f,g){"use strict";var h=a.cancelIdleCallback||d("IdleCallbackImplementation").cancelIdleCallback;function b(b){h.call(a,b)}g["default"]=b}),98);
__d("cancelIdleCallbackWWW",["cr:692209"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:692209")}),98);
__d("getUnwrappedFbt",["FbtResultGK"],(function(a,b,c,d,e,f){function a(a){a=a.contents;var c=b("FbtResultGK").inlineMode,d=b("FbtResultGK").shouldReturnFbtResult;if(!d&&c!=="REPORT")return(a==null?void 0:a.length)===1&&typeof a[0]==="string"?a[0]:a}e.exports=a}),null);
__d("getFbtResult",["FbtResult","FbtResultGK","InlineFbtResult","getUnwrappedFbt","gkx","recoverableViolation"],(function(a,b,c,d,e,f,g){if(c("gkx")("20935")&&c("FbtResultGK").inlineMode==="TRANSLATION"){c("recoverableViolation")("TransAppInlineMode=TRANSLATION should not happen on Comet yet. "+("[inlineMode="+((b=c("FbtResultGK").inlineMode)!=null?b:"")+"]")+("[runtime_site_is_comet="+String(c("gkx")("20935"))+"]"),"internationalization")}function a(a){var b=c("getUnwrappedFbt")(a);if(b!=null)return b;b=a.contents;var d=a.patternString,e=a.patternHash;return c("FbtResultGK").inlineMode!=null&&c("FbtResultGK").inlineMode!=="NO_INLINE"?new(c("InlineFbtResult"))(b,c("FbtResultGK").inlineMode,d,e):c("FbtResult").get(a)}g["default"]=a}),98);
__d("setTimeoutCometLoggingPriWWW",["JSScheduler","setTimeoutCometInternals"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){for(var c=arguments.length,e=new Array(c>2?c-2:0),f=2;f<c;f++)e[f-2]=arguments[f];return d("setTimeoutCometInternals").setTimeoutAtPriority_DO_NOT_USE.apply(d("setTimeoutCometInternals"),[(h||(h=d("JSScheduler"))).priorities.unstable_Low,a,b].concat(e))}g["default"]=a}),98);
__d("setTimeoutCometSpeculative",["JSScheduler","setTimeoutCometInternals"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){for(var c=arguments.length,e=new Array(c>2?c-2:0),f=2;f<c;f++)e[f-2]=arguments[f];return d("setTimeoutCometInternals").setTimeoutAtPriority_DO_NOT_USE.apply(d("setTimeoutCometInternals"),[(h||(h=d("JSScheduler"))).priorities.unstable_Idle,a,b].concat(e))}g["default"]=a}),98);