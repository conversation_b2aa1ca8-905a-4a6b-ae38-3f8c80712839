;/*FB_PKG_DELIM*/

__d("BDHeaderConfig",[],(function(a,b,c,d,e,f){"use strict";a="359341";f.ASBD_ID=a}),66);
__d("Banzai",["cr:7383"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:7383")}),98);
__d("CometImmersivePhotoCanUserDisable3DMotion.relayprovider",["gkx"],(function(a,b,c,d,e,f,g){"use strict";a={get:function(){return c("gkx")("1872")}};g["default"]=a}),98);
__d("TrackingNodeConstants",[],(function(a,b,c,d,e,f){"use strict";a=58;b=126;c=69;d=42;e=47;var g=6,h=100,i=33,j=38,k=(g+1)*c,l="__tn__";f.BASE_CODE_START=a;f.BASE_CODE_END=b;f.BASE_CODE_SIZE=c;f.PREFIX_CODE_START=d;f.PREFIX_CODE_END=e;f.PREFIX_CODE_SIZE=g;f.ENCODE_NUMBER_MAX=h;f.ENCODED_STRING_WITH_TWO_SYMBOLS_PREFIX_CODE=i;f.ENCODED_STRING_WITH_THREE_SYMBOLS_PREFIX_CODE=j;f.TOTAL_IDS_SUPPORTED_BY_LEGACY_ENCODING=k;f.TN_URL_PARAM=l}),66);
__d("decodeTrackingNode",["TrackingNodeConstants"],(function(a,b,c,d,e,f,g){"use strict";function a(a){if(a.length===0)return[0];var b=function(a,b,e){var c=0;for(var f=b;f<e+b;f+=1){if(!(f<a.length&&a.charCodeAt(f)>=d("TrackingNodeConstants").BASE_CODE_START&&a.charCodeAt(f)<=d("TrackingNodeConstants").BASE_CODE_END))return null;c=c*d("TrackingNodeConstants").BASE_CODE_SIZE+(a.charCodeAt(f)-d("TrackingNodeConstants").BASE_CODE_START)}return c},c=function(a,c){if(c>=a.length)return[null,c];var e=c,f=null,g=0;switch(a.charCodeAt(0)){case d("TrackingNodeConstants").ENCODED_STRING_WITH_TWO_SYMBOLS_PREFIX_CODE:f=b(a,c,2);g=d("TrackingNodeConstants").TOTAL_IDS_SUPPORTED_BY_LEGACY_ENCODING;e+=2;break;case d("TrackingNodeConstants").ENCODED_STRING_WITH_THREE_SYMBOLS_PREFIX_CODE:f=b(a,c,3);g=d("TrackingNodeConstants").TOTAL_IDS_SUPPORTED_BY_LEGACY_ENCODING+Math.pow(d("TrackingNodeConstants").BASE_CODE_SIZE,2);e+=3;break;default:return[null,c]}return f===null?[null,c]:[g+((a=f)!=null?a:0)+1,e]},e=a.charCodeAt(0),f=1,g=0,h=0,i=0;if([d("TrackingNodeConstants").ENCODED_STRING_WITH_TWO_SYMBOLS_PREFIX_CODE,d("TrackingNodeConstants").ENCODED_STRING_WITH_THREE_SYMBOLS_PREFIX_CODE].includes(e)){var j;c=c(a,f);if(c[0]===null)return[0];i=(j=c[0])!=null?j:-1;f=c[1]}else{if(e>=d("TrackingNodeConstants").PREFIX_CODE_START&&e<=d("TrackingNodeConstants").PREFIX_CODE_END){if(a.length===1)return[0];h=e-d("TrackingNodeConstants").PREFIX_CODE_START+1;g=a.charCodeAt(1);f=2}else h=0,g=e;if(g<d("TrackingNodeConstants").BASE_CODE_START||g>d("TrackingNodeConstants").BASE_CODE_END)return[0];i=h*d("TrackingNodeConstants").BASE_CODE_SIZE+(g-d("TrackingNodeConstants").BASE_CODE_START)+1}if(a.length>f+2&&a.charAt(f)==="#"&&a.charAt(f+1)>="0"&&a.charAt(f+1)<="9"&&a.charAt(f+2)>="0"&&a.charAt(f+2)<="9")return[f+3,[i,parseInt(a.charAt(f+1)+a.charAt(f+2),10)+1]];return a.length>f&&a.charAt(f)>="0"&&a.charAt(f)<="9"?[f+1,[i,parseInt(a.charAt(f),10)+1]]:[f,[i]]}g["default"]=a}),98);
__d("getRouteInfoForCometProductAttributionDispatch",["FBLogger"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){if(b!=null)switch(b){case"rootView":return a.main;case"hostedView":b=a.hosted;if(b)return b;c("FBLogger")("comet_infra").mustfix("Navigation was dispatched from hostedView, but no hosted route in previous state");break;case"pushView":b=a.pushViewStack;if(b&&b.length>0){b=b[b.length-1];b.depth;b.key;b=babelHelpers.objectWithoutPropertiesLoose(b,["depth","key"]);return b}c("FBLogger")("comet_infra").mustfix("Navigation was dispatched from pushView, but no push view route in previous state");break}return a.main}g["default"]=a}),98);
__d("CometProductAttribution",["Random","WebSession","decodeTrackingNode","getRouteInfoForCometProductAttributionDispatch","getTopMostRouteInfo"],(function(a,b,c,d,e,f,g){"use strict";var h=function(a){var b=a.bookmark_id,c=a.bookmark_type_name,e=a.link_context,f=a.tap_point;a=a.trace_policy;return{bookmark_id:b,bookmark_type_name:c,link_context:e,session:d("WebSession").getId(),subsession:1,tap_point:f,timestamp:Date.now(),trace_policy:a}},i=function(a){var b=a.bookmark_id,c=a.link_context,e=a.navChainContent,f=a.rootName,g=a.tap_point,h=a.tracePolicy;a=a.trackingNodes;return{bookmark_id:b,"class":f,link_context:c,module:h,navChainContent:e,scope_id:Math.floor(d("Random").random()*1e6),tap_point:g,tracking_nodes:a,ts:Date.now()}},j=function(a){var b=a.productAttributionId;a=a.tracePolicy;if(typeof b==="string")return b;return typeof a==="string"?"tp-"+a:"missing"},k=new Set(["create_jewel","mega_menu","tap_tabbar","tap_search_bar","tap_bookmark","tap_community_panel_popover","tap_community_panel_shortcuts","topnav-link","logo","via_notification"]),l=function(a){return k.has(a)},m=function(a,b){var c="mp_listing";return a==="via_cold_start"&&((a=b.entityKeyConfig)==null?void 0:a.entity_type.value)===c},n=function(a,b){var c={"class":"",module:"inboundClick"};return[].concat(a,[babelHelpers["extends"]({},a[a.length-1],{"class":c["class"],module:c.module,navChainContent:b})])};a=function(a,b,c,d,e,f,g){var k;f===void 0&&(f=!1);k=(k=c==null?void 0:c.route)!=null?k:{};k=k.tracePolicy;b=typeof b==="string"?{tap_point:b}:b!=null?b:{tap_point:"unexpected"};var o=b.bookmark_id!=null?String(b.bookmark_id):j(a);k=h({bookmark_id:o,bookmark_type_name:(o=b==null?void 0:b.bookmark_type_name)!=null?o:"",link_context:d,tap_point:b.tap_point,trace_policy:k!=null?k:(o=a.tracePolicy)!=null?o:"missing"});a.productAttributionId!=null&&b.bookmark_id!=null&&a.productAttributionId!==String(b.bookmark_id)&&(k=babelHelpers["extends"]({},k,{route_bookmark_id:a.productAttributionId}));o=[i({bookmark_id:b.bookmark_id!=null?String(b.bookmark_id):a.productAttributionId,link_context:d,navChainContent:null,rootName:(o=a.rootView.resource)==null?void 0:o.getModuleId(),tap_point:b.tap_point,tracePolicy:(d=a.tracePolicy)!=null?d:"missing",trackingNodes:null})];if(c!=null&&!l(b.tap_point))if(f&&c.productAttribution.v2!=null){d=[].concat(c.productAttribution.v2);d[0]=o[0];o=d}else{f=c.productAttribution.v2;if(f!=null){d=f[0];c=f.slice(1);o=[].concat(o,[babelHelpers["extends"]({},d,{navChainContent:(f=g)!=null?f:null,tracking_nodes:(d=e)!=null?d:null})],c)}o.length>10&&(o=[].concat(o.slice(0,9),[o[o.length-1]]))}else if(m(b.tap_point,a)){o=n(o,String((g=a.params)==null?void 0:g.listing_id))}return{0:k,v2:o}};var o=function(a){return a.replace(/,;/g,"_")},p=function(a){return(a=a==null?void 0:(a=a.v2)==null?void 0:a.map(function(a){var b;return[(b=a["class"])!=null?b:"",a.module,a.tap_point,a.ts.toString(),a.scope_id.toString(),(b=a.bookmark_id)!=null?b:"",((b=a.tracking_nodes)!=null?b:[]).reduce(function(a,b){b=c("decodeTrackingNode")(b);return b.length===1?a:a.concat(b[1][0])},[]).join("#"),(b=a.navChainContent)!=null?b:""].map(o).join()}).join(";"))!=null?a:""};b=function(a){return a!=null?p((a=c("getTopMostRouteInfo")(a()))==null?void 0:a.productAttribution):null};e=function(a,b){if(a==null)return null;a=(a=c("getTopMostRouteInfo")(a()))==null?void 0:a.productAttribution.v2;if(a==null)return null;a.length!==0&&(a[0].tracking_nodes=b);return p({v2:a})};g.getProductAttributionEntry=h;g.getProductAttributionEntryV2=i;g.getProductAttributionIdFromRoute=j;g.isSpecialTapPoint=l;g.isMarketplacePDPColdStart=m;g.insertInboundClickEntryWithContentForColdStart=n;g.getProductAttributionFromRoute=a;g.filterEntryValue=o;g.minifyProductAttributionV2=p;g.getMinifiedTopMostRouteProductAttribution=b;g.minifiedNavigationChainWithTrackingNodes=e;g.getRouteInfoForDispatch=c("getRouteInfoForCometProductAttributionDispatch")}),98);
__d("forEachObject",[],(function(a,b,c,d,e,f){"use strict";var g=Object.prototype.hasOwnProperty;function a(a,b,c){for(var d in a){var e=d;g.call(a,e)&&b.call(c,a[e],e,a)}}f["default"]=a}),66);
__d("CometTimeSpentUtils",["forEachObject"],(function(a,b,c,d,e,f,g){"use strict";var h=function(a,b,d){a=(a=a.timeSpentConfig)==null?void 0:a.session_ids;if(d==null||a==null)return b;c("forEachObject")(a,function(a,c){a=a.extradata_key;if(a!=null){b[a]=(a=d[c])!=null?a:void 0}});return b};a=function(a,b,c){if(c==null)return b;c=c||Object.freeze({});var d=c.session_ids;c=babelHelpers.objectWithoutPropertiesLoose(c,["session_ids"]);return h(a,babelHelpers["extends"]({},b,c),d)};g.addSessionIDsInfo=h;g.addTimeSpentMetaData=a}),98);
__d("SearchCometGlobalResultPageTracePolicy",[],(function(a,b,c,d,e,f){a=Object.freeze({CHAT_TAB:"comet.search_results.chat_tab",DEFAULT_TAB:"comet.search_results.default_tab",HASHTAG:"comet.search_results.hashtag",PHOTOS_TAB:"comet.search_results.photos_tab",TOP_TAB:"comet.search_results.top_tab"});f["default"]=a}),66);
__d("isSearchCometGlobalResultPageTracePolicy",["SearchCometGlobalResultPageTracePolicy"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return Object.values(c("SearchCometGlobalResultPageTracePolicy")).includes(a)}g["default"]=a}),98);
__d("pageID",["WebSession"],(function(a,b,c,d,e,f,g){"use strict";a=d("WebSession").getPageId_DO_NOT_USE();g["default"]=a}),98);
__d("CometVisitationManager",["FBLogger","isSearchCometGlobalResultPageTracePolicy","pageID"],(function(a,b,c,d,e,f,g){"use strict";var h={"comet.marketplace.category":"comet.marketplace.home","comet.marketplace.home.hoisted_pdp":"comet.marketplace.home"},i={},j=null,k=null,l=!1;function m(a){return a.tracePolicy+":"+a.instanceId+":"+a.subsessionCount+":"+a.timeStampMs/1e3}function n(a){if(a==null)return;a=h[a]?h[a]:a;if(j===a)return;var b=i[a];b?(b.subsessionCount++,b.timeStampMs=Date.now()):i[a]={instanceId:c("pageID"),subsessionCount:1,timeStampMs:Date.now(),tracePolicy:a};k=j;j=a}function a(){if(!l){c("FBLogger")("CometVisitationManager").mustfix("Attempting to get the current visitation id without initialization.");return null}if(!l||j==null||!i[j])return null;var a=m(i[j]);if(c("isSearchCometGlobalResultPageTracePolicy")(j)&&k!=null&&i[k]){var b=m(i[k]);return a+"|"+b}return a}function b(a){if(l)return;n(a);l=!0}function o(a){if(!l){c("FBLogger")("CometVisitationManager").mustfix("Updating the visitation manager without initialization");return}n(a)}function d(a){o(a.main.route.tracePolicy)}g.getCurrentVisitationId=a;g.init=b;g.update=o;g.updateFromRouterState=d}),98);
__d("ProfileCometSessionConfig",[],(function(a,b,c,d,e,f){"use strict";a="ps";b=3e4;f.PREFIX=a;f.TIMEOUT_MS=b}),66);
__d("ProfileCometRoutingUtils",[],(function(a,b,c,d,e,f){"use strict";function a(a){return a!=null&&a.startsWith("comet.profile.")}f.isProfilePolicy=a}),66);
__d("ProfileCometSessionUtil",["ProfileCometRoutingUtils","ProfileCometSessionConfig"],(function(a,b,c,d,e,f,g){"use strict";function h(a){var b=a.tracePolicy;if(d("ProfileCometRoutingUtils").isProfilePolicy(b)){b=a.params;a=b.profile_idorvanity;b=b.vanity;if(typeof b==="string")return b;return typeof a==="string"?a:null}return null}function i(a){var b=a.tracePolicy;if(d("ProfileCometRoutingUtils").isProfilePolicy(b)){b=a.params;a=b.id;b=b.profile_idorvanity;if(typeof a==="string")return a;if(typeof b==="string")return b}return null}function a(a,b){if(b==null)return!1;var c=b.tracePolicy;if(!d("ProfileCometRoutingUtils").isProfilePolicy(c))return!1;if(a==null)return!0;c=a.tracePolicy;if(!d("ProfileCometRoutingUtils").isProfilePolicy(c))return!1;c=h(b);var e=h(a);b=i(b);a=i(a);return c!=null&&c===e||b!=null&&b===a}function b(a){var b=[];for(var c=0;c<a.length;c++){var e=a.key(c);e!=null&&e.startsWith(d("ProfileCometSessionConfig").PREFIX)&&b.push(e)}return b}g.isSameProfileSession=a;g.getStorageKeys=b}),98);
__d("OdsWebBatchFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1838142");b=d("FalcoLoggerInternal").create("ods_web_batch",a);e=b;g["default"]=e}),98);
__d("FalcoConsentChecker",[],(function(a,b,c,d,e,f){"use strict";function g(a,b,c,d){var e;switch(typeof d){case"string":e=a[String(d)];return!e?!1:e<=b;case"number":return g(a,b,c,c[Number(d)]);default:e=!1;if(Array.isArray(d)){var f=d[0];for(var h=1;h<d.length;h++){e=g(a,b,c,d[h]);if(e){if(f==="or")return!0}else if(f==="and")return!1}}return e}}f["default"]=g}),66);
__d("WebStorageMutex",["WebStorage","clearTimeout","pageID","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h,i=null,j=!1,k=c("pageID");function l(){j||(j=!0,i=(h||(h=c("WebStorage"))).getLocalStorage());return i}a=function(){function a(a){this.name=a}a.testSetPageID=function(a){k=a};var b=a.prototype;b.$2=function(){var a,b=l();if(!b)return k;b=b.getItem("mutex_"+this.name);b=((a=b)!=null?a:"").split(":");return b&&parseInt(b[1],10)>=Date.now()?b[0]:null};b.$3=function(a){var b=l();if(!b)return;a=a==null?1e3:a;a=Date.now()+a;(h||(h=c("WebStorage"))).setItemGuarded(b,"mutex_"+this.name,k+":"+a)};b.hasLock=function(){return this.$2()===k};b.lock=function(a,b,d){var e=this;this.$1&&c("clearTimeout")(this.$1);k===(this.$2()||k)&&this.$3(d);this.$1=c("setTimeout")(function(){e.$1=null;var c=e.hasLock()?a:b;c&&c(e)},0)};b.unlock=function(){this.$1&&c("clearTimeout")(this.$1);var a=l();a&&this.hasLock()&&a.removeItem("mutex_"+this.name)};return a}();g["default"]=a}),98);
__d("guid",[],(function(a,b,c,d,e,f){function a(){if(typeof crypto==="object"&&typeof crypto.getRandomValues==="function"&&typeof String.prototype.padStart==="function"){var a=crypto.getRandomValues(new Uint32Array(2));return"f"+a[0].toString(16).padStart(8,"0")+a[1].toString(16).padStart(8,"0")}return"f"+(Math.random()*(1<<30)).toString(16).replace(".","")}f["default"]=a}),66);
__d("PersistedQueue",["AnalyticsCoreData","BaseEventEmitter","ExecutionEnvironment","Run","WebStorage","WebStorageMutex","cr:8958","err","guid","nullthrows","performanceAbsoluteNow","requestAnimationFrame"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=24*60*60*1e3,m=30*1e3,n=new(c("BaseEventEmitter"))(),o;function p(a){a===void 0&&(a=!1);if(o===void 0){var b;if(((b=(h||(h=c("AnalyticsCoreData"))).queue_activation_experiment)!=null?b:!1)&&a)try{return(i||(i=c("WebStorage"))).getLocalStorageForRead()}catch(a){return null}b="check_quota";try{a=(i||(i=c("WebStorage"))).getLocalStorage();a?(a.setItem(b,b),a.removeItem(b),o=a):o=null}catch(a){o=null}}return o}function q(a){var b=a.prev,c=a.next;c&&(c.prev=b);b&&(b.next=c);a.next=null;a.prev=null}function r(a){return{item:a,next:null,prev:null}}function s(a,b){return a+"^$"+((a=b==null?void 0:b.queueNameSuffix)!=null?a:"")}var t={},u={},v={},w=!1;a=function(){function a(a,b){var d,e=this;this.$7=0;this.$3=a;this.$5=(d=b==null?void 0:b.queueNameSuffix)!=null?d:"";this.$15=b==null?void 0:b.application;this.$4=s(a,b);this.$11=this.$4+"^$"+c("guid")();this.$14=!1;if(b){this.$8=(d=b.max_age_in_ms)!=null?d:l;this.$12=b.migrate;this.$13=b.onLoad}else this.$8=l;this.$9=[n.addListener("active",function(){(e.$10!=null||!e.$14)&&(e.$14=!0,e.$10=null,e.$16())}),n.addListener("inactive",function(){e.$10==null&&(e.$10=Date.now(),e.$17())})];((j||(j=c("ExecutionEnvironment"))).canUseDOM||(j||(j=c("ExecutionEnvironment"))).isInWorker)&&c("requestAnimationFrame")(function(){return e.$16()})}var d=a.prototype;d.isActive=function(){var a=this.$10;if(a==null)return!0;if(Date.now()-a>m){this.$10=null;n.emit("active",null);return!0}return!1};d.$16=function(){this.$18(),this.$19()};d.$17=function(){this.$20()};d.getFullName=function(){return this.$4};d.getQueueNameSuffix=function(){return this.$5};a.isQueueActivateExperiment=function(){return w};a.setOnQueueActivateExperiment=function(){w=!0};a.create=function(b,d){var e=s(b,d);if(e in t)throw c("err")("Duplicate queue created: "+b);d=new a(b,d);t[e]=d;v[b]?v[b].push(d):v[b]=[d];e=u[b];e&&d.setHandler(e);return d};a.setHandler=function(a,b){if(v[a]){var c=v[a];for(c of c)c.setHandler(b)}u[a]=b};d.destroy=function(){this.$9.forEach(function(a){return a.remove()})};a.destroy=function(a,b){a=s(a,b);t[a].destroy();delete t[a]};d.setHandler=function(a){this.$6=a;this.$19();return this};d.$19=function(){this.$7>0&&this.$6&&this.$6(this)};d.length=function(){return this.$7};d.enumeratedLength=function(){return this.$21().length};a.isPersistenceAllowed=function(){var a=p();return!a?!1:!0};a.getSuffixesForKey=function(a){var b=[];try{var c=p(!0);if(!c)return b;a=a+"^$";for(var d=0;d<c.length;d++){var e=c.key(d);if(typeof e==="string"&&e.startsWith("mutex_falco_"))c.removeItem(e);else if(typeof e==="string"&&e.startsWith(a)){e=e.split("^$");if(e.length>2){e=e[1];b.push(e)}else b.push("")}}}catch(a){}return b};d.$18=function(){var d,e=this,a=p(!0);if(!a)return;var f=this.$4+"^$";d=new(c("WebStorageMutex"))((d=this.$15)!=null?d:f);var g=this.$12,h=this.$13;d.lock(function(d){var i=Date.now()-e.$8;try{for(var j=0;j<a.length;j++){var k=a.key(j);if(typeof k==="string"&&k.startsWith(f)){var l=a.getItem(k);a.removeItem(k);if(l!=null&&l.startsWith("{")){k=b("cr:8958").parse(c("nullthrows")(l));if(k.ts>i)try{for(l of k.items){g!=null?g(l):l;k=h!=null?h(l):l;e.$22(k)}}catch(a){}}}}}catch(a){}d.unlock();e.$19()})};d.$20=function(){var a=p();if(!a)return;var d=this.$21();if(d.length===0){a.getItem(this.$11)!=null&&a.removeItem(this.$11);return}(i||(i=c("WebStorage"))).setItemGuarded(a,this.$11,b("cr:8958").stringify({items:d.map(function(a){return a}),ts:(k||(k=c("performanceAbsoluteNow")))()}))};d.$21=function(){var a=this.$1,b=[];if(!a)return b;do b.push(a.item);while(a=a.prev);return b.reverse()};d.markItemAsCompleted=function(a){var b=a.prev;q(a);this.$1===a&&(this.$1=b);this.$7--;this.isActive()||this.$20()};d.markItemAsFailed=function(a){q(a);var b=this.$2;if(b){var c=b.prev;c&&(c.next=a,a.prev=c);a.next=b;b.prev=a}this.$2=a;this.isActive()&&this.$19()};d.markItem=function(a,b){b?this.markItemAsCompleted(a):this.markItemAsFailed(a)};d.$22=function(a){a=r(a);var b=this.$1;b&&(b.next=a,a.prev=b);this.$1=a;this.$2||(this.$2=a);this.$7++};d.wrapAndEnqueueItem=function(a){this.$22(a),this.isActive()?this.$19():this.$20()};d.dequeueItem=function(){if(this.$10!=null)return null;var a=this.$2;if(!a)return null;this.$2=a.next;return a};return a}();a.eventEmitter=n;if((j||(j=c("ExecutionEnvironment"))).canUseDOM){var x=d("Run").maybeOnBeforeUnload(function(){n.emit("inactive",null),x==null?void 0:x.remove()},!1);if(!x)var y=d("Run").onUnload(function(){n.emit("inactive",null),y.remove()})}g["default"]=a}),98);
__d("ServerTime",["ServerTimeData"],(function(a,b,c,d,e,f,g){var h,i=0;f=0;var j=null;h=(h=(typeof window!=="undefined"?window:self).performance)==null?void 0:h.timing;if(h){var k=h.requestStart;h=h.domLoading;if(k&&h){var l=c("ServerTimeData").timeOfResponseStart-c("ServerTimeData").timeOfRequestStart;k=h-k-l;f=k/2;l=h-c("ServerTimeData").timeOfResponseStart-f;h=Math.max(50,k*.8);Math.abs(l)>h&&(i=l,j=Date.now())}}else d(c("ServerTimeData").serverTime);function a(){return Date.now()-i}function b(){return i}function d(a){a=Date.now()-a;Math.abs(i-a)>6e4&&(i=a,j=Date.now())}function e(){return j===null?null:Date.now()-j}f=a;k=b;g.getMillis=a;g.getOffsetMillis=b;g.update=d;g.getMillisSinceLastUpdate=e;g.get=f;g.getSkew=k}),98);
__d("FalcoLoggerInternal",["AnalyticsCoreData","BaseEventEmitter","FBLogger","FalcoConsentChecker","FalcoUtils","PersistedQueue","Promise","Random","ServerTime","WebSession","nullthrows","performanceAbsoluteNow"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=(f=d("FalcoUtils")).getTaggedBitmap(33),l=f.getTaggedBitmap(0),m=f.getTaggedBitmap(37),n=500*1024*.6,o=new Map();function p(a){var b;a.tags=d("FalcoUtils").xorBitmap((b=a.tags)!=null?b:[0,0],k);return a}function a(a,b){var d;d=(d=c("PersistedQueue").getSuffixesForKey(a))!=null?d:[];d.push(b);for(d of d){var e,f=a+"^$"+d;if(o.has(f))continue;e=((e=(i||(i=c("AnalyticsCoreData"))).use_falco_as_mutex_key)!=null?e:!1)?c("PersistedQueue").create(a,{onLoad:p,queueNameSuffix:d,application:"falco"}):c("PersistedQueue").create(a,{onLoad:p,queueNameSuffix:d});o.set(f,e)}return c("nullthrows")(o.get(a+"^$"+b))}f=f.identityToString((i||(i=c("AnalyticsCoreData"))).identity);var q=a("falco_queue_log",f),r=a("falco_queue_immediately",f),s=a("falco_queue_critical",f),t=new(c("BaseEventEmitter"))(),u={};function v(a,b,e){var f=c("Random").coinflip(b.r);if(!f){d("FalcoUtils").bumpODSMetrics(e,"event.filters.sampling",1);return!1}f=b.c;if(f!=null&&(i||(i=c("AnalyticsCoreData"))).consents!=null){b=w(f,(i||(i=c("AnalyticsCoreData"))).consents,a);if(!b){d("FalcoUtils").bumpODSMetrics(e,"event.filters.consent",1);return!1}}return!0}function w(a,b,d){var e=u[a];e==null&&(e=u[a]=JSON.parse(a));return c("FalcoConsentChecker")(b,d,e,e[0])}function x(){return(j||(j=c("performanceAbsoluteNow")))()-d("ServerTime").getOffsetMillis()}function y(a,b,d,e,f,g){if((i||(i=c("AnalyticsCoreData"))).enable_observer){a=babelHelpers["extends"]({name:a,time:b,sampled:d,getData:f,policy:e},g&&{getPrivacyContext:g});t.emit("event",a)}}function z(a,b,e,f,g,h){var j;g=JSON.stringify(g);if(g.length>n){d("FalcoUtils").bumpODSMetrics(a,"event.filters.exceeded_size",1);c("FBLogger")("falco","oversized_message:"+a).warn('Dropping event "%s" due to exceeding the max size %s at %s',a,n,g.length);return}var k=d("FalcoUtils").xorBitmap([0,0],l);k=d("FalcoUtils").xorBitmap(k,m);((j=(i||(i=c("AnalyticsCoreData"))).enable_session_id_bug_fix)!=null?j:!1)?h.wrapAndEnqueueItem({name:a,policy:b,time:e,extra:g,privacyContext:f,tags:k,sessionId:d("WebSession").getId(),deviceId:(i||(i=c("AnalyticsCoreData"))).device_id}):(h.wrapAndEnqueueItem({name:a,policy:b,time:e,extra:g,privacyContext:f,tags:k}),d("FalcoUtils").bumpODSMetrics(a,"event.captured",1))}function A(a,b,c,e,f){try{var g=x();d("FalcoUtils").bumpODSMetrics(a,"event.logged",1);var h=v(g,b,a);if(h){var i=e(),j=c&&c();z(a,b,g,j,i,f)}y(a,g,h,b,e,c)}catch(a){C(a)}}function B(a,c,e,f,g){try{var i=x();d("FalcoUtils").bumpODSMetrics(a,"event.logged",1);var j=v(i,c,a);if(j){var k=f(),l=(h||(h=b("Promise"))).resolve(e&&e());return h.all([k,l]).then(function(b){var d=b[0],e=b[1];z(a,c,i,e,d,g);y(a,i,j,c,function(){return d},e&&function(){return e})})}else{y(a,i,j,c,f,e);return(h||(h=b("Promise"))).resolve()}}catch(a){return(h||(h=b("Promise"))).reject(a)}}function C(a){var b=c("FBLogger")("falco");a instanceof Error?b.catching(a).warn("Error while attempting to log to Falco"):b.warn("Caught non-error while attempting to log to Falco: %s",JSON.stringify(a))}function e(a,b){return{log:function(c,d){A(a,b,d,c,q)},logAsync:function(c,d){B(a,b,d,c,q)["catch"](C)},logImmediately:function(c,d){A(a,b,d,c,r)},logCritical:function(c,d){A(a,b,d,c,s)},logRealtimeEvent:function(c,d){A(a,b,d,c,s)}}}g.observable=t;g.create=e}),98);
__d("FalcoUtils",["AnalyticsCoreData","ODS"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j="ods_web_batch";function a(a){if(a){var b=a.fbIdentity,c=a.appScopedIdentity;a=a.claim;var d="";if(b){var e=b.accountId;b=b.actorId;d=e+"^#"+b+"^#"}else c!==void 0&&(d="^#^#"+c);return d+"^#"+a}return""}function b(a){return a>30?[0,1<<a-30]:[1<<a,0]}function e(a,b){return[a[0]|b[0],a[1]|b[1]]}function f(a,b,e){if(a===j)return;(i||(i=d("ODS"))).bumpEntityKey(7173,"entities.ff_js_web."+a+"."+(h||(h=c("AnalyticsCoreData"))).app_id+"."+((a=(h||(h=c("AnalyticsCoreData"))).app_version)!=null?a:"0").split(".")[0]+"."+h.push_phase,b,e)}g.identityToString=a;g.getTaggedBitmap=b;g.xorBitmap=e;g.bumpODSMetrics=f}),98);
__d("ODS",["ExecutionEnvironment","OdsWebBatchFalcoEvent","Random","Run","clearTimeout","gkx","setTimeout","unrecoverableViolation"],(function(a,b,c,d,e,f,g){var h,i,j=(h||(h=c("ExecutionEnvironment"))).canUseDOM||(h||c("ExecutionEnvironment")).isInWorker,k={};function l(a,b,c,d,e){var f;d===void 0&&(d=1);e===void 0&&(e=1);var g=(f=k[b])!=null?f:null;if(g!=null&&g<=0)return;i=i||{};var h=i[a]||(i[a]={}),j=h[b]||(h[b]={}),l=j[c]||(j[c]={n:0,d:null}),m=Number(d),o=Number(e);g>0&&(m/=g,o/=g);if(!isFinite(m)||!isFinite(o))return;l.n+=m;if(arguments.length>=5){var p=l.d;p==null&&(p=0);l.d=p+o}n()}var m;function n(){if(m!=null)return;m=c("setTimeout")(function(){o()},c("gkx")("20935")?13e3/2:5e3)}function a(a,b){if(!j)return;k[a]=d("Random").random()<b?b:0}function b(a,b,c,d){d===void 0&&(d=1);if(!j)return;l(a,b,c,d)}function e(a,b,c,d,e){d===void 0&&(d=1);e===void 0&&(e=1);if(!j)return;l(a,b,c,d,e)}function o(a){a===void 0&&(a="normal");if(!j)return;c("clearTimeout")(m);m=null;if(i==null)return;var b=i;i=null;function d(){return{batch:b}}a==="critical"?c("OdsWebBatchFalcoEvent").logCritical(d):c("OdsWebBatchFalcoEvent").log(d)}j&&d("Run").onUnload(function(){o("critical")});g.setEntitySample=a;g.bumpEntityKey=b;g.bumpFraction=e;g.flush=o}),98);
__d("ProfileEngagementFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1744234");b=d("FalcoLoggerInternal").create("profile_engagement",a);e=b;g["default"]=e}),98);
__d("uuidv4",[],(function(a,b,c,d,e,f){"use strict";function a(){var a;a=(a=self)==null?void 0:(a=a.crypto)==null?void 0:a.randomUUID;return typeof a==="function"?self.crypto.randomUUID():"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(a){var b=Math.random()*16|0;a=a==="x"?b:b&3|8;return a.toString(16)})}f["default"]=a}),66);
__d("ProfileCometSession",["ProfileCometSessionConfig","ProfileCometSessionUtil","ProfileEngagementFalcoEvent","WebStorage","uuidv4"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(a){var b=j();return b===null?null:d("ProfileCometSessionConfig").PREFIX+":"+a+":"+b}function j(){var a=(h||(h=c("WebStorage"))).getSessionStorageForRead();if(!a)return null;var b=d("ProfileCometSessionConfig").PREFIX+":tabID";a=a.getItem(b);if(a==null){a=c("uuidv4")();var e=(h||(h=c("WebStorage"))).getSessionStorage();if(!e)return null;e.setItem(b,a)}return a}function k(a){if(a==null)return"timeline";if(a==="comet.profile.timeline.grid")return"timeline_overview";if(a.startsWith("comet.profile.collection.friend"))return"friends_page";return a.startsWith("comet.profile.collection")?"about_page":"timeline"}function l(a,b,d){c("ProfileEngagementFalcoEvent").log(function(){return{content_id:null,profile_event_type:"profile_session_impression",profile_id:a,profile_product_bucket:"profile_core",profile_session_id:b,profile_surface:k(d)}})}function m(a,b){var d=(h||(h=c("WebStorage"))).getLocalStorage();if(!d)return null;d=c("uuidv4")();q(a,d);l(a,d,b==null?void 0:b.tracePolicy);return d}function n(a,b){var c=p(a);return c===null?m(a,b):c}function o(a){a=i(a);var b=(h||(h=c("WebStorage"))).getLocalStorageForRead();if(a===null||!b)return null;b=b.getItem(a);if(b==null)return null;a=b.split(":");b=a[0];a=a[1];a=parseInt(a,10);return[b,a]}function p(a){a=o(a);if(a!==null){var b=a[0];a=a[1];if(Date.now()-a<d("ProfileCometSessionConfig").TIMEOUT_MS)return b}return null}function q(a,b){var d=Date.now();a=i(a);var e=(h||(h=c("WebStorage"))).getLocalStorage();if(e&&a!==null){(h||(h=c("WebStorage"))).setItemGuarded(e,a,b+":"+d);return b}return null}function r(a,b){b=n(a,b);if(b==null)return null;q(a,b);return b}function a(a,b){return n(a,b)}function b(a,b,c){if(b!=null){var d=p(a);if(d===null){l(a,b,c);return q(a,b)}}return r(a)}function e(a,b,c,e){return d("ProfileCometSessionUtil").isSameProfileSession(b,c)||e==="popstate"||e==="initial"?r(a,c):m(a,c)}g.extend=r;g.get=a;g.initOrExtend=b;g.navigate=e}),98);
__d("CometTimeSpentNavigation",["CometProductAttribution","CometTimeSpentUtils","CometVisitationManager","ProfileCometSession"],(function(a,b,c,d,e,f,g){"use strict";var h=null,i=null,j=new Set();function k(){j.forEach(function(a){return a({destPathInfo:i,sourcePathInfo:h})})}a={changePath:function(a,b,c){c===void 0&&(c=null);h=i;var e=a.entityID,f=a.parentContainerId,g=a.timeSpentConfig,j=a.tracePolicy,l=a.url;if(c&&c.profile_session_id!=null&&e!=null){var m;c.profile_session_id=(m=d("ProfileCometSession").initOrExtend(e,(m=c)==null?void 0:m.profile_session_id,j))!=null?m:(m=c)==null?void 0:m.profile_session_id}m=babelHelpers["extends"]({},b);delete m.v2;b=d("CometProductAttribution").minifyProductAttributionV2(b);m={pa:JSON.stringify(m),pav2:b,uri:l};m=d("CometTimeSpentUtils").addTimeSpentMetaData(a,m,c);b=d("CometVisitationManager").getCurrentVisitationId();b!=null&&(m.visitation_id=b);m.container_id==null&&(e!=null&&(m.container_id=e));m.parent_container_id==null&&f!=null&&(m.parent_container_id=f);i={extraData:m,name:j,should_remove_navigation:(l=g==null?void 0:g.should_remove_navigation)!=null?l:!1};k()},getPathInfo:function(){return i},getSourcePathInfo:function(){return h},listenToPathChange:function(a){j.add(a);return{cancelListen:function(){return j["delete"](a)}}},resetPathInfoForTestingOnly:function(){h=null,i=null}};b=a;g["default"]=b}),98);
__d("CometUFIReactionsEnableShortName.relayprovider",[],(function(a,b,c,d,e,f){"use strict";function a(){return!1}f.get=a}),66);
__d("CometUFIShareActionMigration.relayprovider",["gkx"],(function(a,b,c,d,e,f,g){"use strict";a={get:function(){var a;return(a=c("gkx")("2411"))!=null?a:!1}};g["default"]=a}),98);
__d("CometUFI_dedicated_comment_routable_dialog_gk.relayprovider",["gkx"],(function(a,b,c,d,e,f,g){"use strict";a={get:function(){return c("gkx")("13962")}};g["default"]=a}),98);
__d("CometVideoHomeLOEExploreLeftRailSectionQuery_facebookRelayOperation",[],(function(a,b,c,d,e,f){e.exports="9838633542916650"}),null);
__d("CometVideoHomeRootProviderQuery_facebookRelayOperation",[],(function(a,b,c,d,e,f){e.exports="9739300912784024"}),null);
__d("CookieStore",["CookieCoreLoggingConfig","FBLogger","Random","performanceNow"],(function(a,b,c,d,e,f,g){"use strict";var h,i=window.I_AM_CORE_COOKIE_INFRASTRUCTURE_AND_NEED_TO_ACCESS_COOKIES!=null?window.I_AM_CORE_COOKIE_INFRASTRUCTURE_AND_NEED_TO_ACCESS_COOKIES():null,j={set:function(a){document.cookie=a},get:function(){return document.cookie}};function k(){return i!=null?i:j}function l(a,b,c,d,e,f,g,h){return b+"="+encodeURIComponent(c)+"; "+(f!==0&&f!==void 0&&f!==null?"expires="+new Date(a+f).toUTCString()+"; ":"")+"path="+d+"; domain="+e+(g?"; secure":"")+(h?"; SameSite="+h:"")}function m(a,b,c){return a+"=; expires=Thu, 01-Jan-1970 00:00:01 GMT; path="+b+"; domain="+c}function n(){if(c("CookieCoreLoggingConfig").sampleRate>0){var a=(h||(h=c("performanceNow")))(),b=k().get();a=h()-a;var d=a>c("CookieCoreLoggingConfig").maximumIgnorableStallMs&&c("Random").coinflip(1/c("CookieCoreLoggingConfig").sampleRate);d&&c("FBLogger")("cookie_infra").addMetadata("COOKIE_INFRA","WALL_TIME",String(a)).warn("Cookie read exceeded %s milliseconds.",c("CookieCoreLoggingConfig").maximumIgnorableStallMs);return b}else return k().get()}var o=function(){function a(){this.$1=0}var b=a.prototype;b.setCookie=function(a,b,c,d,e,f,g,h){k().set(l(a,b,c,d,e,f,g,h))};b.clearCookie=function(a,b,c){k().set(m(a,b,c))};b.getCookie=function(a){var b;this.$1++;b=(b=n())==null?void 0:b.match("(?:^|;\\s*)"+a+"=(.*?)(?:;|$)");return b?decodeURIComponent(b[1]):null};return a}(),p=10*1e3;b=function(){function a(){this.$1={},this.$2=0,this.$3=0,this.$4=0}var b=a.prototype;b.setCookie=function(a,b,c,d,e,f,g,h){k().set(l(a,b,c,d,e,f,g,h)),this.$1[b]={value:c,updated:a}};b.clearCookie=function(a,b,c){k().set(m(a,b,c)),this.$1[a]={value:null,updated:Date.now()}};b.getCookie=function(a){a=this.$5(a);a=a.cookie;return a};b.$5=function(a){var b=Date.now(),c=this.$1[a];if(!c){if(this.$2+p<b){this.$6();return{cookie:this.$5(a).cookie,hit:!1}}this.$3++;return{cookie:null,hit:!0}}if(c.updated+p<b){this.$6();return{cookie:this.$5(a).cookie,hit:!1}}this.$3++;return{cookie:c.value,hit:!0}};b.$6=function(){var a;this.$4++;a=(a=(a=n())==null?void 0:a.split(";"))!=null?a:[];this.$2=Date.now();this.$1={};for(a of a){var b=a.match("\\s*([^=]+)=(.*)");if(!b)continue;this.$1[b[1]]={value:decodeURIComponent(b[2]),updated:this.$2}}};return a}();function a(){return new o()}g.newCookieStore=a;g.CookieCacheForTest=b;g.CookieStoreSlowForTest=o}),98);
__d("JsCrossSiteCookieUsageFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("6476");b=d("FalcoLoggerInternal").create("js_cross_site_cookie_usage",a);e=b;g["default"]=e}),98);
__d("CookieCore",["CookieCoreConfig","CookieDomain","CookiePrivacySandboxConfig","CookieStore","JsCrossSiteCookieUsageFalcoEvent","err","justknobx"],(function(a,b,c,d,e,f,g){var h=/_js_(.*)/,i;function j(){i||(i=d("CookieStore").newCookieStore());return i}function k(){return"."+c("CookieDomain").domain}function l(a){return window.self!==window.top?!1:!0}function m(a,b){if(!r(a))return;n(a,b,t(a),u(a),s(a),v(a));w(a,1)}function a(a,b,c){if(!r(a))return;n(a,b,t(a),u(a),s(a),v(a),c)}function n(a,b,c,d,e,f,g){var h=Date.now();c=c;if(c!=null)if(c>h)c-=h;else if(c===1){o(a,d,g);return}j().setCookie(h,a,b,d,(h=g)!=null?h:k(),c,e,f)}function b(a,b){if(!l(a))return;m(a,b)}function e(a,b,c,d,e,f){if(!l(a))return;n(a,b,c,d,e,null,f)}function o(a,b,c){b===void 0&&(b="/");b=b||"/";j().clearCookie(a,b,(b=c)!=null?b:k());w(a,2)}function f(a){if(!r(a))return null;w(a,0);return j().getCookie(a)}function p(a){return{insecure:a.i||!1,path:a.p||"/",ttlSeconds:a.t||0,sameSite:a.s||"None"}}function q(a){if(c("CookieCoreConfig")[a]!==void 0)return p(c("CookieCoreConfig")[a]);a=a.match(h);return a&&a.length>1?q(a[1]):null}function r(a){return q(a)!==null}function s(a){a=q(a);return a==null?!0:!a.insecure}function t(a){a=q(a);return a==null?null:a.ttlSeconds*1e3}function u(a){a=q(a);return a==null?"/":a.path}function v(a){a=q(a);return a==null||a.sameSite==null?null:a.sameSite}function w(a,b){var e=d("CookiePrivacySandboxConfig").is_affected_by_samesite_lax;e&&c("justknobx")._("2552")&&c("JsCrossSiteCookieUsageFalcoEvent").log(function(){return{cookie_name:a,cookie_op:b,js_backtrace:c("err")("read cookie backtrace: ").stack}})}g.set=m;g.setWithDomain_FOR_MESSENGER_LS_ONLY=a;g.setWithoutChecks=n;g.setIfFirstPartyContext=b;g.setWithoutChecksIfFirstPartyContext=e;g.clear=o;g.get=f}),98);
__d("Cookie",["CookieConsent","CookieCore","InitialCookieConsent","ODS"],(function(a,b,c,d,e,f,g){var h,i,j;function k(a){if(!(j||(j=c("CookieConsent"))).hasFirstPartyConsent()){(h||(h=d("ODS"))).bumpEntityKey(798,"defer_cookies","set."+a);return!1}return!0}function l(){return!(i||(i=c("InitialCookieConsent"))).noCookies}function a(a,b){if(!l()||!k(a))return;d("CookieCore").set(a,b)}function b(a,b){if(!l())return;d("CookieCore").set(a,b)}function e(a,b,c,e,f,g){if(!l()||!k(a))return;d("CookieCore").setWithoutChecks(a,b,c,e,f,null,g)}a={clear:(f=d("CookieCore")).clear,get:f.get,set:a,setIfFirstPartyContext:f.setIfFirstPartyContext,setWithoutCheckingUserConsent_DANGEROUS:b,setWithoutChecks:e,setWithoutChecksIfFirstPartyContext:f.setWithoutChecksIfFirstPartyContext};g["default"]=a}),98);
__d("CurrentAppID",["CurrentUserInitialData"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(){return(h||(h=c("CurrentUserInitialData"))).APP_ID}g.getAppID=a}),98);
__d("CurrentUser",["Cookie","CurrentUserInitialData"],(function(a,b,c,d,e,f){var g,h={getID:function(){return(g||(g=b("CurrentUserInitialData"))).USER_ID},getAccountID:function(){return(g||(g=b("CurrentUserInitialData"))).ACCOUNT_ID},getPossiblyNonFacebookUserID:function(){var a;return(a=(g||(g=b("CurrentUserInitialData"))).NON_FACEBOOK_USER_ID)!=null?a:this.getID()},getEIMU:function(){var a;return(a=(g||(g=b("CurrentUserInitialData"))).IG_USER_EIMU)!=null?a:"0"},getEmployeeWorkUserID:function(){return(g||(g=b("CurrentUserInitialData"))).WORK_USER_ID},getName:function(){return(g||(g=b("CurrentUserInitialData"))).NAME},getShortName:function(){return(g||(g=b("CurrentUserInitialData"))).SHORT_NAME},getAbraID:function(){var a;return(a=(g||(g=b("CurrentUserInitialData"))).ABRA_ID)!=null?a:"0"},getAbraStorageID:function(){var a;return(a=(g||(g=b("CurrentUserInitialData"))).ABRA_STORAGE_ID)!=null?a:"0"},getARID:function(){var a;return(a=(g||(g=b("CurrentUserInitialData"))).AR_ID)!=null?a:"0"},getEPOU:function(){var a;return(a=(g||(g=b("CurrentUserInitialData"))).EPOU_ID)!=null?a:"0"},getEOCPU:function(){var a;return(a=(g||(g=b("CurrentUserInitialData"))).EOCPU_ID)!=null?a:"0"},isLoggedIn:function(){return h.getPossiblyNonFacebookUserID()!=="0"},isLoggedInNow:function(){var a;if(!h.isLoggedIn())return!1;if((g||(g=b("CurrentUserInitialData"))).IS_INTERN_SITE)return!0;if((g||(g=b("CurrentUserInitialData"))).IS_ABRA_USER||(g||(g=b("CurrentUserInitialData"))).IS_ENTERPRISE_USER||(g||(g=b("CurrentUserInitialData"))).IS_IMAGINE_USER||(g||(g=b("CurrentUserInitialData"))).IS_INSTAGRAM_USER||(g||(g=b("CurrentUserInitialData"))).IS_META_SPARK_USER||(g||(g=b("CurrentUserInitialData"))).IS_OCULUS_USER||(g||(g=b("CurrentUserInitialData"))).IS_TOGETHER_APP_USER||(g||(g=b("CurrentUserInitialData"))).IS_WORK_MESSENGER_CALL_GUEST_USER||(g||(g=b("CurrentUserInitialData"))).IS_WORK_USER||(g||(g=b("CurrentUserInitialData"))).IS_WORKROOMS_USER||(g||(g=b("CurrentUserInitialData"))).IS_ANONYMOUS_CASTING_USER)return!0;if((g||(g=b("CurrentUserInitialData"))).ORIGINAL_USER_ID!=null&&(g||(g=b("CurrentUserInitialData"))).ORIGINAL_USER_ID!="")return(g||(g=b("CurrentUserInitialData"))).ORIGINAL_USER_ID===b("Cookie").get("c_user");return(g||(g=b("CurrentUserInitialData"))).IS_BUSINESS_DOMAIN===!0?(g||(g=b("CurrentUserInitialData"))).USER_ID==b("Cookie").get("c_user"):(g||(g=b("CurrentUserInitialData"))).USER_ID===((a=b("Cookie").get("i_user"))!=null?a:b("Cookie").get("c_user"))},isEmployee:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_EMPLOYEE},isContingentWorker:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_CONTINGENT},isTestUser:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_TEST_USER},hasWorkUser:function(){return!!(g||(g=b("CurrentUserInitialData"))).HAS_WORK_USER},isWorkUser:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_WORK_USER},isWorkroomsUser:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_WORKROOMS_USER},isGray:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_GRAY},isUnderage:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_UNDERAGE},isManagedMetaAccount:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_MANAGED_META_ACCOUNT},isMessengerOnlyUser:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_MESSENGER_ONLY_USER},isDeactivatedAllowedOnMessenger:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_DEACTIVATED_ALLOWED_ON_MESSENGER},isMessengerCallGuestUser:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_MESSENGER_CALL_GUEST_USER},isBusinessPersonAccount:function(){return(g||(g=b("CurrentUserInitialData"))).IS_BUSINESS_PERSON_ACCOUNT},hasSecondaryBusinessPerson:function(){return(g||(g=b("CurrentUserInitialData"))).HAS_SECONDARY_BUSINESS_PERSON},getAppID:function(){return(g||(g=b("CurrentUserInitialData"))).APP_ID},isFacebookWorkAccount:function(){return(g||(g=b("CurrentUserInitialData"))).IS_FACEBOOK_WORK_ACCOUNT},isInstagramBusinessPerson:function(){return(g||(g=b("CurrentUserInitialData"))).IS_INSTAGRAM_BUSINESS_PERSON},getPageMessagingMailboxId:function(){var a;return String((a=(g||(g=b("CurrentUserInitialData"))).PAGE_MESSAGING_MAILBOX_ID)!=null?a:"0")}};e.exports=h}),null);
__d("Deferred",["Promise"],(function(a,b,c,d,e,f){"use strict";var g;(g||(g=b("Promise"))).resolve();a=function(){function a(a){var c=this;a=a||g||(g=b("Promise"));this.$1=!1;this.$2=new a(function(a,b){c.$3=a,c.$4=b})}var c=a.prototype;c.getPromise=function(){return this.$2};c.resolve=function(a){this.$1=!0,this.$3(a)};c.reject=function(a){this.$1=!0,this.$4(a)};c.isSettled=function(){return this.$1};return a}();f["default"]=a}),66);
__d("EventEmitterWithValidation",["BaseEventEmitter"],(function(a,b,c,d,e,f){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c){var d;d=a.call(this)||this;d.$EventEmitterWithValidation1=Object.keys(b);d.$EventEmitterWithValidation2=Boolean(c);return d}var c=b.prototype;c.emit=function(b){if(this.$EventEmitterWithValidation1.indexOf(b)===-1){if(this.$EventEmitterWithValidation2)return;throw new TypeError(g(b,this.$EventEmitterWithValidation1))}return a.prototype.emit.apply(this,arguments)};return b}(b("BaseEventEmitter"));function g(a,b){a='Unknown event type "'+a+'". ';a+="Known event types: "+b.join(", ")+".";return a}e.exports=a}),null);
__d("FBJSON",[],(function(a,b,c,d,e,f){a=JSON.parse;b=JSON.stringify;f.parse=a;f.stringify=b}),66);
__d("FBReelsIFUTileContent_reelsIFUPlayOnHover.relayprovider",["qex"],(function(a,b,c,d,e,f,g){"use strict";a={get:function(){return c("qex")._("3296")===!0}};g["default"]=a}),98);
__d("FBReelsMediaFooter_comet_enable_reels_ads_gk.relayprovider",["gkx"],(function(a,b,c,d,e,f,g){"use strict";a={get:function(){return c("gkx")("7085")}};g["default"]=a}),98);
__d("FBReels_deprecate_short_form_video_context_gk.relayprovider",["gkx"],(function(a,b,c,d,e,f,g){"use strict";a={get:function(){return c("gkx")("10255")}};g["default"]=a}),98);
__d("MetaConfig",["invariant","BanzaiLazyQueue","ExecutionEnvironment","MetaConfigMap"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j={};a={_:function(a){var b=c("MetaConfigMap").get(a);b!=null||h(0,57910,a);var e=b.value;b=b.log_id;b!=null&&j[a]!==!0&&((i||(i=c("ExecutionEnvironment"))).isInBrowser&&d("BanzaiLazyQueue").queuePost("metaconfig_exposure",{identifier:a,log_id:b}),j[a]=!0);return e}};b=a;g["default"]=b}),98);
__d("GHLShouldChangeAdIdFieldName.relayprovider",["MetaConfig"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("MetaConfig")._("103")&&c("MetaConfig")._("164")}g.get=a}),98);
__d("GHLShouldChangeSponsoredDataFieldName.relayprovider",["MetaConfig"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("MetaConfig")._("34")&&c("MetaConfig")._("70")}g.get=a}),98);
__d("IntlVariations",[],(function(a,b,c,d,e,f){e.exports={BITMASK_NUMBER:28,BITMASK_GENDER:3,NUMBER_ZERO:16,NUMBER_ONE:4,NUMBER_TWO:8,NUMBER_FEW:20,NUMBER_MANY:12,NUMBER_OTHER:24,GENDER_MALE:1,GENDER_FEMALE:2,GENDER_UNKNOWN:3}}),null);
__d("IsMergQAPolls.relayprovider",["gkx"],(function(a,b,c,d,e,f,g){"use strict";a={get:function(){return c("gkx")("24565")}};g["default"]=a}),98);
__d("IsWorkUser.relayprovider",["gkx"],(function(a,b,c,d,e,f,g){"use strict";a={get:function(){var a;return(a=c("gkx")("20836"))!=null?a:!1}};g["default"]=a}),98);
__d("JSResource",["JSResourceReferenceImpl"],(function(a,b,c,d,e,f,g){var h={};function i(a,b){h[a]=b}function j(a){return h[a]}function a(a){a=a;var b=j(a);if(b)return b;b=new(c("JSResourceReferenceImpl"))(a);i(a,b);return b}a.loadAll=c("JSResourceReferenceImpl").loadAll;g["default"]=a}),98);
__d("JSResourceForInteraction",["JSResource"],(function(a,b,c,d,e,f,g){function a(a){return c("JSResource").call(null,a)}b=a;g["default"]=b}),98);
__d("JstlMigrationFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1814852");b=d("FalcoLoggerInternal").create("jstl_migration",a);e=b;g["default"]=e}),98);
__d("ProfileCometViewAsConstants.entrypoint",[],(function(a,b,c,d,e,f){"use strict";a="100000686899395";f.VIEW_AS_EVERYONE_USER=a}),66);
__d("ResourceTimingStore",["performance"],(function(a,b,c,d,e,f,g){"use strict";var h,i=4e3,j=3e3,k=new Map(),l=!1;function m(){var a=Array.from(k.entries());k=new Map(a.slice(-j))}function n(a){var b=a.indexOf("#");return b===-1?a:a.slice(0,b)}function o(a){for(a of a){if(!(a instanceof PerformanceResourceTiming))continue;var b="";try{b=new URL(a.name).pathname}catch(a){}if(!/\.(css|js)$/.test(b))continue;b=a;if(!(b!=null&&typeof b==="object"&&typeof b.encodedBodySize==="number"&&typeof b.decodedBodySize==="number"&&typeof b.transferSize==="number"))continue;k.set(n(a.name),b)}k.size>i&&m()}function p(a){o(a.getEntries())}function q(){if(l)return;l=!0;var a;if(typeof PerformanceObserver!=="undefined"){a=new PerformanceObserver(p);try{a.observe({buffered:!0,type:"resource"})}catch(a){}}typeof (h||(h=c("performance"))).getEntriesByType==="function"&&o((h||(h=c("performance"))).getEntriesByType("resource"))}function a(a){q();return k.get(n(a))}g.init=q;g.getEntryForURL=a}),98);
__d("StoriesArmadilloReplyEnabled.relayprovider",["qex"],(function(a,b,c,d,e,f,g){"use strict";a={get:function(){var a;return(a=c("qex")._("564"))!=null?a:!1}};g["default"]=a}),98);
__d("VersionRange",["invariant"],(function(a,b,c,d,e,f,g,h){"use strict";var i=/\./,j=/\|\|/,k=/\s+\-\s+/,l=/^(<=|<|=|>=|~>|~|>|)?\s*(.+)/,m=/^(\d*)(.*)/;function n(a,b){a=a.split(j);if(a.length>1)return a.some(function(a){return E.contains(a,b)});else return o(a[0].trim(),b)}function o(a,b){a=a.split(k);a.length>0&&a.length<=2||h(0,11800);if(a.length===1)return p(a[0],b);else{var c=a[0];a=a[1];y(c)&&y(a)||h(0,11801);return p(">="+c,b)&&p("<="+a,b)}}function p(a,b){a=a.trim();if(a==="")return!0;b=b.split(i);a=w(a);var c=a.modifier;a=a.rangeComponents;switch(c){case"<":return q(b,a);case"<=":return r(b,a);case">=":return t(b,a);case">":return u(b,a);case"~":case"~>":return v(b,a);default:return s(b,a)}}function q(a,b){return D(a,b)===-1}function r(a,b){a=D(a,b);return a===-1||a===0}function s(a,b){return D(a,b)===0}function t(a,b){a=D(a,b);return a===1||a===0}function u(a,b){return D(a,b)===1}function v(a,b){var c=b.slice();b=b.slice();b.length>1&&b.pop();var d=b.length-1,e=parseInt(b[d],10);x(e)&&(b[d]=e+1+"");return t(a,c)&&q(a,b)}function w(a){a=a.split(i);var b=a[0].match(l);b||h(0,3074);return{modifier:b[1],rangeComponents:[b[2]].concat(a.slice(1))}}function x(a){return!isNaN(a)&&isFinite(a)}function y(a){return!w(a).modifier}function z(a,b){for(var c=a.length;c<b;c++)a[c]="0"}function A(a,b){a=a.slice();b=b.slice();z(a,b.length);for(var c=0;c<b.length;c++){var d=b[c].match(/^[x*]$/i);if(d){b[c]=a[c]="0";if(d[0]==="*"&&c===b.length-1)for(d=c;d<a.length;d++)a[d]="0"}}z(b,a.length);return[a,b]}function B(a,b){var c=a.match(m),d=b.match(m);c=c&&c[1];d=d&&d[1];c=parseInt(c,10);d=parseInt(d,10);if(x(c)&&x(d)&&c!==d)return C(c,d);else return C(a,b)}function C(a,b){typeof a===typeof b||h(0,11802);if(typeof a==="string"&&typeof b==="string")if(a>b)return 1;else if(a<b)return-1;else return 0;if(typeof a==="number"&&typeof b==="number")if(a>b)return 1;else if(a<b)return-1;else return 0;typeof a===typeof b||h(0,11802);return 0}function D(a,b){a=A(a,b);b=a[0];a=a[1];for(var c=0;c<a.length;c++){var d=B(b[c],a[c]);if(d)return d}return 0}var E={contains:function(a,b){return n(a.trim(),b.trim())}};a=E;g["default"]=a}),98);
__d("UserAgent",["UserAgentData","VersionRange","memoizeStringOnly"],(function(a,b,c,d,e,f,g){"use strict";function h(a,b,d,e){if(a===d)return!0;if(!d.startsWith(a))return!1;d=d.slice(a.length);if(b!=null){d=e?e(d):d;return c("VersionRange").contains(d,b)}return!1}function i(a){return c("UserAgentData").platformName==="Windows"?a.replace(/^\s*NT/,""):a}b={isBrowser:(a=c("memoizeStringOnly"))(function(a){return h(c("UserAgentData").browserName,c("UserAgentData").browserFullVersion,a)}),isBrowserArchitecture:a(function(a){return h(c("UserAgentData").browserArchitecture,null,a)}),isDevice:a(function(a){return h(c("UserAgentData").deviceName,null,a)}),isEngine:a(function(a){return h(c("UserAgentData").engineName,c("UserAgentData").engineVersion,a)}),isEngine_DEPRECATED_DANGEROUS:a(function(a){return h(c("UserAgentData").engineName,c("UserAgentData").engineVersion,a)}),isPlatform:a(function(a){return h(c("UserAgentData").platformName,c("UserAgentData").platformFullVersion,a,i)}),isPlatformArchitecture:a(function(a){return h(c("UserAgentData").platformArchitecture,null,a)})};d=b;g["default"]=d}),98);
__d("WebPixelRatio",["SiteData"],(function(a,b,c,d,e,f,g){function a(){return c("SiteData").pr!=null&&c("SiteData").pr>0?c("SiteData").pr:window.devicePixelRatio||1}g.get=a}),98);
__d("WorkCometIsEmployeeGKProvider.relayprovider",["gkx"],(function(a,b,c,d,e,f,g){"use strict";a={get:function(){return c("gkx")("20861")}};g["default"]=a}),98);
__d("getCrossOriginTransport",["invariant","ExecutionEnvironment","err"],(function(a,b,c,d,e,f,g){var h;function i(){if(!(h||(h=b("ExecutionEnvironment"))).isInBrowser)throw b("err")("getCrossOriginTransport: %s","Cross origin transport unavailable in the server environment.");try{var a=new XMLHttpRequest();!("withCredentials"in a)&&typeof XDomainRequest!=="undefined"&&(a=new XDomainRequest());return a}catch(a){throw b("err")("getCrossOriginTransport: %s",a.message)}}i.withCredentials=function(){var a=i();"withCredentials"in a||g(0,5150);var b=a.open;a.open=function(){b.apply(this,arguments),this.withCredentials=!0};return a};e.exports=i}),null);
__d("ZeroRewrites",["URI","ZeroRewriteRules","getCrossOriginTransport","getSameOriginTransport","isFacebookURI"],(function(a,b,c,d,e,f){var g,h={rewriteURI:function(a){if(!b("isFacebookURI")(a)||h._isWhitelisted(a))return a;var c=h._getRewrittenSubdomain(a);c!==null&&c!==void 0&&(a=a.setSubdomain(c));return a},getTransportBuilderForURI:function(a){return h.isRewritten(a)?b("getCrossOriginTransport").withCredentials:b("getSameOriginTransport")},isRewriteSafe:function(a){if(Object.keys(b("ZeroRewriteRules").rewrite_rules).length===0||!b("isFacebookURI")(a))return!1;var c=h._getCurrentURI().getDomain(),d=new(g||(g=b("URI")))(a).qualify().getDomain();return c===d||h.isRewritten(a)},isRewritten:function(a){a=a.getQualifiedURI();if(Object.keys(b("ZeroRewriteRules").rewrite_rules).length===0||!b("isFacebookURI")(a)||h._isWhitelisted(a))return!1;var c=a.getSubdomain(),d=h._getCurrentURI(),e=h._getRewrittenSubdomain(d);return a.getDomain()!==d.getDomain()&&c===e},_isWhitelisted:function(a){a=a.getPath();a.endsWith("/")||(a+="/");return b("ZeroRewriteRules").whitelist&&b("ZeroRewriteRules").whitelist[a]===1},_getRewrittenSubdomain:function(a){a=a.getQualifiedURI().getSubdomain();return b("ZeroRewriteRules").rewrite_rules[a]},_getCurrentURI:function(){return new(g||(g=b("URI")))("/").qualify()}};e.exports=h}),null);
__d("errorCode",[],(function(a,b,c,d,e,f){"use strict";function a(a){throw new Error('errorCode("'+a+'"): This should not happen. Oh noes!')}f["default"]=a}),66);
__d("getDataWithLoggerOptions",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){return babelHelpers["extends"]({},a,{__options:babelHelpers["extends"]({event_time:Date.now()/1e3},b)})}f["default"]=a}),66);
__d("generateLiteTypedLogger",["Banzai","JstlMigrationFalcoEvent","getDataWithLoggerOptions"],(function(a,b,c,d,e,f,g){"use strict";function h(a,b,d){var e=a.split(":")[0],f=a.split(":")[1];e=="logger"?c("JstlMigrationFalcoEvent").log(function(){return{logger_config_name:f,payload:b}}):c("Banzai").post(a,b,d)}function a(a){return{log:function(b,d){h(a,c("getDataWithLoggerOptions")(b,d),c("Banzai").BASIC)},logVital:function(b,d){h(a,c("getDataWithLoggerOptions")(b,d),c("Banzai").VITAL)},logImmediately:function(b,d){h(a,c("getDataWithLoggerOptions")(b,d),{signal:!0})}}}g["default"]=a}),98);
__d("getAsyncHeaders",["BDHeaderConfig","LSD","ZeroCategoryHeader","isFacebookURI","requireWeak"],(function(a,b,c,d,e,f,g){function a(a){var b={},d=c("isFacebookURI")(a);d&&c("ZeroCategoryHeader").value&&(b[c("ZeroCategoryHeader").header]=c("ZeroCategoryHeader").value);d=h(a);d&&(b["X-FB-LSD"]=d);d=i(a);d&&(b["X-ASBD-ID"]=d);c("requireWeak")("MessengerPWAVersionForUserAgent",function(c){c=c();c!=null&&!j(a)&&(b["X-FB-PWA"]=""+c)});return b}function h(a){return j(a)?null:c("LSD").token}function i(a){return j(a)?null:d("BDHeaderConfig").ASBD_ID}function j(a){var b;b=(b=(b=k())==null?void 0:(b=b.location)==null?void 0:b.origin)!=null?b:(b=window)==null?void 0:(b=b.location)==null?void 0:b.origin;return b==null?!0:!a.toString().startsWith("/")&&a.getOrigin()!==b}function k(){if(typeof document!=="undefined")return document;else return null}g["default"]=a}),98);
__d("isInIframe",[],(function(a,b,c,d,e,f){var g=typeof window!=="undefined"&&window.top!=null&&window!=window.top;function a(){return g}f["default"]=a}),66);
__d("mixInEventEmitter",["invariant","EventEmitterWithHolding","EventEmitterWithValidation","EventHolder"],(function(a,b,c,d,e,f,g,h){"use strict";function a(a,b,c){b||h(0,3159);var d=a.prototype||a;d.__eventEmitter&&h(0,3160);a=a.constructor;a&&(a===Object||a===Function||h(0,3161));d.__types=babelHelpers["extends"]({},d.__types,b);d.__ignoreUnknownEvents=Boolean(c);Object.assign(d,i)}var i={emit:function(a,b,c,d,e,f,g){return this.__getEventEmitter().emit(a,b,c,d,e,f,g)},emitAndHold:function(a,b,c,d,e,f,g){return this.__getEventEmitter().emitAndHold(a,b,c,d,e,f,g)},addListener:function(a,b,c){return this.__getEventEmitter().addListener(a,b,c)},once:function(a,b,c){return this.__getEventEmitter().once(a,b,c)},addRetroactiveListener:function(a,b,c){return this.__getEventEmitter().addRetroactiveListener(a,b,c)},listeners:function(a){return this.__getEventEmitter().listeners(a)},removeAllListeners:function(){this.__getEventEmitter().removeAllListeners()},removeCurrentListener:function(){this.__getEventEmitter().removeCurrentListener()},releaseHeldEventType:function(a){this.__getEventEmitter().releaseHeldEventType(a)},__getEventEmitter:function(){if(!this.__eventEmitter){var a=new(c("EventEmitterWithValidation"))(this.__types,this.__ignoreUnknownEvents),b=new(c("EventHolder"))();this.__eventEmitter=new(c("EventEmitterWithHolding"))(a,b)}return this.__eventEmitter}};g["default"]=a}),98);
__d("objectEntries",[],(function(a,b,c,d,e,f){function a(a){return Object.entries(a)}f["default"]=a}),66);
__d("passiveEventListenerUtil",[],(function(a,b,c,d,e,f){"use strict";b=!1;try{c=Object.defineProperty({},"passive",{get:function(){b=!0}});window.addEventListener("test",null,c)}catch(a){}var g=b;function a(a){return g?a:typeof a==="boolean"?a:a.capture||!1}f.isPassiveEventListenerSupported=g;f.makeEventOptions=a}),66);
__d("qpl",["QPLHasteSupportDataStorage","recoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";var h={};a={_:function(a,b){var d=h[b];if(d==null){var e=c("QPLHasteSupportDataStorage").get(b);e==null?(c("recoverableViolation")("Failed to find a Haste-supplied config for the QPL event "+("identified by token `"+b+"`."),"staticresources"),d={i:a}):d=babelHelpers["extends"]({i:a},e);h[b]=d}return d}};b=a;g["default"]=b}),98);
__d("setTimeoutCometLoggingPri",["cr:619"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=b("cr:619")}),98);
__d("structuredClone",[],(function(a,b,c,d,e,f){"use strict";b=(a=window)==null?void 0:a.structuredClone;f["default"]=b}),66);