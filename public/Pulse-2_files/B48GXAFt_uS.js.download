;/*FB_PKG_DELIM*/

__d("Base64",[],(function(a,b,c,d,e,f){var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function h(a){a=a.charCodeAt(0)<<16|a.charCodeAt(1)<<8|a.charCodeAt(2);return String.fromCharCode(g.charCodeAt(a>>>18),g.charCodeAt(a>>>12&63),g.charCodeAt(a>>>6&63),g.charCodeAt(a&63))}var i=">___?456789:;<=_______\0\x01\x02\x03\x04\x05\x06\x07\b\t\n\v\f\r\x0e\x0f\x10\x11\x12\x13\x14\x15\x16\x17\x18\x19______\x1a\x1b\x1c\x1d\x1e\x1f !\"#$%&'()*+,-./0123";function j(a){a=i.charCodeAt(a.charCodeAt(0)-43)<<18|i.charCodeAt(a.charCodeAt(1)-43)<<12|i.charCodeAt(a.charCodeAt(2)-43)<<6|i.charCodeAt(a.charCodeAt(3)-43);return String.fromCharCode(a>>>16,a>>>8&255,a&255)}var k={encode:function(a){a=unescape(encodeURI(a));var b=(a.length+2)%3;a=(a+"\0\0".slice(b)).replace(/[\s\S]{3}/g,h);return a.slice(0,a.length+b-2)+"==".slice(b)},decode:function(a){a=a.replace(/[^A-Za-z0-9+\/]/g,"");var b=a.length+3&3;a=(a+"AAA".slice(b)).replace(/..../g,j);a=a.slice(0,a.length+b-3);try{return decodeURIComponent(escape(a))}catch(a){throw new Error("Not valid UTF-8")}},encodeObject:function(a){return k.encode(JSON.stringify(a))},decodeObject:function(a){return JSON.parse(k.decode(a))},encodeNums:function(a){return String.fromCharCode.apply(String,a.map(function(a){return g.charCodeAt((a|-(a>63?1:0))&-(a>0?1:0)&63)}))}};a=k;f["default"]=a}),66);
__d("react-compiler-runtime",[],(function(a,b,c,d,e,f){"use strict";var g=null;function a(a){return g.H.useMemoCache(a)}function b(a){g=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE}f.c=a;f.initReactCompilerRuntime=b}),66);
__d("CometAppKey",[],(function(a,b,c,d,e,f){a=Object.freeze({NONE:0,GENERIC_COMET_DO_NOT_USE:1,COMET_ON_MOBILE:2,COMET_ON_INSTAGRAM_DO_NOT_USE:3,FB_ACCOUNTS_CENTER:5,CANVAS:6,IG_WWW:7,FRL_ACCOUNTS_CENTER:8,NOVI_CHECKOUT:9,ENTERPRISE_CENTER:10,BIZ_WEB:11,BUSINESS_FB:12,HORIZON_WORLDS:14,FB_WEB:15,WHATSAPP:17,META_DOT_COM:18,OCULUS_DOT_COM:19,FRL_FAMILY_CENTER:20,WHATSAPP_FAQ:23,IG_ACCOUNTS_CENTER:24,ADS_MANAGER_ON_BLUE:25,MESSENGER_FAMILY_CENTER:26,META_WORK_PORTFOLIO:27,BARCELONA_WEB:29,FB_FAMILY_CENTER:30,CANDIDATE_PORTAL:31,META_HELP:32,FRL_AUTH:33,META_LLAMA:34,IG_GEN_AI_STUDIO:35,FB_GEN_AI_STUDIO:36,IG_FAMILY_CENTER:37,IG_PRIVACY_CENTER:38,IG_HELP_CENTER:39,ABOUT_META:40,IG_GEN_AI_IMAGINE:41,FB_GEN_AI_IMAGINE:42,INTERNALFB:43,COMMERCE_MANAGER:44,QUEST_DEV_CENTER:45,ABRA:46,META_BUG_BOUNTY:47,CTRL_VERSE_DATA_COLLECTION:48,META_CONTENT_LIBRARY_UI:49,SUPPORT_PORTAL:50,MSE_RATING_TOOL:51,MEDIA_PORTAL:52,COMMERCE_PERMISSION_WIZARD:53,SA_DEMO_BOOKING:55,COMMERCE_EXTENSION:56,FB_PRIVACY_CENTER:57,ADS_MANAGER_ON_COMET:58,FB_HELP_CENTER:59,MONETIZATION_DOGFOODING:61,AI_DEMOS:62,DEVELOPER_PLATFORM:63,PARTNERSHIP_ADS_HUB:64,INSTAGRAM_ABOUT:65,TRANSPARENCY:66,BUSINESS_USER_PROFILE_MANAGED_ACCOUNT:67,WHATSAPP_FLOWS:68,GENERATIVE_OFFENSIVE_ASSESSMENT_TOOL:69,SHOPIFY_APP:70,LLAMA_DEV_CENTER:71,KADABRA:72,THREADS_FAMILY_CENTER:73,PAYMENTS:74,HORIZON_MANAGED_SOLUTIONS:75,TEST_COMET:76,WHATSAPP_MINIAPPS:77,META_ACCOUNT_AUTH:78,DEV_HUB:79,THREADS_ACCOUNTS_CENTER:80,SHOPIFY_WHATSAPP_APP:81,WILDFLOWER:82,FAMILY_CENTER_MARKETING:83,CONSENT_TEMPLATES:84,FB_COMMUNITY_NOTES:85,WEARABLES_DEV_CENTER:86,AI_META:87,EMPLOYEE_HELPDESK:88,RESEARCH_TOOLS_MANAGER:89,SOCIAL_PLUGIN:90});f["default"]=a}),66);
__d("DangerouslyAccessReactElementInternals_DO_NOT_USE_IN_NEW_CODE",[],(function(a,b,c,d,e,f){"use strict";function a(a){return a}f["default"]=a}),66);
__d("react-forget-runtime",["invariant","CometAppKey","DangerouslyAccessReactElementInternals_DO_NOT_USE_IN_NEW_CODE","FBLogger","SiteData","err","gkx"],(function(a,b,c,d,e,f,g,h){var i=null,j=null,k=!1,l=a.console,m=[],n={},o="Jest-Only Fatal: ",p="";function q(a){if(!k){var b=c("err")(o+a);l.error(o+a+"\n"+b.stack);k=!0;c("FBLogger")("React").warn("React compiler guard functions used in production.")}}["useCallback","useContext","useEffect","useImperativeHandle","useInsertionEffect","useLayoutEffect","useMemo","useReducer","useRef","useState","useDebugValue","useDeferredValue","useTransition","useMutableSource","useSyncExternalStore","useId","useCacheRefresh","useOptimistic"].forEach(function(a){n[a]=function(){var a="[React] Unexpected React hook call {name} from a React Forget compiled function. Check that all hooks are called directly and named according to convention ('use[A-Z]') ";q(a)}});a=function(a){n[a]=function(){if(j==null)throw c("FBLogger")("React").mustfixThrow("React Forget internal error: unexpected null dispatcher");else{var b;return(b=j)[a].apply(b,arguments)}}};for(var r of["useMemoCache","unstable_useMemoCache","readContext","unstable_isNewReconciler","getCacheSignal","getCacheForType","use"])a(r);function b(a){i.H==null;var b=i.H;if(b==null)return;if(a===0){m.push(b);m.length===1&&(j=b);if(b===n){var d="[React] Unexpected call to custom hook or component from a React Forget compiled function. Check that (1) all hooks are called directly and named according to convention ('use[A-Z]') and (2) components are returned as JSX instead of being directly invoked.";q(d)}i.H=n}else if(a===1){d=m.pop();if(d==null)throw c("FBLogger")("React").mustfixThrow("React Forget internal error: unexpected null in guard stack");m.length===0&&(j=null);i.H=d}else if(a===2)m.push(b),j!=null&&(i.H=j);else if(a===3){d=m.pop();if(d==null)throw c("FBLogger")("React").mustfixThrow("React Forget internal error: unexpected null in guard stack");i.H=d}}function d(a){s=a.isValidElement,i=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,a.unstable_useContextWithBailout,a.useContext}a=!1;function e(a,b){l.log(a,b)}var s=null;function t(a){return(a=s==null?void 0:s(a))!=null?a:!1}var u=new Set();function f(a,b,d,e,f,g){function i(a,b,c,h){c=p+"Forget change detection: "+f+":"+g+" ["+e+"]: "+d+c+" changed from "+a+" to "+b+" (depth "+h+")";if(u.has(c))return;u.add(c);l.error(c)}var j=2;function k(a,b,d,e){if(e>j)return;else if(a===b)return;else if(typeof a!==typeof b)i("type "+typeof a,"type "+typeof b,d,e);else if(typeof a==="object"){typeof b==="object"||h(0,18576);if(a===null||b===null)(a!==null||b!==null)&&i("type "+(a===null?"null":"object"),"type "+(b===null?"null":"object"),d,e);else if(a instanceof Map)if(!(b instanceof Map))i("Map instance","other value",d,e);else if(a.size!==b.size)i("Map instance with size "+a.size,"Map instance with size "+b.size,d,e);else for(var f of a){var g=f[0],l=f[1];!b.has(g)?i("Map instance with key "+String(g),"Map instance without key "+String(l),d,e):k(l,b.get(g),d+".get("+String(g)+")",e+1)}else if(b instanceof Map)i("other value","Map instance",d,e);else if(a instanceof Set)if(!(b instanceof Set))i("Set instance","other value",d,e);else if(a.size!==b.size)i("Set instance with size "+a.size,"Set instance with size "+b.size,d,e);else for(l of b)a.has(l)||i("Set instance without element "+String(l),"Set instance with element "+String(l),d,e);else if(b instanceof Set)i("other value","Set instance",d,e);else if(Array.isArray(a)||Array.isArray(b))if(!Array.isArray(a)||!Array.isArray(b))i("type "+(Array.isArray(a)?"array":"object"),"type "+(Array.isArray(b)?"array":"object"),d,e);else if(a.length!==b.length)i("array with length "+a.length,"array with length "+b.length,d,e);else for(g=0;g<a.length;g++)k(a[g],b[g],d+"["+g+"]",e+1);else if(t(a)||t(b))!t(a)||!t(b)?i("type "+(t(a)?"React element":"object"),"type "+(t(b)?"React element":"object"),d,e):c("DangerouslyAccessReactElementInternals_DO_NOT_USE_IN_NEW_CODE")(a).type!==c("DangerouslyAccessReactElementInternals_DO_NOT_USE_IN_NEW_CODE")(b).type?i("React element of type "+String(c("DangerouslyAccessReactElementInternals_DO_NOT_USE_IN_NEW_CODE")(a).type.name),"React element of type "+String(c("DangerouslyAccessReactElementInternals_DO_NOT_USE_IN_NEW_CODE")(b).type.name),d,e):k(c("DangerouslyAccessReactElementInternals_DO_NOT_USE_IN_NEW_CODE")(a).props,c("DangerouslyAccessReactElementInternals_DO_NOT_USE_IN_NEW_CODE")(b).props,"[props of "+d+"]",e+1);else{for(f in b)f in a||i("object without key "+f,"object with key "+f,d,e);for(l in a)!(l in b)?i("object with key "+l,"object without key "+l,d,e):k(a[l],b[l],d+"."+l,e+1)}}else if(typeof a==="function")return;else isNaN(a)||isNaN(b)?isNaN(a)!==isNaN(b)&&i(""+(isNaN(a)?"NaN":"non-NaN value"),""+(isNaN(b)?"NaN":"non-NaN value"),d,e):a!==b&&i(String(a),String(b),d,e)}k(a,b,"",0)}g.$dispatcherGuard=b;g.initReactForgetRuntime=d;g.shouldLogRender=a;g.logRender=e;g.$structuralCheck=f}),98);
__d("setupReactRefresh",["cr:1108857"],(function(a,b,c,d,e,f,g){}),34);
__d("shimReactSecretInternals_DEPRECATED",["FBLogger","justknobx"],(function(a,b,c,d,e,f,g){"use strict";var h=new Set();function i(a,b){b===void 0&&(b="warn");if(c("justknobx")._("1806")){if(h.has(a))return;h.add(a);var d=c("FBLogger")("react","secret-internals-shim-"+a);b==="warn"?d.warn("Access to a renamed property of React's secret internals: %s. A library or hack needs to be updated.",a):d.mustfix("Access to a renamed property of React's secret internals: %s. A library or hack needs to be updated.",a)}}function a(a){var b=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;Object.assign(b,{ReactCurrentDispatcher:{get current(){i("ReactCurrentDispatcher-current-get");return b.H},set current(a){i("ReactCurrentDispatcher-current-set"),b.H=a}},ReactCurrentCache:{get current(){i("ReactCurrentCache-current-get");return b.A},set current(a){i("ReactCurrentCache-current-set","mustfix")}},ReactCurrentBatchConfig:{get transition(){i("ReactCurrentBatchConfig-transition-get");return b.T},set transition(a){i("ReactCurrentBatchConfig-transition-set","mustfix")}},ReactCurrentOwner:{get current(){i("ReactCurrentOwner-current-get");var a=b.A;return a===null?null:a.getOwner()},set current(a){i("ReactCurrentOwner-current-set","mustfix")}}});["ReactCurrentDispatcher","ReactCurrentCache","ReactCurrentBatchConfig","ReactCurrentOwner"].forEach(function(a){var c;((c=Object.getOwnPropertyDescriptor(b,a))==null?void 0:c.configurable)&&Object.defineProperty(b,a,{enumerable:!1})});a.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=b}g["default"]=a}),98);
__d("VultureJSGating",["justknobx"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("justknobx")._("2635")}function b(){return c("justknobx")._("3532")}g.isLoggingEnabled=a;g.isLowVolumeLoggingEnabled=b}),98);
__d("RDRequireDeferredReference",["RequireDeferredReference"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(){return a.apply(this,arguments)||this}b.disableForSSR_DO_NOT_USE=function(){this.$RDRequireDeferredReference$p_1=!1};var c=b.prototype;c.isAvailableInSSR_DO_NOT_USE=function(){return this.constructor.$RDRequireDeferredReference$p_1};return b}(c("RequireDeferredReference"));a.$RDRequireDeferredReference$p_1=!0;g["default"]=a}),98);
__d("requireDeferred",["RDRequireDeferredReference"],(function(a,b,c,d,e,f,g){"use strict";var h={};function i(a,b){h[a]=b}function j(a){return h[a]}function a(a){var b=j(a);if(b)return b;b=new(c("RDRequireDeferredReference"))(a);b.setRequireDeferredForDisplay(!1);i(a,b);return b}g["default"]=a}),98);
__d("vulture",["ExecutionEnvironment","FBLogger","JSResource","VultureJSGating","asyncToGeneratorRuntime","clearTimeout","objectEntries","requireDeferred","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h,i=0,j=-1,k=null;c("requireDeferred")("bumpVultureJSHash").__setRef("vulture").onReadyImmediately(function(a){k=a,u()});var l=!1,m=!1,n=null,o=new Map(),p=[],q=12e4;function r(a){var b=o.get(a);if(b===i||k==null)return;b==null?k(a,1):b===j?(k(a,1),d("VultureJSGating").isLowVolumeLoggingEnabled()&&c("FBLogger")("vulture_js","low_volume_"+a).addToCategoryKey(a).addMetadata("VULTURE_JS","LOW_VOLUME_HASH",a).warn("Low volume vulture with hash %s hit",a)):Math.floor(Math.random()*b)===0&&k(a,b)}function s(a){p.push(a)}function t(){n!=null&&(c("clearTimeout")(n),n=null),m=!1,l=!0,u()}function u(){if(l&&k!=null)while(p.length>0){var a=p.pop();a!=null&&r(a)}}function v(){if(m)return;m=!0;(h||(h=c("ExecutionEnvironment"))).canUseDOM&&(n=c("setTimeout")(function(){t(),c("FBLogger")("vulture_js","sample_rate_load_timeout").warn("Timed out attemping to fetch VultureJS sample rates")},q));c("JSResource")("VultureJSSampleRatesLoader").__setRef("vulture").load().then(function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){a=(yield a.loadSampleRates());for(a of c("objectEntries")(a)){var b=a[0],d=a[1];o.set(b,d)}});return function(b){return a.apply(this,arguments)}}())["catch"](function(a){c("FBLogger")("vulture_js","sample_rate_load_timeout").catching(a).mustfix("Failed to fetch sample rates:  %s",a.getMessage())})["finally"](t)}function a(a){d("VultureJSGating").isLoggingEnabled()&&(l&&k!=null?r(a):(s(a),v()))}g["default"]=a}),98);
__d("React",["FBLogger","cr:1294158","cr:15957","react-compiler-runtime","react-forget-runtime","setupReactRefresh","shimReactSecretInternals_DEPRECATED","vulture"],(function(a,b,c,d,e,f){b("setupReactRefresh");a=b("cr:1294158");b("cr:15957");b("shimReactSecretInternals_DEPRECATED")(a);b("react-forget-runtime").initReactForgetRuntime(a);b("react-compiler-runtime").initReactCompilerRuntime(a);var g=a.createFactory;a.createFactory=function(){b("vulture")("pB3SJkIu2GS8TCgsuxQ3RWJ--gc=");b("FBLogger")("react","createfactory").mustfix("React.createFactory is not supported anymore");return g.apply(void 0,arguments)};e.exports=a}),null);
__d("CometLruCache",["recoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";var h=function(){function a(a,b){this.$1=a,this.$2=b,a<=0&&c("recoverableViolation")("CometLruCache: Unable to create instance of cache with zero or negative capacity.","CometLruCache"),this.$3=new Map()}var b=a.prototype;b.set=function(a,b){this.$3["delete"](a);this.$3.set(a,{timestamp:Date.now(),value:b});if(this.$3.size>this.$1){a=this.$3.keys().next();a.done||this.$3["delete"](a.value)}};b.get=function(a){var b=this.$3.get(a);if(b!=null){if(Date.now()>b.timestamp+this.$2){this.$3["delete"](a);return null}this.$3["delete"](a);this.$3.set(a,b);return b.value}return null};b.has=function(a){return this.$3.has(a)};b["delete"]=function(a){this.$3["delete"](a)};b.size=function(){return this.$3.size};b.capacity=function(){return this.$1-this.$3.size};b.clear=function(){this.$3.clear()};return a}();function a(a,b){b===void 0&&(b=Number.MAX_SAFE_INTEGER);return new h(a,b)}g.create=a}),98);
__d("ConstUriUtils",["CometLruCache","ExecutionEnvironment","FBLogger","PHPQuerySerializer","PHPQuerySerializerNoEncoding","URIRFC3986","URISchemes","UriNeedRawQuerySVConfig","isSameOrigin","nullthrows","recoverableViolation","structuredClone"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=d("CometLruCache").create(5e3),m=new RegExp("(^|\\.)facebook\\.com$","i"),n=new RegExp("(^|\\.)messenger\\.com$","i"),o=new RegExp("(^|\\.)instagram\\.com$","i"),p=new RegExp("(^|\\\\.)meta\\\\.(com|ai)$","i"),q=new RegExp("^(?:[^/]*:|[\\x00-\\x1f]*/[\\x00-\\x1f]*/)"),r=new RegExp("[\\x00-\\x2c\\x2f\\x3b-\\x40\\x5c\\x5e\\x60\\x7b-\\x7f\\uFDD0-\\uFDEF\\uFFF0-\\uFFFF\\u2047\\u2048\\uFE56\\uFE5F\\uFF03\\uFF0F\\uFF1F]"),s=c("UriNeedRawQuerySVConfig").uris.map(function(a){return{domain:a,valid:z(a)}}),t=[],u=[];function v(a,b){var d={};if(a!=null)for(a of a.entries())d[a[0]]=a[1];else c("FBLogger")("ConstUriUtils").warn("Passed a null query map in, this means poor client side flow coverage or client/server boundary type issue.");return b.serialize(d)}function w(a,b,d){var e=k||(k=c("PHPQuerySerializer"));if(["http","https"].includes(b)&&x(a)){if(a.includes("doubleclick.net")&&d!=null&&!d.startsWith("http"))return e;e=c("PHPQuerySerializerNoEncoding")}return e}function x(a){return a!=null&&s.some(function(b){return b.valid&&y(a,b.domain)})}function y(a,b){if(b===""||a==="")return!1;if(a.endsWith(b)){b=a.length-b.length-1;if(b===-1||a[b]===".")return!0}return!1}function z(a){return!r.test(a)}function A(a,b){var c=b.protocol!=null&&b.protocol!==""?b.protocol:a.getProtocol();c=b.domain!=null?w(b.domain,c):a.getSerializer();c={domain:a.getDomain(),fragment:a.getFragment(),fragmentSeparator:a.hasFragmentSeparator(),isGeneric:a.isGeneric(),originalRawQuery:a.getOriginalRawQuery(),path:a.getPath(),port:a.getPort(),protocol:a.getProtocol(),queryParams:a.getQueryParams(),serializer:c,subdomain:a.getSubdomain()};a=babelHelpers["extends"]({},c,b);c=b.queryParams!=null&&b.queryParams.size!==0;return F.getUribyObject(a,c)}function B(a,b,c,d){c===void 0&&(c=!1);var e=a.protocol!==""?a.protocol+":"+(a.isGeneric?"":"//"):"",f=a.domain!==""?a.domain:"",g=a.port!==""?":"+a.port:"",h=a.path!==""?a.path:e!==""&&e!=="mailto:"||f!==""||g!==""?"/":"";c=C(f,a.originalRawQuery,a.queryParams,b,c,(b=d)!=null?b:a.serializer);d=c.length>0?"?":"";b=a.fragment!==""?"#"+a.fragment:"";a=a.fragment===""&&a.fragmentSeparator?"#":"";return""+e+f+g+h+d+c+a+b}function C(a,b,c,d,e,f){e===void 0&&(e=!1);if(!d&&(e||x(a))){return(d=b)!=null?d:""}return v(c,f)}function D(a){var b=a.trim();b=(h||(h=d("URIRFC3986"))).parse(b)||{fragment:null,host:null,isGenericURI:!1,query:null,scheme:null,userinfo:null};var c=b.host||"",e=c.split(".");e=e.length>=3?e[0]:"";var f=w(c,b.scheme||"",b.query),g=f.deserialize(b.query||"")||{};g=new Map(Object.entries(g));g=E({domain:c,fragment:b.fragment||"",fragmentSeparator:b.fragment==="",isGeneric:b.isGenericURI,originalRawQuery:b.query,path:b.path||"",port:b.port!=null?String(b.port):"",protocol:(b.scheme||"").toLowerCase(),queryParams:g,serializer:f,subdomain:e,userInfo:(c=b==null?void 0:b.userinfo)!=null?c:""},a);return g}function E(a,b,c,e){c===void 0&&(c=(j||(j=d("URISchemes"))).Options.INCLUDE_DEFAULTS);var f={components:babelHelpers["extends"]({},a),error:"",valid:!0},g=f.components;if(!(j||(j=d("URISchemes"))).isAllowed(a.protocol,c,e)){f.valid=!1;f.error='The URI protocol "'+String(a.protocol)+'" is not allowed.';return f}if(!z(a.domain||"")){f.valid=!1;f.error="This is an unsafe domain "+String(a.domain);return f}g.port=a.port!=null&&String(a.port)||"";if(Boolean(a.userInfo)){f.valid=!1;f.error="Invalid URI: (userinfo is not allowed in a URI "+String(a.userInfo)+")";return f}c=b!=null&&b!==""?b:B(g,!1);if(g.domain===""&&g.path.indexOf("\\")!==-1){f.valid=!1;f.error="Invalid URI: (no domain but multiple back-slashes "+c+")";return f}if(!g.protocol&&q.test(c)){f.valid=!1;f.error="Invalid URI: (unsafe protocol-relative URI "+c+")";return f}if(g.domain!==""&&g.path!==""&&!g.path.startsWith("/")){f.valid=!1;f.error="Invalid URI: (domain and pathwhere path lacks leading slash "+c+")";return f}return f}var F=function(){function a(a){this.queryParams=new Map(),this.domain=a.domain,this.fragment=a.fragment,this.fragmentSeparator=Boolean(a.fragmentSeparator),this.isGenericProtocol=Boolean(a.isGeneric),this.path=a.path,this.originalRawQuery=a.originalRawQuery,this.port=a.port,this.protocol=a.protocol,this.queryParams=a.queryParams,this.serializer=a.serializer,this.subdomain=a.subdomain}var b=a.prototype;b.addQueryParam=function(a,b){if(Boolean(a)){var c=this.getQueryParams();c.set(a,b);return A(this,{queryParams:c})}return this};b.addQueryParams=function(a){if(a.size>0){var b=this.getQueryParams();a.forEach(function(a,c){b.set(c,a)});return A(this,{queryParams:b})}return this};b.addQueryParamString=function(a){if(Boolean(a)){a=a.startsWith("?")?a.slice(1):a;var b=this.getQueryParams();a.split("&").map(function(a){a=a.split("=");var c=a[0];a=a[1];b.set(c,a)});return A(this,{queryParams:b})}return this};b.addTrailingSlash=function(){var a=this.getPath();return a.length>0&&a[a.length-1]!=="/"?this.setPath(a+"/"):this};b.getDomain=function(){return this.domain};b.getFragment=function(){return this.fragment};b.getOrigin=function(){var a=this.getPort();return this.getProtocol()+"://"+this.getDomain()+(a?":"+a:"")};b.getOriginalRawQuery=function(){return this.originalRawQuery};b.getPath=function(){return this.path};b.getPort=function(){return this.port};b.getProtocol=function(){return this.protocol.toLowerCase()};b.getQualifiedUri=function(){if(!this.getDomain()){var b;b=(b=typeof window!=="undefined"?window:self)==null?void 0:(b=b.location)==null?void 0:b.href;if(b==null){c("FBLogger")("ConstUriUtils").blameToPreviousFile().warn("Cannot get qualified URI for current URI as there is no current location");return null}(i||(i=c("ExecutionEnvironment"))).isInWorker&&b.startsWith("blob:")&&(b=b.substring(5,b.length));b=b.slice(0,b.indexOf("/",b.indexOf(":")+3));return a.getUri(b+this.toString())}return this};b.getQueryParam=function(a){a=this.queryParams.get(a);if(typeof a==="string")return a;else{a=JSON.stringify(a);return a==null?a:JSON.parse(a)}};b.getQueryData=function(){return Object.fromEntries(this.getQueryParams())};b.getQueryParams=function(){if(c("structuredClone")!=null)return c("structuredClone")(this.queryParams);var a=JSON.stringify(Array.from(this.queryParams),function(a,b){return Array.isArray(b)?{__CUUArr:!0,value:babelHelpers["extends"]({},b)}:b});a=JSON.parse(a,function(a,b){return b!=null&&typeof b==="object"&&b.__CUUArr?Object.keys(b.value).reduce(function(a,c){a[c]=b.value[c];return a},[]):b});return new Map(a)};b.getQueryString=function(a){a===void 0&&(a=!1);return C(this.domain,this.originalRawQuery,this.queryParams,!1,a,this.serializer)};b.getRegisteredDomain=function(){if(!this.getDomain())return"";if(!this.isFacebookUri())return null;var a=this.getDomain().split("."),b=a.indexOf("facebook");b===-1&&(b=a.indexOf("workplace"));return a.slice(b).join(".")};b.getSerializer=function(){return this.serializer};b.getSubdomain=function(){return this.subdomain};b.getUnqualifiedUri=function(){if(this.getDomain()){var b=this.toString();return a.getUri(b.slice(b.indexOf("/",b.indexOf(":")+3)))}return this};a.getUri=function(b){b=b.trim();var d=l.get(b);if(d==null){var e=D(b);if(e.valid)d=new a(e.components),l.set(b,d);else{c("FBLogger")("ConstUriUtils").blameToPreviousFrame().warn(e.error);return null}}return d};a.getUriOrThrow=function(b){return c("nullthrows")(a.getUri(b))};a.getUribyObject=function(b,d){var e=B(b,d),f=l.get(e);if(f==null){d&&(b.originalRawQuery=v(b.queryParams,b.serializer));d=E(b);if(d.valid)f=new a(d.components),l.set(e,f);else{c("recoverableViolation")(d.error,"ConstUri");return null}}return f};b.hasFragmentSeparator=function(){return this.fragmentSeparator};b.isEmpty=function(){return!(this.getPath()||this.getProtocol()||this.getDomain()||this.getPort()||this.queryParams.size>0||this.getFragment())};b.isFacebookUri=function(){var a=this.toString();if(a==="")return!1;return!this.getDomain()&&!this.getProtocol()?!0:["https","http"].indexOf(this.getProtocol())!==-1&&(m.test(this.getDomain())||n.test(this.getDomain())||o.test(this.getDomain())||p.test(this.getDomain()))};b.isGeneric=function(){return this.isGenericProtocol};b.isSameOrigin=function(a){return c("isSameOrigin")(this,a)};b.isSubdomainOfDomain=function(b){var c=a.getUri(b);return c!=null&&y(this.domain,b)};b.isSecure=function(){return this.getProtocol()==="https"};b.removeQueryParams=function(a){if(Array.isArray(a)&&a.length>0){var b=this.getQueryParams();a.map(function(a){return b["delete"](a)});return A(this,{queryParams:b})}return this};b.removeQueryParam=function(a){if(Boolean(a)){var b=this.getQueryParams();b["delete"](a);return A(this,{queryParams:b})}return this};b.removeSubdomain=function(){var a=this.getQualifiedUri();if(a==null)return null;var b=a.getDomain();b=b.split(".");b.length>=3&&(b=b.slice(-2));return A(a,{domain:b.join("."),subdomain:""})};b.replaceQueryParam=function(a,b){if(Boolean(a)){var c=this.getQueryParams();c.set(a,b);return A(this,{queryParams:c})}return this};b.replaceQueryParams=function(a){return A(this,{queryParams:a})};b.replaceQueryParamString=function(a){if(a!=null){a=a.startsWith("?")?a.slice(1):a;var b=this.getQueryParams();a.split("&").map(function(a){a=a.split("=");var c=a[0];a=a[1];b.set(c,a)});return A(this,{queryParams:b})}return this};b.setDomain=function(a){if(Boolean(a)){var b=a.split(".");b=b.length>=3?b[0]:"";return A(this,{domain:a,subdomain:b})}return this};b.setFragment=function(a){return a==="#"?A(this,{fragment:"",fragmentSeparator:!0}):A(this,{fragment:a,fragmentSeparator:a!==""})};b.setPath=function(a){return a!=null?A(this,{path:a}):this};b.setPort=function(a){return Boolean(a)?A(this,{port:a}):this};b.setProtocol=function(a){return Boolean(a)?A(this,{protocol:a}):this};b.setSecure=function(a){return this.setProtocol(a?"https":"http")};b.setSubDomain=function(a){if(Boolean(a)){var b=this.getQualifiedUri();if(b==null)return null;var c=b.getDomain();c=c.split(".");c.length>=3?c[0]=a:c.unshift(a);return A(b,{domain:c.join("."),subdomain:a})}return this};b.stripTrailingSlash=function(){return this.setPath(this.getPath().replace(/\/$/,""))};a.$1=function(a){a=a;for(var b of t)a=b(a);return a};a.$2=function(a,b){b=b;for(var c of u)b=c(a,b);return b};b.$3=function(b,c){c===void 0&&(c=!1);return B({domain:a.$1(this.domain),fragment:this.fragment,fragmentSeparator:this.fragmentSeparator,isGeneric:this.isGenericProtocol,originalRawQuery:this.originalRawQuery,path:this.path,port:this.port,protocol:this.protocol,queryParams:a.$2(this.domain,this.queryParams),serializer:b,subdomain:this.subdomain,userInfo:""},!1,c)};b.toStringRawQuery=function(){this.rawStringValue==null&&(this.rawStringValue=this.$3(c("PHPQuerySerializerNoEncoding")));return this.rawStringValue};b.toString=function(){this.stringValue==null&&(this.stringValue=this.$3(this.serializer));return this.stringValue};b.toStringPreserveQuery=function(){return this.$3(this.serializer,!0)};a.isValidUri=function(b){var c=l.get(b);if(c!=null)return!0;c=D(b);if(c.valid){l.set(b,new a(c.components));return!0}return!1};return a}();function a(a){if(a instanceof F)return a;else return null}function b(a){t.push(a)}function e(a){u.push(a)}f=F.getUri;var G=F.getUriOrThrow,H=F.isValidUri;g.isSubdomainOfDomain=y;g.isConstUri=a;g.registerDomainFilter=b;g.registerQueryParamsFilter=e;g.getUri=f;g.getUriOrThrow=G;g.isValidUri=H}),98);
__d("uriIsRelativePath",[],(function(a,b,c,d,e,f){"use strict";function a(a){return!a.getProtocol()&&!a.getDomain()&&!a.getPort()&&a.toString()!==""}f["default"]=a}),66);
__d("routeBuilderUtils",[],(function(a,b,c,d,e,f){"use strict";function a(a){a=a.split("/");return a.filter(function(a){return a!==""}).map(function(a){var b=a.split(/{|}/);if(b.length<3)return{isToken:!1,part:a};else{a=b[0];var c=b[1];b=b[2];var d=c[0]==="?",e=c[d?1:0]==="*";c=c.substring((d?1:0)+(e?1:0));return{isToken:!0,optional:d,catchAll:e,prefix:a,suffix:b,token:c}}})}f.getPathParts=a}),66);
__d("jsRouteBuilder",["ConstUriUtils","FBLogger","routeBuilderUtils"],(function(a,b,c,d,e,f,g){"use strict";var h="#";function a(a,b,e,f,g){g===void 0&&(g=!1);var i=d("routeBuilderUtils").getPathParts(a);function j(j){try{var k=f!=null?babelHelpers["extends"]({},f,j):(j=j)!=null?j:{},l={};j="";var m=!1;j=i.reduce(function(a,c){if(!c.isToken)return a+"/"+c.part;else{var d,e=c.optional,f=c.prefix,g=c.suffix;c=c.token;if(e&&m)return a;d=(d=k[c])!=null?d:b[c];if(d==null&&e){m=!0;return a}if(d==null)throw new Error("Missing required template parameter: "+c);if(d==="")throw new Error("Required template parameter is an empty string: "+c);l[c]=!0;return a+"/"+f+d+g}},"");a.slice(-1)==="/"&&(j+="/");j===""&&(j="/");var n=d("ConstUriUtils").getUri(j);for(var o in k){var p=k[o];!l[o]&&p!=null&&n!=null&&(e!=null&&e.has(o)?p!==!1&&(n=n.addQueryParam(o,null)):n=n.addQueryParam(o,p))}return[n,j]}catch(b){p=b==null?void 0:b.message;o=c("FBLogger")("JSRouteBuilder").blameToPreviousFrame().blameToPreviousFrame();g&&(o=o.blameToPreviousFrame());o.mustfix("Failed building URI for base path: %s message: %s",a,p);return[null,h]}}return{buildUri:function(a){a=(a=j(a)[0])!=null?a:d("ConstUriUtils").getUri(h);if(a==null)throw new Error("Not even the fallback URL parsed validly!");return a},buildUriNullable:function(a){return j(a)[0]},buildURL:function(a){a=j(a);var b=a[0];a=a[1];return(b=b==null?void 0:b.toString())!=null?b:a},buildURLStringDEPRECATED:function(a){a=j(a);var b=a[0];a=a[1];return(b=b==null?void 0:b.toString())!=null?b:a},getPath:function(){return a}}}g["default"]=a}),98);
__d("BaseInlinePressable.react",["CometPressable.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j={defaultCursor:{cursor:"xt0e3qv",$$css:!0},disabled:{textDecoration:"x1hl2dhg",$$css:!0},disabledColor:{color:"x1dntmbh",$$css:!0},disabledLink:{opacity:"xbyyjgo",$$css:!0},expanding:{display:"x3nfvp2",$$css:!0},link:{":hover_textDecoration":"xt0b8zv",$$css:!0},linkColor:{color:"x1fey0fg",$$css:!0},root:{display:"xt0psk2",position:"x1n2onr6",userSelect:"x87ps6o",$$css:!0}};function a(a){var b=d("react-compiler-runtime").c(28),e,f;b[0]!==a?(f=a.ref,e=babelHelpers.objectWithoutPropertiesLoose(a,["ref"]),b[0]=a,b[1]=e,b[2]=f):(e=b[1],f=b[2]);var g,h,k,l,m,n,o,p;if(b[3]!==e){a=e;g=a.ariaLabel;h=a.children;n=a.color;k=a.cursorDisabled;o=a.expanding;l=a.linkProps;m=a.onPress;p=a.xstyle;a=babelHelpers.objectWithoutPropertiesLoose(a,["ariaLabel","children","color","cursorDisabled","expanding","linkProps","onPress","xstyle"]);b[3]=e;b[4]=g;b[5]=h;b[6]=k;b[7]=l;b[8]=m;b[9]=a;b[10]=n;b[11]=o;b[12]=p}else g=b[4],h=b[5],k=b[6],l=b[7],m=b[8],a=b[9],n=b[10],o=b[11],p=b[12];e=n===void 0?"blue":n;n=o===void 0?!1:o;o=k===!0&&j.defaultCursor;k=n&&j.expanding;b[13]!==o||b[14]!==k||b[15]!==p?(n=[j.root,o,k,p],b[13]=o,b[14]=k,b[15]=p,b[16]=n):n=b[16];var q=n,r=e!=="inherit"&&(l!=null||m!=null);b[17]!==q||b[18]!==r?(o=function(a){var b=a.disabled;a=a.hovered;return[q,r&&j.linkColor,a&&!b&&j.link,b&&j.disabled,b&&!r&&j.disabledColor,b&&r&&j.disabledLink]},b[17]=q,b[18]=r,b[19]=o):o=b[19];b[20]!==g||b[21]!==h||b[22]!==l||b[23]!==m||b[24]!==a||b[25]!==f||b[26]!==o?(k=i.jsx(c("CometPressable.react"),babelHelpers["extends"]({"aria-label":g,linkProps:l,onPress:m,overlayDisabled:!0},a,{ref:f,xstyle:o,children:h})),b[20]=g,b[21]=h,b[22]=l,b[23]=m,b[24]=a,b[25]=f,b[26]=o,b[27]=k):k=b[27];return k}g["default"]=a}),98);
__d("CometGlobalKeyCommandWidget",["createKeyCommandWidget"],(function(a,b,c,d,e,f,g){"use strict";a=c("createKeyCommandWidget")(!1);g["default"]=a}),98);
__d("getCometKey",["CometKeys","isStringNullOrEmpty"],(function(a,b,c,d,e,f,g){"use strict";var h={8:"Backspace",13:"Enter",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",46:"Delete"},i=new Set(Object.values(c("CometKeys")));function a(a){var b=a.key;a=a.which||a.keyCode;(a>=48&&a<=57||a>=65&&a<=90)&&(b=String.fromCharCode(a));a>=96&&a<=105&&(b=String.fromCharCode(a-48));if(!c("isStringNullOrEmpty")(b)){b=b.toLowerCase();if(i.has(b))return b}if(Object.prototype.hasOwnProperty.call(h,a)){b=h[a].toLowerCase();if(i.has(b))return b}return null}g["default"]=a}),98);
__d("getKeyCommand",["UserAgent","createKeyCommand","getCometKey"],(function(a,b,c,d,e,f,g){"use strict";function a(a){var b=c("getCometKey")(a);if(b==null)return null;var d=!1;c("UserAgent").isPlatform("Mac OS X")?a.metaKey&&(d=!0):a.ctrlKey&&(d=!0);d={alt:a.altKey,command:d,key:b,shift:a.shiftKey};return c("createKeyCommand")(d)}g["default"]=a}),98);
__d("applyKeyCommand",["getKeyCommand"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,d,e){var f=c("getKeyCommand")(a);if(f==null)return!1;b=b;while(b!=null){if(b&&b.applyCommand(f,a))return!0;b=b&&b.getParent()}if(d!=null&&d.applyCommand(f,a))return!0;return e!=null&&e.applyCommand(f,a)?!0:!1}g["default"]=a}),98);
__d("getActiveCommands",[],(function(a,b,c,d,e,f){"use strict";function a(a,b,c){var d=new Map();function e(a){a.forEach(function(a,b){var c=d.get(b);if(c){var e=c.every(function(a){return a.shouldStopPropagation===!1});e&&c.push(a)}else d.set(b,[a])})}a=a;while(a!=null){var f=a&&a.getCommandMap();e(f);a=a&&a.getParent()}b&&e(b.getCommandMap());c&&e(c.getCommandMap());return d}f["default"]=a}),66);
__d("useGetComposingState",["react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");b.useCallback;var i=b.useEffect,j=b.useRef;function a(){var a=d("react-compiler-runtime").c(5),b=j(!1),c;a[0]===Symbol["for"]("react.memo_cache_sentinel")?(c=function(a){b.current=!0},a[0]=c):c=a[0];var e=c;a[1]===Symbol["for"]("react.memo_cache_sentinel")?(c=function(a){b.current=!1},a[1]=c):c=a[1];var f=c,g;a[2]===Symbol["for"]("react.memo_cache_sentinel")?(c=function(){window.addEventListener("compositionstart",e);window.addEventListener("compositionend",f);return function(){window.removeEventListener("compositionstart",e),window.removeEventListener("compositionend",f)}},g=[f,e],a[2]=c,a[3]=g):(c=a[2],g=a[3]);i(c,g);a[4]===Symbol["for"]("react.memo_cache_sentinel")?(c=function(a){return a.isComposing||b.current},a[4]=c):c=a[4];return c}g["default"]=a}),98);
__d("useGlobalEventListener",["react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useEffect,j={fullscreenchange:["webkitfullscreenchange","mozfullscreenchange","MSFullscreenChange","fullscreenchange"]};a=function(a,b,c){i(function(){if(b!=null){var d,e=(d=j[a])!=null?d:a;window.addEventListener(e,b,c);return function(){window.removeEventListener(e,b,c)}}},[b,a,c])};g["default"]=a}),98);
__d("BaseKeyCommandListener.react",["CometGlobalKeyCommandWidget","CometKeyCommandUtilsContext","ReactDOM","applyKeyCommand","getActiveCommands","getKeyCommand","react","recoverableViolation","stylex","useGetComposingState","useGlobalEventListener","useUnsafeRef_DEPRECATED"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||(j=d("react"));b=j;var l=b.useCallback,m=b.useContext,n=b.useMemo,o=b.useRef;function p(a,b){var c;return function(){window.clearTimeout(c),c=window.setTimeout(a,b)}}var q=100;function a(a){var b=a.children,e=a.observersEnabled;a=a.xstyle;var f=o(null),g=o(null),j=o(new Set()),r=m(c("CometGlobalKeyCommandWidget").Context),s=l(function(a){if(!e)return{getActiveCommands:function(){c("recoverableViolation")("Key Command observers are not supported in this context","comet_ax");return null},remove:function(){}};var b=j.current;b.add(a);return{getActiveCommands:function(){return c("getActiveCommands")(g.current,f.current,r)},remove:function(){b["delete"](a)}}},[r,e]),t=l(function(a){e&&j.current.forEach(function(b){return b({key:a,type:"triggered"})})},[e]),u=n(function(){return p(function(){e&&j.current.forEach(function(a){return a({type:"update"})})},q)},[e]),v=l(function(a){var b=f.current!==a;f.current=a;b&&u()},[u]),w=l(function(a){var b=g.current!==a;g.current=a;b&&u()},[u]);s=(h||(h=c("useUnsafeRef_DEPRECATED")))({addObserver:s,notifyCommandUpdate:u,setActiveLayer:v,setActiveWrapper:w});v=l(function(){var a=g.current!==null;g.current=null;a&&u()},[u]);var x=c("useGetComposingState")();w=l(function(a){if(x(a))return;d("ReactDOM").flushSync(function(){var b=c("applyKeyCommand")(a,g.current,f.current,r);if(b){b=c("getKeyCommand")(a);t(b)}})},[x,r,t]);c("useGlobalEventListener")("keydown",w);c("useGlobalEventListener")("keyup",w);return k.jsx(c("CometKeyCommandUtilsContext").Provider,{value:s.current,children:k.jsx("div",babelHelpers["extends"]({},(i||(i=c("stylex"))).props(a),{onBlurCapture:v,children:b}))})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("BootloaderResource",["ExecutionEnvironment","suspendOrThrowIfUsedInSSR"],(function(a,b,c,d,e,f,g){var h,i,j={};function a(a){a.load()}function b(b){var a=b.getModuleIfRequireable();if(a==null){!(h||(h=c("ExecutionEnvironment"))).isInBrowser&&!b.isAvailableInSSR_DO_NOT_USE()&&(i||(i=c("suspendOrThrowIfUsedInSSR")))("Loading of bootloaded and T3 components is disabled during SSR");var d=b.getModuleId();if(!j[d]){b=j[d]=b.load();b["finally"](function(){delete j[d]})}throw j[d]}return a}g.preload=a;g.read=b}),98);
__d("CometAsyncFetchError",[],(function(a,b,c,d,e,f){"use strict";function g(a){if(a==null)return"";if(typeof a==="string")return a;try{return String.fromCharCode.apply(null,new Uint16Array(a))}catch(a){return"<error parsing ArrayBuffer>"}}a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,d,e,f){var g;g=a.call(this,b)||this;g.errorMsg=b;g.errorCode=c;g.errorRawResponseHeaders=d;g.errorRawTransport=e;g.errorType=f;return g}var c=b.prototype;c.toString=function(){var a;a=((a=this.errorCode)!=null?a:"")+"."+g(this.errorMsg)+"."+((a=this.errorRawResponseHeaders)!=null?a:"")+"."+((a=this.errorRawTransport)!=null?a:"")+"."+((a=this.errorType)!=null?a:"")+"."+((a=this.errorRawTransportStatus)!=null?a:"");return"CometAyncFetchError: "+a};return b}(babelHelpers.wrapNativeSuper(Error));f["default"]=a}),66);
__d("CometAsyncFetchResponse",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a,b){this.$1=a,this.$2=this.$3(b)}var b=a.prototype;b.getFullResponsePayload=function(){return this.$1};b.getResponsePayload=function(){var a;return(a=this.$1)==null?void 0:a.payload};b.getResponseHeader=function(a){var b;return(b=this.$2)==null?void 0:b.get(a.toLowerCase())};b.getAllResponseHeadersMap=function(){return new Map(this.$2)};b.$3=function(a){if(a==null||a.length===0)return null;var b=new Map();a=a.split("\r\n");for(a of a){var c=a.indexOf(": ");if(c<=0)continue;var d=a.substring(0,c).toLowerCase();c=a.substring(c+2);var e=b.get(d);e=e!=null?e+", "+c:c;b.set(d,e)}return b};return a}();f["default"]=a}),66);
__d("CometDialogTransitionTypes",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";a=b("$InternalEnum")({Enter:"comet-dialog-enter",Exit:"comet-dialog-exit"});c=a;f["default"]=c}),66);
__d("CometErrorCodeExtraHandlers",[],(function(a,b,c,d,e,f){"use strict";var g=[];function a(a){g.push(a)}function b(a){a=g.indexOf(a);a>-1&&g.splice(a,1)}function c(a){try{g.forEach(function(b){return b(a)})}catch(a){}}f.addHandler=a;f.removeHandler=b;f.executeHandlers=c}),66);
__d("CometErrorOverlay",["ReactDOM","react"],(function(a,b,c,d,e,f,g){"use strict";var h;h||d("react");function i(){var a=document.body;if(a==null)return null;var b=document.createElement("div");a.appendChild(b);return b}function a(a){var b=i();if(b!=null){var c=function(){window.setTimeout(function(){e.unmount(),b.remove()},0)},e=d("ReactDOM").createRoot(b,{unstable_strictMode:!0});a=a(c);e.render(a);return c}}g.injectComponent=a}),98);
__d("CometEventListener",["unrecoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";function h(a,b,d,e){if(a.addEventListener){a.addEventListener(b,d,e);return{remove:function(){a.removeEventListener(b,d,e)}}}else throw c("unrecoverableViolation")('Attempted to listen to eventType "'+b+'" on a target that does not have addEventListener.',"comet_ui")}a={addListenerWithOptions:function(a,b,c,d){return h(a,b,c,d)},bubbleWithPassiveFlag:function(a,b,c,d){return h(a,b,c,{capture:!1,passive:d})},capture:function(a,b,c){return h(a,b,c,!0)},captureWithPassiveFlag:function(a,b,c,d){return h(a,b,c,{capture:!0,passive:d})},listen:function(a,b,c){return h(a,b,c,!1)},registerDefault:function(a,b){throw c("unrecoverableViolation")("EventListener.registerDefault is not implemented.","comet_ui")},suppress:function(a){a.preventDefault(),a.stopPropagation()}};g["default"]=a}),98);
__d("CometHeroLogging",["hero-tracing"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=d("hero-tracing").HeroLogger}),98);
__d("CometInteractionTracingQPLConfigContext",["qpl","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));f=h;var j=f.useContext;f.useMemo;f={dialogTraceQPLEvent:c("qpl")._(30605361,"6204"),popoverTraceQPLEvent:c("qpl")._(30605361,"6204")};var k=i.createContext(f);function a(){return j(k).dialogTraceQPLEvent}function b(){return j(k).popoverTraceQPLEvent}function e(a){var b=d("react-compiler-runtime").c(6),c=a.children,e=a.dialogTraceQPLEvent;a=a.popoverTraceQPLEvent;var f;b[0]!==e||b[1]!==a?(f={dialogTraceQPLEvent:e,popoverTraceQPLEvent:a},b[0]=e,b[1]=a,b[2]=f):f=b[2];e=f;b[3]!==c||b[4]!==e?(a=i.jsx(k.Provider,{value:e,children:c}),b[3]=c,b[4]=e,b[5]=a):a=b[5];return a}g.defaultInteractionQPLEvents=f;g.useDialogTraceQPLEvent=a;g.usePopoverTraceQPLEvent=b;g.CometInteractionTracingQPLConfigContextProvider=e}),98);
__d("CometLayerKeyCommandWidget",["createKeyCommandWidget"],(function(a,b,c,d,e,f,g){"use strict";a=c("createKeyCommandWidget")(!1);g["default"]=a}),98);
__d("SetActiveLayerIfAttached.react",["CometKeyCommandUtilsContext","CometLayerKeyCommandWidget","HiddenSubtreeContext","react","react-compiler-runtime","recoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");var i=b.useContext,j=b.useEffect;function a(a){a=d("react-compiler-runtime").c(5);var b=i(c("HiddenSubtreeContext")),e=i(c("CometKeyCommandUtilsContext")),f=e&&e.setActiveLayer,g=i(c("CometLayerKeyCommandWidget").Context),h;a[0]!==g||a[1]!==b||a[2]!==f?(e=function(){if(!f){c("recoverableViolation")("The current layer is not wrapped in a *KeyCommandListener","comet_ax");return}if(!g){c("recoverableViolation")("setActiveLayer not wrapped in CometLayerKeyCommandWidget.Wrapper","comet_ax");return}b.hiddenOrBackgrounded||f(g)},h=[g,b,f],a[0]=g,a[1]=b,a[2]=f,a[3]=e,a[4]=h):(e=a[3],h=a[4]);j(e,h);return null}g["default"]=a}),98);
__d("CometLayerKeyCommandWrapper.react",["CometLayerKeyCommandWidget","SetActiveLayerIfAttached.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(6),e=a.children;a=a.debugName;var f;b[0]!==a?(f=i.jsx(c("SetActiveLayerIfAttached.react"),{debugName:a}),b[0]=a,b[1]=f):f=b[1];var g;b[2]!==e||b[3]!==a||b[4]!==f?(g=i.jsxs(c("CometLayerKeyCommandWidget").Wrapper,{debugName:a,children:[f,e]}),b[2]=e,b[3]=a,b[4]=f,b[5]=g):g=b[5];return g}g["default"]=a}),98);
__d("FbtErrorListenerWWW",["FBLogger"],(function(a,b,c,d,e,f,g){a=function(){function a(a){this.$1=a.hash,this.$2=a.translation}var b=a.prototype;b.onStringSerializationError=function(a){var b="Context not logged.";try{var d=JSON.stringify(a);d!=null&&(b=d.substr(0,250))}catch(a){b=a.message}d=(a==null?void 0:(d=a.constructor)==null?void 0:d.name)||"";c("FBLogger")("fbt").blameToPreviousDirectory().blameToPreviousDirectory().mustfix('Converting to a string will drop content data. Hash="%s" Translation="%s" Content="%s" (type=%s,%s)',this.$1,this.$2,b,typeof a,d)};b.onStringMethodUsed=function(a){c("FBLogger")("fbt").blameToPreviousDirectory().blameToPreviousDirectory().mustfix("Error using fbt string. Used method %s on Fbt string. Fbt string is designed to be immutable and should not be manipulated.",a)};b.onMissingParameterError=function(a,b){c("FBLogger")("fbt").blameToPreviousDirectory().blameToPreviousDirectory().mustfix('Expected fbt parameter names (%s) to also contain `%s`. Hash="%s" Translation="%s"',a.join(", "),b,this.$1,this.$2)};return a}();g["default"]=a}),98);
__d("FbtReactUtil",[],(function(a,b,c,d,e,f){a=typeof Symbol==="function"&&Symbol["for"]&&Symbol["for"]("react.element")||60103;var g=!1;b={REACT_ELEMENT_TYPE:a,injectReactShim:function(a){var b={validated:!0};g?Object.defineProperty(a,"_store",{configurable:!1,enumerable:!1,writable:!1,value:b}):a._store=b}};e.exports=b}),null);
__d("FbtResultBase",[],(function(a,b,c,d,e,f){"use strict";var g=function(){function a(a,b){this.$1=a,this.__errorListener=b,this.$3=!1,this.$2=null}var b=a.prototype;b.flattenToArray=function(){return a.flattenToArray(this.$1)};b.getContents=function(){return this.$1};b.toString=function(){if(Object.isFrozen(this))return this.$4();if(this.$3)return"<<Reentering fbt.toString() is forbidden>>";this.$3=!0;try{return this.$4()}finally{this.$3=!1}};b.$4=function(){if(this.$2!=null)return this.$2;var b="",c=this.flattenToArray();for(var d=0;d<c.length;++d){var e=c[d];if(typeof e==="string"||e instanceof a)b+=e.toString();else{var f;(f=this.__errorListener)==null?void 0:f.onStringSerializationError==null?void 0:f.onStringSerializationError(e)}}Object.isFrozen(this)||(this.$2=b);return b};b.toJSON=function(){return this.toString()};a.flattenToArray=function(b){var c=[];for(var d=0;d<b.length;++d){var e=b[d];Array.isArray(e)?c.push.apply(c,a.flattenToArray(e)):e instanceof a?c.push.apply(c,e.flattenToArray()):c.push(e)}return c};return a}();["anchor","big","blink","bold","charAt","charCodeAt","codePointAt","contains","endsWith","fixed","fontcolor","fontsize","includes","indexOf","italics","lastIndexOf","link","localeCompare","match","normalize","repeat","replace","search","slice","small","split","startsWith","strike","sub","substr","substring","sup","toLocaleLowerCase","toLocaleUpperCase","toLowerCase","toUpperCase","trim","trimLeft","trimRight"].forEach(function(a){g.prototype[a]=function(){var b;(b=this.__errorListener)==null?void 0:b.onStringMethodUsed==null?void 0:b.onStringMethodUsed(a);for(var c=arguments.length,d=new Array(c),e=0;e<c;e++)d[e]=arguments[e];return String.prototype[a].apply(this,d)}});a=g;e.exports=a}),null);
__d("FbtResult",["FbtReactUtil","FbtResultBase"],(function(a,b,c,d,e,f){var g=function(a){return a.content};a=function(a){"use strict";babelHelpers.inheritsLoose(c,a);function c(c,d){d=a.call(this,c,d)||this;d.$$typeof=b("FbtReactUtil").REACT_ELEMENT_TYPE;d.key=null;d.ref=null;d.type=g;d.props={content:c};return d}c.get=function(a){return new c(a.contents,a.errorListener)};return c}(b("FbtResultBase"));e.exports=a}),null);
__d("FbtPureStringResult",["FbtResult"],(function(a,b,c,d,e,f){a=function(a){"use strict";babelHelpers.inheritsLoose(b,a);function b(){return a.apply(this,arguments)||this}return b}(b("FbtResult"));c=a;e.exports=c}),null);
__d("getFbsResult",["FbtPureStringResult"],(function(a,b,c,d,e,f){function a(a){return new(b("FbtPureStringResult"))(a.contents,a.errorListener)}e.exports=a}),null);
__d("getTranslatedInput",["Env","ExecutionEnvironment","FBLogger","MakeHasteTranslationsMap"],(function(a,b,c,d,e,f,g){var h,i;b="JHASH";var j=new RegExp("__"+b+"__(.+?)__"+b+"__"),k=!!(h||(h=c("Env"))).use_fbt_virtual_modules;function a(a){var b=a.table;if(k&&(i||(i=c("ExecutionEnvironment"))).isInBrowser){if(typeof b==="string"){var e=b.match(j);if(e!=null)return l(babelHelpers["extends"]({},a,{table:d("MakeHasteTranslationsMap").get(e[1])}))}c("FBLogger")("binary_transparency","inlined_translations").warn("Found inlined translated contents in client_fetch_translations experiment! Input: %s",JSON.stringify(b))}return l(a)}function l(a){var b=a.table;return typeof b==="string"&&b[0]==="_"&&b[1]==="j"&&(b[2]==="{"||b[2]==="[")?babelHelpers["extends"]({},a,{table:JSON.parse(b.substring(2))}):a}g["default"]=a}),98);
__d("translationOverrideListener",["requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h=c("requireDeferred")("IntlQtEventFalcoEvent").__setRef("translationOverrideListener");function a(a){h.onReady(function(b){return b.log(function(){return{hash:a}})})}g["default"]=a}),98);
__d("FbtEnv",["FbtErrorListenerWWW","FbtHooks","IntlViewerContext","cr:7730","getFbsResult","getTranslatedInput","justknobx","promiseDone","requireDeferred","translationOverrideListener"],(function(a,b,c,d,e,f,g){"use strict";var h,i=c("requireDeferred")("FbtLogging").__setRef("FbtEnv");d="JHASH";var j=new RegExp("__"+d+"__(.+?)__"+d+"__"),k=!1;function a(){if(k)return;k=!0;(h||(h=b("FbtHooks"))).register({errorListener:function(a){return new(c("FbtErrorListenerWWW"))(a)},getFbsResult:c("getFbsResult"),getFbtResult:b("cr:7730"),getTranslatedInput:c("getTranslatedInput"),onTranslationOverride:c("translationOverrideListener"),getViewerContext:function(){return c("IntlViewerContext")},logImpression:function(a,b){return c("promiseDone")(i.load().then(function(d){d.logImpression==null?void 0:d.logImpression(a);if(!c("justknobx")._("2269")){var e,f=b==null?void 0:b.inputTable;e=(e=b==null?void 0:b.tokens)!=null?e:[];if(typeof f==="string"){f=f.match(j);f!=null&&(d.logImpressionV2==null?void 0:d.logImpressionV2(f[1],e))}}}))}})}g.setupOnce=a}),98);
__d("FbtHooksImpl",[],(function(a,b,c,d,e,f){var g={};a={getErrorListener:function(a){return g.errorListener==null?void 0:g.errorListener(a)},logImpression:function(a,b){g.logImpression==null?void 0:g.logImpression(a,b)},onTranslationOverride:function(a){g.onTranslationOverride==null?void 0:g.onTranslationOverride(a)},getFbsResult:function(a){return g.getFbsResult(a)},getFbtResult:function(a){return g.getFbtResult(a)},getTranslatedInput:function(a){var b;return(b=g.getTranslatedInput==null?void 0:g.getTranslatedInput(a))!=null?b:a},getViewerContext:function(){return g.getViewerContext()},register:function(a){Object.assign(g,a)}};e.exports=a}),null);
__d("FbtHooks",["FbtEnv","FbtHooksImpl"],(function(a,b,c,d,e,f){e.exports=b("FbtHooksImpl"),b("FbtEnv").setupOnce()}),null);
__d("FbtTable",["invariant"],(function(a,b,c,d,e,f,g){"use strict";var h={ARG:{INDEX:0,SUBSTITUTION:1},access:function(a,b,c,d){if(c>=b.length){typeof a==="string"||Array.isArray(a)||g(0,21388,JSON.stringify(a));return a}var e=b[c];e=e[h.ARG.INDEX];if(e==null)return h.access(a,b,c+1,d);typeof a!=="string"&&!Array.isArray(a)||g(0,20954,typeof a);for(var f=0;f<e.length;++f){var i=a[e[f]];if(i==null)continue;d.push(e[f]);i=h.access(i,b,c+1,d);if(i!=null)return i}return null}};e.exports=h}),null);
__d("FbtTableAccessor",[],(function(a,b,c,d,e,f){"use strict";a={getEnumResult:function(a){return[[a],null]},getGenderResult:function(a,b,c){return[a,b]},getNumberResult:function(a,b,c){return[a,b]},getSubstitution:function(a){return[null,a]},getPronounResult:function(a){return[[a,"*"],null]}};e.exports=a}),null);
__d("GenderConst",[],(function(a,b,c,d,e,f){e.exports={NOT_A_PERSON:0,FEMALE_SINGULAR:1,MALE_SINGULAR:2,FEMALE_SINGULAR_GUESS:3,MALE_SINGULAR_GUESS:4,MIXED_UNKNOWN:5,NEUTER_SINGULAR:6,UNKNOWN_SINGULAR:7,FEMALE_PLURAL:8,MALE_PLURAL:9,NEUTER_PLURAL:10,UNKNOWN_PLURAL:11}}),null);
__d("FbtNumberType",["IntlNumberTypeProps"],(function(a,b,c,d,e,f,g){g["default"]=c("IntlNumberTypeProps").module}),98);
__d("IntlNumberType",["FbtNumberType"],(function(a,b,c,d,e,f,g){a=function(a){return c("FbtNumberType")};g.get=a}),98);
__d("IntlVariationResolverImpl",["invariant","FbtHooks","IntlNumberType","IntlVariations"],(function(a,b,c,d,e,f,g){var h,i="_1";a={EXACTLY_ONE:i,getNumberVariations:function(a){var c=b("IntlNumberType").get((h||(h=b("FbtHooks"))).getViewerContext().locale).getVariation(a);c&b("IntlVariations").BITMASK_NUMBER||g(0,11647,c,typeof c);return a===1?[i,c,"*"]:[c,"*"]},getGenderVariations:function(a){a&b("IntlVariations").BITMASK_GENDER||g(0,11648,a,typeof a);return[a,"*"]}};e.exports=a}),null);
__d("IntlVariationResolver",["IntlVariationHoldout","IntlVariationResolverImpl"],(function(a,b,c,d,e,f,g){a={getNumberVariations:function(a){return d("IntlVariationResolverImpl").getNumberVariations(a)},getGenderVariations:function(a){return d("IntlVariationHoldout").disable_variation?["*"]:d("IntlVariationResolverImpl").getGenderVariations(a)}};b=a;g["default"]=b}),98);
__d("NumberFormatConsts",["NumberFormatConfig"],(function(a,b,c,d,e,f){a={get:function(a){return b("NumberFormatConfig")}};e.exports=a}),null);
__d("escapeRegex",[],(function(a,b,c,d,e,f){"use strict";function a(a){return a.replace(/([.?*+\^$\[\]\\(){}|\-])/g,"\\$1")}e.exports=a}),null);
__d("intlNumUtils",["FbtHooks","NumberFormatConsts","escapeRegex"],(function(a,b,c,d,e,f){var g,h=3;f=["\u0433\u0440\u043d.","\u0434\u0435\u043d.","\u043b\u0432.","\u043c\u0430\u043d.","\u0564\u0580.","\u062c.\u0645.","\u062f.\u0625.","\u062f.\u0627.","\u062f.\u0628.","\u062f.\u062a.","\u062f.\u062c.","\u062f.\u0639.","\u062f.\u0643.","\u062f.\u0644.","\u062f.\u0645.","\u0631.\u0633.","\u0631.\u0639.","\u0631.\u0642.","\u0631.\u064a.","\u0644.\u0633.","\u0644.\u0644.","\u0783.","B/.","Bs.","Fr.","kr.","L.","p.","S/."];var i={};function j(a){i[a]||(i[a]=new RegExp(a,"i"));return i[a]}var k=j(f.reduce(function(a,c,d){return a+(d?"|":"")+"("+b("escapeRegex")(c)+")"},""));function l(a,c,d,e,f,g,i){d===void 0&&(d="");e===void 0&&(e=".");f===void 0&&(f=0);g===void 0&&(g={primaryGroupSize:h,secondaryGroupSize:h});var k=g.primaryGroupSize||h;g=g.secondaryGroupSize||k;i=i&&i.digits;var l;c==null?l=a.toString():typeof a==="string"?l=r(a,c):l=p(a,c);a=l.split(".");c=a[0];l=a[1];if(Math.abs(parseInt(c,10)).toString().length>=f){a="$1"+d+"$2$3";f="(\\d)(\\d{"+(k-0)+"})($|\\D)";k=c.replace(j(f),a);if(k!=c){c=k;f="(\\d)(\\d{"+(g-0)+"})("+b("escapeRegex")(d)+")";g=j(f);while((k=c.replace(g,a))!=c)c=k}}i!=null&&(c=m(c,i),l=l&&m(l,i));d=c;l&&(d+=e+l);return d}function m(a,b){var c="";for(var d=0;d<a.length;++d){var e=b[a.charCodeAt(d)-48];c+=e!==void 0?e:a[d]}return c}function a(a,c){var d=b("NumberFormatConsts").get((g||(g=b("FbtHooks"))).getViewerContext().locale);return l(a,c,"",d.decimalSeparator,d.minDigitsForThousandsSeparator,d.standardDecimalPatternInfo,d.numberingSystemData)}function n(a,c){var d=b("NumberFormatConsts").get((g||(g=b("FbtHooks"))).getViewerContext().locale);return l(a,c,d.numberDelimiter,d.decimalSeparator,d.minDigitsForThousandsSeparator,d.standardDecimalPatternInfo,d.numberingSystemData)}function o(a){return a&&Math.floor(Math.log10(Math.abs(a)))}function c(a,b,c){var d=o(a),e=a;d<c&&(e=a*Math.pow(10,-d+c));a=Math.pow(10,o(e)-c+1);e=Math.round(e/a)*a;if(d<c){e/=Math.pow(10,-d+c);if(b==null)return n(e,c-d-1)}return n(e,b)}function p(a,b){b=b==null?0:b;var c=Math.pow(10,b);a=(Math.round(a*c)/c).toString();if(!b)return a;if(a.indexOf("e-")!==-1)return a;c=a.indexOf(".");var d;c===-1?(a+=".",d=b):d=b-(a.length-c-1);for(b=0,c=d;b<c;b++)a+="0";return a}var q=function(a,b){a=a;for(var c=0;c<b;c++)a+="0";return a};function r(a,b){var c=a.indexOf("."),d=c===-1?a:a.slice(0,c);a=c===-1?"":a.slice(c+1);return b!=null?d+"."+q(a.slice(0,b),b-a.length):d}function s(a,c,d){d===void 0&&(d="");var e=u(),f=a;e&&(f=a.split("").map(function(a){return e[a]||a}).join("").trim());f=f.replace(/^[^\d]*\-/,"\x02");f=f.replace(k,"");a=b("escapeRegex")(c);c=b("escapeRegex")(d);d=j("^[^\\d]*\\d.*"+a+".*\\d[^\\d]*$");if(!d.test(f)){d=j("(^[^\\d]*)"+a+"(\\d*[^\\d]*$)");if(d.test(f)){f=f.replace(d,"$1\x01$2");return t(f)}d=j("^[^\\d]*[\\d "+b("escapeRegex")(c)+"]*[^\\d]*$");d.test(f)||(f="");return t(f)}d=j("(^[^\\d]*[\\d "+c+"]*)"+a+"(\\d*[^\\d]*$)");f=d.test(f)?f.replace(d,"$1\x01$2"):"";return t(f)}function t(a){a=a.replace(/[^0-9\u0001\u0002]/g,"").replace("\x01",".").replace("\x02","-");var b=Number(a);return a===""||isNaN(b)?null:b}function u(){var a=b("NumberFormatConsts").get((g||(g=b("FbtHooks"))).getViewerContext().locale),c={};a=a.numberingSystemData&&a.numberingSystemData.digits;if(a==null)return null;for(var d=0;d<a.length;d++)c[a.charAt(d)]=d.toString();return c}function d(a){var c=b("NumberFormatConsts").get((g||(g=b("FbtHooks"))).getViewerContext().locale);return s(a,c.decimalSeparator||".",c.numberDelimiter)}var v={formatNumber:a,formatNumberRaw:l,formatNumberWithThousandDelimiters:n,formatNumberWithLimitedSigFig:c,parseNumber:d,parseNumberRaw:s,truncateLongNumber:r,getFloatString:function(a,b,c){a=String(a);a=a.split(".");b=v.getIntegerString(a[0],b);return a.length===1?b:b+c+a[1]},getIntegerString:function(a,b){b=b;b===""&&(b=",");a=String(a);var c=/(\d+)(\d{3})/;while(c.test(a))a=a.replace(c,"$1"+b+"$2");return a}};e.exports=v}),null);
__d("IntlPhonologicalRewrites",["IntlPhonologicalRules"],(function(a,b,c,d,e,f){"use strict";a={get:function(a){return b("IntlPhonologicalRules")}};e.exports=a}),null);
__d("IntlRedundantStops",[],(function(a,b,c,d,e,f){a=Object.freeze({equivalencies:{".":["\u0964","\u104b","\u3002"],"\u2026":["\u0e2f","\u0eaf","\u1801"],"!":["\uff01"],"?":["\uff1f"]},redundancies:{"?":["?",".","!","\u2026"],"!":["!","?","."],".":[".","!"],"\u2026":["\u2026",".","!"]}});f["default"]=a}),66);
__d("IntlPunctuation",["FbtHooks","IntlPhonologicalRewrites","IntlRedundantStops"],(function(a,b,c,d,e,f,g){var h;d="[.!?\u3002\uff01\uff1f\u0964\u2026\u0eaf\u1801\u0e2f\uff0e]";var i={};function j(a){var b;b=(b=a)!=null?b:"";var c=i[b];c==null&&(c=i[b]=k(a));return c}function k(a){var b=[];a=c("IntlPhonologicalRewrites").get(a);for(var d in a.patterns){var e=a.patterns[d];for(var f in a.meta){var g=new RegExp(f.slice(1,-1),"g"),h=a.meta[f];d=d.replace(g,h);e=e.replace(g,h)}e==="javascript"&&(e=function(a){return a.slice(1).toLowerCase()});b.push([new RegExp(d.slice(1,-1),"g"),e])}return b}function a(a){var b=j((h||(h=c("FbtHooks"))).getViewerContext().locale);a=a;for(var d=0;d<b.length;d++){var e=b[d],f=e[0];e=e[1];a=a.replace(f,e)}return a.replace(/\x01/g,"")}var l=new Map();for(e in c("IntlRedundantStops").equivalencies)for(f of[e].concat(c("IntlRedundantStops").equivalencies[e]))l.set(f,e);var m=new Map();for(f in c("IntlRedundantStops").redundancies)m.set(f,new Set(c("IntlRedundantStops").redundancies[f]));function n(a,b){a=l.get(a);b=l.get(b);return((a=m.get(a))==null?void 0:a.has(b))===!0}function b(a,b){return n(a[a.length-1],b)?"":b}g.PUNCT_CHAR_CLASS=d;g.applyPhonologicalRules=a;g.dedupeStops=b}),98);
__d("substituteTokens",["invariant","IntlPunctuation"],(function(a,b,c,d,e,f,g,h){var i=Object.prototype.hasOwnProperty,j=new RegExp("\\{([^}]+)\\}("+d("IntlPunctuation").PUNCT_CHAR_CLASS+"*)","g");function k(a){return a}function a(a,b,c){if(b==null)return a;typeof b==="object"||h(0,6041,a);var e=[],f=[];a=a.replace(j,function(a,g,h){i.call(b,g)||(c==null?void 0:c.onMissingParameterError==null?void 0:c.onMissingParameterError(Object.keys(b),g));a=b[g];if(a!=null&&typeof a==="object"){e.push(a);f.push(g);return"\x17"+h}else if(a==null)return"";return String(a)+d("IntlPunctuation").dedupeStops(String(a),h)}).split("\x17").map(d("IntlPunctuation").applyPhonologicalRules);if(a.length===1)return a[0];var g=a[0]!==""?[a[0]]:[];for(var l=0;l<e.length;l++)g.push(k(e[l])),a[l+1]!==""&&g.push(a[l+1]);return g}f.exports=a}),34);
__d("fbt",["invariant","FbtEnv","FbtHooks","FbtQTOverrides","FbtResultBase","FbtTable","FbtTableAccessor","GenderConst","IntlVariationResolver","intlNumUtils","substituteTokens"],(function(a,b,c,d,e,f,g){var h;b("FbtEnv").setupOnce();var i=b("FbtQTOverrides").overrides,j=b("IntlVariationResolver").getGenderVariations,k=b("IntlVariationResolver").getNumberVariations,l=Object.prototype.hasOwnProperty,m=!1,n=b("FbtTable").ARG,o={number:0,gender:1},p={object:0,possessive:1,reflexive:2,subject:3},q={};function a(a,c,d){if(((d==null?void 0:d.hk)||(d==null?void 0:d.ehk))&&m)return{text:a,fbt:!0,hashKey:d.hk};c=(h||(h=b("FbtHooks"))).getTranslatedInput({table:a,args:c,options:d});var e=c.args;c=c.table;var f={};if(c.__vcg!=null){e=e||[];var k=(h||(h=b("FbtHooks"))).getViewerContext();k=k.GENDER;var l=j(k);e.unshift(b("FbtTableAccessor").getGenderResult(l,null,k))}l=[];e&&(typeof c!=="string"&&(c=b("FbtTable").access(c,e,0,l)),f=r(e),c!==null||g(0,479));var n;if(Array.isArray(c)){k=c[0];n=c[1];e="1_"+n;i[e]!=null&&i[e]!==""&&(k=i[e],(h||(h=b("FbtHooks"))).onTranslationOverride(n));e={inputTable:a,tokens:l};(h||(h=b("FbtHooks"))).logImpression(n,e)}else if(typeof c==="string")k=c;else throw new Error("Table access did not result in string: "+(c===void 0?"undefined":JSON.stringify(c))+", Type: "+typeof c);a=this.cachedResults[k];l=s(f);if(a&&!l)return a;else{e=b("substituteTokens")(k,f,(h||(h=b("FbtHooks"))).getErrorListener==null?void 0:(h||(h=b("FbtHooks"))).getErrorListener({translation:k,hash:n}));c=this._wrapContent(e,k,n,d==null?void 0:d.eo);l||(this.cachedResults[k]=c);return c}}function r(a){var b={};a.forEach(function(a){a=a[n.SUBSTITUTION];if(!a)return;for(var c in a)l.call(a,c)&&(b[c]==null||g(0,56656,c),b[c]=a[c])});return b}function s(a){for(a in a)return!0;return!1}function c(a,c){return b("FbtTableAccessor").getEnumResult(a)}function d(a){return b("FbtTableAccessor").getGenderResult(j(a),null,a)}function f(a,c,d){var e;e=(e={},e[a]=c,e);if(d)if(d[0]===o.number){var f=d.length>1?d[1]:c;typeof f==="number"||g(0,484);var h=k(f);typeof c==="number"&&(e[a]=b("intlNumUtils").formatNumberWithThousandDelimiters(c));return b("FbtTableAccessor").getNumberResult(h,e,f)}else if(d[0]===o.gender){a=d[1];a!=null||g(0,485);return b("FbtTableAccessor").getGenderResult(j(a),e,a)}else g(0,486);else return b("FbtTableAccessor").getSubstitution(e)}function t(a,b,c){return this._param(a,b,c)}function u(a,c,d){var e=k(a),f={};c&&(typeof d==="number"?f[c]=b("intlNumUtils").formatNumberWithThousandDelimiters(d):f[c]=d||b("intlNumUtils").formatNumberWithThousandDelimiters(a));return b("FbtTableAccessor").getNumberResult(e,f,a)}function v(a,c,d){c!==b("GenderConst").NOT_A_PERSON||!d||!d.human||g(0,11835);d=w(a,c);return b("FbtTableAccessor").getPronounResult(d)}function w(a,c){switch(c){case b("GenderConst").NOT_A_PERSON:return a===p.object||a===p.reflexive?b("GenderConst").NOT_A_PERSON:b("GenderConst").UNKNOWN_PLURAL;case b("GenderConst").FEMALE_SINGULAR:case b("GenderConst").FEMALE_SINGULAR_GUESS:return b("GenderConst").FEMALE_SINGULAR;case b("GenderConst").MALE_SINGULAR:case b("GenderConst").MALE_SINGULAR_GUESS:return b("GenderConst").MALE_SINGULAR;case b("GenderConst").MIXED_UNKNOWN:case b("GenderConst").FEMALE_PLURAL:case b("GenderConst").MALE_PLURAL:case b("GenderConst").NEUTER_PLURAL:case b("GenderConst").UNKNOWN_PLURAL:return b("GenderConst").UNKNOWN_PLURAL;case b("GenderConst").NEUTER_SINGULAR:case b("GenderConst").UNKNOWN_SINGULAR:return a===p.reflexive?b("GenderConst").NOT_A_PERSON:b("GenderConst").UNKNOWN_PLURAL}return b("GenderConst").NOT_A_PERSON}function x(a,c,d){var e=j(d),f={};f[a]=c;return b("FbtTableAccessor").getGenderResult(e,f,d)}function y(a,c,d,e){a=typeof a==="string"?[a]:a;var f=(h||(h=b("FbtHooks"))).getErrorListener({translation:c,hash:d});a=h.getFbtResult({contents:a,errorListener:f,extraOptions:e,patternHash:d,patternString:c});return a}function z(){m=!0}function A(){m=!1}function B(a){return a instanceof b("FbtResultBase")}var C=function(){};C._=a;C._enum=c;C._implicitParam=t;C._name=x;C._param=f;C._plural=u;C._pronoun=v;C._subject=d;C._wrapContent=y;C.disableJsonExportMode=A;C.enableJsonExportMode=z;C.isFbtInstance=B;C.cachedResults=q;C._getCachedFbt=void 0;a=C;e.exports=a}),null);
__d("useIsCalledDuringRender",["FBLogger","react"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||(h=d("react"));e=h;var i=e.useCallback,j=e.useRef;f=b.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function a(){var a=j(void 0);return i(function(){c("FBLogger")("comet_ui").blameToPreviousFrame().warn("useIsCalledDuringRender should only be used for development purpose. It is implemented in a way that will not work correctly in production.");return!1},[])}g["default"]=a}),98);
__d("useIsMountedRef",["react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");var i=b.useLayoutEffect,j=b.useRef;function a(){var a=d("react-compiler-runtime").c(2),b=j(!1),c,e;a[0]===Symbol["for"]("react.memo_cache_sentinel")?(c=function(){b.current=!0;return function(){b.current=!1}},e=[],a[0]=c,a[1]=e):(c=a[0],e=a[1]);i(c,e);return b}g["default"]=a}),98);
__d("CometTransientDialogProvider.react",["fbt","BaseModal.react","CometDialogContext","CometDialogTransitionTypes","CometErrorBoundary.react","CometHeroLogging","CometInteractionTracingQPLConfigContext","FBLogger","cometPushToast","cr:945","react","react-compiler-runtime","useCometInteractionTracing","useIsCalledDuringRender","useIsMountedRef"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=i||(i=d("react"));e=i;var k=e.startTransition,l=e.unstable_addTransitionType,m=e.useCallback,n=e.useEffect,o=e.useRef,p=e.useState;function q(a){var b=a.dialogConfig,e=a.dialogConfigsRef,f=a.removeDialogConfig,g=o(null);n(function(){return function(){g.current!=null&&window.cancelAnimationFrame(g.current)}},[]);a=b.dialog;var i=b.dialogProps,q=p(!1),r=q[0];q=q[1];var s=m(function(){for(var a=arguments.length,d=new Array(a),h=0;h<a;h++)d[h]=arguments[h];g.current!=null&&window.cancelAnimationFrame(g.current);var i=e.current.indexOf(b);i<0&&c("FBLogger")("comet_ui").blameToPreviousFrame().mustfix("Attempting to close a dialog that does not exist anymore.");g.current=window.requestAnimationFrame(function(){b.unstable_animateExit?k(function(){l(c("CometDialogTransitionTypes").Exit),f(b,d)}):f(b,d),g.current=null})},[b,e,f]),t=m(function(){s(),d("cometPushToast").cometPushErrorToast({message:h._(/*BTDS*/"Something isn't working. This may be because of a technical error that we're working to fix."),truncateText:!1})},[s]);a=j.jsx(a,babelHelpers["extends"]({},i,{onClose:s,onHide:q}));return j.jsx(c("CometErrorBoundary.react"),{onError:t,children:j.jsx(c("BaseModal.react"),{backdropXStyle:(i=b.baseModalProps)==null?void 0:i.backdropXStyle,disableGeoToCometModalsCompatibility_DO_NOT_USE:(q=b.baseModalProps)==null?void 0:q.disableGeoToCometModalsCompatibility_DO_NOT_USE,hidden:r,interactionDesc:b.interactionDesc,interactionUUID:b.interactionUUID,isOverlayTransparent:(t=b.baseModalProps)==null?void 0:t.isOverlayTransparent,stackingBehavior:"above-nav",unstable_animateEnter:(i=b.baseModalProps)==null?void 0:i.unstable_animateEnter,unstable_animateExit:(q=b.baseModalProps)==null?void 0:q.unstable_animateExit,children:a})})}q.displayName=q.name+" [from "+f.id+"]";function a(a){var e=d("react-compiler-runtime").c(18),f;e[0]===Symbol["for"]("react.memo_cache_sentinel")?(f=[],e[0]=f):f=e[0];f=p(f);var g=f[0],h=f[1];f=d("CometInteractionTracingQPLConfigContext").useDialogTraceQPLEvent();var i=c("useCometInteractionTracing")(f,"fluid","INTERACTION");f=c("useIsCalledDuringRender")();var m;e[1]!==f||e[2]!==i?(m=function(a,d,e,f,g){var j=e.loadType,m=e.preloadTrigger,n=e.tracePolicy;i(function(e){var i=c("CometHeroLogging").genHeroInteractionUUIDAndMarkStart(e.getTraceId());e.addMetadata("interaction_type","dialog");e.addMetadata("load_type",j);m!=null&&e.addMetadata("preload_trigger",m);var o=function(){h(function(b){var c,e;c=(c=g==null?void 0:g.replaceCurrentDialog)!=null?c:!1;e={baseModalProps:g==null?void 0:g.baseModalProps,dialog:a,dialogProps:d,interactionDesc:"Dialog",interactionUUID:i,onClose:f,tracePolicy:n,unstable_animateEnter:(g==null?void 0:(e=g.baseModalProps)==null?void 0:e.unstable_animateEnter)===!0,unstable_animateExit:(g==null?void 0:(e=g.baseModalProps)==null?void 0:e.unstable_animateExit)===!0};return c?b.slice(0,b.length-1).concat(e):b.concat(e)})};(g==null?void 0:(e=g.baseModalProps)==null?void 0:e.unstable_animateEnter)===!0?k(function(){l(c("CometDialogTransitionTypes").Enter),o()}):o();b("cr:945")&&b("cr:945").logOpen(n,i)},void 0,void 0,n)},e[1]=f,e[2]=i,e[3]=m):m=e[3];f=m;var r=o(g),s;e[4]!==g?(m=function(){r.current=g},s=[g],e[4]=g,e[5]=m,e[6]=s):(m=e[5],s=e[6]);n(m,s);var t=c("useIsMountedRef")();e[7]!==t?(m=function(a,c){if(!t.current)return;h(function(b){var c=b.indexOf(a);return c<0?b:b.slice(0,c)});a.onClose&&a.onClose.apply(a,c);b("cr:945")&&b("cr:945").logClose(a.tracePolicy,a.interactionUUID)},e[7]=t,e[8]=m):m=e[8];var u=m;s=a.children;if(e[9]!==g||e[10]!==u){e[12]!==u?(m=function(a,b){return j.jsx(q,{dialogConfig:a,dialogConfigsRef:r,removeDialogConfig:u},b)},e[12]=u,e[13]=m):m=e[13];m=g.map(m);e[9]=g;e[10]=u;e[11]=m}else m=e[11];e[14]!==f||e[15]!==a.children||e[16]!==m?(s=j.jsxs(c("CometDialogContext").Provider,{value:f,children:[s,m]}),e[14]=f,e[15]=a.children,e[16]=m,e[17]=s):s=e[17];return s}g["default"]=a}),226);
__d("CurrentMessengerUser",["CurrentEnvironment","CurrentUser"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("CurrentUser").getID()}function b(){return c("CurrentEnvironment").instagramdotcom?c("CurrentUser").getEIMU():c("CurrentUser").getID()}function d(){return c("CurrentUser").getPageMessagingMailboxId()}function e(){return c("CurrentUser").isWorkUser()}function f(){return c("CurrentUser").isTestUser()}function h(){return c("CurrentUser").isEmployee()}function i(){return c("CurrentUser").getAppID()}function j(){return c("CurrentUser").getAccountID()}function k(){return c("CurrentUser").isLoggedInNow()}g.getID=a;g.getIDorEIMU=b;g.getPageMessagingMailboxId=d;g.isWorkUser=e;g.isTestUser=f;g.isEmployee=h;g.getAppID=i;g.getAccountID=j;g.isLoggedInNow=k}),98);
__d("DataStore",["DataStoreConfig","gkx","isEmpty"],(function(a,b,c,d,e,f){"use strict";var g,h=b("DataStoreConfig").expandoKey,i=b("DataStoreConfig").useExpando,j=b("gkx")("25572")&&window.WeakMap?new window.WeakMap():null,k={},l=1;function m(a){if(typeof a==="string")return"str_"+a;else{var b;return"elem_"+((b=a.__FB_TOKEN)!=null?b:a.__FB_TOKEN=[l++])[0]}}function n(a){if(j!=null&&typeof a==="object"){j.get(a)===void 0&&j.set(a,{});return j.get(a)}else if(i&&typeof a==="object")return a[h]||(a[h]={});a=m(a);return k[a]||(k[a]={})}var o={set:function(a,b,c){if(!a)throw new TypeError("DataStore.set: namespace is required, got "+typeof a);var d=n(a);d[b]=c;return a},get:function(a,b,c){if(!a)throw new TypeError("DataStore.get: namespace is required, got "+typeof a);var d=n(a),e=d[b];if(e===void 0&&a.getAttribute!=null)if(a.hasAttribute!=null&&!a.hasAttribute("data-"+b))e=void 0;else{a=a.getAttribute("data-"+b);e=a===null?void 0:a}c!==void 0&&e===void 0&&(e=d[b]=c);return e},remove:function(a,c){if(!a)throw new TypeError("DataStore.remove: namespace is required, got "+typeof a);var d=n(a),e=d[c];delete d[c];(g||(g=b("isEmpty")))(d)&&o.purge(a);return e},purge:function(a){if(j!=null&&typeof a==="object")return j["delete"](a);else i&&typeof a==="object"?delete a[h]:delete k[m(a)]},_storage:k};e.exports=o}),null);
__d("Dsp2BaseThemeGated.react",["cr:21097"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=b("cr:21097")}),98);
__d("IntlCLDRNumberType09",["IntlVariations"],(function(a,b,c,d,e,f,g){"use strict";a={getVariation:function(a){if(a===1)return c("IntlVariations").NUMBER_ONE;else return c("IntlVariations").NUMBER_OTHER}};b=a;g["default"]=b}),98);
__d("LogHistory",[],(function(a,b,c,d,e,f){var g=500,h={},i=[];function j(a,b,c,d){var e=d[0];if(typeof e!=="string"||d.length!==1)return;i.push({date:Date.now(),level:a,category:b,event:c,args:e});i.length>g&&i.shift()}var k=function(){function a(a){this.category=a}var b=a.prototype;b.debug=function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];j("debug",this.category,a,c);return this};b.log=function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];j("log",this.category,a,c);return this};b.warn=function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];j("warn",this.category,a,c);return this};b.error=function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];j("error",this.category,a,c);return this};return a}();function a(a){h[a]||(h[a]=new k(a));return h[a]}function b(){return i}function c(){i.length=0}function d(a){return a.map(function(a){var b=new Date(a.date).toISOString();return[b,a.level,a.category,a.event,a.args].join(" | ")}).join("\n")}f.getInstance=a;f.getEntries=b;f.clearEntries=c;f.formatEntries=d}),66);
__d("OutsideExceptionKeyCommandListener.react",["BaseKeyCommandListener.react","CometLayerKeyCommandWrapper.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(2);a=a.children;var e;b[0]!==a?(e=i.jsx(c("BaseKeyCommandListener.react"),{observersEnabled:!1,children:i.jsx(c("CometLayerKeyCommandWrapper.react"),{debugName:"outside exception layer",children:a})}),b[0]=a,b[1]=e):e=b[1];return e}g["default"]=a}),98);
__d("PlaybackSpeedExperiments",["CurrentUser","gkx"],(function(a,b,c,d,e,f,g){"use strict";function h(){return c("gkx")("26216")}function i(){return!1}function a(){return h()||i()?!0:!1}function j(){return i()?!1:!1}function b(){return c("CurrentUser").getID()!=="0"}function d(){if(h())return!1;if(i())return!0;return j()?!1:!1}function e(){return!0}g.enableWwwPlaybackSpeedControl=a;g.isInCometHeadroomTest=j;g.enableCometPlaybackSpeedControl=b;g.enableCometPlaybackSpeedControlNUX=d;g.enablePlaybackSpeedLogging=e}),98);
__d("ServerHTML.react",["react"],(function(a,b,c,d,e,f,g){var h,i=h||d("react");a=function(a){babelHelpers.inheritsLoose(b,a);function b(){var b,c;for(var d=arguments.length,e=new Array(d),f=0;f<d;f++)e[f]=arguments[f];return(b=c=a.call.apply(a,[this].concat(e))||this,c.elementRef=i.createRef(),b)||babelHelpers.assertThisInitialized(c)}var c=b.prototype;c.render=function(){var a=this.props,b=a.blob;a=babelHelpers.objectWithoutPropertiesLoose(a,["blob"]);return typeof b==="string"?i.jsx("div",babelHelpers["extends"]({},a,{ref:this.elementRef,children:b})):i.jsx("div",babelHelpers["extends"]({},a,{ref:this.elementRef,dangerouslySetInnerHTML:b}))};return b}(i.Component);g["default"]=a}),98);
__d("SubscriptionList",["recoverableViolation"],(function(a,b,c,d,e,f,g){a=function(){function a(a,b){this.$1=[],this.$2=a,this.$3=b}var b=a.prototype;b.add=function(a){var b=this,d={callback:a};this.$1.push(d);this.$2&&this.$1.length===1&&this.$2();return{remove:function(){var a=b.$1.indexOf(d);if(a===-1){c("recoverableViolation")("SubscriptionList: Callback already removed.","SubscriptionList");return}b.$1.splice(a,1);b.$3&&b.$1.length===0&&b.$3()}}};b.getCallbacks=function(){return this.$1.map(function(a){return a.callback})};b.fireCallbacks=function(a){this.getCallbacks().forEach(function(b){b(a)})};return a}();g["default"]=a}),98);
__d("TrustedTypesNoOpPolicy_DO_NOT_USE",["TrustedTypes","TrustedTypesUtils"],(function(a,b,c,d,e,f,g){"use strict";a={createScriptURL:d("TrustedTypesUtils").noop,createHTML:d("TrustedTypesUtils").noop,createScript:d("TrustedTypesUtils").noop};b=c("TrustedTypes").createPolicy("noop-do-not-use",a);e=b;g["default"]=e}),98);
__d("UserMismatchExpected",[],(function(a,b,c,d,e,f){"use strict";var g=!1;function a(a){g=a}function b(){return g}f.setIsUserMismatchExpected=a;f.getIsUserMismatchExpected=b}),66);
__d("isSSR",["ExecutionEnvironment","XPlatReactEnvironment"],(function(a,b,c,d,e,f,g){"use strict";var h;a=d("XPlatReactEnvironment").isWeb()&&!(h||(h=c("ExecutionEnvironment"))).canUseDOM;b=a;g["default"]=b}),98);
__d("VideoPlayerConnectionQuality",["isSSR"],(function(a,b,c,d,e,f,g){"use strict";var h={POOR:"POOR",MODERATE:"MODERATE",GOOD:"GOOD",EXCELLENT:"EXCELLENT"},i=[{bandwidth:5e5,connectionQuality:h.POOR},{bandwidth:2e6,connectionQuality:h.MODERATE},{bandwidth:1e7,connectionQuality:h.GOOD}],j=100,k=null,l=null;a=function(a){if(c("isSSR"))return"MODERATE";if(k!==null&&l!==null&&k>=Date.now()-j)return l;a=a();var b=null;if(a!=null)for(var d=0;d<i.length;d++)if(a<i[d].bandwidth){b=i[d].connectionQuality;break}b===null&&(b=h.EXCELLENT);k=Date.now();l=b;return b};g.evaluate=a}),98);
__d("goForceFullPageRedirectTo",["ConstUriUtils","FBLogger","err"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){var e=typeof a==="string"?d("ConstUriUtils").getUri(a):d("ConstUriUtils").isConstUri(a);if(e)b===!0?window.location.replace(e.toString()):window.location=e.toString();else{b="Invalid URI "+a.toString()+" provided to goFullPageRedirectTo";c("FBLogger")("comet_infra").blameToPreviousFrame().mustfix(b)}}g["default"]=a}),98);
__d("handleCheckpointRedirect",["ConstUriUtils","goForceFullPageRedirectTo"],(function(a,b,c,d,e,f,g){"use strict";function a(a){a=(a=d("ConstUriUtils").getUri(a))==null?void 0:a.addQueryParam("next",location.toString());c("goForceFullPageRedirectTo")((a=a)!=null?a:"/")}g["default"]=a}),98);
__d("handleErrorCodeBasicSideEffects",["errorCode","UserMismatchExpected"],(function(a,b,c,d,e,f,g,h){"use strict";function a(a){if(a===1357032&&d("UserMismatchExpected").getIsUserMismatchExpected()===!0)return!0;if(a===1357001||a===1357032){window.location.reload(!0);return!0}return!1}g["default"]=a}),98);
__d("lazyLoadComponent",["BootloaderResource","cr:2448","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){var h,i=h||d("react"),j=new Map();function k(a,b){j.set(a,b)}function l(a){return j.get(a)}function a(a){var c=l(a);if(c)return c;function e(c){var e=d("react-compiler-runtime").c(6),f,g;e[0]!==c?(g=c.ref,f=babelHelpers.objectWithoutPropertiesLoose(c,["ref"]),e[0]=c,e[1]=f,e[2]=g):(f=e[1],g=e[2]);c=g===void 0?void 0:g;e[3]===Symbol["for"]("react.memo_cache_sentinel")?(g=d("BootloaderResource").read(a),e[3]=g):g=e[3];g=g;b("cr:2448")(a);e[4]!==f?(g=i.jsx(g,babelHelpers["extends"]({},f,{ref:c})),e[4]=f,e[5]=g):g=e[5];return g}e.displayName="lazyLoadComponent("+a.getModuleId()+")";k(a,e);return e}g["default"]=a}),98);
__d("handleCometErrorCodeSideEffects",["errorCode","fbt","CometErrorCodeExtraHandlers","CometErrorOverlay","CometPlaceholder.react","FDSDialogLoadingState.react","JSResourceForInteraction","NewsRegulationErrorMessageData","OutsideExceptionKeyCommandListener.react","ServerHTML.react","cr:9610","deferredLoadComponent","handleCheckpointRedirect","handleErrorCodeBasicSideEffects","lazyLoadComponent","react","requireDeferredForDisplay"],(function(a,b,c,d,e,f,g,h,i){"use strict";var j,k=j||d("react"),l=c("deferredLoadComponent")(c("requireDeferredForDisplay")("CometExceptionDialog.react").__setRef("handleCometErrorCodeSideEffects")),m=c("lazyLoadComponent")(c("JSResourceForInteraction")("CometNewsRegulationDialog.react").__setRef("handleCometErrorCodeSideEffects")),n=new Set();function o(a){return typeof a==="object"&&a!=null&&Object.prototype.hasOwnProperty.call(a,"__html")}function p(a,b,e){d("CometErrorOverlay").injectComponent(function(d){return k.jsx(c("OutsideExceptionKeyCommandListener.react"),{children:k.jsx(c("CometPlaceholder.react"),{fallback:k.jsx(c("FDSDialogLoadingState.react"),{}),children:k.jsx(m,{errorDescription:b,onClose:function(){n["delete"](a),d()},regulationType:e})})})})}function q(a,b,e,f){f===void 0&&(f=null);var g=e,h=b;g=k.jsx(c("ServerHTML.react"),{blob:g});o(h)&&(h=i._(/*BTDS*/"Something went wrong."));d("CometErrorOverlay").injectComponent(function(b){return k.jsx(c("OutsideExceptionKeyCommandListener.react"),{children:k.jsx(c("CometPlaceholder.react"),{fallback:k.jsx(c("FDSDialogLoadingState.react"),{}),children:k.jsx(l,{debugInfo:f,errorDescription:g,errorSummary:h,onClose:function(){n["delete"](a),b()},testid:void 0})})})})}function a(a,e,f,g,h,i){g===void 0&&(g=null);h===void 0&&(h=!0);i===void 0&&(i=null);if(b("cr:9610")==null?void 0:b("cr:9610")(a))return;if(!c("handleErrorCodeBasicSideEffects")(a))if(a===1357053&&g!=null)c("handleCheckpointRedirect")(g);else if(!n.has(a)){d("CometErrorCodeExtraHandlers").executeHandlers(a);n.add(a);if(h){g=c("NewsRegulationErrorMessageData").errorCodeToRegType[a];g!=null?p(a,f,g):q(a,e,f,i)}}}g["default"]=a}),226);
__d("isArDotMetaDotComURI",[],(function(a,b,c,d,e,f){var g=new RegExp("(^|\\.)ar\\.meta\\.com$","i"),h=["https"];function a(a){if(a.isEmpty()&&a.toString()!=="#")return!1;return!a.getDomain()&&!a.getProtocol()?!1:h.indexOf(a.getProtocol())!==-1&&g.test(a.getDomain())}f["default"]=a}),66);
__d("isHorizonDotMetaDotComURI",[],(function(a,b,c,d,e,f){var g=new RegExp("(^|\\.)horizon\\.meta\\.com$","i"),h=["https"];function a(a){if(a.isEmpty()&&a.toString()!=="#")return!1;return!a.getDomain()&&!a.getProtocol()?!1:h.indexOf(a.getProtocol())!==-1&&g.test(a.getDomain())}f["default"]=a}),66);
__d("isWorkDotMetaDotComURI",[],(function(a,b,c,d,e,f){var g=new RegExp("(^|\\.)work\\.meta\\.com$","i"),h=["https"];function a(a){if(a.isEmpty()&&a.toString()!=="#")return!1;return!a.getDomain()&&!a.getProtocol()?!1:h.indexOf(a.getProtocol())!==-1&&g.test(a.getDomain())}f["default"]=a}),66);
__d("isWorkroomsDotComURI",[],(function(a,b,c,d,e,f){var g=new RegExp("(^|\\.)workrooms\\.com$","i"),h=["https"];function a(a){if(a.isEmpty()&&a.toString()!=="#")return!1;return!a.getDomain()&&!a.getProtocol()?!1:h.indexOf(a.getProtocol())!==-1&&g.test(a.getDomain())}f["default"]=a}),66);
__d("getCometAsyncFetchResponse",["CSRFGuard","CometAsyncFetchError","CometAsyncFetchResponse","ConstUriUtils","DTSG","DTSG_ASYNC","NetworkStatus","PHPQuerySerializer","Promise","XHRRequest","cometAsyncRequestHeaders","getAsyncParams","handleCometErrorCodeSideEffects","isArDotMetaDotComURI","isFacebookURI","isHorizonDotMetaDotComURI","isInstagramURI","isInternalFBURI","isMessengerDotComURI","isMetaAIURI","isWorkDotMetaDotComURI","isWorkplaceDotComURI","isWorkroomsDotComURI","recoverableViolation","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=250;function a(a,e){var f=0,g;return new(i||(i=b("Promise")))(function(b,i){var m,n=function(f,g){if(e.ignoreResponse===!0)return b();var h;f=f.trim();try{d("CSRFGuard").exists(f)&&(f=d("CSRFGuard").clean(f)),h=JSON.parse(f)}catch(b){c("recoverableViolation")('Unable to parse uri "'+a.toString()+'" response. Error: '+(b==null?void 0:b.message)+", response: "+f.substring(0,1e3),"comet_infra");i(b);return}if(l(a)){var j;f=(f=h)==null?void 0:f.dtsgToken;j=(j=h)==null?void 0:j.dtsgAsyncGetToken;f&&d("DTSG").setToken(f);j&&d("DTSG_ASYNC").setToken(j)}if(h.error){c("handleCometErrorCodeSideEffects")(h.error,h.errorSummary,h.errorDescription,h.redirectTo,e.shouldShowErrorDialog);i({error:h.error,errorMsg:h.errorDescription,errorType:h.errorSummary,redirectTo:h.redirectTo});return}return b(new(c("CometAsyncFetchResponse"))(h,g))};n.includeHeaders=!0;function o(a){var b=e.retryCount!=null&&e.retryCount>0&&f<=e.retryCount;if(b)c("setTimeout")(q,j);else{b=new(c("CometAsyncFetchError"))(a.errorMsg,a.errorCode,a.errorRawResponseHeaders,a.errorRawTransport,a.errorType);return i(b)}}function p(){var b=new(c("CometAsyncFetchError"))("Request to "+a+" was aborted",null,null,null,"Abort");return i(b)}function q(){var a;if(((a=e.abortSignal)==null?void 0:a.aborted)===!0)return p();r();s()}function r(){m!=null&&(m.abort(),m=null)}function s(){var b,g=(b=e.requestHeaders)!=null?b:{};Object.assign(g,d("cometAsyncRequestHeaders").getHeaders());b=Object.keys(g).reduce(function(a,b){return a.setRequestHeader(b,g[b])},new(c("XHRRequest"))(a)).setMethod(e.method).setData(babelHelpers["extends"]({},e.data,c("getAsyncParams")(e.method,(b=e.skipSRState)!=null?b:!1))).setRawData(e.formData).setResponseHandler(n).setErrorHandler(o).setAbortHandler(p).setUploadProgressHandler(e.onUploadProgress).setDataSerializer((h||(h=c("PHPQuerySerializer"))).serialize);m=b;e.withCredentials===!0&&k(a)&&b.setWithCredentials(!0);b.send();f++}e.abortSignal&&(e.abortSignal.onabort=function(){r()});c("NetworkStatus").isOnline()?q():g=c("NetworkStatus").onChange(function(a){a=a.online;a&&(q(),g.remove())})})}function k(a){a=d("ConstUriUtils").getUri(a);return a==null?!1:c("isMetaAIURI")(a)||c("isFacebookURI")(a)||c("isInstagramURI")(a)||c("isInternalFBURI")(a)||c("isMessengerDotComURI")(a)||c("isWorkplaceDotComURI")(a)||c("isWorkroomsDotComURI")(a)||c("isWorkDotMetaDotComURI")(a)||c("isHorizonDotMetaDotComURI")(a)||c("isArDotMetaDotComURI")(a)}function l(a){a=d("ConstUriUtils").getUri(a);if(a==null)return!1;return!a.getProtocol()&&!a.getDomain()?!0:document.location.origin===a.getOrigin()}g["default"]=a}),98);
__d("cometAsyncFetch",["getCometAsyncFetchResponse"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){b===void 0&&(b={data:{},method:"GET"});return c("getCometAsyncFetchResponse")(a,b).then(function(a){var c;return((c=b)==null?void 0:c.getFullPayload)===!0?a==null?void 0:a.getFullResponsePayload():a==null?void 0:a.getResponsePayload()})}g["default"]=a}),98);
__d("getPlayerFormatForLogData",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){a=a.isFullscreen;return a===!0?"full_screen":b!=null?b:"inline"}f["default"]=a}),66);
__d("getVideoBrowserTabId",["guid"],(function(a,b,c,d,e,f,g){var h=c("guid")().slice(-8);function a(){return h}g["default"]=a}),98);
__d("isNode",[],(function(a,b,c,d,e,f){function a(a){var b;b=a!=null?(b=a.ownerDocument)!=null?b:a:document;b=(b=b.defaultView)!=null?b:window;return!!(a!=null&&(typeof b.Node==="function"?a instanceof b.Node:typeof a==="object"&&typeof a.nodeType==="number"&&typeof a.nodeName==="string"))}f["default"]=a}),66);
__d("usePrevious",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");var i=b.useEffect,j=b.useRef;function a(a){var b=j(null);i(function(){b.current=a});return b.current}g["default"]=a}),98);