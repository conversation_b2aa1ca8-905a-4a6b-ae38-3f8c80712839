;/*FB_PKG_DELIM*/

__d("AccessibilityWebAssistiveTechTypedLoggerLite",["generateLiteTypedLogger"],(function(a,b,c,d,e,f){"use strict";e.exports=b("generateLiteTypedLogger")("logger:AccessibilityWebAssistiveTechLoggerConfig")}),null);
__d("AsyncTypedRequest",["AsyncRequest"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b){b=a.call(this,b)||this;b.setReplaceTransportMarkers();return b}var c=b.prototype;c.promisePayload=function(b){return a.prototype.promisePayload.call(this,b)};c.setPayloadHandler=function(b){a.prototype.setPayloadHandler.call(this,b);return this};return b}(c("AsyncRequest"));g["default"]=a}),98);
__d("BDSignalBufferData",[],(function(a,b,c,d,e,f){"use strict";a={};b=a;f["default"]=b}),66);
__d("SignalValueContext",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a){this.cn=a}var b=a.prototype;b.getSignalValueContextName=function(){return this.cn};return a}();f["default"]=a}),66);
__d("BDSignalCollectorBase",["BDSignalBufferData","SignalValueContext","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a){this.signalType=a}var d=a.prototype;d.executeSignalCollection=function(){throw new Error("Child class responsibility to implement executeSignalCollection")};d.executeAsyncSignalCollection=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var a=(yield this.executeSignalCollection());return a});function c(){return a.apply(this,arguments)}return c}();a.getSanitizedURI=function(){var a=window.location.href,b=a.indexOf("?");return b<0?a:a.substring(0,b)};d.getContext=function(){return new(c("SignalValueContext"))(a.getSanitizedURI())};d.throwIfNotInitialized=function(){if(!(this.signalType in c("BDSignalBufferData")))throw new Error("Signal is not intialized")};return a}();g["default"]=a}),98);
__d("BDBiometricSignalCollectorBase",["BDSignalCollectorBase","err"],(function(a,b,c,d,e,f,g){"use strict";a="biometric_signal_collected";b=function(a){babelHelpers.inheritsLoose(b,a);function b(){return a.apply(this,arguments)||this}var d=b.prototype;d.listenForSignals=function(){throw c("err")("Child class responsibility to implement listenForSignals")};d.executeSignalCollection=function(){throw c("err")("executeAsyncSignalCollection and executeSignalCollection should not be called on biometric signals")};return b}(c("BDSignalCollectorBase"));g.BIOMETRIC_SIGNAL_COLLECTED_EVENT_NAME=a;g.BDBiometricSignalCollectorBase=b}),98);
__d("BDSignalWrapper",["BDSignalBufferData","SignalCollectorMap"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b){this.signalFlags=a,this.signalType=b}var b=a.prototype;b.getSignalCollector=function(){return c("SignalCollectorMap").get(this.signalType)};b.getBufferConfig=function(){return c("BDSignalBufferData")[this.signalType]};return a}();g["default"]=a}),98);
__d("BDLoggingConstants",[],(function(a,b,c,d,e,f){"use strict";a={ERROR:"error",WARNING:"warning",INFO:"info"};b={KEY_NOT_FOUND:"key_not_found",APPEND_SIGNAL:"bd_append_signal",APPEND_SIGNAL_FAIL:"bd_append_signal_fail",HB_COLLECTED:"append_hb",HB_COLLECTION_FAILED:"hb_collection_failed",BD_EXCEPTION:"bd_exception",SIGNAL_NOT_IMPLEMENTED:"signal_not_implemented",SIGNAL_VALUE_NULL:"signal_value_null",EMPTY_SIGNAL_CONFIG:"empty_signal_config",INVALID_BUFFER_SIZE:"invalid_buffer_size",INVALID_DURATION:"invalid_duration",SIGNAL_FLAGS_MISSING:"signal_flags_missing",DYNAMIC_SIGNAL_COLLECTION_STARTED:"dynamic_signal_collection_started",BIOMETRIC_SIGNAL_COLLECTION_STARTED:"biometric_signal_collection_started",INVALID_GUID:"invalid_guid",INVALID_LENGTH:"invalid_length",GET_LOCAL_STORAGE_ERROR:"get_local_storage_error",WEB_STORAGE:"web_storage",PARSE_CONFIG_ERROR:"parse_config_error",HB_START_FAILURE:"hb_start_failure",HB_ALREADY_RUNNING:"hb_already_running",TRY_RESTARTING_HB:"try_restarting_hb",BANZAI_LOG_ERROR:"banzai_log_error"};c="JS";f.LEVELS=a;f.OPERATIONS=b;f.COMPONENT_NAME=c}),66);
__d("BDOperationTypedLogger",["Banzai","GeneratedLoggerUtils"],(function(a,b,c,d,e,f){"use strict";a=function(){function a(){this.$1={}}var c=a.prototype;c.log=function(a){b("GeneratedLoggerUtils").log("logger:BDOperationLoggerConfig",this.$1,b("Banzai").BASIC,a)};c.logVital=function(a){b("GeneratedLoggerUtils").log("logger:BDOperationLoggerConfig",this.$1,b("Banzai").VITAL,a)};c.logImmediately=function(a){b("GeneratedLoggerUtils").log("logger:BDOperationLoggerConfig",this.$1,{signal:!0},a)};c.clear=function(){this.$1={};return this};c.getData=function(){return babelHelpers["extends"]({},this.$1)};c.updateData=function(a){this.$1=babelHelpers["extends"]({},this.$1,a);return this};c.setBdSessionID=function(a){this.$1.bd_session_id=a;return this};c.setComponent=function(a){this.$1.component=a;return this};c.setDurationUs=function(a){this.$1.duration_us=a;return this};c.setExceptionMessage=function(a){this.$1.exception_message=a;return this};c.setExceptionStackTrace=function(a){this.$1.exception_stack_trace=a;return this};c.setExceptionType=function(a){this.$1.exception_type=a;return this};c.setIntValue=function(a){this.$1.int_value=a;return this};c.setLevel=function(a){this.$1.level=a;return this};c.setOperation=function(a){this.$1.operation=a;return this};c.setOperationInfo=function(a){this.$1.operation_info=b("GeneratedLoggerUtils").serializeMap(a);return this};c.setSessionlets=function(a){this.$1.sessionlets=b("GeneratedLoggerUtils").serializeVector(a);return this};return a}();c={bd_session_id:!0,component:!0,duration_us:!0,exception_message:!0,exception_stack_trace:!0,exception_type:!0,int_value:!0,level:!0,operation:!0,operation_info:!0,sessionlets:!0};f["default"]=a}),66);
__d("BDOperationLogHelper",["BDLoggingConstants","BDOperationTypedLogger","Random"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,c,e){h(a,d("BDLoggingConstants").LEVELS.INFO,b,c,e)}function b(a,b,c,e){h(a,d("BDLoggingConstants").LEVELS.WARNING,b,c,e)}function e(a,b,c,e){h(a,d("BDLoggingConstants").LEVELS.ERROR,b,c,e)}function h(a,b,e,f,g){f===void 0&&(f={});if(d("Random").coinflip(i(e))){if(f===null)throw new Error("opeartion info null");f.source=a;new(c("BDOperationTypedLogger"))().setLevel(b).setDurationUs(g).setOperation(e).setComponent(d("BDLoggingConstants").COMPONENT_NAME).setOperationInfo(f).log()}}function i(a){var b=d("BDLoggingConstants").OPERATIONS;switch(a){case b.APPEND_SIGNAL:case b.HB_COLLECTED:case b.GET_LOCAL_STORAGE_ERROR:case b.WEB_STORAGE:case b.SIGNAL_NOT_IMPLEMENTED:case b.BIOMETRIC_SIGNAL_COLLECTION_STARTED:return 1e3;default:return 1}}g.logInfo=a;g.logWarning=b;g.logError=e;g.log=h;g.getFlipSamplingByOperation=i}),98);
__d("SignalErrorValueTypeDef",[],(function(a,b,c,d,e,f){"use strict";var g="ec",h="en",i="es",j=500;a=function(){function a(a,b,c){this.$1=a,this.$2=b,c!=null&&(this.$3=c.substr(0,j))}var b=a.prototype;b.getErrorCode=function(){return this.$1};b.getErrorName=function(){return this.$2};b.getErrorDetails=function(){return this.$3};b.isEqual=function(a){return this.$1===a.getErrorCode()&&this.$3===a.getErrorDetails()&&this.$2===a.getErrorName()};b.toJSON=function(){var a={};a[g]=this.$1;switch(this.$1){case 0:a[h]=this.$2;a[i]=this.$3;break}return a};return a}();f.SignalErrorValueTypeDef=a}),66);
__d("SignalValueTypeDef",["BDLoggingConstants","BDOperationLogHelper","SignalErrorValueTypeDef"],(function(a,b,c,d,e,f,g){"use strict";var h="SignalValueTypeDef",i="t",j="ctx",k="v",l="e",m={NUMBER:"NUMBER",STRING:"STRING",BOOLEAN:"BOOLEAN",CUSTOM_OBJECT:"CUSTOM_OBJECT",INT_ARRAY:"INT_ARRAY",TOUCH:"TOUCH",MAP:"MAP",LIST:"LIST",SENSOR:"SENSOR",ERROR:"ERROR"};a=function(){function a(a,b,c,d,e){this.$1=a,this.$2=b,this.$3=c,this.$4=d,this.$5=e!=null?e:0}var b=a.prototype;b.getTimeStampMS=function(){return this.$1};b.getSignalContext=function(){return this.$2};b.getSignalValue=function(){return this.$3};b.getSignalValueType=function(){return this.$4};b.isEqual=function(a,b){if(a==null){d("BDOperationLogHelper").logWarning(h,d("BDLoggingConstants").OPERATIONS.SIGNAL_VALUE_NULL);return!1}if(this.getSignalValueType()!==a.getSignalValueType())return!1;if(this.getSignalValueType()===m.ERROR&&a.getSignalValueType()===m.ERROR)return this.equalValue(a);var c=!1;b.has(128)&&(c=this.equalValue(a));b.has(256)&&(c=c&&this.getSignalContext()!=null&&a.getSignalContext()!=null&&this.getSignalContext().getSignalValueContextName()===a.getSignalContext().getSignalValueContextName());b.has(512)&&(c=c&&Math.abs(this.getTimeStampMS()-a.getTimeStampMS())<=this.$5);return c};b.equalValue=function(a){if(this.isPrimitiveType())return this.getSignalValue()===a.getSignalValue();throw new Error("Must implement in the subclasses")};b.toJSON=function(a){var b={};a&&(b[i]=this.$1/1e3,this.$2!=null&&(b[j]=this.$2));this.$3==null?b[l]=new(d("SignalErrorValueTypeDef").SignalErrorValueTypeDef)(2):this.addValueOrErrorToJSON(b);return b};b.addValueOrErrorToJSON=function(a){if(this.isPrimitiveType())a[k]=this.$3;else throw new Error("Must implement in the subclasses")};b.isPrimitiveType=function(){switch(typeof this.$3){case"number":case"boolean":case"string":return!0;default:return!1}};return a}();g.BD_VALUE=k;g.BD_ERROR=l;g.VALUE_TYPES=m;g.SignalValueTypeDef=a}),98);
__d("ErrorSignalValueType",["SignalValueTypeDef"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,e){return a.call(this,b,c,e,d("SignalValueTypeDef").VALUE_TYPES.ERROR)||this}var c=b.prototype;c.equalValue=function(a){return this.getSignalValue().isEqual(a.getSignalValue())};c.addValueOrErrorToJSON=function(a){a[d("SignalValueTypeDef").BD_ERROR]=this.getSignalValue().toJSON()};return b}(d("SignalValueTypeDef").SignalValueTypeDef);g["default"]=a}),98);
__d("NumberSignalValueType",["SignalValueTypeDef"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,e){return a.call(this,b,c,e,d("SignalValueTypeDef").VALUE_TYPES.NUMBER)||this}return b}(d("SignalValueTypeDef").SignalValueTypeDef);g["default"]=a}),98);
__d("BDConnectionRTTSignalCollector",["BDSignalCollectorBase","ErrorSignalValueType","NumberSignalValueType","SignalErrorValueTypeDef"],(function(a,b,c,d,e,f,g){"use strict";var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,j.signalType)||this}var e=a.prototype;e.executeSignalCollection=function(){if(navigator.connection!=null&&navigator.connection.rtt!=null){var a=navigator.connection.rtt;a=new(c("NumberSignalValueType"))(Date.now(),this.getContext(),a)}else a=new(c("ErrorSignalValueType"))(Date.now(),this.getContext(),new(d("SignalErrorValueTypeDef").SignalErrorValueTypeDef)(3,"navigator.connection.rtt not supported"));return{valueOrError:a}};return a}(c("BDSignalCollectorBase")),i=null,j={signalType:30004,get:function(){i==null&&(i=new h());return i}};a=j;g["default"]=a}),98);
__d("CustomObjectSignalValueType",["SignalValueTypeDef"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,e){return a.call(this,b,c,e,d("SignalValueTypeDef").VALUE_TYPES.CUSTOM_OBJECT)||this}var c=b.prototype;c.equalValue=function(a){return this.getSignalValue().isEqual(a.getSignalValue())};c.addValueOrErrorToJSON=function(a){a[d("SignalValueTypeDef").BD_VALUE]=this.getSignalValue().toJSON()};return b}(d("SignalValueTypeDef").SignalValueTypeDef);g["default"]=a}),98);
__d("HeartbeatObject",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(){this.isAppForeground=!0}var b=a.prototype;b.toJSON=function(){return{f:this.isAppForeground}};b.isEqual=function(a){return!1};return a}();f["default"]=a}),66);
__d("BDHeartbeatSignalCollector",["BDSignalCollectorBase","CustomObjectSignalValueType","HeartbeatObject"],(function(a,b,c,d,e,f,g){"use strict";var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,j.signalType)||this}var d=a.prototype;d.executeSignalCollection=function(){var a=new(c("CustomObjectSignalValueType"))(Date.now(),this.getContext(),new(c("HeartbeatObject"))());return{valueOrError:a}};return a}(c("BDSignalCollectorBase")),i=null,j={signalType:38e3,get:function(){i==null&&(i=new h());return i}};a=j;g["default"]=a}),98);
__d("KeyDownUpObject",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a,b){this.action=null,this.key_code=null,this.action=a,this.key_code=b}var b=a.prototype;b.toJSON=function(){return{action:this.action,key_code:this.key_code}};b.isEqual=function(b){return b instanceof a?this.action===b.action&&this.key_code===b.key_code:!1};return a}();f["default"]=a}),66);
__d("BDKeyDownUpSignalCollector",["BDBiometricSignalCollectorBase","CustomObjectSignalValueType","KeyDownUpObject","gkx"],(function(a,b,c,d,e,f,g){"use strict";var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,j.signalType)||this}var e=a.prototype;e.listenForSignals=function(){var a=this;c("gkx")("21049")&&(document.addEventListener("keydown",function(b){return a.collectSignals(2)}),document.addEventListener("keyup",function(b){return a.collectSignals(1)}))};e.collectSignals=function(a){a=new(c("CustomObjectSignalValueType"))(Date.now(),this.getContext(),new(c("KeyDownUpObject"))(a,0));a={signalId:this.signalType,data:{valueOrError:a}};window.dispatchEvent(new CustomEvent(d("BDBiometricSignalCollectorBase").BIOMETRIC_SIGNAL_COLLECTED_EVENT_NAME,{detail:a}))};return a}(d("BDBiometricSignalCollectorBase").BDBiometricSignalCollectorBase),i=null,j={signalType:30100,get:function(){i==null&&(i=new h());return i}};a=j;g["default"]=a}),98);
__d("StringArrayObject",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a){this.strings=[],this.strings=a}var b=a.prototype;b.toJSON=function(){return this.strings};b.isEqual=function(b){if(!(b instanceof a))return!1;if(b.strings===this.strings)return!0;if(b.strings.length!==this.strings.length)return!1;for(var c=0;c<this.strings.length;c++)if(this.strings[c]!==b.strings[c])return!1;return!0};return a}();f["default"]=a}),66);
__d("BDLanguagesSignalCollector",["BDSignalCollectorBase","CustomObjectSignalValueType","StringArrayObject"],(function(a,b,c,d,e,f,g){"use strict";var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,j.signalType)||this}var d=a.prototype;d.executeSignalCollection=function(){var a=[].concat(navigator.languages);a=new(c("CustomObjectSignalValueType"))(Date.now(),this.getContext(),new(c("StringArrayObject"))(a));return{valueOrError:a}};return a}(c("BDSignalCollectorBase")),i=null,j={signalType:30003,get:function(){i==null&&(i=new h());return i}};a=j;g["default"]=a}),98);
__d("BDMimeTypeCountSignalCollector",["BDSignalCollectorBase","NumberSignalValueType"],(function(a,b,c,d,e,f,g){"use strict";var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,j.signalType)||this}var d=a.prototype;d.executeSignalCollection=function(){var a=navigator.mimeTypes?navigator.mimeTypes.length:-1;a=new(c("NumberSignalValueType"))(Date.now(),this.getContext(),a);return{valueOrError:a}};return a}(c("BDSignalCollectorBase")),i=null,j={signalType:30002,get:function(){i==null&&(i=new h());return i}};a=j;g["default"]=a}),98);
__d("BooleanSignalValueType",["SignalValueTypeDef"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,e){return a.call(this,b,c,e,d("SignalValueTypeDef").VALUE_TYPES.BOOLEAN)||this}return b}(d("SignalValueTypeDef").SignalValueTypeDef);g["default"]=a}),98);
__d("BDTouchOrMouseSignalCollectorBase",["BDBiometricSignalCollectorBase","BDLoggingConstants","BDOperationLogHelper","BooleanSignalValueType"],(function(a,b,c,d,e,f,g){"use strict";var h="BDTouchOrMouseSignalCollectorBase";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,d){var e;e=a.call(this,b)||this;e.eventsToSubscribe=[];e.pauseTimeout=60*60*1e3;e.eventsToSubscribe=c;e.pauseTimeout=d;e.handler=function(a){return e.collectSignals(a)};return e}var e=b.prototype;e.listenForSignals=function(){this.collectSignals(),this.addListeners()};e.addListeners=function(){var a=this;this.eventsToSubscribe.forEach(function(b){try{document.addEventListener(b,a.handler)}catch(a){d("BDOperationLogHelper").logError(h,d("BDLoggingConstants").OPERATIONS.BD_EXCEPTION,{e:a})}})};e.pauseListeners=function(){var a=this;this.eventsToSubscribe.forEach(function(b){document.removeEventListener(b,a.handler)});window.setTimeout(function(){return a.addListeners()},this.pauseTimeout)};e.collectSignals=function(a){a=a!=null;a&&this.pauseListeners();a=new(c("BooleanSignalValueType"))(Date.now(),this.getContext(),a);a={signalId:this.signalType,data:{valueOrError:a}};window.dispatchEvent(new CustomEvent(d("BDBiometricSignalCollectorBase").BIOMETRIC_SIGNAL_COLLECTED_EVENT_NAME,{detail:a}))};return b}(d("BDBiometricSignalCollectorBase").BDBiometricSignalCollectorBase);g["default"]=a}),98);
__d("BDMousePresenceSignalCollector",["BDTouchOrMouseSignalCollectorBase"],(function(a,b,c,d,e,f,g){"use strict";var h=60*60*1e3,i=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,k.signalType,["mousedown","mousemove"],h)||this}return a}(c("BDTouchOrMouseSignalCollectorBase")),j=null,k={signalType:30106,get:function(){j==null&&(j=new i());return j}};a=k;g["default"]=a}),98);
__d("StringSignalValueType",["SignalValueTypeDef"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,e){return a.call(this,b,c,e,d("SignalValueTypeDef").VALUE_TYPES.STRING)||this}return b}(d("SignalValueTypeDef").SignalValueTypeDef);g["default"]=a}),98);
__d("BDNavigatorAppVersionSignalCollector",["BDSignalCollectorBase","StringSignalValueType"],(function(a,b,c,d,e,f,g){"use strict";var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,j.signalType)||this}var d=a.prototype;d.executeSignalCollection=function(){var a=navigator.appVersion;a=new(c("StringSignalValueType"))(Date.now(),this.getContext(),a);return{valueOrError:a}};return a}(c("BDSignalCollectorBase")),i=null,j={signalType:30013,get:function(){i==null&&(i=new h());return i}};a=j;g["default"]=a}),98);
__d("BDNavigatorHardwareConcurrencySignalCollector",["BDSignalCollectorBase","NumberSignalValueType"],(function(a,b,c,d,e,f,g){"use strict";var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,j.signalType)||this}var d=a.prototype;d.executeSignalCollection=function(){var a;a=new(c("NumberSignalValueType"))(Date.now(),this.getContext(),(a=navigator.hardwareConcurrency)!=null?a:0);return{valueOrError:a}};return a}(c("BDSignalCollectorBase")),i=null,j={signalType:30018,get:function(){i==null&&(i=new h());return i}};a=j;g["default"]=a}),98);
__d("BDNavigatorMaxTouchPointSignalCollector",["BDSignalCollectorBase","NumberSignalValueType"],(function(a,b,c,d,e,f,g){"use strict";var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,j.signalType)||this}var d=a.prototype;d.executeSignalCollection=function(){var a=new(c("NumberSignalValueType"))(Date.now(),this.getContext(),navigator.maxTouchPoints);return{valueOrError:a}};return a}(c("BDSignalCollectorBase")),i=null,j={signalType:30093,get:function(){i==null&&(i=new h());return i}};a=j;g["default"]=a}),98);
__d("BDNavigatorNotificationPermissionSignalCollector",["BDSignalCollectorBase","ErrorSignalValueType","SignalErrorValueTypeDef","StringSignalValueType","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h=function(e){babelHelpers.inheritsLoose(a,e);function a(){return e.call(this,j.signalType)||this}var f=a.prototype;f.executeAsyncSignalCollection=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){try{if(navigator.permissions){var a=(yield navigator.permissions.query({name:"notifications"}));a=new(c("StringSignalValueType"))(Date.now(),this.getContext(),a.state)}else a=new(c("ErrorSignalValueType"))(Date.now(),this.getContext(),new(d("SignalErrorValueTypeDef").SignalErrorValueTypeDef)(3,"navigator.permissions not supported"))}catch(b){a=new(c("ErrorSignalValueType"))(Date.now(),this.getContext(),new(d("SignalErrorValueTypeDef").SignalErrorValueTypeDef)(3,"notifications cannot be queried from navigator.permissions"))}return{valueOrError:a}});function e(){return a.apply(this,arguments)}return e}();return a}(c("BDSignalCollectorBase")),i=null,j={signalType:30008,get:function(){i==null&&(i=new h());return i}};a=j;g["default"]=a}),98);
__d("BDNavigatorPlatformSignalCollector",["BDSignalCollectorBase","StringSignalValueType"],(function(a,b,c,d,e,f,g){"use strict";var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,j.signalType)||this}var d=a.prototype;d.executeSignalCollection=function(){var a=new(c("StringSignalValueType"))(Date.now(),this.getContext(),navigator.platform);return{valueOrError:a}};return a}(c("BDSignalCollectorBase")),i=null,j={signalType:30015,get:function(){i==null&&(i=new h());return i}};a=j;g["default"]=a}),98);
__d("BDNavigatorPluginsFileExtensionsSignalCollector",["BDSignalCollectorBase","ErrorSignalValueType","SignalErrorValueTypeDef","StringArrayObject"],(function(a,b,c,d,e,f,g){"use strict";var h=10,i=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,k.signalType)||this}var e=a.prototype;e.executeSignalCollection=function(){var a=null;try{var b=navigator.plugins?navigator.plugins.length:0,e=new Set();for(var f=0;f<b;f++){var g=navigator.plugins[f].filename;if(!g)continue;var i=g.indexOf(".");if(i==-1||i==g.length-1)continue;e.add(g.substr(i+1));if(e.size>=h)break}e.size&&(a=new(c("StringArrayObject"))(Array.from(e)))}catch(b){a=new(c("ErrorSignalValueType"))(Date.now(),this.getContext(),new(d("SignalErrorValueTypeDef").SignalErrorValueTypeDef)(3,"navigator.plugins is not defined"))}return{valueOrError:a}};return a}(c("BDSignalCollectorBase")),j=null,k={signalType:30019,get:function(){j==null&&(j=new i());return j}};a=k;g["default"]=a}),98);
__d("BDNavigatorUserAgentSignalCollector",["BDSignalCollectorBase","StringSignalValueType"],(function(a,b,c,d,e,f,g){"use strict";var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,j.signalType)||this}var d=a.prototype;d.executeSignalCollection=function(){var a=new(c("StringSignalValueType"))(Date.now(),this.getContext(),navigator.userAgent);return{valueOrError:a}};return a}(c("BDSignalCollectorBase")),i=null,j={signalType:30094,get:function(){i==null&&(i=new h());return i}};a=j;g["default"]=a}),98);
__d("BDNavigatorVendorSignalCollector",["BDSignalCollectorBase","StringSignalValueType"],(function(a,b,c,d,e,f,g){"use strict";var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,j.signalType)||this}var d=a.prototype;d.executeSignalCollection=function(){var a=navigator.vendor;a=new(c("StringSignalValueType"))(Date.now(),this.getContext(),a);return{valueOrError:a}};return a}(c("BDSignalCollectorBase")),i=null,j={signalType:30012,get:function(){i==null&&(i=new h());return i}};a=j;g["default"]=a}),98);
__d("BDNotificationPermissionSignalCollector",["BDSignalCollectorBase","ErrorSignalValueType","SignalErrorValueTypeDef","StringSignalValueType"],(function(a,b,c,d,e,f,g){"use strict";var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,j.signalType)||this}var e=a.prototype;e.executeSignalCollection=function(){var a;window.Notification?a=new(c("StringSignalValueType"))(Date.now(),this.getContext(),Notification.permission):a=new(c("ErrorSignalValueType"))(Date.now(),this.getContext(),new(d("SignalErrorValueTypeDef").SignalErrorValueTypeDef)(3,"Notification not supported"));return{valueOrError:a}};return a}(c("BDSignalCollectorBase")),i=null,j={signalType:30007,get:function(){i==null&&(i=new h());return i}};a=j;g["default"]=a}),98);
__d("BDPluginCountSignalCollector",["BDSignalCollectorBase","NumberSignalValueType"],(function(a,b,c,d,e,f,g){"use strict";var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,j.signalType)||this}var d=a.prototype;d.executeSignalCollection=function(){var a=navigator.plugins?navigator.plugins.length:-1;a=new(c("NumberSignalValueType"))(Date.now(),this.getContext(),a);return{valueOrError:a}};return a}(c("BDSignalCollectorBase")),i=null,j={signalType:30001,get:function(){i==null&&(i=new h());return i}};a=j;g["default"]=a}),98);
__d("BDTimezoneOffsetSignalCollector",["BDSignalCollectorBase","NumberSignalValueType"],(function(a,b,c,d,e,f,g){"use strict";var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,j.signalType)||this}var d=a.prototype;d.executeSignalCollection=function(){var a;a=new(c("NumberSignalValueType"))(Date.now(),this.getContext(),(a=new Date().getTimezoneOffset())!=null?a:999);return{valueOrError:a}};return a}(c("BDSignalCollectorBase")),i=null,j={signalType:30040,get:function(){i==null&&(i=new h());return i}};a=j;g["default"]=a}),98);
__d("BDTouchPresenceSignalCollector",["BDTouchOrMouseSignalCollectorBase"],(function(a,b,c,d,e,f,g){"use strict";var h=60*60*1e3,i=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,k.signalType,["touchstart","touchcancel"],h)||this}return a}(c("BDTouchOrMouseSignalCollectorBase")),j=null,k={signalType:30107,get:function(){j==null&&(j=new i());return j}};a=k;g["default"]=a}),98);
__d("BDWebdriverSignalCollector",["BDSignalCollectorBase","BooleanSignalValueType"],(function(a,b,c,d,e,f,g){"use strict";var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,j.signalType)||this}var d=a.prototype;d.executeSignalCollection=function(){var a=!!navigator.webdriver;a=new(c("BooleanSignalValueType"))(Date.now(),this.getContext(),a);return{valueOrError:a}};return a}(c("BDSignalCollectorBase")),i=null,j={signalType:3e4,get:function(){i==null&&(i=new h());return i}};a=j;g["default"]=a}),98);
__d("BDWebglSupportSignalCollector",["BDSignalCollectorBase","BooleanSignalValueType"],(function(a,b,c,d,e,f,g){"use strict";var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,j.signalType)||this}var d=a.prototype;d.executeSignalCollection=function(){var a=document.createElement("canvas"),b=null;try{b=a.getContext("webgl")||a.getContext("experimental-webgl")}catch(a){}a=Boolean(b);b=new(c("BooleanSignalValueType"))(Date.now(),this.getContext(),a);return{valueOrError:b}};return a}(c("BDSignalCollectorBase")),i=null,j={signalType:30022,get:function(){i==null&&(i=new h());return i}};a=j;g["default"]=a}),98);
__d("BDWindowHistoryLengthSignalCollector",["BDSignalCollectorBase","NumberSignalValueType"],(function(a,b,c,d,e,f,g){"use strict";var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,j.signalType)||this}var d=a.prototype;d.executeSignalCollection=function(){var a=new(c("NumberSignalValueType"))(Date.now(),this.getContext(),window.history?window.history.length:0);return{valueOrError:a}};return a}(c("BDSignalCollectorBase")),i=null,j={signalType:30095,get:function(){i==null&&(i=new h());return i}};a=j;g["default"]=a}),98);
__d("DimensionObject",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a,b){this.width=null,this.height=null,this.width=a,this.height=b}var b=a.prototype;b.toJSON=function(){return{w:this.width,h:this.height}};b.isEqual=function(b){if(b instanceof a)return this.width===b.width&&this.height===b.height;else return!1};return a}();f["default"]=a}),66);
__d("BDWindowOuterDimensionSignalCollector",["BDSignalCollectorBase","CustomObjectSignalValueType","DimensionObject"],(function(a,b,c,d,e,f,g){"use strict";var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,j.signalType)||this}var d=a.prototype;d.executeSignalCollection=function(){var a=window.innerHeight,b=window.innerWidth;b=new(c("CustomObjectSignalValueType"))(Date.now(),this.getContext(),new(c("DimensionObject"))(b,a));return{valueOrError:b}};return a}(c("BDSignalCollectorBase")),i=null,j={signalType:30005,get:function(){i==null&&(i=new h());return i}};a=j;g["default"]=a}),98);
__d("SignalCollectorMap",["BDConnectionRTTSignalCollector","BDHeartbeatSignalCollector","BDHeartbeatV2SignalCollector","BDKeyDownUpSignalCollector","BDLanguagesSignalCollector","BDMimeTypeCountSignalCollector","BDMousePresenceSignalCollector","BDNavigatorAppVersionSignalCollector","BDNavigatorHardwareConcurrencySignalCollector","BDNavigatorMaxTouchPointSignalCollector","BDNavigatorNotificationPermissionSignalCollector","BDNavigatorPlatformSignalCollector","BDNavigatorPluginsFileExtensionsSignalCollector","BDNavigatorUserAgentSignalCollector","BDNavigatorVendorSignalCollector","BDNotificationPermissionSignalCollector","BDPluginCountSignalCollector","BDTimezoneOffsetSignalCollector","BDTouchPresenceSignalCollector","BDWebdriverSignalCollector","BDWebglSupportSignalCollector","BDWindowHistoryLengthSignalCollector","BDWindowOuterDimensionSignalCollector"],(function(a,b,c,d,e,f,g){"use strict";a={get:function(a){switch(a){case 3e4:return c("BDWebdriverSignalCollector").get();case 30001:return c("BDPluginCountSignalCollector").get();case 30002:return c("BDMimeTypeCountSignalCollector").get();case 30003:return c("BDLanguagesSignalCollector").get();case 30004:return c("BDConnectionRTTSignalCollector").get();case 30005:return c("BDWindowOuterDimensionSignalCollector").get();case 30007:return c("BDNotificationPermissionSignalCollector").get();case 30008:return c("BDNavigatorNotificationPermissionSignalCollector").get();case 30012:return c("BDNavigatorVendorSignalCollector").get();case 30013:return c("BDNavigatorAppVersionSignalCollector").get();case 30015:return c("BDNavigatorPlatformSignalCollector").get();case 30018:return c("BDNavigatorHardwareConcurrencySignalCollector").get();case 30019:return c("BDNavigatorPluginsFileExtensionsSignalCollector").get();case 30022:return c("BDWebglSupportSignalCollector").get();case 30040:return c("BDTimezoneOffsetSignalCollector").get();case 30093:return c("BDNavigatorMaxTouchPointSignalCollector").get();case 30094:return c("BDNavigatorUserAgentSignalCollector").get();case 30095:return c("BDWindowHistoryLengthSignalCollector").get();case 30100:return c("BDKeyDownUpSignalCollector").get();case 30106:return c("BDMousePresenceSignalCollector").get();case 30107:return c("BDTouchPresenceSignalCollector").get();case 38e3:return c("BDHeartbeatSignalCollector").get();case 38001:return c("BDHeartbeatV2SignalCollector").get()}}};b=a;g["default"]=b}),98);
__d("HeartbeatV2Object",["HeartbeatObject"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b){var c;c=a.call(this)||this;c.id="";c.id=b;return c}var c=b.prototype;c.toJSON=function(){return{f:this.isAppForeground,id:this.id}};return b}(c("HeartbeatObject"));g["default"]=a}),98);
__d("BDHeartbeatV2SignalCollector",["BDClientConfig","BDSignalCollectorBase","CustomObjectSignalValueType","HeartbeatV2Object"],(function(a,b,c,d,e,f,g){"use strict";var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.call(this,j.signalType)||this}var d=a.prototype;d.executeSignalCollection=function(){var a=new(c("CustomObjectSignalValueType"))(Date.now(),this.getContext(),new(c("HeartbeatV2Object"))(c("BDClientConfig").get().getHeartbeatVersion()));return{valueOrError:a}};return a}(c("BDSignalCollectorBase")),i=null,j={signalType:38001,get:function(){i==null&&(i=new h());return i}};a=j;g["default"]=a}),98);
__d("BDClientConfig",["BDLoggingConstants","BDOperationLogHelper","BDSignalWrapper"],(function(a,b,c,d,e,f,g){"use strict";var h="BDClientConfig",i=function(){function a(){this.staticSignalBufferSize=1,this.dynamicSignalBufferSize=1,this.biometricSignalBufferSize=1,this.staticSignals=[],this.dynamicSignals=[],this.biometricSignals=[],this.biometricSignalsMap=new Map(),this.heartbeatSignal=new(c("BDSignalWrapper"))([],38001),this.bufferSizeBySignalIdMap={},this.periodicCollectionIntervalMs=Number.MAX_SAFE_INTEGER,this.signalConfigGenerationTimeStampMs=0,this.suspiciousTiersFlushDurationMs=Number.MAX_SAFE_INTEGER,this.allTiersFlushDurationMs=Number.MAX_SAFE_INTEGER,this.heartbeatIntervalMs=-1,this.parsingDone=!1,this.sid=-1,this.hbVersion="",this.bufferSizeBySignalIdMap[38001]=1}var b=a.prototype;b.setStaticSignalBufferSize=function(a){a>0?this.staticSignalBufferSize=a:d("BDOperationLogHelper").logError(h,d("BDLoggingConstants").OPERATIONS.INVALID_BUFFER_SIZE,{size:a.toString(),type:"s"});return this};b.getStaticSignalBufferSize=function(){return this.staticSignalBufferSize};b.setDynamicSignalBufferSize=function(a){a>1?this.dynamicSignalBufferSize=a:d("BDOperationLogHelper").logError(h,d("BDLoggingConstants").OPERATIONS.INVALID_BUFFER_SIZE,{size:a.toString(),type:"d"});return this};b.getDynamicSignalBufferSize=function(){return this.dynamicSignalBufferSize};b.setBiometricSignalBufferSize=function(a){a>1?this.biometricSignalBufferSize=a:d("BDOperationLogHelper").logError(h,d("BDLoggingConstants").OPERATIONS.INVALID_BUFFER_SIZE,{size:a.toString(),type:"b"});return this};b.setSID=function(a){this.sid=a;return this};b.setHeartbeatVersion=function(a){this.hbVersion=a;return this};b.getHeartbeatVersion=function(){return this.hbVersion};b.getBiometricSignalBufferSize=function(){return this.biometricSignalBufferSize};b.setConfigGenerationTimeStamp=function(a){this.signalConfigGenerationTimeStampMs=a;return this};b.getConfigGenerationTimeStamp=function(){return this.signalConfigGenerationTimeStampMs};b.setHeartbeatIntervalMinutes=function(a){this.heartbeatIntervalMs=a*60*1e3;return this};b.getHeartbeatIntervalMs=function(){return this.heartbeatIntervalMs};b.setSuspiciousTiersFlushDurationMinutes=function(a){a>0?this.suspiciousTiersFlushDurationMs=a*60*1e3:d("BDOperationLogHelper").logError(h,d("BDLoggingConstants").OPERATIONS.INVALID_DURATION,{size:a.toString(),type:"fds"});return this};b.getSuspiciousTiersFlushDurationMs=function(){return this.suspiciousTiersFlushDurationMs};b.setAllTiersFlushDurationMinutes=function(a){a>0?this.allTiersFlushDurationMs=a*60*1e3:d("BDOperationLogHelper").logError(h,d("BDLoggingConstants").OPERATIONS.INVALID_DURATION,{size:a.toString(),type:"fda"});return this};b.getAllTiersFlushDurationMs=function(){return this.allTiersFlushDurationMs};b.addMultipleSignalsToClientConfig=function(a){var b=this;a.forEach(function(a){return b.addSignalToClientConfig(a)});return this};b.addSignalToClientConfig=function(a){var b=a.getSignalFlags(),d=a.getSignalId();if(d===38e3)return this;var e=new(c("BDSignalWrapper"))(b,d);if(d===38001){this.heartbeatSignal=e;return this}!b.includes(2)?this.staticSignals.push(e):b.includes(4)?this.biometricSignals.push(e):this.dynamicSignals.push(e);a.getBufferSize()>0&&(this.bufferSizeBySignalIdMap[a.getSignalId()]=a.getBufferSize());return this};b.setPeriodicCollectionIntervalSeconds=function(a){a>0?this.periodicCollectionIntervalMs=a*1e3:d("BDOperationLogHelper").logError(h,d("BDLoggingConstants").OPERATIONS.INVALID_DURATION,{size:a.toString(),type:"pi"});return this};b.getPeriodicCollectionIntervalMs=function(){return this.periodicCollectionIntervalMs};b.getDynamicSignals=function(){return this.dynamicSignals};b.getStaticSignals=function(){return this.staticSignals};b.getBiometricSignals=function(){return this.biometricSignals};b.getBiometricSignalsMap=function(){this.biometricSignalsMap.size===0&&this.biometricSignals.length>0&&(this.biometricSignalsMap=this.biometricSignals.reduce(function(a,b){return a.set(b.signalType,b)},new Map()));return this.biometricSignalsMap};b.getHeartbeatSignal=function(){return this.heartbeatSignal};b.getBufferSizeBySignalId=function(a){return this.bufferSizeBySignalIdMap[a]};b.setParsingDone=function(a){this.parsingDone=a};b.isParsingDone=function(){return this.parsingDone};return a}(),j=null;a={get:function(){j==null&&(j=new i());return j}};b=a;g["default"]=b}),98);
__d("BDCollectionTypeEnum",[],(function(a,b,c,d,e,f){"use strict";a=Object.freeze({STATIC:0,DYNAMIC:1,BIOMETRIC:2});f["default"]=a}),66);
__d("BotDetection_SignalFlags",[],(function(a,b,c,d,e,f){a=Object.freeze({ACTIVE:1,DYNAMIC:2,BIOMETRIC:4,DEPRECATED:8,WEB:16,IOS_NATIVE:32,ANDROID_NATIVE:64,EQUAL_BY_VALUE:128,EQUAL_BY_CONTEXT:256,EQUAL_BY_TIMESTAMP:512,SUSPICIOUS_TIER:1024,PARANOID_TIER:2048,RANDOM_SAMPLE_TIER_DEPRECATED:4096,BENIGN_TIER:262144,EMPLOYEES_TIER:524288,BUNDLE:8192,ONSITE:16384,OFFSITE:32768,OFFSITE_SENSITIVE:65536,SENSITIVE:131072});f["default"]=a}),66);
__d("BDServerSignalConfig",["BDLoggingConstants","BDOperationLogHelper","BotDetection_SignalFlags"],(function(a,b,c,d,e,f,g){"use strict";var h="BDServerSignalConfig";a=function(){function a(a,b,c){this.parsedSignalFlags=[],this.signalId=a,this.signalFlags=b,this.bufferSize=c}var b=a.prototype;b.getSignalId=function(){return this.signalId};b.getSignalFlags=function(){var a=this;if(this.parsedSignalFlags.length===0){var b=Object.keys(c("BotDetection_SignalFlags"));b.forEach(function(b){(c("BotDetection_SignalFlags")[b]&a.signalFlags)===c("BotDetection_SignalFlags")[b]&&a.parsedSignalFlags.push(c("BotDetection_SignalFlags")[b])})}this.parsedSignalFlags.length===0&&d("BDOperationLogHelper").logError(h,d("BDLoggingConstants").OPERATIONS.SIGNAL_FLAGS_MISSING,{id:this.signalId.toString(),flags:this.signalFlags.toString()});return this.parsedSignalFlags};b.getBufferSize=function(){return this.bufferSize!=null?this.bufferSize:0};return a}();g["default"]=a}),98);
__d("BDServerConfig",["BDClientConfig","BDLoggingConstants","BDOperationLogHelper","BDServerSignalConfig"],(function(a,b,c,d,e,f,g){"use strict";var h="BDServerConfig";function a(a){var b=[];try{var e=JSON.parse(a.sc),f=new Map(e.c);f.forEach(function(a,d){return b.push(new(c("BDServerSignalConfig"))(d,a))});if(f.size===0){d("BDOperationLogHelper").logError(h,d("BDLoggingConstants").OPERATIONS.EMPTY_SIGNAL_CONFIG);return}f=c("BDClientConfig").get();f.setPeriodicCollectionIntervalSeconds(a.i).addMultipleSignalsToClientConfig(b).setConfigGenerationTimeStamp(e.t).setAllTiersFlushDurationMinutes(a.fda).setSuspiciousTiersFlushDurationMinutes(a.fds).setHeartbeatIntervalMinutes(a.hbi).setStaticSignalBufferSize(a.sbs).setDynamicSignalBufferSize(a.dbs).setBiometricSignalBufferSize(a.bbs).setSID(a.sid).setHeartbeatVersion(a.hbv).setParsingDone(!0)}catch(a){d("BDOperationLogHelper").logError(h,d("BDLoggingConstants").OPERATIONS.PARSE_CONFIG_ERROR,{e:a})}}g.parseConfig=a}),98);
__d("BDSignalBuffer",["BDClientConfig","BDCollectionTypeEnum","BDLoggingConstants","BDOperationLogHelper","BDSignalBufferData"],(function(a,b,c,d,e,f,g){"use strict";var h="BDSignalBuffer";function a(a,b,d){if(a in c("BDSignalBufferData"))return;var e=1;b!==void 0&&(e=b);c("BDSignalBufferData")[a]={values:[],max_buffer_size:e,signal_flags:(b=d)!=null?b:[]}}function i(a){if(a in c("BDSignalBufferData"))c("BDSignalBufferData")[a].values=[];else throw new Error("Tried to clear signal buffer that was not intialized:")}function b(a,b){if(a in c("BDSignalBufferData")){var e=c("BDSignalBufferData")[a],f=e.max_buffer_size;e.values.length>=f&&e.values.shift();e.values.push(b);d("BDOperationLogHelper").logInfo(h,d("BDLoggingConstants").OPERATIONS.APPEND_SIGNAL,{id:a.toString()})}else{d("BDOperationLogHelper").logError(h,d("BDLoggingConstants").OPERATIONS.APPEND_SIGNAL_FAIL,{id:a.toString()});throw new Error("Tried to append signal that was not intialized:")}}function e(){var a=c("BDClientConfig").get();a=a.getBiometricSignals();a.forEach(function(a){a.signalType in c("BDSignalBufferData")&&i(a.signalType)})}function f(a){var b;a=c("BDSignalBufferData")[a];b=a==null?void 0:(b=a.values)==null?void 0:b.length;return b!=null&&b>0?a.values[b-1]:{valueOrError:void 0}}function j(a){return c("BDSignalBufferData")[a].values}function k(a){var b={};for(a of a)a in c("BDSignalBufferData")&&(b[a]=j(a).map(function(a){return a.valueOrError}));return b}function l(a){a=m(a);return JSON.stringify(k(a))}function m(a){var b=c("BDClientConfig").get(),d=[];a.forEach(function(a){switch(a){case c("BDCollectionTypeEnum").STATIC:d=[].concat(d,b.getStaticSignals());break;case c("BDCollectionTypeEnum").DYNAMIC:d=[].concat(d,b.getDynamicSignals());break;case c("BDCollectionTypeEnum").BIOMETRIC:d=[].concat(d,b.getBiometricSignals());break}});return d.map(function(a){return a.signalType})}g.initialize=a;g.clearBuffer=i;g.appendSignal=b;g.clearBiometricSignals=e;g.getLastSignalFormatBySignalId=f;g.retrieveSignal=j;g.retrieveSignals=k;g.getSignalsAsJSONString=l;g._getSignalIdsByCollectionType=m}),98);
__d("BDUtils",[],(function(a,b,c,d,e,f){"use strict";function a(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(a){var b=Math.random()*16|0;a=a=="x"?b:b&3|8;return a.toString(16)})}f.uuid=a}),66);
__d("BdPdcSignalsFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1743095");b=d("FalcoLoggerInternal").create("bd_pdc_signals",a);e=b;g["default"]=e}),98);
__d("SignalCollectionManager",["BDBiometricSignalCollectorBase","BDClientConfig","BDLoggingConstants","BDOperationLogHelper","BDSignalBuffer","Promise","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h,i="SignalCollectionManager",j=function(){function a(){var a=this;this.$1=!1;this.$6=function(b){if(b instanceof CustomEvent&&b.detail!=null&&b.detail.data!=null){var d=c("BDClientConfig").get().getBiometricSignalsMap().get(b.detail.signalId);d!=null&&a.$4(b.detail.data,d)}}}var e=a.prototype;e.collectSignals=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var c=this,d=[];a.forEach(function(a){a.signalFlags.includes(4)?c.$2(a):d.push(c.$3(a))});yield (h||(h=b("Promise"))).all(d)});function c(b){return a.apply(this,arguments)}return c}();e.isEqualToLastCollectedSignal=function(a,b){var c=d("BDSignalBuffer").getLastSignalFormatBySignalId(b.signalType);return c.valueOrError==void 0?!1:a.isEqual(c.valueOrError,new Set(b.signalFlags))};e.getCircularBufferSize=function(a){var b=c("BDClientConfig").get(),d=b.getBufferSizeBySignalId(a.signalType);if(d!=null&&b.getBufferSizeBySignalId(a.signalType)>0)return d;if(!a.signalFlags.includes(2))return b.getStaticSignalBufferSize();else if(a.signalFlags.includes(4))return b.getBiometricSignalBufferSize();else return b.getDynamicSignalBufferSize()};e.$3=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=a.getSignalCollector();if(b!=null)try{b=(yield b.executeAsyncSignalCollection());this.$4(b,a)}catch(a){d("BDOperationLogHelper").logError(i,d("BDLoggingConstants").OPERATIONS.BD_EXCEPTION,{error:a})}});function c(b){return a.apply(this,arguments)}return c}();e.$2=function(a){this.$5();a=a.getSignalCollector();a!=null&&a instanceof d("BDBiometricSignalCollectorBase").BDBiometricSignalCollectorBase&&a.listenForSignals()};e.$5=function(){if(this.$1)return;window.addEventListener(d("BDBiometricSignalCollectorBase").BIOMETRIC_SIGNAL_COLLECTED_EVENT_NAME,this.$6);this.$1=!0};e.$4=function(a,b){b.getBufferConfig()==null&&d("BDSignalBuffer").initialize(b.signalType,this.getCircularBufferSize(b),b.signalFlags),a.valueOrError&&!this.isEqualToLastCollectedSignal(a.valueOrError,b)&&d("BDSignalBuffer").appendSignal(b.signalType,a)};return a}(),k=null;a={get:function(){k==null&&(k=new j());return k}};e=a;g["default"]=e}),98);
__d("BDClientSignalCollectionTrigger",["BDClientConfig","BDCollectionTypeEnum","BDLoggingConstants","BDOperationLogHelper","BDServerConfig","BDSignalBuffer","BDUtils","BdPdcSignalsFalcoEvent","Promise","SignalCollectionManager","WebStorage","asyncToGeneratorRuntime","javascript-blowfish"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j="BDClientSignalCollectionTrigger",k="signal_flush_timestamp",l=!1,m=!1,n=!1,o=!1,p=0,q=0,r=0,s=30,t=5,u,v,w=d("BDUtils").uuid(),x=c("BDClientConfig").get(),y=Object.freeze({NONE:0,VITAL:1,CRITICAL:2}),z={startSignalCollection:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){x.setSID(a.sid);if(!l){l=!0;d("BDServerConfig").parseConfig(a);if(!x.isParsingDone()){d("BDOperationLogHelper").logError(j,d("BDLoggingConstants").OPERATIONS.PARSE_CONFIG_ERROR,{config:JSON.stringify(a)});l=!1;return}a!=null&&(q=a.hbcbc&&a.hbcbc>0?a.hbcbc:q,r=a.hbvbc&&a.hbvbc>0?a.hbvbc:r,s=a.hbbi&&a.hbbi>0?a.hbbi:s);z.startHeartbeatDelayed();v=new(i||(i=b("Promise")))(function(a,b){try{x.getDynamicSignals().length>0&&(z.collectDynamicSignals(),d("BDOperationLogHelper").logInfo(j,d("BDLoggingConstants").OPERATIONS.DYNAMIC_SIGNAL_COLLECTION_STARTED,{ts:Date.now().toString()})),x.getBiometricSignals().length>0&&(z.collectBiometricSignals(),d("BDOperationLogHelper").logInfo(j,d("BDLoggingConstants").OPERATIONS.BIOMETRIC_SIGNAL_COLLECTION_STARTED,{ts:Date.now().toString()})),a()}catch(a){b(a)}});yield v;yield z.startSignalPosting()}l&&!n&&!o&&t>0&&(t-=1,d("BDOperationLogHelper").logWarning(j,d("BDLoggingConstants").OPERATIONS.TRY_RESTARTING_HB),z.startHeartbeatDelayed())});function c(b){return a.apply(this,arguments)}return c}(),retrieveSignals:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){yield v,yield z.postSignals([c("BDCollectionTypeEnum").DYNAMIC,c("BDCollectionTypeEnum").BIOMETRIC,c("BDCollectionTypeEnum").STATIC])});function d(){return a.apply(this,arguments)}return d}(),postSignals:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){yield z.collectStaticSignals(),z._postSignalsHelper(d("BDSignalBuffer").getSignalsAsJSONString(a),y.NONE)&&z.setTimestampInStorage(Date.now(),k)});function c(b){return a.apply(this,arguments)}return c}(),_postSignalsHelper:function(a,b){if(a.length<=2)return!1;var e=x.getConfigGenerationTimeStamp(),f=z.encryptDataUsingAsid(w,a);a=function(){return{asid:w,ct:e,sjd:f,sid:x.sid}};var g=!1;try{b===y.CRITICAL?c("BdPdcSignalsFalcoEvent").logCritical(a):b===y.VITAL?c("BdPdcSignalsFalcoEvent").logImmediately(a):c("BdPdcSignalsFalcoEvent").log(a),g=!0}catch(a){d("BDOperationLogHelper").logError(j,d("BDLoggingConstants").OPERATIONS.BANZAI_LOG_ERROR,a),g=!1}finally{return g}},getInitialVector:function(a){if(a.length!==16){d("BDOperationLogHelper").logError(j,d("BDLoggingConstants").OPERATIONS.INVALID_LENGTH);return""}var b="";for(var c=0;c<8;c++)b+=String.fromCharCode(a.charCodeAt(c)^a.charCodeAt(8+c));return b},encryptDataUsingAsid:function(a,b){if(a.length!==36){d("BDOperationLogHelper").logError(j,d("BDLoggingConstants").OPERATIONS.INVALID_GUID);return b}a=a.slice(19,23)+a.slice(24,36);var e=z.getInitialVector(a);a=new(c("javascript-blowfish"))(a,"cbc");return a.base64Encode(a.encrypt(b,e))},startSignalPosting:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var a=z.getTimestampInStorage(k);a=Date.now()-a;a>=x.getAllTiersFlushDurationMs()?yield z.postSignalsIntermittently():window.setTimeout(function(){z.postSignalsIntermittently()},x.getAllTiersFlushDurationMs()-a)});function c(){return a.apply(this,arguments)}return c}(),postSignalsIntermittently:function(){z.postSignals([c("BDCollectionTypeEnum").STATIC]),window.setInterval(function(){z.postSignals([c("BDCollectionTypeEnum").STATIC,c("BDCollectionTypeEnum").DYNAMIC,c("BDCollectionTypeEnum").BIOMETRIC])},x.getAllTiersFlushDurationMs())},setTimestampInStorage:function(a,b){var e=(h||(h=c("WebStorage"))).getLocalStorage();if(!e){d("BDOperationLogHelper").logWarning(j,d("BDLoggingConstants").OPERATIONS.GET_LOCAL_STORAGE_ERROR);return}e=h.setItemGuarded(e,b,a.toString());e!=null&&d("BDOperationLogHelper").logWarning(j,d("BDLoggingConstants").OPERATIONS.WEB_STORAGE,{error:e.message})},getTimestampInStorage:function(a){var b=(h||(h=c("WebStorage"))).getLocalStorage();if(!b){d("BDOperationLogHelper").logWarning(j,d("BDLoggingConstants").OPERATIONS.GET_LOCAL_STORAGE_ERROR);return 0}b=b.getItem(a);if(b==null)return 0;a=Number.parseInt(b,10);return Number.isFinite(a)?a:0},resetHeartbeatStartedForTest:function(){n=!1,o=!1},startHeartbeatDelayed:function(){if(n||o)return;var a=z.getTimestampInStorage(z.HEARTBEAT_TIMESTAMP_STORAGE_KEY);a=Date.now()-a;try{a>=x.getHeartbeatIntervalMs()?z.startHeartbeat():(window.setTimeout(function(){return z.startHeartbeat()},x.getHeartbeatIntervalMs()-a),o=!0)}catch(b){d("BDOperationLogHelper").logError(j,d("BDLoggingConstants").OPERATIONS.HB_START_FAILURE,{lastBeatSince:a.toString(),hbi:x.getHeartbeatIntervalMs().toString(),e:b})}},HEARTBEAT_TIMESTAMP_STORAGE_KEY:"hb_timestamp",startHeartbeat:function(){!n&&x.getHeartbeatIntervalMs()>0&&(z.collectHeartbeatTimes(q,r),p!==0&&(window.clearInterval(p),d("BDOperationLogHelper").logWarning(j,d("BDLoggingConstants").OPERATIONS.HB_ALREADY_RUNNING)),p=window.setInterval(function(){return z.collectHeartbeatTimes(q,r)},x.getHeartbeatIntervalMs()),n=!0)},collectHeartbeatTimes:function(a,b){if(a<=0&&b<=0)return;a>0&&z.collectHeartbeat(y.CRITICAL);b>0&&z.collectHeartbeat(y.VITAL);(a>1||b>1)&&window.setTimeout(function(){return z.collectHeartbeatTimes(a-1,b-1)},s*1e3)},collectHeartbeat:function(a){var b;b=x==null?void 0:(b=x.getHeartbeatSignal())==null?void 0:(b=b.getSignalCollector())==null?void 0:b.executeSignalCollection();if(b==null){d("BDOperationLogHelper").logError(j,d("BDLoggingConstants").OPERATIONS.HB_COLLECTION_FAILED,{urgency:a.toString()});return}else d("BDOperationLogHelper").logInfo(j,d("BDLoggingConstants").OPERATIONS.HB_COLLECTED,{urgency:a.toString()});z.postHeartbeat(a,b)&&z.setTimestampInStorage(Date.now(),z.HEARTBEAT_TIMESTAMP_STORAGE_KEY)},postHeartbeat:function(a,b){var c={};c[38001]=[b==null?void 0:b.valueOrError];b=JSON.stringify(c);return z._postSignalsHelper(b,a)},collectStaticSignals:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){yield z.collectSignalsOneTime(x.getStaticSignals())});function c(){return a.apply(this,arguments)}return c}(),collectDynamicSignals:function(){z.stopDynamicSignalCollection(),u=window.setInterval(b("asyncToGeneratorRuntime").asyncToGenerator(function*(){yield z.collectSignalsOneTime(x.getDynamicSignals())}),x.getPeriodicCollectionIntervalMs())},collectBiometricSignals:function(){z.collectSignalsOneTime(x.getBiometricSignals())},stopDynamicSignalCollection:function(){u!=null&&(window.clearInterval(u),u=null)},collectSignalsOneTime:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){yield c("SignalCollectionManager").get().collectSignals(a)});function d(b){return a.apply(this,arguments)}return d}(),startLoginTimeSignalCollection:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){m||(m=!0,d("BDServerConfig").parseConfig(a),yield z.collectSignalsOneTime(x.getDynamicSignals()),yield z.collectSignalsOneTime(x.getBiometricSignals()),yield z.collectSignalsOneTime(x.getStaticSignals()))});function c(b){return a.apply(this,arguments)}return c}(),getSignalsAsJSONString:function(){return d("BDSignalBuffer").getSignalsAsJSONString([c("BDCollectionTypeEnum").DYNAMIC,c("BDCollectionTypeEnum").STATIC,c("BDCollectionTypeEnum").BIOMETRIC])}};a=z;g["default"]=a}),98);
__d("BDCometSignalCollectionTrigger",["BDClientSignalCollectionTrigger","BDSignalCollectionData"],(function(a,b,c,d,e,f,g){"use strict";function a(){c("BDClientSignalCollectionTrigger").startSignalCollection(c("BDSignalCollectionData"))}g.initSignalCollection=a}),98);
__d("Chromedome",["fbt"],(function(a,b,c,d,e,f,g,h){function i(){if(document.domain==null)return null;var a=document.domain,b=/^intern\./.test(a);if(b)return null;b=/(^|\.)facebook\.(com|sg)$/.test(a);if(b)return"facebook";b=/(^|\.)instagram\.com$/.test(a);if(b)return"instagram";b=/(^|\.)threads\.(com|net)$/.test(a);if(b)return"threads";b=/(^|\.)messenger\.com$/.test(a);return b?"messenger":null}function j(a){if(a==="instagram")return h._(/*BTDS*/"This is a browser feature intended for developers. If someone told you to copy-paste something here to enable an Instagram feature or \"hack\" someone's account, it is a scam and will give them access to your Instagram account.");return a==="threads"?h._(/*BTDS*/"This is a browser feature intended for developers. If someone told you to copy-paste something here to enable a Threads feature or \"hack\" someone's account, it is a scam and will give them access to your Threads account."):h._(/*BTDS*/"This is a browser feature intended for developers. If someone told you to copy-paste something here to enable a Facebook feature or \"hack\" someone's account, it is a scam and will give them access to your Facebook account.")}function a(a){if(top!==window)return;a=i();if(a==null)return;var b=h._(/*BTDS*/"Stop!");a=j(a);var c=h._(/*BTDS*/"See {url} for more information.",[h._param("url","https://www.facebook.com/selfxss")]),d="font-family:helvetica; font-size:20px; ";[[b,d+"font-size:50px; font-weight:bold; color:red; -webkit-text-stroke:1px black;"],[a,d],[c,d],["",""]].map(function(a){window.setTimeout(console.log.bind(console,"\n%c"+a[0].toString(),a[1]))})}g.start=a}),226);
__d("ClientConsistencyFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1764786");b=d("FalcoLoggerInternal").create("client_consistency",a);e=b;g["default"]=e}),98);
__d("WebBrowserDimensionsTypedLoggerLite",["generateLiteTypedLogger"],(function(a,b,c,d,e,f){"use strict";e.exports=b("generateLiteTypedLogger")("logger:WebBrowserDimensionsLoggerConfig")}),null);
__d("CometBrowserDimensionsLogger",["CometDebounce","Cookie","ExecutionEnvironment","SiteData","WebBrowserDimensionsTypedLoggerLite","getViewportDimensions","isInIframe"],(function(a,b,c,d,e,f,g){"use strict";var h,i=null,j=!1,k={height:0,width:0};function l(){i===null&&(i=c("getViewportDimensions")());return i}function m(){var a=c("getViewportDimensions")(),b=a.height;a=a.width;(k.width!==a||k.height!==b)&&(k={height:b,width:a},c("Cookie").set("wd",a+"x"+b))}function n(){if(!(h||(h=c("ExecutionEnvironment"))).canUseDOM||c("isInIframe")()||j)return;j=!0;m();window.addEventListener("resize",c("CometDebounce")(m,{wait:250}),{passive:!0});window.addEventListener("focus",m,{passive:!0})}function a(a){var b,d=l();n();b={client_hint_width:a==null?void 0:a.clientHintWidth,pixel_ratio:(b=window.devicePixelRatio)!=null?b:1,screen_x:window.screen.width,screen_y:window.screen.height,server_pixel_ratio:c("SiteData").pr,server_viewport_x:a==null?void 0:a.viewportWidth,server_viewport_y:a==null?void 0:a.viewportHeight,viewport_x:d.width,viewport_y:d.height};window.navigator&&window.navigator.hardwareConcurrency&&(b.cpu_cores_count=window.navigator.hardwareConcurrency);if(window.innerWidth>0&&window.outerWidth>0){a=Math.round(window.outerWidth/window.innerWidth*100)/100;b.zoom_ratio=a}if(window.getComputedStyle&&document.documentElement!=null){d=window.getComputedStyle(document.documentElement);d!=null&&(b.document_font_size=d.fontSize)}c("WebBrowserDimensionsTypedLoggerLite").log(b)}g.init=a}),98);
__d("CometChromeDome",["JSScheduler","requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h,i=c("requireDeferred")("Chromedome").__setRef("CometChromeDome");function a(){i.onReady(function(a){(h||(h=d("JSScheduler"))).scheduleLoggingPriCallback(function(){a.start({})})})}g.init=a}),98);
__d("FDSButtonGroup.react",["CometFocusTableContext","CometRow.react","CometRowItem.react","FDSButton.react","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react")),j=h.useContext,k={resetFlexBasis:{flexBasis:"xdl72j9",$$css:!0}},l="secondary";function m(a){var b=j(c("CometFocusTableContext"));b=b.FocusCell;a=a.children;return b!=null?i.jsx(b,{children:a}):a}m.displayName=m.name+" [from "+f.id+"]";function a(a){var b=a.align,d=b===void 0?"justify":b;b=a.direction;b=b===void 0?"forward":b;var e=a.expanding;e=e===void 0?!1:e;var f=a.paddingHorizontal,g=a.paddingTop,h=a.paddingVertical,j=a.primary,n=a.secondary,o=a.secondaryIcon,p=a.secondaryIconGroup,q=a.size,r=a.verticalAlign;a=a.wrap;a=a===void 0?"none":a;var s=[],t=[],u=null;if(j!=null){var v=j.ref,w=j.testid;w=babelHelpers.objectWithoutPropertiesLoose(j,["ref","testid"]);u=i.jsx(m,{children:i.jsx(c("FDSButton.react"),babelHelpers["extends"]({},w,{ref:v,size:q,testid:void 0}))});s.push({hidden:i.jsx(c("FDSButton.react"),babelHelpers["extends"]({},w,{disabled:!0,padding:"normal",size:q})),visible:u})}if(n!=null){j=n.ref;v=n.testid;w=babelHelpers.objectWithoutPropertiesLoose(n,["ref","testid"]);s.push({hidden:i.jsx(c("FDSButton.react"),babelHelpers["extends"]({},w,{disabled:!0,padding:"normal",size:q,type:"secondary"})),visible:i.jsx(m,{children:i.jsx(c("FDSButton.react"),babelHelpers["extends"]({},w,{ref:j,size:q,testid:void 0,type:"secondary"}))})})}else if(o!=null){t.push(i.jsx(c("CometRowItem.react"),{children:i.jsx(m,{children:i.jsx(c("FDSButton.react"),babelHelpers["extends"]({},o,{labelIsHidden:!0,size:q,type:(v=o.type)!=null?v:l}))})},"secondary-icon"))}else if(p!=null){n=p.primaryIcon;w=p.secondaryIcon;t.push(i.jsx(c("CometRowItem.react"),{children:i.jsx(m,{children:i.jsx(c("FDSButton.react"),babelHelpers["extends"]({},n,{labelIsHidden:!0,size:q,type:(j=n.type)!=null?j:l}))})},"secondary-icon-1"),i.jsx(c("CometRowItem.react"),{children:i.jsx(m,{children:i.jsx(c("FDSButton.react"),babelHelpers["extends"]({},w,{labelIsHidden:!0,size:q,type:(o=w.type)!=null?o:l}))})},"secondary-icon-2"))}v=s.map(function(a,b){return i.jsx(c("CometRowItem.react"),{expanding:d==="justify",xstyle:k.resetFlexBasis,children:s.map(function(a,c){return i.jsx(i.Fragment,{children:b!==c?i.jsx("div",babelHelpers["extends"]({"aria-hidden":!0},{className:"xqtp20y x6ikm8r x10wlt62 xlshs6z"},{children:a.hidden})):a.visible},c)})},b)});p=u!=null?i.jsx(c("CometRowItem.react"),{expanding:d==="justify",xstyle:k.resetFlexBasis,children:u},"primary"):null;n=[p].concat(t);j=s.length===2;q=j||u==null?v:n;w=b==="forward"?q:q.reverse();return i.jsx(c("CometRow.react"),{align:d,expanding:e,paddingHorizontal:f,paddingTop:g,paddingVertical:h,spacing:8,verticalAlign:r,wrap:a,children:w})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("CometOnBeforeUnloadDialog.react",["fbt","CometTrackingNodeProvider.react","FDSButtonGroup.react","FDSCardedDialogLegacy.react","FDSText.react","react"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=i||d("react");function a(a){var b=a.body,d=a.cancel,e=a.confirm,f=a.disabled;f=f===void 0?!1:f;var g=a.onClose,i=a.onCloseButtonPress,k=a.primaryAction,l=a.secondaryAction;a=a.title;var m=function(){k(),g()},n=function(){l(),g()};return j.jsx(c("CometTrackingNodeProvider.react"),{trackingNode:163,children:j.jsxs(c("FDSCardedDialogLegacy.react"),{onClose:function(){i(),g()},testid:void 0,title:a,titleHorizontalAlignment:"start",withCloseButton:!0,children:[j.jsx("div",babelHelpers["extends"]({className:"xz9dl7a xsag5q8 xf7dkkf xv54qhq x1n2onr6"},{children:j.jsx(c("FDSText.react"),{type:"body3",children:b})})),j.jsx("div",babelHelpers["extends"]({className:"x78zum5 x1q0g3np x13a6bvl x1l90r2v xyri2b x1c1uobl xexx8yu"},{children:j.jsx(c("FDSButtonGroup.react"),{direction:"backward",paddingHorizontal:16,primary:{disabled:f,label:(a=e)!=null?a:h._(/*BTDS*/"Confirm"),onPress:m,testid:"CometOnBeforeUnloadDialogConfirmButton"},secondary:{disabled:f,label:(b=d)!=null?b:h._(/*BTDS*/"Cancel"),onPress:n,reduceEmphasis:!0,testid:"CometOnBeforeUnloadDialogCancelButton"}})}))]})})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),226);
__d("CometPixelRatioUpdater",["Cookie","Run","WebPixelRatio"],(function(a,b,c,d,e,f,g){"use strict";var h="dpr";function i(a){c("Cookie").set(h,String(a))}function j(){window.devicePixelRatio!==d("WebPixelRatio").get()&&i(window.devicePixelRatio)}function a(){j(),d("Run").onBeforeUnload(j,!1),"onpagehide"in window&&window.addEventListener("pageHide",j)}g.startDetecting=a}),98);
__d("CometVirtualCursorStatus",["UserAgent","cr:1345969","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h=null,i=null;function j(){i||(i=window.addEventListener("blur",function(){h=null,k()}))}function k(){i&&(i.remove(),i=null)}function a(a){h=a.keyCode,j()}function d(a){h=null,k()}if(typeof window!=="undefined"&&window.document&&window.document.createElement){f=document.documentElement;if(f)if(f.addEventListener)f.addEventListener("keydown",a,!0),f.addEventListener("keyup",d,!0);else if(f.attachEvent){f=f.attachEvent;f("onkeydown",a);f("onkeyup",d)}}var l={getKeyDownCode:function(){return h},isKeyDown:function(){return!!h}},m=!1,n=!1,o=null,p=!1;function q(a){var b=new Set(),d=l.isKeyDown(),e=a.WEBKIT_FORCE_AT_MOUSE_DOWN,f=a.clientX,g=a.clientY,h=a.isTrusted,i=a.mozInputSource,j=a.offsetX,k=a.offsetY,o=a.webkitForce;f===0&&g===0&&j>=0&&k>=0&&n&&h&&i==null&&b.add("Chrome");m&&n&&!d&&o!=null&&o<e&&j===0&&k===0&&i==null&&b.add("Safari-edge");f===0&&g===0&&j<0&&k<0&&n&&i==null&&b.add("Safari-old");if(!m&&!n&&!d&&h&&c("UserAgent").isBrowser("IE >= 10")&&i==null){o=a.target;e=o.clientHeight;d=o.clientWidth;f<0&&g<0?b.add("IE"):(j<0||j>d)&&(k<0||k>e)&&b.add("MSIE")}i===0&&h&&b.add("Firefox");return b}function r(){m=!0,c("setTimeout")(function(){m=!1},0)}function s(){n=!0,c("setTimeout")(function(){n=!1},0)}function t(a){p&&b("cr:1345969").log({extra_data:{source_app:"comet"},indicated_browsers:o,is_virtual_cursor_action:a})}function u(a){o===null&&(o=Array.from(q(a)));p=o!=null&&o.length>0;a=a.target!=null&&a.target.getAttribute!=null&&a.target.getAttribute("data-accessibilityid")==="virtual_cursor_trigger";t(a);c("setTimeout")(function(){p=!1,o=null},0)}function e(){document.addEventListener("click",u,!0),document.addEventListener("mousedown",r,!0),document.addEventListener("mouseup",s,!0)}g.initLogging=e}),98);
__d("SimplePageLoadLogger",["ODS","performance"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(){(i||(i=d("ODS"))).bumpEntityKey(1678,"www_client_navigations",j())}function j(){if((h||(h=c("performance"))).navigation==null||(h||(h=c("performance"))).navigation.type==null)return"UNSUPPORTED";switch((h||(h=c("performance"))).navigation.type){case(h||(h=c("performance"))).navigation.TYPE_NAVIGATE:return"NAVIGATE";case(h||(h=c("performance"))).navigation.TYPE_RELOAD:return"RELOAD";case(h||(h=c("performance"))).navigation.TYPE_BACK_FORWARD:return"BACK_FORWARD";case(h||(h=c("performance"))).navigation.TYPE_RESERVED:return"RESERVED";default:return"UNKNOWN"}}g.logPageNavigationType=a}),98);
__d("StringTransformations",[],(function(a,b,c,d,e,f){"use strict";a={unicodeEscape:function(a){return a.replace(/[^A-Za-z0-9\-\.\:\_\$\/\+\=]/g,function(a){a=a.charCodeAt(0).toString(16);return"\\u"+("0000"+a.toUpperCase()).slice(-4)})},unicodeUnescape:function(a){return a.replace(/(\\u[0-9A-Fa-f]{4})/g,function(a){return String.fromCharCode(parseInt(a.slice(2),16))})}};f["default"]=a}),66);
__d("WebStorageMonster",["AsyncRequest","CacheStorage","Event","ExecutionEnvironment","NetworkStatus","StringTransformations","UserActivity","WebStorage","WebStorageCleanupReason","WebStorageMonsterLoggingURI","ifRequired","isEmpty","setTimeoutAcrossTransitions"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=1e4,l=5,m=!1;function n(a){var b={};for(var d in a){var e=a.getItem(d),f=c("StringTransformations").unicodeEscape(d);typeof e==="string"&&(b[f]=e.length)}return b}function o(a){var b=(h||(h=c("WebStorage"))).getLocalStorage();if(!b||a==null||!a.keys)return;r._getLocalStorageKeys().forEach(function(c){a.keys.includes(c)&&b.removeItem(c)})}function p(a){var b=(h||(h=c("WebStorage"))).getLocalStorage();b&&r._getLocalStorageKeys().forEach(function(c){a.some(function(a){return new RegExp(a).test(c)})||b.removeItem(c)})}function q(a,b){a===void 0&&(a=!1);b===void 0&&(b=k);if(c("UserActivity").isActive(b)){var d=Math.max(k,Math.floor(b/l));c("setTimeoutAcrossTransitions")(function(){q(a,d)},d)}else{r.cleanNow(a);var e=b*l;c("setTimeoutAcrossTransitions")(function(){q(a,e)},e)}}var r={_getLocalStorageKeys:function(){var a=(h||(h=c("WebStorage"))).getLocalStorage();return a?Object.keys(a):[]},cleanNow:function(a){a===void 0&&(a=!1);var b=Date.now(),d={},e=(h||(h=c("WebStorage"))).getLocalStorage();e&&(d.local_storage=n(e));e=h.getSessionStorage();e&&(d.session_storage=n(e));e=!(i||(i=c("isEmpty")))(d);var f=Date.now();d.logtime=f-b;if(e){var g,j=c("WebStorageMonsterLoggingURI").uri;if(j===null)return null;var k=function(){new(c("AsyncRequest"))(j).setData(d).setHandler(function(b){b=b.getPayload();b&&b.keys&&(b.keys=b.keys.map(c("StringTransformations").unicodeUnescape));a||o(b);c("NetworkStatus").reportSuccess()}).setErrorHandler(function(){c("NetworkStatus").reportError()}).setOption("retries",2).send()};if(c("NetworkStatus").isOnline())k();else{f=function(a){a=a.online;a&&(k(),g.remove())};g=c("NetworkStatus").onChange(f)}}},cleanOnLogout:function(a,b){d("WebStorageCleanupReason").setLastCleanupReason(b);c("CacheStorage").disablePersistentWrites();c("ifRequired")("WebAsyncStorage",function(a){a.disablePersistentWrites()});a?p(a):p([]);b=(h||(h=c("WebStorage"))).getSessionStorage();b&&b.clear();c("ifRequired")("WebAsyncStorage",function(a){a.clear(function(){})})},registerLogoutForm:function(a,b){c("Event").listen(a,"submit",function(a){r.cleanOnLogout(b,"WebStorageMonster.registerLogoutForm")})},schedule:function(a){a===void 0&&(a=!1);if(m||!(j||(j=c("ExecutionEnvironment"))).isInBrowser)return;m=!0;q(a)}};a=r;g["default"]=a}),98);
__d("setCometDeferredCookies",["DeferredCookie","ErrorGuard"],(function(a,b,c,d,e,f,g){"use strict";var h,i=!1,j=new Set();function a(a){i?a():j.add(a)}function b(a){Object.entries(a).forEach(function(a){var b=a[0];a=a[1];var d=a.expiration_for_js,e=a.first_party_only,f=a.path,g=a.secure;a=a.value;c("DeferredCookie").addToQueue(b,a,d,f,e,!1,g)}),i=!0,j.forEach(function(a){return(h||(h=c("ErrorGuard"))).applyWithGuard(a,null,[])}),j.clear()}g.onCometDeferredCookieSet=a;g.setCometDeferredCookies=b}),98);
__d("CometRootDeferredShared",["CometBootloaderLoggerUtil","CometOfflineTracing","CometVirtualCursorStatus","ErrorGuard","JSScheduler","RunComet","SimplePageLoadLogger","WebStorageMonster","cr:1201738","cr:1332233","cr:1516609","cr:17080","cr:1813330","cr:2718","cr:9041","gkx","setCometDeferredCookies","vc-tracker"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=null;function a(a){var e=a.blLoggingCavalryFields,f=a.deferredCookies;a=a.networkStatusToast;var g=a===void 0?b("cr:17080"):a;(i||(i=c("ErrorGuard"))).applyWithGuard(function(){return d("CometBootloaderLoggerUtil").initLogging(e)},null,[]);i.applyWithGuard(function(){return d("CometVirtualCursorStatus").initLogging()},null,[]);g!=null&&!c("gkx")("11394")&&(i||(i=c("ErrorGuard"))).applyWithGuard(function(){return g.subscribe()},null,[]);i.applyWithGuard(function(){return d("CometOfflineTracing").initialize()},null,[]);i.applyWithGuard(function(){return d("SimplePageLoadLogger").logPageNavigationType()},null,[]);(h||(h=d("JSScheduler"))).scheduleSpeculativeCallback(function(){c("WebStorageMonster").schedule()});b("cr:1201738")&&c("vc-tracker").VisualCompletionTraceObserver.subscribe(function(a){a.interactionType==="INITIAL_LOAD"&&b("cr:1201738").init("comet.idle")});j&&c("vc-tracker").VisualCompletionTraceObserver.subscribe(function(a){a.interactionType==="INITIAL_LOAD"&&j.init()});d("setCometDeferredCookies").setCometDeferredCookies(f);b("cr:1332233")&&(i||(i=c("ErrorGuard"))).applyWithGuard(function(){return b("cr:1332233")()},null,[]);b("cr:1516609")!=null&&(i||(i=c("ErrorGuard"))).applyWithGuard(function(){return b("cr:1516609").initSignalCollection()},null,[]);b("cr:1813330")&&(i||(i=c("ErrorGuard"))).applyWithGuard(function(){return b("cr:1813330").init()},null,[]);b("cr:2718")&&(i||(i=c("ErrorGuard"))).applyWithGuard(function(){return b("cr:2718").init()},null,[]);if(b("cr:9041"))var k=d("RunComet").onAfterLoad(function(){(i||(i=c("ErrorGuard"))).applyWithGuard(function(){b("cr:9041")==null?void 0:b("cr:9041").startRSTFromMainThread(),k.remove()},null,[])})}g.initDeferred=a}),98);
__d("IGDSPrivateToast.react",["BaseTheme.react","IGDSBox.react","IGDSTextVariants.react","IGDSThemeConfig","react","react-compiler-runtime","useCurrentDisplayMode","useFadeEffect"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j="x1xuf6tn-B",k={messageBox:{flexBasis:"x1t1x2f9",flexGrow:"x1iyjqo2",flexShrink:"xs83m0k",height:"x5yr21d",overflowX:"xw2csxc",overflowY:"x1odjw0f",$$css:!0},rootBox:{flexBasis:"x1t1x2f9",flexGrow:"x1iyjqo2",flexShrink:"xs83m0k",$$css:!0}};function a(a){var b=d("react-compiler-runtime").c(31),e=a.ref,f=a.actionComponent,g=a["data-testid"],h=a.icon,l=a.isVisible,m=a.message,n=a.messageSecondary,o=a.onClose;a=a.target;a=a===void 0?"center":a;var p=c("useFadeEffect")(l),q=p[1];p=p[2];var r=c("useCurrentDisplayMode")();r=r==="dark"?"light":"dark";var s;b[0]!==f||b[1]!==l||b[2]!==q||b[3]!==a?(s={0:{className:"x6s0dn4 x1jx94hy x1lq5wgf xgqcy7u x30kzoy x9jhf4c xdy0x9s x78zum5 x2lah0s x1qughib xg01cxk xyamay9 x1xnnf8n x1l90r2v x106a9eq x2b8uid xeroabh x1ww3oar"},4:{className:"x6s0dn4 x1jx94hy x1lq5wgf xgqcy7u x30kzoy x9jhf4c xdy0x9s x78zum5 x2lah0s x1qughib xg01cxk xyamay9 x1xnnf8n x1l90r2v x106a9eq x2b8uid"},2:{className:"x6s0dn4 x1jx94hy x1lq5wgf xgqcy7u x30kzoy x9jhf4c xdy0x9s x78zum5 x2lah0s x1qughib xyamay9 x1xnnf8n x1l90r2v x106a9eq x2b8uid xeroabh x1ww3oar xs4xyr0 x1wpepyw x4hg4is x1iy03kw"},6:{className:"x6s0dn4 x1jx94hy x1lq5wgf xgqcy7u x30kzoy x9jhf4c xdy0x9s x78zum5 x2lah0s x1qughib xyamay9 x1xnnf8n x1l90r2v x106a9eq x2b8uid xs4xyr0 x1wpepyw x4hg4is x1iy03kw"},1:{className:"x6s0dn4 x1jx94hy x1lq5wgf xgqcy7u x30kzoy x9jhf4c xdy0x9s x78zum5 x2lah0s x1qughib xyamay9 x1xnnf8n x1l90r2v x106a9eq x2b8uid xeroabh x1ww3oar xs4xyr0 xqf3suz x4hg4is xg01cxk"},5:{className:"x6s0dn4 x1jx94hy x1lq5wgf xgqcy7u x30kzoy x9jhf4c xdy0x9s x78zum5 x2lah0s x1qughib xyamay9 x1xnnf8n x1l90r2v x106a9eq x2b8uid xs4xyr0 xqf3suz x4hg4is xg01cxk"},3:{className:"x6s0dn4 x1jx94hy x1lq5wgf xgqcy7u x30kzoy x9jhf4c xdy0x9s x78zum5 x2lah0s x1qughib xyamay9 x1xnnf8n x1l90r2v x106a9eq x2b8uid xeroabh x1ww3oar xs4xyr0 xqf3suz x4hg4is xg01cxk"},7:{className:"x6s0dn4 x1jx94hy x1lq5wgf xgqcy7u x30kzoy x9jhf4c xdy0x9s x78zum5 x2lah0s x1qughib xyamay9 x1xnnf8n x1l90r2v x106a9eq x2b8uid xs4xyr0 xqf3suz x4hg4is xg01cxk"}}[!!(a==="center"&&f==null)<<2|!!q<<1|!!!l<<0],b[0]=f,b[1]=l,b[2]=q,b[3]=a,b[4]=s):s=b[4];b[5]!==o?(l=function(a){a.animationName===j&&o!=null&&o()},b[5]=o,b[6]=l):l=b[6];b[7]!==h?(q=h!=null&&i.jsx(c("IGDSBox.react"),{marginEnd:3,children:h}),b[7]=h,b[8]=q):q=b[8];b[9]!==m?(a=i.jsx(d("IGDSTextVariants.react").IGDSTextBody,{maxLines:3,textAlign:"start",zeroMargin:!0,children:m}),b[9]=m,b[10]=a):a=b[10];b[11]!==n?(h=n!=null&&i.jsx(c("IGDSBox.react"),{marginTop:1,children:i.jsx(d("IGDSTextVariants.react").IGDSTextBody,{color:"secondaryText",maxLines:1,textAlign:"start",zeroMargin:!0,children:n})}),b[11]=n,b[12]=h):h=b[12];b[13]!==a||b[14]!==h?(m=i.jsxs(c("IGDSBox.react"),{direction:"column",justifyContent:"center",wrap:!0,xstyle:k.messageBox,children:[a,h]}),b[13]=a,b[14]=h,b[15]=m):m=b[15];b[16]!==q||b[17]!==m?(n=i.jsxs(c("IGDSBox.react"),{alignItems:"center",containerRef:e,direction:"row",overflow:"hidden",xstyle:k.rootBox,children:[q,m]}),b[16]=q,b[17]=m,b[18]=n):n=b[18];b[19]!==f?(a=f!=null&&i.jsx("div",babelHelpers["extends"]({className:"x1c4vz4f x2lah0s"},{children:f})),b[19]=f,b[20]=a):a=b[20];b[21]!==s||b[22]!==l||b[23]!==n||b[24]!==a?(h=i.jsxs("div",babelHelpers["extends"]({},s,{onAnimationEnd:l,children:[n,a]})),b[21]=s,b[22]=l,b[23]=n,b[24]=a,b[25]=h):h=b[25];b[26]!==p||b[27]!==g||b[28]!==r||b[29]!==h?(e=i.jsx(c("BaseTheme.react"),{"aria-atomic":!0,config:c("IGDSThemeConfig"),displayMode:r,ref:p,role:"alert",testid:void 0,children:h}),b[26]=p,b[27]=g,b[28]=r,b[29]=h,b[30]=e):e=b[30];return e}g["default"]=a}),98);
__d("IGDSPrivateToastContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext({onHideFactory:null});g["default"]=b}),98);
__d("IGDSPrivateToaster.react",["BaseContextualLayer.react","DateConsts","IGDSPrivateToast.react","IGDSPrivateToastContext","PolarisUA","clearTimeout","react","react-compiler-runtime","setTimeout","stylex","useSinglePartialViewImpression"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react"));b=i;b.useCallback;var k=b.useEffect,l=b.useLayoutEffect;b.useMemo;var m=b.useRef,n=b.useState,o={bottomContainer:{alignItems:"x6s0dn4",bottom:"x3h4tne",display:"x78zum5",flexDirection:"xdt5ytf",left:"x1nrll8i",start:null,end:null,position:"xixxii4",transform:"xjebx4m",$$css:!0},bottomLeftContainer:{alignItems:"x6s0dn4",bottom:"x3h4tne",display:"x78zum5",flexDirection:"xdt5ytf",start:"x3f4u1h",left:null,right:null,position:"xixxii4",$$css:!0},bottomRightContainer:{alignItems:"x6s0dn4",bottom:"x3h4tne",display:"x78zum5",flexDirection:"xdt5ytf",end:"x1a87ojn",left:null,right:null,position:"xixxii4",$$css:!0},centerContainer:{left:"x1nrll8i",start:null,end:null,position:"xixxii4",top:"xwa60dl",transform:"x11lhmoz",zIndex:"x1vjfegm",$$css:!0}},p=3*d("DateConsts").MS_PER_SEC;function q(a){var b=d("react-compiler-runtime").c(29),e=a.config;a=a.toast;var f=n("transition"),g=f[0],h=f[1];f=n(null);var i=f[0],o=f[1],q=e.isVisible===!1;f=n(q);var r=f[0],s=f[1],t=m(null),v=e.isSticky!=null&&e.isSticky,w=e.duration!=null?e.duration:p,x;b[0]!==q?(f=function(){return s(!q)},x=[q],b[0]=q,b[1]=f,b[2]=x):(f=b[1],x=b[2]);l(f,x);b[3]!==w||b[4]!==v||b[5]!==r?(f=function(){r&&!v?t.current=c("setTimeout")(function(){s(!1)},w):c("clearTimeout")(t.current);return function(){return c("clearTimeout")(t.current)}},b[3]=w,b[4]=v,b[5]=r,b[6]=f):f=b[6];b[7]!==e||b[8]!==w||b[9]!==v||b[10]!==r?(x=[r,e,w,v],b[7]=e,b[8]=w,b[9]=v,b[10]=r,b[11]=x):x=b[11];k(f,x);b[12]!==e||b[13]!==g||b[14]!==i?(f=function(){i==null?void 0:i(g),e.onAfterHide==null?void 0:e.onAfterHide()},b[12]=e,b[13]=g,b[14]=i,b[15]=f):f=b[15];var y=f;b[16]===Symbol["for"]("react.memo_cache_sentinel")?(x=function(a){o(function(b){return a});return function(a){s(!1),h(a)}},b[16]=x):x=b[16];f=x;b[17]===Symbol["for"]("react.memo_cache_sentinel")?(x={onHideFactory:f},b[17]=x):x=b[17];f=x;x=f;b[18]===Symbol["for"]("react.memo_cache_sentinel")?(f={className:"xjkvuk6"},b[18]=f):f=b[18];var z;b[19]!==r||b[20]!==y?(z=function(){r||y()},b[19]=r,b[20]=y,b[21]=z):z=b[21];b[22]!==e||b[23]!==r||b[24]!==a?(x=j.jsx(c("IGDSPrivateToastContext").Provider,{value:x,children:j.jsx(u,{config:e,isVisible:r,toast:a})}),b[22]=e,b[23]=r,b[24]=a,b[25]=x):x=b[25];b[26]!==z||b[27]!==x?(a=j.jsx("div",babelHelpers["extends"]({},f,{onAnimationEnd:z,children:x})),b[26]=z,b[27]=x,b[28]=a):a=b[28];return a}function a(a){var b=d("react-compiler-runtime").c(23),e=a["data-testid"];a=a.items;var f;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(f=d("PolarisUA").isMobile(),b[0]=f):f=b[0];var g=f;f=m(null);if(a.length===0)return null;var h,i,k,l;b[1]!==a?(l=[],k=[],h=[],i=[],a.map(function(a){bb5:switch(a.toast.target){case"center":l.push(a);break bb5;case"bottom":k.push(a);break bb5;case"bottom-left":g?k.push(a):h.push(a);break bb5;case"bottom-right":g?k.push(a):i.push(a);break bb5;default:v(a.toast)?k.push(a):l.push(a)}}),b[1]=a,b[2]=h,b[3]=i,b[4]=k,b[5]=l):(h=b[2],i=b[3],k=b[4],l=b[5]);b[6]!==l||b[7]!==e?(a=j.jsx(s,{"data-testid":void 0,items:l,target:"center"}),b[6]=l,b[7]=e,b[8]=a):a=b[8];var n;b[9]!==k||b[10]!==e?(n=j.jsx(s,{"data-testid":void 0,items:k,target:"bottom"}),b[9]=k,b[10]=e,b[11]=n):n=b[11];var o;b[12]!==h||b[13]!==e?(o=j.jsx(s,{"data-testid":void 0,items:h,target:"bottom-left"}),b[12]=h,b[13]=e,b[14]=o):o=b[14];var p;b[15]!==i||b[16]!==e?(p=j.jsx(s,{"data-testid":void 0,items:i,target:"bottom-right"}),b[15]=i,b[16]=e,b[17]=p):p=b[17];b[18]!==a||b[19]!==n||b[20]!==o||b[21]!==p?(e=j.jsxs(c("BaseContextualLayer.react"),{contextRef:f,children:[a,n,o,p]}),b[18]=a,b[19]=n,b[20]=o,b[21]=p,b[22]=e):e=b[22];return e}function r(a){switch(a){case"center":return o.centerContainer;case"bottom-left":return o.bottomLeftContainer;case"bottom-right":return o.bottomRightContainer;case"bottom":default:return o.bottomContainer}}function s(a){var b=d("react-compiler-runtime").c(8),e=a["data-testid"],f=a.items;a=a.target;if(f.length>0){var g;b[0]!==a?(g=(h||(h=c("stylex"))).props(r(a)),b[0]=a,b[1]=g):g=b[1];b[2]!==f?(a=f.map(t),b[2]=f,b[3]=a):a=b[3];b[4]!==e||b[5]!==g||b[6]!==a?(f=j.jsx("div",babelHelpers["extends"]({},g,{"data-testid":void 0,children:a})),b[4]=e,b[5]=g,b[6]=a,b[7]=f):f=b[7];return f}return null}function t(a){var b=a.config;a=a.toast;return j.jsx(q,{config:b,toast:a},b.key)}t.displayName=t.name+" [from "+f.id+"]";function u(a){var b=d("react-compiler-runtime").c(12),e=a.config,f=a.isVisible;a=a.toast;var g=e==null?void 0:e.onImpressionCallback,h;b[0]!==g?(h={onImpressionStart:g},b[0]=g,b[1]=h):h=b[1];g=c("useSinglePartialViewImpression")(h);b[2]!==e.key||b[3]!==e.onAfterHide||b[4]!==f||b[5]!==a.actionComponent||b[6]!==a.icon||b[7]!==a.message||b[8]!==a.messageSecondary||b[9]!==a.target||b[10]!==g?(h=j.jsx(c("IGDSPrivateToast.react"),{actionComponent:a.actionComponent,"data-testid":void 0,icon:a.icon,isVisible:f,message:a.message,messageSecondary:a.messageSecondary,onClose:e.onAfterHide,ref:g,target:a.target}),b[2]=e.key,b[3]=e.onAfterHide,b[4]=f,b[5]=a.actionComponent,b[6]=a.icon,b[7]=a.message,b[8]=a.messageSecondary,b[9]=a.target,b[10]=g,b[11]=h):h=b[11];return h}function v(a){return a.actionComponent!=null}g["default"]=a}),98);
__d("InstagramWebNotificationFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("5614");b=d("FalcoLoggerInternal").create("instagram_web_notification",a);e=b;g["default"]=e}),98);
__d("LogWebMemoryUsageFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1765264");b=d("FalcoLoggerInternal").create("log_web_memory_usage",a);e=b;g["default"]=e}),98);
__d("PolarisBackgroundFeedDataFetchEager",["CometRelay","CurrentUser","PolarisFeedActionLoadFeedPageExtras","PolarisFeedQueryConstants","PolarisFeedTimelineRootV2Query.graphql","PolarisRelayEnvironmentFactory","getPolarisFeedInitialPageSize.entrypointutils","gkx"],(function(a,b,c,d,e,f,g){"use strict";function a(a){var b=c("PolarisRelayEnvironmentFactory").getForActorID(c("CurrentUser").getPossiblyNonFacebookUserID()),e=d("PolarisFeedQueryConstants").FEED_TIMELINE_REQUEST_BODY,f=c("gkx")("4142");d("CometRelay").fetchQuery(b,c("PolarisFeedTimelineRootV2Query.graphql"),{data:e,first:d("getPolarisFeedInitialPageSize.entrypointutils").getPolarisFeedInitialPageSize(),pass_prefetch_pagination_gk:f,variant:String("home")}).subscribe({});a.dispatch(d("PolarisFeedActionLoadFeedPageExtras").loadFeedPageExtras())}g.runBackgroundFeedDataFetch=a}),98);
__d("PolarisFBSDK",[],(function(a,b,c,d,e,f){function a(){try{window.FB&&!window.FB.__buffer||(function(){var c=Function.prototype.apply;function d(a,b){return function(){return c.call(a,b,arguments)}}var b={__type:"JS_SDK_SANDBOX",window:window,document:window.document},e=["setTimeout","setInterval","clearTimeout","clearInterval"];for(var f=0;f<e.length;f++)b[e[f]]=d(window[e[f]],window);(function(){var a=window,c=this,d={};(function(a){var b={},e=function(a,b){if(!a&&!b)return null;var c={};typeof a!=="undefined"&&(c.type=a);typeof b!=="undefined"&&(c.signature=b);return c},f=function(a,b){return e(a&&/^[A-Z]/.test(a)?a:void 0,b&&(b.params&&b.params.length||b.returns)?"function("+(b.params?b.params.map(function(a){return/\?/.test(a)?"?"+a.replace("?",""):a}).join(","):"")+")"+(b.returns?":"+b.returns:""):void 0)};a=function(a,b,c){return a};var g=function(a,b,e){"sourcemeta"in d&&(a.__SMmeta=b);"typechecks"in d&&(b=f(b?b.name:void 0,e),b&&c.__w(a,b));return a},h=function(a,b,c){return c.apply(a,b)},i;(function(a,b,d,e){e&&e.params&&c.__t.apply(a,e.params);d=d.apply(a,b);e&&e.returns&&c.__t([d,e.returns]);return d});i=function(a,c,d,f,e){e&&(e.callId||(e.callId=e.module+":"+(e.line||0)+":"+(e.column||0)),f=e.callId,b[f]=(b[f]||0)+1);return d.apply(a,c)};typeof d==="undefined"?(c.__annotator=a,c.__bodyWrapper=h):(c.__annotator=g,"codeusage"in d?(c.__annotator=a,c.__bodyWrapper=i,c.__bodyWrapper.getCodeUsage=function(){return b},c.__bodyWrapper.clearCodeUsage=function(){b={}}):"typechecks"in d?c.__bodyWrapper=h:c.__bodyWrapper=h)})(typeof c!=="undefined"?c:typeof b!=="undefined"?b:typeof window!=="undefined"?window:typeof this!=="undefined"?this:typeof a!=="undefined"?a:{});(function(a){a.__t=function(a){return a[0]},a.__w=function(a){return a}})(typeof c!=="undefined"?c:typeof b!=="undefined"?b:typeof window!=="undefined"?window:typeof this!=="undefined"?this:typeof a!=="undefined"?a:{});(function(b){var d={},e=["global","require","requireDynamic","requireLazy","module","exports"],f=["global","require","importDefault","importNamespace","requireLazy","module","exports"],g=1,h=32,i=64,j={},a=Object.prototype.hasOwnProperty;function k(c,j){if(!a.call(d,c)){if(j)return null;throw new Error("Module "+c+" has not been defined")}j=d[c];if(j.resolved)return j;c=j.special;var k=j.factory.length,o=c&h?f.concat(j.deps):e.concat(j.deps),p=[],q;for(var r=0;r<k;r++){switch(o[r]){case"module":q=j;break;case"exports":q=j.exports;break;case"global":q=b;break;case"require":q=l;break;case"requireDynamic":q=null;break;case"requireLazy":q=null;break;case"importDefault":q=m;break;case"importNamespace":q=n;break;default:typeof o[r]==="string"&&(q=l.call(null,o[r]))}p.push(q)}o=j.factory.apply(b,p);o&&(j.exports=o);c&i?j.exports!=null&&a.call(j.exports,"default")&&(j.defaultExport=j.exports["default"]):j.defaultExport=j.exports;j.resolved=!0;return j}function l(a,b){a=k(a,b);if(a)return a.defaultExport!==j?a.defaultExport:a.exports}function m(a){a=k(a);if(a)return a.defaultExport!==j?a.defaultExport:null}function n(a){a=k(a);if(a)return a.exports}function o(a,b,c,e){typeof c==="function"?(d[a]={factory:c,deps:b,defaultExport:j,exports:{},special:e||0,resolved:!1},e!=null&&e&g&&l.call(null,a)):d[a]={defaultExport:c,exports:c,resolved:!0}}c.__d=o;c.require=l;c.importDefault=m;c.importNamespace=n;c.$RefreshReg$=function(){};c.$RefreshSig$=function(){return function(a){return a}}})(this);c.__d("ES5FunctionPrototype",[],function(a,b,c,d,e,f){a={bind:function(a){if(typeof this!=="function")throw new TypeError("Bind must be called on a function");var b=this,c=Array.prototype.slice.call(arguments,1);function d(){return b.apply(a,c.concat(Array.prototype.slice.call(arguments)))}d.displayName="bound:"+(b.displayName||b.name||"(?)");d.toString=function(){return"bound: "+b};return d}},b=a,f["default"]=b},66);c.__d("ES5StringPrototype",[],function(a,b,c,d,e,f){a={startsWith:function(a){var b=String(this);if(this==null)throw new TypeError("String.prototype.startsWith called on null or undefined");var c=arguments.length>1?Number(arguments[1]):0;isNaN(c)&&(c=0);var d=Math.min(Math.max(c,0),b.length);return b.indexOf(String(a),c)==d},endsWith:function(a){var b=String(this);if(this==null)throw new TypeError("String.prototype.endsWith called on null or undefined");var c=b.length,d=String(a),e=arguments.length>1?Number(arguments[1]):c;isNaN(e)&&(e=0);var f=Math.min(Math.max(e,0),c),g=f-d.length;return g<0?!1:b.lastIndexOf(d,g)==g},includes:function(a){if(this==null)throw new TypeError("String.prototype.contains called on null or undefined");var b=String(this),c=arguments.length>1?Number(arguments[1]):0;isNaN(c)&&(c=0);return b.indexOf(String(a),c)!=-1},repeat:function(a){if(this==null)throw new TypeError("String.prototype.repeat called on null or undefined");var b=String(this);a=a?Number(a):0;isNaN(a)&&(a=0);if(a<0||a===Infinity)throw RangeError();if(a===1)return b;if(a===0)return"";var c="";while(a)a&1&&(c+=b),(a>>=1)&&(b+=b);return c}},b=a,f["default"]=b},66);c.__d("ES6Array",[],function(a,b,c,d,e,f){"use strict";a={from:function(b){if(b==null)throw new TypeError("Object is null or undefined");var c=arguments[1],d=arguments[2],e=this,f=Object(b),g=typeof Symbol==="function"&&navigator.userAgent.indexOf("Trident/7.0")===-1?typeof Symbol==="function"?typeof Symbol==="function"?Symbol.iterator:"@@iterator":"@@iterator":"@@iterator",h=typeof c==="function",i=typeof f[g]==="function",a=0,j,k;if(i){j=typeof e==="function"?new e():[];var l=f[g](),m;while(!(m=l.next()).done)k=m.value,h&&(k=c.call(d,k,a)),j[a]=k,a+=1;j.length=a;return j}var n=f.length;(isNaN(n)||n<0)&&(n=0);j=typeof e==="function"?new e(n):new Array(n);while(a<n)k=f[a],h&&(k=c.call(d,k,a)),j[a]=k,a+=1;j.length=a;return j}},b=a,f["default"]=b},66);c.__d("ES6ArrayPrototype",[],function(a,b,c,d,e,f){var g={find:function(a,b){if(this==null)throw new TypeError("Array.prototype.find called on null or undefined");if(typeof a!=="function")throw new TypeError("predicate must be a function");a=g.findIndex.call(this,a,b);return a===-1?void 0:this[a]},findIndex:function(a,b){if(this==null)throw new TypeError("Array.prototype.findIndex called on null or undefined");if(typeof a!=="function")throw new TypeError("predicate must be a function");var c=Object(this),d=c.length>>>0;for(var e=0;e<d;e++)if(a.call(b,c[e],e,c))return e;return-1},fill:function(b,c,d){if(this==null)throw new TypeError("Array.prototype.fill called on null or undefined");var e=Object(this),f=e.length>>>0,h=arguments[1],g=h>>0,i=g<0?Math.max(f+g,0):Math.min(g,f),a=arguments[2],j=a===void 0?f:a>>0,k=j<0?Math.max(f+j,0):Math.min(j,f);while(i<k)e[i]=b,i++;return e}};a=g;f["default"]=a},66);c.__d("ES6Number",[],function(a,b,c,d,e,f){a=Math.pow(2,-52),b=Math.pow(2,53)-1,c=-1*b,d={isFinite:function(a){function b(b){return a.apply(this,arguments)}b.toString=function(){return a.toString()};return b}(function(a){return typeof a==="number"&&isFinite(a)}),isNaN:function(a){function b(b){return a.apply(this,arguments)}b.toString=function(){return a.toString()};return b}(function(a){return typeof a==="number"&&isNaN(a)}),isInteger:function(a){return this.isFinite(a)&&Math.floor(a)===a},isSafeInteger:function(a){return this.isFinite(a)&&a>=this.MIN_SAFE_INTEGER&&a<=this.MAX_SAFE_INTEGER&&Math.floor(a)===a},EPSILON:a,MAX_SAFE_INTEGER:b,MIN_SAFE_INTEGER:c},e=d,f["default"]=e},66);c.__d("ES6Object",[],function(a,b,c,d,e,f){var g={}.hasOwnProperty;a={assign:function(a){if(a==null)throw new TypeError("Object.assign target cannot be null or undefined");a=Object(a);for(var b=0;b<(arguments.length<=1?0:arguments.length-1);b++){var c=b+1<1||arguments.length<=b+1?void 0:arguments[b+1];if(c==null)continue;c=Object(c);for(var d in c)g.call(c,d)&&(a[d]=c[d])}return a},is:function(a,b){if(a===b)return a!==0||1/a===1/b;else return a!==a&&b!==b}};b=a;f["default"]=b},66);c.__d("ES5Array",[],function(a,b,c,d,e,f){a={isArray:function(a){return Object.prototype.toString.call(a)=="[object Array]"}},b=a,f["default"]=b},66);c.__d("ES5ArrayPrototype",[],function(a,b,c,d,e,f){a={indexOf:function(a,b){b=b;var c=this.length;b|=0;b<0&&(b+=c);for(;b<c;b++)if(b in this&&this[b]===a)return b;return-1}},b=a,f["default"]=b},66);c.__d("ES7ArrayPrototype",["ES5Array","ES5ArrayPrototype"],function(b,c,d,e,f,g){var h=c("ES5Array").isArray,i=c("ES5ArrayPrototype").indexOf;function a(a){return Math.min(Math.max(j(a),0),Number.MAX_SAFE_INTEGER)}function j(a){a=Number(a);return isFinite(a)&&a!==0?k(a)*Math.floor(Math.abs(a)):a}function k(a){return a>=0?1:-1}b={includes:function(b){"use strict";if(b!==void 0&&h(this)&&!(typeof b==="number"&&isNaN(b)))return i.apply(this,arguments)!==-1;var c=Object(this),d=c.length?a(c.length):0;if(d===0)return!1;var e=arguments.length>1?j(arguments[1]):0,f=e<0?Math.max(d+e,0):e,g=isNaN(b)&&typeof b==="number";while(f<d){var k=c[f];if(k===b||typeof k==="number"&&g&&isNaN(k))return!0;f++}return!1}};f.exports=b},null);c.__d("ES7Object",[],function(a,b,c,d,e,f){var g={}.hasOwnProperty;a={entries:function(a){if(a==null)throw new TypeError("Object.entries called on non-object");var b=[];for(var c in a)g.call(a,c)&&b.push([c,a[c]]);return b},values:function(a){if(a==null)throw new TypeError("Object.values called on non-object");var b=[];for(var c in a)g.call(a,c)&&b.push(a[c]);return b}};b=a;f["default"]=b},66);c.__d("ES7StringPrototype",[],function(a,b,c,d,e,f){a={trimLeft:function(){return this.replace(/^\s+/,"")},trimRight:function(){return this.replace(/\s+$/,"")}},b=a,f["default"]=b},66);c.__d("json3-3.3.2",[],function(b,c,d,e,f,g){"use strict";var h={},i={exports:h},a;function j(){(function(){var c=typeof a==="function"&&a.amd,d={"function":!0,object:!0},e=d[typeof h]&&h&&!h.nodeType&&h,f=d[typeof window]&&window||this,j=e&&d[typeof i]&&i&&!i.nodeType&&typeof b=="object"&&b;j&&(j.global===j||j.window===j||j.self===j)&&(f=j);function k(c,b){c||(c=f.Object());b||(b=f.Object());var e=c.Number||f.Number,o=c.String||f.String,i=c.Object||f.Object,a=c.Date||f.Date,j=c.SyntaxError||f.SyntaxError,l=c.TypeError||f.TypeError,m=c.Math||f.Math;c=c.JSON||f.JSON;typeof c=="object"&&c&&(b.stringify=c.stringify,b.parse=c.parse);c=i.prototype;var n=c.toString,p,q,r,s=new a(-3509827334573292);try{s=s.getUTCFullYear()==-109252&&s.getUTCMonth()===0&&s.getUTCDate()===1&&s.getUTCHours()==10&&s.getUTCMinutes()==37&&s.getUTCSeconds()==6&&s.getUTCMilliseconds()==708}catch(a){}function t(c){if(t[c]!==r)return t[c];var d;if(c=="bug-string-char-index")d="a"[0]!="a";else if(c=="json")d=t("json-stringify")&&t("json-parse");else{var f,h='{"a":[1,true,false,null,"\\u0000\\b\\n\\f\\r\\t"]}';if(c=="json-stringify"){var l=b.stringify,m=typeof l=="function"&&s;if(m){(f=function(){return 1}).toJSON=f;try{m=l(0)==="0"&&l(new e())==="0"&&l(new o())=='""'&&l(n)===r&&l(r)===r&&l()===r&&l(f)==="1"&&l([f])=="[1]"&&l([r])=="[null]"&&l(null)=="null"&&l([r,n,null])=="[null,null,null]"&&l({a:[f,!0,!1,null,"\0\b\n\f\r\t"]})==h&&l(null,f)==="1"&&l([1,2],null,1)=="[\n 1,\n 2\n]"&&l(new a(-864e13))=='"-271821-04-20T00:00:00.000Z"'&&l(new a(864e13))=='"+275760-09-13T00:00:00.000Z"'&&l(new a(-621987552e5))=='"-000001-01-01T00:00:00.000Z"'&&l(new a(-1))=='"1969-12-31T23:59:59.999Z"'}catch(a){m=!1}}d=m}if(c=="json-parse"){l=b.parse;if(typeof l=="function")try{if(l("0")===0&&!l(!1)){f=l(h);var p=f.a.length==5&&f.a[0]===1;if(p){try{p=!l('"\t"')}catch(a){}if(p)try{p=l("01")!==1}catch(a){}if(p)try{p=l("1.")!==1}catch(a){}}}}catch(a){p=!1}d=p}}return t[c]=!!d}if(!t("json")){var u="[object Function]",v="[object Date]",w="[object Number]",x="[object String]",y="[object Array]",z="[object Boolean]",A=t("bug-string-char-index");if(!s)var B=m.floor,C=[0,31,59,90,120,151,181,212,243,273,304,334],D=function(a,b){return C[b]+365*(a-1970)+B((a-1969+(b=+(b>1)))/4)-B((a-1901+b)/100)+B((a-1601+b)/400)};(p=c.hasOwnProperty)||(p=function(a){var b={},c;(b.__proto__=null,b.__proto__={toString:1},b).toString!=n?p=function(a){var b=this.__proto__;a=a in(this.__proto__=null,this);this.__proto__=b;return a}:(c=b.constructor,p=function(a){var b=(this.constructor||c).prototype;return a in this&&!(a in b&&this[a]===b[a])});return p.call(this,a)});q=function(a,b){var c=0,e,f;(e=function(){this.valueOf=0}).prototype.valueOf=0;f=new e();for(e in f)p.call(f,e)&&c++;f=null;c?c==2?q=function(a,b){var c={},d=n.call(a)==u,e;for(e in a)!(d&&e=="prototype")&&!p.call(c,e)&&(c[e]=1)&&p.call(a,e)&&b(e)}:q=function(a,b){var c=n.call(a)==u,d,e;for(d in a)!(c&&d=="prototype")&&p.call(a,d)&&!(e=d==="constructor")&&b(d);(e||p.call(a,d="constructor"))&&b(d)}:(f=["valueOf","toString","toLocaleString","propertyIsEnumerable","isPrototypeOf","hasOwnProperty","constructor"],q=function(a,b){var c=n.call(a)==u,e,g=!c&&typeof a.constructor!="function"&&d[typeof a.hasOwnProperty]&&a.hasOwnProperty||p;for(e in a)!(c&&e=="prototype")&&g.call(a,e)&&b(e);for(c=f.length;e=f[--c];g.call(a,e)&&b(e));});return q(a,b)};if(!t("json-stringify")){var E={92:"\\\\",34:'\\"',8:"\\b",12:"\\f",10:"\\n",13:"\\r",9:"\\t"},F="000000",G=function(a,b){return(F+(b||0)).slice(-a)},H="\\u00",I=function(a){var b='"',c=0,d=a.length,e=!A||d>10,f=e&&(A?a.split(""):a);for(;c<d;c++){var i=a.charCodeAt(c);switch(i){case 8:case 9:case 10:case 12:case 13:case 34:case 92:b+=E[i];break;default:if(i<32){b+=H+G(2,i.toString(16));break}b+=e?f[c]:a.charAt(c)}}return b+'"'},J=function a(c,d,e,f,o,E,F){var H,b,J,K,L,M,N,O,P,Q;try{H=d[c]}catch(a){}if(typeof H=="object"&&H){b=n.call(H);if(b==v&&!p.call(H,"toJSON"))if(H>-1/0&&H<1/0){if(D){L=B(H/864e5);for(J=B(L/365.2425)+1970-1;D(J+1,0)<=L;J++);for(K=B((L-D(J,0))/30.42);D(J,K+1)<=L;K++);L=1+L-D(J,K);M=(H%864e5+864e5)%864e5;N=B(M/36e5)%24;O=B(M/6e4)%60;P=B(M/1e3)%60;M=M%1e3}else J=H.getUTCFullYear(),K=H.getUTCMonth(),L=H.getUTCDate(),N=H.getUTCHours(),O=H.getUTCMinutes(),P=H.getUTCSeconds(),M=H.getUTCMilliseconds();H=(J<=0||J>=1e4?(J<0?"-":"+")+G(6,J<0?-J:J):G(4,J))+"-"+G(2,K+1)+"-"+G(2,L)+"T"+G(2,N)+":"+G(2,O)+":"+G(2,P)+"."+G(3,M)+"Z"}else H=null;else typeof H.toJSON=="function"&&(b!=w&&b!=x&&b!=y||p.call(H,"toJSON"))&&(H=H.toJSON(c))}e&&(H=e.call(d,c,H));if(H===null)return"null";b=n.call(H);if(b==z)return""+H;else if(b==w)return H>-1/0&&H<1/0?""+H:"null";else if(b==x)return I(""+H);if(typeof H=="object"){for(J=F.length;J--;)if(F[J]===H)throw l();F.push(H);Q=[];K=E;E+=o;if(b==y){for(L=0,J=H.length;L<J;L++)N=a(L,H,e,f,o,E,F),Q.push(N===r?"null":N);O=Q.length?o?"[\n"+E+Q.join(",\n"+E)+"\n"+K+"]":"["+Q.join(",")+"]":"[]"}else q(f||H,function(b){var c=a(b,H,e,f,o,E,F);c!==r&&Q.push(I(b)+":"+(o?" ":"")+c)}),O=Q.length?o?"{\n"+E+Q.join(",\n"+E)+"\n"+K+"}":"{"+Q.join(",")+"}":"{}";F.pop();return O}};b.stringify=function(b,c,e){var f,l,m,o;if(d[typeof c]&&c)if((o=n.call(c))==u)l=c;else if(o==y){m={};for(var a=0,p=c.length,q;a<p;q=c[a++],(o=n.call(q),o==x||o==w)&&(m[q]=1));}if(e)if((o=n.call(e))==w){if((e-=e%1)>0)for(f="",e>10&&(e=10);f.length<e;f+=" ");}else o==x&&(f=e.length<=10?e:e.slice(0,10));return J("",(q={},q[""]=b,q),l,m,f,"",[])}}if(!t("json-parse")){var K=o.fromCharCode,L={92:"\\",34:'"',47:"/",98:"\b",116:"\t",110:"\n",102:"\f",114:"\r"},M,N,O=function(){M=N=null;throw j()},P=function(){var a=N,b=a.length,c,d,e,f,i;while(M<b){i=a.charCodeAt(M);switch(i){case 9:case 10:case 13:case 32:M++;break;case 123:case 125:case 91:case 93:case 58:case 44:c=A?a.charAt(M):a[M];M++;return c;case 34:for(c="@",M++;M<b;){i=a.charCodeAt(M);if(i<32)O();else if(i==92){i=a.charCodeAt(++M);switch(i){case 92:case 34:case 47:case 98:case 116:case 110:case 102:case 114:c+=L[i];M++;break;case 117:d=++M;for(e=M+4;M<e;M++)i=a.charCodeAt(M),i>=48&&i<=57||i>=97&&i<=102||i>=65&&i<=70||O();c+=K("0x"+a.slice(d,M));break;default:O()}}else{if(i==34)break;i=a.charCodeAt(M);d=M;while(i>=32&&i!=92&&i!=34)i=a.charCodeAt(++M);c+=a.slice(d,M)}}if(a.charCodeAt(M)==34){M++;return c}O();default:d=M;i==45&&(f=!0,i=a.charCodeAt(++M));if(i>=48&&i<=57){i==48&&(i=a.charCodeAt(M+1),i>=48&&i<=57)&&O();f=!1;for(;M<b&&(i=a.charCodeAt(M),i>=48&&i<=57);M++);if(a.charCodeAt(M)==46){e=++M;for(;e<b&&(i=a.charCodeAt(e),i>=48&&i<=57);e++);e==M&&O();M=e}i=a.charCodeAt(M);if(i==101||i==69){i=a.charCodeAt(++M);(i==43||i==45)&&M++;for(e=M;e<b&&(i=a.charCodeAt(e),i>=48&&i<=57);e++);e==M&&O();M=e}return+a.slice(d,M)}f&&O();if(a.slice(M,M+4)=="true"){M+=4;return!0}else if(a.slice(M,M+5)=="false"){M+=5;return!1}else if(a.slice(M,M+4)=="null"){M+=4;return null}O()}}return"$"},Q=function a(b){var c,d;b=="$"&&O();if(typeof b=="string"){if((A?b.charAt(0):b[0])=="@")return b.slice(1);if(b=="["){c=[];for(;;d||(d=!0)){b=P();if(b=="]")break;d&&(b==","?(b=P(),b=="]"&&O()):O());b==","&&O();c.push(a(b))}return c}else if(b=="{"){c={};for(;;d||(d=!0)){b=P();if(b=="}")break;d&&(b==","?(b=P(),b=="}"&&O()):O());(b==","||typeof b!="string"||(A?b.charAt(0):b[0])!="@"||P()!=":")&&O();c[b.slice(1)]=a(P())}return c}O()}return b},R=function(a,b,c){c=S(a,b,c),c===r?delete a[b]:a[b]=c},S=function(a,b,c){var d=a[b],e;if(typeof d=="object"&&d)if(n.call(d)==y)for(e=d.length;e--;)R(d,e,c);else q(d,function(a){R(d,a,c)});return c.call(a,b,d)};b.parse=function(a,b){var c;M=0;N=""+a;a=Q(P());P()!="$"&&O();M=N=null;return b&&n.call(b)==u?S((c={},c[""]=a,c),"",b):a}}}b.runInContext=k;return b}if(e&&!c)k(f,e);else{var l=f.JSON,m=f.JSON3,n=!1,o=k(f,f.JSON3={noConflict:function(){n||(n=!0,f.JSON=l,f.JSON3=m,l=m=null);return o}});f.JSON={parse:o.parse,stringify:o.stringify}}c&&a(function(){return o})}).call(this)}var k=!1;function l(){k||(k=!0,j());return i.exports}function c(a){switch(a){case void 0:return l()}}f.exports=c},null);c.__d("json3",["json3-3.3.2"],function(a,b,c,d,e,f){e.exports=b("json3-3.3.2")()},null);c.__d("ES",["ES5FunctionPrototype","ES5StringPrototype","ES6Array","ES6ArrayPrototype","ES6Number","ES6Object","ES7ArrayPrototype","ES7Object","ES7StringPrototype","json3"],function(b,c,d,e,f,g,h){var i={}.toString,a={"JSON.stringify":d("json3").stringify,"JSON.parse":d("json3").parse};e={"Function.prototype":d("ES5FunctionPrototype"),"String.prototype":d("ES5StringPrototype")};f={Object:d("ES6Object"),"Array.prototype":d("ES6ArrayPrototype"),Number:d("ES6Number"),Array:d("ES6Array")};g={Object:d("ES7Object"),"String.prototype":d("ES7StringPrototype"),"Array.prototype":d("ES7ArrayPrototype")};function b(b){for(var c in b){if(!Object.prototype.hasOwnProperty.call(b,c))continue;var d=b[c],e=c.split(".");if(e.length===2){var f=e[0],g=e[1];if(!f||!g||!window[f]||!window[f][g]){var h=f?window[f]:"-",i=f&&window[f]&&g?window[f][g]:"-";throw new Error("Unexpected state (t11975770): "+(f+", "+g+", "+h+", "+i+", "+c))}}f=e.length===2?window[e[0]][e[1]]:window[c];for(g in d){if(!Object.prototype.hasOwnProperty.call(d,g))continue;if(typeof d[g]!=="function"){a[c+"."+g]=d[g];continue}h=f[g];a[c+"."+g]=h&&/\{\s+\[native code\]\s\}/.test(h)?h:d[g]}}}b(e);b(f);b(g);function c(b,c,d){var e=d?i.call(b).slice(8,-1)+".prototype":b,f;if(Array.isArray(b))if(typeof e==="string")f=a[e+"."+c];else throw new Error("Can't polyfill "+c+" directly on an Array.");else if(typeof e==="string")f=a[e+"."+c];else if(typeof b==="string")throw new Error("Can't polyfill "+c+" directly on a string.");else f=b[c];if(typeof f==="function"){for(var g=arguments.length,h=new Array(g>3?g-3:0),j=3;j<g;j++)h[j-3]=arguments[j];return f.apply(b,h)}else if(f)return f;throw new Error("Polyfill "+e+" does not have implementation of "+c)}h["default"]=c},98);c.__d("ES5Object",[],function(a,b,c,d,e,f){var g={}.hasOwnProperty;a={create:function(a){var b=typeof a;if(b!="object"&&b!="function")throw new TypeError("Object prototype may only be a Object or null");h.prototype=a;return new h()},keys:function(a){var b=typeof a;if(b!="object"&&b!="function"||a===null)throw new TypeError("Object.keys called on non-object");b=[];for(var c in a)g.call(a,c)&&b.push(c);return b},freeze:function(a){return a},isFrozen:function(){return!1},seal:function(a){return a}};function h(){}b=a;f["default"]=b},66);c.__d("sdk.babelHelpers",["ES5FunctionPrototype","ES5Object","ES6Object"],function(a,b,c,d,e,f){var g={},h=Object.prototype.hasOwnProperty;g.inheritsLoose=function(a,c){b("ES6Object").assign(a,c);a.prototype=b("ES5Object").create(c&&c.prototype);a.prototype.constructor=a;a.__superConstructor__=c;return c};g.inherits=g.inheritsLoose;g.wrapNativeSuper=function(a){var b=typeof Map==="function"?new Map():void 0;g.wrapNativeSuper=function(a){if(a===null)return null;if(typeof a!=="function")throw new TypeError("Super expression must either be null or a function");if(b!==void 0){if(b.has(a))return b.get(a);b.set(a,c)}g.inheritsLoose(c,a);function c(){a.apply(this,arguments)}return c};return g.wrapNativeSuper(a)};g.assertThisInitialized=function(a){if(a===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a};g._extends=b("ES6Object").assign;g["extends"]=g._extends;g.construct=function(a,b){var c=[null];c.push.apply(c,b);return new(Function.prototype.bind.apply(a,c))()};g.objectWithoutPropertiesLoose=function(a,b){var c={};for(var d in a){if(!h.call(a,d)||b.indexOf(d)>=0)continue;c[d]=a[d]}return c};g.objectWithoutProperties=g.objectWithoutPropertiesLoose;g.taggedTemplateLiteralLoose=function(a,b){b||(b=a.slice(0));a.raw=b;return a};g.bind=b("ES5FunctionPrototype").bind;e.exports=g},null);var e=c.require("ES"),f=c.require("sdk.babelHelpers");(function(b,c){var d="keys",f="values",g="entries",h=function(){var a=j(Array),b;a||(b=function(){"use strict";function a(a,b){this.$1=a,this.$2=b,this.$3=0}var b=a.prototype;b.next=function(){if(this.$1==null)return{value:void 0,done:!0};var a=this.$1,b=this.$1.length,c=this.$3,e=this.$2;if(c>=b){this.$1=void 0;return{value:void 0,done:!0}}this.$3=c+1;if(e===d)return{value:c,done:!1};else if(e===f)return{value:a[c],done:!1};else if(e===g)return{value:[c,a[c]],done:!1}};b[typeof Symbol==="function"?typeof Symbol==="function"?Symbol.iterator:"@@iterator":"@@iterator"]=function(){return this};return a}());return{keys:a?function(a){return a.keys()}:function(a){return new b(a,d)},values:a?function(a){return a.values()}:function(a){return new b(a,f)},entries:a?function(a){return a.entries()}:function(a){return new b(a,g)}}}(),i=function(){var a=j(String),b;a||(b=function(){"use strict";function a(a){this.$1=a,this.$2=0}var b=a.prototype;b.next=function(){if(this.$1==null)return{value:void 0,done:!0};var a=this.$2,b=this.$1,c=b.length;if(a>=c){this.$1=void 0;return{value:void 0,done:!0}}var d=b.charCodeAt(a);d<55296||d>56319||a+1===c?d=b[a]:(c=b.charCodeAt(a+1),c<56320||c>57343?d=b[a]:d=b[a]+b[a+1]);this.$2=a+d.length;return{value:d,done:!1}};b[typeof Symbol==="function"?typeof Symbol==="function"?Symbol.iterator:"@@iterator":"@@iterator"]=function(){return this};return a}());return{keys:function(){throw TypeError("Strings default iterator doesn't implement keys.")},values:a?function(a){return a[typeof Symbol==="function"?typeof Symbol==="function"?Symbol.iterator:"@@iterator":"@@iterator"]()}:function(a){return new b(a)},entries:function(){throw TypeError("Strings default iterator doesn't implement entries.")}}}();function j(a){return typeof a.prototype[typeof Symbol==="function"?typeof Symbol==="function"?Symbol.iterator:"@@iterator":"@@iterator"]==="function"&&typeof a.prototype.values==="function"&&typeof a.prototype.keys==="function"&&typeof a.prototype.entries==="function"}var a=function(){"use strict";function a(a,b){this.$1=a,this.$2=b,this.$3=Object.keys(a),this.$4=0}var b=a.prototype;b.next=function(){var a=this.$3.length,b=this.$4,c=this.$2,e=this.$3[b];if(b>=a){this.$1=void 0;return{value:void 0,done:!0}}this.$4=b+1;if(c===d)return{value:e,done:!1};else if(c===f)return{value:this.$1[e],done:!1};else if(c===g)return{value:[e,this.$1[e]],done:!1}};b[typeof Symbol==="function"?typeof Symbol==="function"?Symbol.iterator:"@@iterator":"@@iterator"]=function(){return this};return a}(),k={keys:function(b){return new a(b,d)},values:function(b){return new a(b,f)},entries:function(b){return new a(b,g)}};function l(a,b){if(typeof a==="string")return i[b||f](a);else if(Array.isArray(a))return h[b||f](a);else if(a[typeof Symbol==="function"?typeof Symbol==="function"?Symbol.iterator:"@@iterator":"@@iterator"])return a[typeof Symbol==="function"?typeof Symbol==="function"?Symbol.iterator:"@@iterator":"@@iterator"]();else return k[b||g](a)}e("Object","assign",!1,l,{KIND_KEYS:d,KIND_VALUES:f,KIND_ENTRIES:g,keys:function(a){return l(a,d)},values:function(a){return l(a,f)},entries:function(a){return l(a,g)},generic:k.entries});b.FB_enumerate=l})(typeof b==="object"?b:typeof this==="object"?this:typeof window==="object"?window:typeof a==="object"?a:{});c.__d("JSSDKCanvasPrefetcherConfig",[],{enabled:!0,excludedAppIds:[144959615576466,768691303149786,320528941393723],sampleRate:500});c.__d("JSSDKConfig",[],{features:{allow_non_canvas_app_events:!1,xd_timeout:{rate:1,value:6e4},use_bundle:!1,should_log_response_error:!0,https_only_enforce_starting:25388092e5,https_only_learn_more:"https://developers.facebook.com/blog/post/2018/06/08/enforce-https-facebook-login/",https_only_scribe_logging:{rate:.001},log_perf:{rate:.001},legacy_xd_init:{rate:0},use_x_xd:{rate:100},cache_auth_response:{rate:100},oauth_funnel_logger_version:1,iab_login_status:!0,iab_login_android_support_version:"277.0",force_popup_to_canvas_apps_with_id:[],force_popup_to_all_canvas_app:!1,max_oauth_dialog_retries:{rate:100,value:99999},idle_callback_wait_time_ms:3e3,chat_plugin_facade_timeout_ms:8e3,chat_plugin_facade_enabled_pageids:["102493178867330","107331571710078","1032787970130843","107771111665395","261907812360345","101305975654752","275483104252055","101664622285042","112682113428700","271628573687012","385757598521443","100545935690488"],should_enable_ig_login_status_fetch:!0,log_cookies_usage:{rate:.1},allow_shadow_dom_for_apps_with_id:[520916077950649,152351391599356,132081130190180,468663283258845,409976882430412,189845245141894,360467581347,274266067164],allow_shadow_dom:!0,xfoa_login_enabled:!1}});c.__d("JSSDKCssConfig",[],{rules:".fb_hidden{position:absolute;top:-10000px;z-index:10001}.fb_reposition{overflow:hidden;position:relative}.fb_invisible{display:none}.fb_reset{background:none;border:0;border-spacing:0;color:#000;cursor:auto;direction:ltr;font-family:'lucida grande', tahoma, verdana, arial, sans-serif;font-size:11px;font-style:normal;font-variant:normal;font-weight:normal;letter-spacing:normal;line-height:1;margin:0;overflow:visible;padding:0;text-align:left;text-decoration:none;text-indent:0;text-shadow:none;text-transform:none;visibility:visible;white-space:normal;word-spacing:normal}.fb_reset>div{overflow:hidden}@keyframes fb_transform{from{opacity:0;transform:scale(.95)}to{opacity:1;transform:scale(1)}}.fb_animate{animation:fb_transform .3s forwards}\n.fb_hidden{position:absolute;top:-10000px;z-index:10001}.fb_reposition{overflow:hidden;position:relative}.fb_invisible{display:none}.fb_reset{background:none;border:0;border-spacing:0;color:#000;cursor:auto;direction:ltr;font-family:'lucida grande', tahoma, verdana, arial, sans-serif;font-size:11px;font-style:normal;font-variant:normal;font-weight:normal;letter-spacing:normal;line-height:1;margin:0;overflow:visible;padding:0;text-align:left;text-decoration:none;text-indent:0;text-shadow:none;text-transform:none;visibility:visible;white-space:normal;word-spacing:normal}.fb_reset>div{overflow:hidden}@keyframes fb_transform{from{opacity:0;transform:scale(.95)}to{opacity:1;transform:scale(1)}}.fb_animate{animation:fb_transform .3s forwards}\n.fb_dialog{background:rgba(82, 82, 82, .7);position:absolute;top:-10000px;z-index:10001}.fb_dialog_advanced{border-radius:8px;padding:10px}.fb_dialog_content{background:#fff;color:#373737}.fb_dialog_close_icon{background:url(https://connect.facebook.net/rsrc.php/v3/yq/r/IE9JII6Z1Ys.png) no-repeat scroll 0 0 transparent;cursor:pointer;display:block;height:15px;position:absolute;right:18px;top:17px;width:15px}.fb_dialog_mobile .fb_dialog_close_icon{left:5px;right:auto;top:5px}.fb_dialog_padding{background-color:transparent;position:absolute;width:1px;z-index:-1}.fb_dialog_close_icon:hover{background:url(https://connect.facebook.net/rsrc.php/v3/yq/r/IE9JII6Z1Ys.png) no-repeat scroll 0 -15px transparent}.fb_dialog_close_icon:active{background:url(https://connect.facebook.net/rsrc.php/v3/yq/r/IE9JII6Z1Ys.png) no-repeat scroll 0 -30px transparent}.fb_dialog_iframe{line-height:0}.fb_dialog_content .dialog_title{background:#6d84b4;border:1px solid #365899;color:#fff;font-size:14px;font-weight:bold;margin:0}.fb_dialog_content .dialog_title>span{background:url(https://connect.facebook.net/rsrc.php/v3/yd/r/Cou7n-nqK52.gif) no-repeat 5px 50%;float:left;padding:5px 0 7px 26px}body.fb_hidden{height:100%;left:0;margin:0;overflow:visible;position:absolute;top:-10000px;transform:none;width:100%}.fb_dialog.fb_dialog_mobile.loading{background:url(https://connect.facebook.net/rsrc.php/v3/ya/r/3rhSv5V8j3o.gif) white no-repeat 50% 50%;min-height:100%;min-width:100%;overflow:hidden;position:absolute;top:0;z-index:10001}.fb_dialog.fb_dialog_mobile.loading.centered{background:none;height:auto;min-height:initial;min-width:initial;width:auto}.fb_dialog.fb_dialog_mobile.loading.centered #fb_dialog_loader_spinner{width:100%}.fb_dialog.fb_dialog_mobile.loading.centered .fb_dialog_content{background:none}.loading.centered #fb_dialog_loader_close{clear:both;color:#fff;display:block;font-size:18px;padding-top:20px}#fb-root #fb_dialog_ipad_overlay{background:rgba(0, 0, 0, .4);bottom:0;left:0;min-height:100%;position:absolute;right:0;top:0;width:100%;z-index:10000}#fb-root #fb_dialog_ipad_overlay.hidden{display:none}.fb_dialog.fb_dialog_mobile.loading iframe{visibility:hidden}.fb_dialog_mobile .fb_dialog_iframe{position:sticky;top:0}.fb_dialog_content .dialog_header{background:linear-gradient(from(#738aba), to(#2c4987));border-bottom:1px solid;border-color:#043b87;box-shadow:white 0 1px 1px -1px inset;color:#fff;font:bold 14px Helvetica, sans-serif;text-overflow:ellipsis;text-shadow:rgba(0, 30, 84, .296875) 0 -1px 0;vertical-align:middle;white-space:nowrap}.fb_dialog_content .dialog_header table{height:43px;width:100%}.fb_dialog_content .dialog_header td.header_left{font-size:12px;padding-left:5px;vertical-align:middle;width:60px}.fb_dialog_content .dialog_header td.header_right{font-size:12px;padding-right:5px;vertical-align:middle;width:60px}.fb_dialog_content .touchable_button{background:linear-gradient(from(#4267B2), to(#2a4887));background-clip:padding-box;border:1px solid #29487d;border-radius:3px;display:inline-block;line-height:18px;margin-top:3px;max-width:85px;padding:4px 12px;position:relative}.fb_dialog_content .dialog_header .touchable_button input{background:none;border:none;color:#fff;font:bold 12px Helvetica, sans-serif;margin:2px -12px;padding:2px 6px 3px 6px;text-shadow:rgba(0, 30, 84, .296875) 0 -1px 0}.fb_dialog_content .dialog_header .header_center{color:#fff;font-size:16px;font-weight:bold;line-height:18px;text-align:center;vertical-align:middle}.fb_dialog_content .dialog_content{background:url(https://connect.facebook.net/rsrc.php/v3/y9/r/jKEcVPZFk-2.gif) no-repeat 50% 50%;border:1px solid #4a4a4a;border-bottom:0;border-top:0;height:150px}.fb_dialog_content .dialog_footer{background:#f5f6f7;border:1px solid #4a4a4a;border-top-color:#ccc;height:40px}#fb_dialog_loader_close{float:left}.fb_dialog.fb_dialog_mobile .fb_dialog_close_icon{visibility:hidden}#fb_dialog_loader_spinner{animation:rotateSpinner 1.2s linear infinite;background-color:transparent;background-image:url(https://connect.facebook.net/rsrc.php/v3/yD/r/t-wz8gw1xG1.png);background-position:50% 50%;background-repeat:no-repeat;height:24px;width:24px}@keyframes rotateSpinner{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}\n.fb_iframe_widget{display:inline-block;position:relative}.fb_iframe_widget span{display:inline-block;position:relative;text-align:justify}.fb_iframe_widget iframe{position:absolute}.fb_iframe_widget_fluid_desktop,.fb_iframe_widget_fluid_desktop span,.fb_iframe_widget_fluid_desktop iframe{max-width:100%}.fb_iframe_widget_fluid_desktop iframe{min-width:220px;position:relative}.fb_iframe_widget_lift{z-index:1}.fb_iframe_widget_fluid{display:inline}.fb_iframe_widget_fluid span{width:100%}\n.fb_mpn_mobile_landing_page_slide_out{animation-duration:200ms;animation-name:fb_mpn_landing_page_slide_out;transition-timing-function:ease-in}.fb_mpn_mobile_landing_page_slide_out_from_left{animation-duration:200ms;animation-name:fb_mpn_landing_page_slide_out_from_left;transition-timing-function:ease-in}.fb_mpn_mobile_landing_page_slide_up{animation-duration:500ms;animation-name:fb_mpn_landing_page_slide_up;transition-timing-function:ease-in}.fb_mpn_mobile_bounce_in{animation-duration:300ms;animation-name:fb_mpn_bounce_in;transition-timing-function:ease-in}.fb_mpn_mobile_bounce_out{animation-duration:300ms;animation-name:fb_mpn_bounce_out;transition-timing-function:ease-in}.fb_mpn_mobile_bounce_out_v2{animation-duration:300ms;animation-name:fb_mpn_fade_out;transition-timing-function:ease-in}.fb_customer_chat_bounce_in_v2{animation-duration:300ms;animation-name:fb_bounce_in_v2;transition-timing-function:ease-in}.fb_customer_chat_bounce_in_from_left{animation-duration:300ms;animation-name:fb_bounce_in_from_left;transition-timing-function:ease-in}.fb_customer_chat_bounce_out_v2{animation-duration:300ms;animation-name:fb_bounce_out_v2;transition-timing-function:ease-in}.fb_customer_chat_bounce_out_from_left{animation-duration:300ms;animation-name:fb_bounce_out_from_left;transition-timing-function:ease-in}.fb_invisible_flow{display:inherit;height:0;overflow-x:hidden;width:0}@keyframes fb_mpn_landing_page_slide_out{0%{margin:0 12px;width:100% - 24px}60%{border-radius:18px}100%{border-radius:50%;margin:0 24px;width:60px}}@keyframes fb_mpn_landing_page_slide_out_from_left{0%{left:12px;width:100% - 24px}60%{border-radius:18px}100%{border-radius:50%;left:12px;width:60px}}@keyframes fb_mpn_landing_page_slide_up{0%{bottom:0;opacity:0}100%{bottom:24px;opacity:1}}@keyframes fb_mpn_bounce_in{0%{opacity:.5;top:100%}100%{opacity:1;top:0}}@keyframes fb_mpn_fade_out{0%{bottom:30px;opacity:1}100%{bottom:0;opacity:0}}@keyframes fb_mpn_bounce_out{0%{opacity:1;top:0}100%{opacity:.5;top:100%}}@keyframes fb_bounce_in_v2{0%{opacity:0;transform:scale(0, 0);transform-origin:bottom right}50%{transform:scale(1.03, 1.03);transform-origin:bottom right}100%{opacity:1;transform:scale(1, 1);transform-origin:bottom right}}@keyframes fb_bounce_in_from_left{0%{opacity:0;transform:scale(0, 0);transform-origin:bottom left}50%{transform:scale(1.03, 1.03);transform-origin:bottom left}100%{opacity:1;transform:scale(1, 1);transform-origin:bottom left}}@keyframes fb_bounce_out_v2{0%{opacity:1;transform:scale(1, 1);transform-origin:bottom right}100%{opacity:0;transform:scale(0, 0);transform-origin:bottom right}}@keyframes fb_bounce_out_from_left{0%{opacity:1;transform:scale(1, 1);transform-origin:bottom left}100%{opacity:0;transform:scale(0, 0);transform-origin:bottom left}}@keyframes slideInFromBottom{0%{opacity:.1;transform:translateY(100%)}100%{opacity:1;transform:translateY(0)}}@keyframes slideInFromBottomDelay{0%{opacity:0;transform:translateY(100%)}97%{opacity:0;transform:translateY(100%)}100%{opacity:1;transform:translateY(0)}}",components:["css:fb.css.base","css:fb.css.dialog","css:fb.css.iframewidget","css:fb.css.customer_chat_plugin_iframe"]});c.__d("JSSDKRuntimeConfig",[],{locale:"en_US",revision:"1007242444",rtl:!1,sdkab:null,sdkns:"",sdkurl:"https://connect.facebook.net/en_US/sdk.js",scribeurl:"https://www.facebook.com/platform/scribe_endpoint.php/"});c.__d("JSSDKXDConfig",[],{XXdUrl:"/x/connect/xd_arbiter/?version=C-8998888-1007242444-20230404094308",useCdn:!0});c.__d("UrlMapConfig",[],{www:"www.facebook.com",m:"m.facebook.com",business:"business.facebook.com",api:"api.facebook.com",api_read:"api-read.facebook.com",graph:"graph.facebook.com",an:"an.facebook.com",fbcdn:"static.xx.fbcdn.net",cdn:"staticxx.facebook.com",graph_facebook:"graph.facebook.com",graph_gaming:"graph.fb.gg",graph_instagram:"graph.instagram.com",www_instagram:"www.instagram.com",social_plugin:"socialplugin.facebook.net"});c.__d("JSSDKShadowCssConfig",[],{"css:fb.shadow.css.fb_login_button":".fb_login_button_container{align-content:center;align-items:center;border:0;color:#fff;display:flex;font-family:'Roboto', 'Freight Sans LF Pro', Helvetica, Arial, 'Lucida Grande', sans-serif;font-weight:bold;margin:auto}.fb-button-main-element{display:flex;flex-wrap:nowrap;overflow:hidden}.fb-iframe-overlay{display:flex}.fb-button-main-element:hover{cursor:pointer}.fb-button-main-element:focus{filter:brightness(80%)}.fb_button_label_element{align-items:center;display:flex;font-weight:bold;justify-content:center}.fb_button_label{margin:auto;pointer-events:none}.fb_button_svg_logo{height:1.33em;margin-left:.4em;margin-right:.4em;padding:.065em}.login_fb_logo .f_logo_f{fill:transparent}.single_button_svg_logo{margin-bottom:.08em}"});c.__d("DOMWrapper",[],function(b,c,d,e,f,g){"use strict";var h,i;function b(a){h=a}function c(){return h||document.body}function d(a){i=a}function e(){return i||a}g.setRoot=b;g.getRoot=c;g.setWindow=d;g.getWindow=e},66);c.__d("dotAccess",[],function(a,b,c,d,e,f){function a(a,b,c){b=b.split(".");do{var d=b.shift();a=a[d]||c&&(a[d]={})}while(b.length&&a);return a}f["default"]=a},66);c.__d("guid",[],function(a,b,c,d,e,f){function a(){return"f"+(Math.random()*(1<<30)).toString(16).replace(".","")}f["default"]=a},66);c.__d("wrapFunction",[],function(a,b,c,d,e,f){var g={};function a(a,b,c){var d=b in g?g[b](a,c):a;return function(){for(var a=arguments.length,b=new Array(a),c=0;c<a;c++)b[c]=arguments[c];return d.apply(this,b)}}a.setWrapper=function(a,b){g[b]=a};f["default"]=a},66);c.__d("GlobalCallback",["DOMWrapper","dotAccess","guid","wrapFunction"],function(b,c,d,e,f,g,h){var i,a;function j(b){i=d("dotAccess")(e("DOMWrapper").getWindow(),b,!0),a=b}function b(b,c){i||j("__globalCallbacks");var e=d("guid")();i[e]=d("wrapFunction")(b,"entry",(b=c)!=null?b:"GlobalCallback");return a+"."+e}function c(b){b=b.substring(a.length+1),delete i[b]}h.setPrefix=j;h.create=b;h.remove=c},98);c.__d("Log",[],function(b,c,d,f,g,h){"use strict";var i=-1;c={DEBUG:3,INFO:2,WARNING:1,ERROR:0};d=function(b,c,k){for(var d=arguments.length,e=new Array(d>3?d-3:0),f=3;f<d;f++)e[f-3]=arguments[f];var j=0,a=k.replace(/%s/g,function(){return String(e[j++])}),l=window.console;l&&i>=c&&l[b in l?b:"log"](a)};function b(a){i=a}f=e(d,"bind",!0,null,"debug",c.DEBUG);g=e(d,"bind",!0,null,"info",c.INFO);var j=e(d,"bind",!0,null,"warn",c.WARNING),a=e(d,"bind",!0,null,"error",c.ERROR);h.Level=c;h.log=d;h.setLevel=b;h.debug=f;h.info=g;h.warn=j;h.error=a},66);c.__d("sdk.UA",[],function(b,c,d,e,f,g){b=navigator.userAgent;var h={iphone:/\b(iPhone|iP[ao]d)/.test(b),ipad:/\b(iP[ao]d)/.test(b),android:/Android/i.test(b),nativeApp:/FBAN\/\w+;/i.test(b)&&!/FBAN\/mLite;/.test(b),nativeAndroidApp:/FB_IAB\/\w+;/i.test(b),nativeInstagramApp:/Instagram/i.test(b),nativeMessengeriOSApp:/MessengerForiOS/i.test(b),nativeMessengerAndroidApp:/Orca\-Android/i.test(b),ucBrowser:/UCBrowser/i.test(b)},i=/Mobile/i.test(b),a={ie:NaN,firefox:NaN,chrome:NaN,webkit:NaN,osx:NaN,edge:NaN,operaMini:NaN,ucWeb:NaN};c=/(?:MSIE.(\d+\.\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\d+\.\d+))|(?:AppleWebKit.(\d+(?:\.\d+)?))|(?:Trident\/\d+\.\d+.*rv:(\d+\.\d+))/.exec(b);c&&(a.ie=c[1]?parseFloat(c[1]):c[4]?parseFloat(c[4]):NaN,a.firefox=c[2]||"",a.webkit=c[3]||"",c[3]&&(d=/(?:Chrome\/(\d+\.\d+))/.exec(b),a.chrome=d?d[1]:"",e=/(?:Edge\/(\d+\.\d+))/.exec(b),a.edge=e?e[1]:""));f=/(?:Mac OS X (\d+(?:[._]\d+)?))/.exec(b);f&&(a.osx=f[1]);c=/(?:Opera Mini\/(\d+(?:\.\d+)?))/.exec(b);c&&(a.operaMini=c[1]);d=/(?:UCWEB\/(\d+(?:\.\d+))?)/.exec(b);d&&(a.ucWeb=d[1]||"2.0");function j(a){return String(a).split(".").map(function(a){return parseFloat(a)})}var k={};Object.keys(a).map(function(b){var c=function(){return parseFloat(a[b])};c.getVersionParts=function(){return j(a[b])};k[b]=c});Object.keys(h).map(function(a){k[a]=function(){return h[a]}});k.mobile=function(){return h.iphone||h.ipad||h.android||i};k.mTouch=function(){return h.android||h.iphone||h.ipad};k.facebookInAppBrowser=function(){return h.nativeApp||h.nativeAndroidApp};k.inAppBrowser=function(){return h.nativeApp||h.nativeAndroidApp||h.nativeInstagramApp};k.mBasic=function(){return!!(a.ucWeb||a.operaMini)};k.instagram=function(){return h.nativeInstagramApp};k.messenger=function(){return h.nativeMessengeriOSApp||h.nativeMessengerAndroidApp};k.isSupportedIABVersion=function(a){if(!k.facebookInAppBrowser())return!1;var b=/(?:FBAV\/(\d+(\.\d+)+))/.exec(navigator.userAgent);if(b){b=parseFloat(b[1]);if(b>=a)return!0}return!1};e=k;g["default"]=e},66);c.__d("sdk.domReady",[],function(a,b,c,d,e,f){var g;c="readyState"in document?/loaded|complete/.test(document.readyState):!!document.body;function a(){if(!g)return;var a;while(a=g.shift())a();g=null}function b(a){if(g){g.push(a);return}else a()}c||(g=[],document.addEventListener?(document.addEventListener("DOMContentLoaded",a,!1),window.addEventListener("load",a,!1)):document.attachEvent&&(document.attachEvent("onreadystatechange",a),window.attachEvent("onload",a)));f["default"]=b},67);c.__d("sdk.Content",["Log","sdk.UA","sdk.domReady"],function(b,c,d,e,f,g,h){"use strict";var i,a;function j(a,b){b||(i?b=i:(i=b=document.getElementById("fb-root"),b||(e("Log").warn('The "fb-root" div has not been created, auto-creating'),i=b=document.createElement("div"),b.id="fb-root",d("sdk.UA").ie()||!document.body?d("sdk.domReady")(function(){b&&document.body&&document.body.appendChild(b)}):document.body.appendChild(b)),b.className+=" fb_reset"));b.appendChild(a);return a}function k(b){if(!a){a=document.createElement("div");var c=a.style;c.position="absolute";c.top="-10000px";c.width="0";c.height="0";a=j(a)}return j(b,a)}function b(a,b){var c=document.createElement("form");c.action=a.url;c.target=a.target;c.method=b?"GET":"POST";k(c);for(b in a.params)if(Object.prototype.hasOwnProperty.call(a.params,b)){var d=a.params[b];if(d!=null){var e=document.createElement("input");e.name=b;e.value=d;c.appendChild(e)}}c.submit();c.parentNode&&c.parentNode.removeChild(c)}h.append=j;h.appendHidden=k;h.submitToTarget=b},98);c.__d("sdk.DOM",["guid","sdk.domReady"],function(b,c,d,f,g,h,i){var j={},a={};function k(a,b){a=a.getAttribute(b)||a.getAttribute(b.replace(/_/g,"-"))||a.getAttribute(b.replace(/-/g,"_"))||a.getAttribute(b.replace(/-/g,""))||a.getAttribute(b.replace(/_/g,""))||a.getAttribute("data-"+b)||a.getAttribute("data-"+b.replace(/_/g,"-"))||a.getAttribute("data-"+b.replace(/-/g,"_"))||a.getAttribute("data-"+b.replace(/-/g,""))||a.getAttribute("data-"+b.replace(/_/g,""));return a!=null?String(a):null}function b(a,b){a=k(a,b);return a!=null?/^(true|1|yes|on)$/.test(a):null}function c(a,b){try{a.innerHTML=b}catch(a){throw new Error("Could not set innerHTML : "+a.message)}}function l(a,b){a=" "+a.className+" ";return a.indexOf(" "+b+" ")>=0}function f(a,b){if(a==null)return;l(a,b)||(a.className=a.className+" "+b)}function g(a,b){if(a==null)return;b=new RegExp("\\s*"+b,"g");a.className=a.className.replace(b,"").trim()}function h(a,b,c){c===void 0&&(c="*");b=b||document.body;if(b==null)return[];c=c||"*";return e("Array","from",!1,b.querySelectorAll(c+"."+a))}function m(a,b){b=v(b);var c=document.defaultView.getComputedStyle(a).getPropertyValue(b);c=a.style.getPropertyValue(b);/background-position?/.test(b)&&/top|left/.test(c)&&(c="0%");return c}function n(a,b,c){a.style.setProperty(v(b),c)}function o(b,c,d,e,f){b=b.styleSheets;for(var k=0;k<b.length;k++){var l;if(b[k].ownerNode instanceof HTMLElement&&b[k].ownerNode.dataset!=null&&((l=b[k].ownerNode.dataset.fbcssmodules)==null?void 0:l.indexOf(c))!==-1){l=b[k];if(l instanceof CSSStyleSheet){for(var m=0;m<l.cssRules.length;m++){var a=l.cssRules[m];if(a instanceof CSSStyleRule&&a.selectorText===d){a.style.setProperty(v(e),f);return}}l.insertRule(d+"{"+v(e)+":"+((a=f)!=null?a:"")+"}",0)}}}}function p(b,c,e){var f;if(e!=null&&e.nodeType===11){var m=e;m.host.id!=null&&a[m.host.id]!=null?f=a[m.host.id]:(m.host.id||(m.host.id=d("guid")()),f={},a[m.host.id]=f)}else f=j;m=!0;for(var n=0,o;o=c[n++];)o in f||(m=!1,f[o]=!0);if(m)return;o=document.createElement("style");o.type="text/css";o.textContent=b;var p="";c.forEach(function(a){return p+=a+" "});o.setAttribute("data-fbcssmodules",p.trim());e==null||e===document?document.getElementsByTagName("head")[0].appendChild(o):e.appendChild(o)}function q(a){if(!a||!a.parentNode)return null;else return a.parentNode.removeChild(a)}function r(){var a,b=document.documentElement&&document.compatMode=="CSS1Compat"?document.documentElement:document.body;return{scrollTop:(b==null?void 0:b.scrollTop)||((a=document.body)==null?void 0:a.scrollTop),scrollLeft:(b==null?void 0:b.scrollLeft)||((a=document.body)==null?void 0:a.scrollLeft),width:window.innerWidth?window.innerWidth:b==null?void 0:b.clientWidth,height:window.innerHeight?window.innerHeight:b==null?void 0:b.clientHeight}}var s=/[A-Z]/g,t=/^\([^-]\)-/,u=["o","moz","ms","webkit"];function v(a){a=a.replace(s,"-$&").toLowerCase();var b=a.match(t);b&&u.indexOf(b[1])!==-1&&(a="-"+a);return a}i.getAttr=k;i.getBoolAttr=b;i.html=c;i.containsCss=l;i.addCss=f;i.removeCss=g;i.getByClass=h;i.getStyle=m;i.setStyle=n;i.updateOrAddCssRule=o;i.addCssRules=p;i.remove=q;i.getViewportInfo=r;i.ready=d("sdk.domReady")},98);c.__d("ManagedError",[],function(a,b,c,d,e,g){a=function(a){f.inheritsLoose(b,a);function b(b,c){var d;d=a.call(this,b!==null&&b!==void 0?b:"")||this;b!==null&&b!==void 0?d.message=b:d.message="";d.innerError=c;return d}return b}(f.wrapNativeSuper(Error)),g["default"]=a},66);c.__d("normalizeError",["sdk.UA"],function(a,b,c,d,e,f,g){"use strict";function a(a){var b={line:a.lineNumber||a.line,message:a.message,name:a.name,script:a.fileName||a.sourceURL||a.script,stack:a.stackTrace||a.stack};b._originalError=a;a=/([\w:\.\/]+\.js):(\d+)/.exec(a.stack);c("sdk.UA").chrome()&&a&&(b.script=a[1],b.line=parseInt(a[2],10));for(a in b)b[a]==null&&delete b[a];return b}g["default"]=a},98);c.__d("ObservableMixin",[],function(a,b,c,d,e,f){function a(){this.__observableEvents={}}a.prototype={inform:function(a){var b=Array.prototype.slice.call(arguments,1),c=Array.prototype.slice.call(this.getSubscribers(a));for(var d=0;d<c.length;d++){if(c[d]===null)continue;try{c[d].apply(this,b)}catch(a){window.setTimeout(function(){throw a},0)}}return this},getSubscribers:function(a){return this.__observableEvents[a]||(this.__observableEvents[a]=[])},clearSubscribers:function(a){a&&(this.__observableEvents[a]=[]);return this},subscribe:function(a,b){a=this.getSubscribers(a);a.push(b);return this},unsubscribe:function(a,b){a=this.getSubscribers(a);for(var c=0;c<a.length;c++)if(a[c]===b){a.splice(c,1);break}return this}};e.exports=a},null);c.__d("AssertionError",["ManagedError"],function(a,b,c,d,e,g,h){a=function(a){f.inheritsLoose(b,a);function b(b){return a.call(this,b)||this}return b}(c("ManagedError")),h["default"]=a},98);c.__d("sprintf",[],function(a,b,c,d,e,f){function a(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];var e=0;return a.replace(/%s/g,function(){return String(c[e++])})}f["default"]=a},66);c.__d("Assert",["AssertionError","sprintf"],function(b,c,d,f,g,h,i){function j(a,b){if(typeof a!=="boolean"||a===!1)throw new(d("AssertionError"))(b);return a}function a(a,b,c){var e;if(b===void 0)e="undefined";else if(b===null)e="null";else{var f=Object.prototype.toString.call(b);f=/\s(\w*)/.exec(f);e=f==null?typeof f:f[1].toLowerCase()}j(a.indexOf(e)!==-1,(f=c)!=null?f:d("sprintf")("Expression is of type %s, not %s",e,a));return b}function b(a,b,c){j(b instanceof a,(a=c)!=null?a:"Expression not instance of type");return b}function k(a,b){l["is"+a]=b,l["maybe"+a]=function(a,c){return a==null?a:b(a,c)}}c=function(a,b){return a};var l={isInstanceOf:b,isTrue:j,isTruthy:function(a,b){return j(!!a,b)},isBoolean:c,isFunction:c,isNumber:c,isObject:c,isString:c,isUndefined:c,maybeObject:c,maybeNumber:c,maybeFunction:c};["Boolean","Function","Number","Object","String","Undefined"].forEach(function(b){k(b,e(a,"bind",!0,null,b.toLowerCase()))});f=l;i["default"]=f},98);c.__d("Type",["Assert"],function(b,c,d,f,g,h){function i(){var a=this.__mixins;if(a)for(var b=0;b<a.length;b++)a[b].apply(this,arguments)}function j(a,b){if(b instanceof a)return!0;if(b instanceof i)for(var c=0;c<b.__mixins.length;c++)if(b.__mixins[c]==a)return!0;return!1}function a(a,b){var c=a.prototype;Array.isArray(b)||(b=[b]);for(a=0;a<b.length;a++){var d=b[a];typeof d==="function"&&(c.__mixins.push(d),d=d.prototype);Object.keys(d).forEach(function(a){c[a]=d[a]})}}function k(b,d,f){var j=d&&Object.prototype.hasOwnProperty.call(d,"constructor")?d.constructor:function(){this.parent.apply(this,arguments)};c("Assert").isFunction(j);if(b&&b.prototype instanceof i===!1)throw new Error("parent type does not inherit from Type");b=b||i;function h(){}h.prototype=b.prototype;j.prototype=new h();d&&e("Object","assign",!1,j.prototype,d);j.prototype.constructor=j;j.parent=b;j.prototype.__mixins=b.prototype.__mixins?Array.prototype.slice.call(b.prototype.__mixins):[];f&&a(j,f);j.prototype.parent=function(){this.parent=b.prototype.parent,b.apply(this,arguments)};j.prototype.parentCall=function(a){return b.prototype[a].apply(this,Array.prototype.slice.call(arguments,1))};j.extend=function(a,b){return k(this,a,b)};return j}e("Object","assign",!1,i.prototype,{instanceOf:function(a){return j(a,this)}});e("Object","assign",!1,i,{extend:function(a,b){return typeof a==="function"?k.apply(null,arguments):k(null,a,b)},instanceOf:j});g.exports=i},null);c.__d("sdk.Model",["ObservableMixin","Type"],function(a,b,c,d,e,f,g){"use strict";a=c("Type").extend({constructor:function(a){this.parent();var b={},c=this;Object.keys(a).forEach(function(d){b[d]=a[d],c["set"+d]=function(a){if(a===b[d])return c;b[d]=a;c.inform(d+".change",a);return c},c["get"+d]=function(){return b[d]}})}},c("ObservableMixin")),b=a,g["default"]=b},98);c.__d("sdk.Runtime",["JSSDKRuntimeConfig","sdk.Model"],function(b,c,d,f,g,h,i){var j={UNKNOWN:0,PAGETAB:1,CANVAS:2,PLATFORM:4},a=new(d("sdk.Model"))({AccessToken:"",AutoLogAppEvents:!1,ClientID:"",CookieUserID:"",EnforceHttps:!1,Environment:j.UNKNOWN,FamilyLoginLoaded:!1,GraphDomain:"",Initialized:!1,IsSPIN:Boolean(f("JSSDKRuntimeConfig").isSPIN),IsVersioned:!1,KidDirectedSite:void 0,Locale:f("JSSDKRuntimeConfig").locale,LoggedIntoFacebook:void 0,LoginStatus:void 0,Revision:f("JSSDKRuntimeConfig").revision,Rtl:f("JSSDKRuntimeConfig").rtl,Scope:void 0,SDKAB:f("JSSDKRuntimeConfig").sdkab,SDKUrl:f("JSSDKRuntimeConfig").sdkurl,SDKNS:f("JSSDKRuntimeConfig").sdkns,ShouldLoadFamilyLogin:!1,UseCookie:!1,UseLocalStorage:!0,UserID:"",Version:void 0});e("Object","assign",!1,a,{ENVIRONMENTS:j,isEnvironment:function(a){var b=this.getEnvironment();return(a|b)===b},isCanvasEnvironment:function(){return this.isEnvironment(j.CANVAS)||this.isEnvironment(j.PAGETAB)}});(function(){var b=/app_runner/.test(window.name)?j.PAGETAB:/iframe_canvas/.test(window.name)?j.CANVAS:j.UNKNOWN;(b|j.PAGETAB)===b&&(b|=j.CANVAS);a.setEnvironment(b)})();b=a;i["default"]=b},98);c.__d("sdk.ErrorHandler",["ManagedError","normalizeError","sdk.Runtime","wrapFunction"],function(a,b,c,d,f,g,h){function a(b,d){var f="";function i(a){var b=a._originalError;delete a._originalError;d(a);throw b}function g(a,d){return function(){if(!b)return a.apply(this,arguments);try{f=d;return a.apply(this,arguments)}catch(a){if(a instanceof c("ManagedError"))throw a;var g=c("normalizeError")(a);if(!g.script){var j=/.*\/([^?#]+)/.exec(c("sdk.Runtime").getSDKUrl());g.script=j!==null?j[1]:""}g.entry=d;var i=Array.prototype.slice.call(arguments).map(function(a){var b=Object.prototype.toString.call(a);return/^\[object (String|Number|Boolean|Object|Date)\]$/.test(b)?a:a.toString()});g.args=e("JSON","stringify",!1,i).substring(0,200);i(g)}finally{f=""}}}function h(a){a.__wrapper||(a.__wrapper=function(){try{return a.apply(this,arguments)}catch(a){window.setTimeout(function(){throw a},0);return!1}});return a.__wrapper}function j(a){try{return a&&a.callee&&a.callee.caller?a.callee.caller.name:""}catch(a){return""}}function a(a,b){return function(d,e){var h=b+":"+(f||"[global]")+":"+(d.name||"[anonymous]"+j(arguments));return a(c("wrapFunction")(d,"entry",h),e)}}b&&(setTimeout=a(setTimeout,"setTimeout"),setInterval=a(setInterval,"setInterval"),c("wrapFunction").setWrapper(g,"entry"));return{guard:g,unguard:h}}h.create=a},98);c.__d("QueryString",[],function(a,b,c,d,e,f){function g(a){var b=[];Object.keys(a).sort().forEach(function(c){var d=a[c];if(d===void 0)return;if(d===null){b.push(c);return}b.push(encodeURIComponent(c)+"="+encodeURIComponent(String(d)))});return b.join("&")}function a(a,b){b===void 0&&(b=!1);var c={};if(a==="")return c;a=a.split("&");for(var d=0;d<a.length;d++){var e=a[d].split("=",2),f=decodeURIComponent(e[0]);if(b&&Object.prototype.hasOwnProperty.call(c,f))throw new URIError("Duplicate key: "+f);c[f]=e.length===2?decodeURIComponent(e[1]):null}return c}function b(a,b){return a+(a.indexOf("?")!==-1?"&":"?")+(typeof b==="string"?b:g(b))}c={encode:g,decode:a,appendToUrl:b};f["default"]=c},66);c.__d("Env",[],function(a,b,c,d,f,g){b={ajaxpipe_token:null,compat_iframe_token:null,iframeKey:"",iframeTarget:"",iframeToken:"",isCQuick:!1,jssp_header_sent:!1,jssp_targeting_enabled:!1,start:Date.now(),nocatch:!1,useTrustedTypes:!1,isTrustedTypesReportOnly:!1,enableDefaultTrustedTypesPolicy:!1,ig_server_override:"",ig_mqtt_wss_endpoint:"",ig_mqtt_polling_endpoint:"",scheduledCSSJSScheduler:!1},a.Env&&e("Object","assign",!1,b,a.Env),a.Env=b,c=b,g["default"]=c},66);c.__d("fb-error-lite",[],function(a,b,c,d,e,f){"use strict";var g={PREVIOUS_FILE:1,PREVIOUS_FRAME:2,PREVIOUS_DIR:3,FORCED_KEY:4};function a(a){var b=new Error(a);if(b.stack===void 0)try{throw b}catch(a){}b.messageFormat=a;for(var c=arguments.length,d=new Array(c>1?c-1:0),e=1;e<c;e++)d[e-1]=arguments[e];b.messageParams=d.map(function(a){return String(a)});b.taalOpcodes=[g.PREVIOUS_FRAME];return b}b={err:a,TAALOpcode:g};f["default"]=b},66);c.__d("invariant",["Env","fb-error-lite","sprintf"],function(b,c,d,e,f,g,h){"use strict";function b(b,c){if(!b){var e=c;for(var f=arguments.length,g=new Array(f>2?f-2:0),h=2;h<f;h++)g[h-2]=arguments[h];if(typeof e==="number"){var a=i(e,g),j=a.message,k=a.decoderLink;e=j;g.unshift(k)}else if(e===void 0){e="Invariant: ";for(var l=0;l<g.length;l++)e+="%s,"}var m=e,n=new Error(m);n.name="Invariant Violation";n.messageFormat=e;n.messageParams=g.map(function(a){return String(a)});n.taalOpcodes=[d("fb-error-lite").TAALOpcode.PREVIOUS_FRAME];n.stack;throw n}}function i(b,c){var e="Minified invariant #"+b+"; %s";c.length>0&&(e+=" Params: "+c.map(function(a){return"%s"}).join(", "));b=d("Env").show_invariant_decoder===!0?"visit "+a(b,c)+" to see the full message.":"";return{message:e,decoderLink:b}}function a(a,b){a="https://www.internalfb.com/intern/invariant/"+a+"/";b.length>0&&(a+="?"+b.map(function(a,b){return"args["+b+"]="+encodeURIComponent(String(a))}).join("&"));return a}h["default"]=b},98);c.__d("UrlMap",["invariant","UrlMapConfig","sdk.Runtime"],function(a,b,c,d,e,f,g,h){function a(a){var b="https";if(a==="graph_domain"){var e=c("sdk.Runtime").getGraphDomain();e?a="graph_".concat(e):a="graph"}if(a in d("UrlMapConfig"))return b+"://"+d("UrlMapConfig")[a];a in d("UrlMapConfig")||h(0,2511,a);return""}g.resolve=a},98);c.__d("sdk.Scribe",["QueryString","UrlMap","sdk.Runtime"],function(a,b,c,d,g,h,i){var j={};function a(a,b,g){g===void 0&&(g=!1);if(a==="jssdk_error"){var h=e("JSON","stringify",!1,b);if(Object.prototype.hasOwnProperty.call(j,h))return;else j[h]=!0}b.extra!=null&&typeof b.extra==="object"&&(h=b.extra,h.revision=c("sdk.Runtime").getRevision());h=new Image();var i=d("UrlMap").resolve("www")+"/platform/scribe_endpoint.php/";g||(h.crossOrigin="anonymous");h.src=c("QueryString").appendToUrl(i,{c:a,m:e("JSON","stringify",!1,f["extends"]({},b,{isSPIN:c("sdk.Runtime").getIsSPIN()}))})}i.log=a},98);c.__d("sdk.FeatureFunctor",[],function(a,b,c,d,e,f){function g(a,b,c){if(a.features&&b in a.features){a=a.features[b];if(typeof a==="object"&&typeof a.rate==="number")if(a.rate&&Math.random()*100<=a.rate)return a.value||!0;else return a.value?null:!1;else return a}return c}function a(a){return function(){for(var b=arguments.length,c=new Array(b),d=0;d<b;d++)c[d]=arguments[d];if(c.length<2)throw new Error("Default value is required");var e=c[0],f=c[1];return g(a,e,f)}}f.create=a},66);c.__d("sdk.feature",["JSSDKConfig","sdk.FeatureFunctor"],function(a,b,c,d,e,f,g){a=d("sdk.FeatureFunctor").create(d("JSSDKConfig")),g["default"]=a},98);c.__d("sdk.ErrorHandling",["sdk.ErrorHandler","sdk.Runtime","sdk.Scribe","sdk.feature"],function(a,b,c,d,e,f,g){a=c("sdk.feature")("error_handling",!1),b=d("sdk.ErrorHandler").create(a,function(a){d("sdk.Scribe").log("jssdk_error",{appId:c("sdk.Runtime").getClientID(),error:a.name||a.message,extra:a})}),g["default"]=b},98);c.__d("FB",["DOMWrapper","GlobalCallback","JSSDKCssConfig","Log","dotAccess","sdk.Content","sdk.DOM","sdk.ErrorHandling","sdk.domReady"],function(b,c,d,g,h,i,j){window.FB&&window.FB.__buffer&&(window.__buffer=f["extends"]({},window.FB.__buffer));var k=window.FB={};c={};g("Log").setLevel(0);g("GlobalCallback").setPrefix("FB.__globalCallbacks");var a=document.createElement("div");g("DOMWrapper").setRoot(a);d("sdk.domReady")(function(){g("Log").info("domReady"),g("sdk.Content").appendHidden(a),d("JSSDKCssConfig").rules&&g("sdk.DOM").addCssRules(d("JSSDKCssConfig").rules,d("JSSDKCssConfig").components)});function l(a,b,c,e){return d("sdk.ErrorHandling").guard(function(){function f(a){return Array.isArray(a)?a.map(f):a&&typeof a==="object"&&a.__wrapped?a.__wrapped:typeof a==="function"&&/^function/.test(a.toString())?d("sdk.ErrorHandling").unguard(a):a}var h=Array.prototype.slice.call(arguments).map(f),j=a.apply(e,h),k,c=!0;if(j&&typeof j==="object"){k=Object.create(j);k.__wrapped=j;for(var m in j){var n=j[m];if(typeof n!=="function"||m==="constructor")continue;c=!1;k[m]=l(n,b+":"+m,m,j)}}return c?c?j:k:k},b)}function b(a,b){var c=a?d("dotAccess")(k,a,!0):k;Object.keys(b).forEach(function(d){var e=b[d];if(typeof e==="function"){var f=(a?a+".":"")+d;f=l(e,f,d,b);f&&(c[d]=f)}else(typeof e==="object"||typeof e==="number")&&(c[d]=e)})}e("Object","assign",!1,c,{provide:b});h=c;j["default"]=h},98);c.__d("AppUserPropertyAPIBuiltinField",[],function(a,b,c,d,e,f){e.exports={GENDER:"$gender",CITY:"$city",STATE:"$state",ZIPCODE:"$zipcode",COUNTRY:"$country",LANGUAGE:"$language",CURRENCY:"$currency",INSTALL_SOURCE:"$install_source",USER_TYPE:"$user_type",ACCOUNT_CREATED_TIME:"$account_created_time",APP_ID:"$app_id"}},null);c.__d("ArgumentError",["ManagedError"],function(a,b,c,d,e,g,h){a=function(a){f.inheritsLoose(b,a);function b(b,c){return a.call(this,b,c)||this}return b}(c("ManagedError")),h["default"]=a},98);c.__d("flattenObject",[],function(a,b,c,d,f,g){"use strict";function a(a){var b={};for(var c in a)if(Object.prototype.hasOwnProperty.call(a,c)){var d=a[c];if(d==null)continue;else typeof d==="string"?b[c]=d:b[c]=e("JSON","stringify",!1,d)}return b}g["default"]=a},66);c.__d("performance",[],function(a,b,c,d,e,f){"use strict";b=a.performance||a.msPerformance||a.webkitPerformance||{},c=b,f["default"]=c},66);c.__d("performanceNow",["performance"],function(b,c,d,e,f,g,h){if(d("performance").now)c=function(){return d("performance").now()};else{e=b._cstart;f=Date.now();var i=typeof e==="number"&&e<f?e:f,a=0;c=function(){var b=Date.now(),c=b-i;c<a&&(i-=a-c,c=b-i);a=c;return c}}g=c;h["default"]=g},98);c.__d("performanceNowSinceAppStart",["performanceNow"],function(a,b,c,d,e,f,g){g["default"]=c("performanceNow")},98);c.__d("removeFromArray",[],function(a,b,c,d,e,f){function a(a,b){b=a.indexOf(b),b!==-1&&a.splice(b,1)}f["default"]=a},66);c.__d("fb-error",["performanceNowSinceAppStart","removeFromArray"],function(b,c,d,g,h,i){"use strict";var j={PREVIOUS_FILE:1,PREVIOUS_FRAME:2,PREVIOUS_DIR:3,FORCED_KEY:4};function k(b){var a=new Error(b);if(a.stack===void 0)try{throw a}catch(a){}a.messageFormat=b;for(var c=arguments.length,d=new Array(c>1?c-1:0),e=1;e<c;e++)d[e-1]=arguments[e];a.messageParams=d.map(function(a){return String(a)});a.taalOpcodes=[j.PREVIOUS_FRAME];return a}var a=!1,l={errorListener:function(c){var d=b.console,e=d[c.type]?c.type:"error";(c.type==="fatal"||e==="error"&&!a)&&(e=c.message,d.error("ErrorUtils caught an error:\n\n"+e+"\n\nSubsequent non-fatal errors won't be logged; see https://fburl.com/debugjs."),a=!0)}},m={access_token:null},n=6,o=6e4,p=10*o,q=new Map(),r=0;function s(){var a=c("performanceNowSinceAppStart")();if(a>r+o){var b=a-p;for(var d=q,e=Array.isArray(d),f=0,d=e?d:d[typeof Symbol==="function"?typeof Symbol==="function"?Symbol.iterator:"@@iterator":"@@iterator"]();;){var l;if(e){if(f>=d.length)break;l=d[f++]}else{f=d.next();if(f.done)break;l=f.value}l=l;var m=l[0];l=l[1];l.lastAccessed<b&&q["delete"](m)}r=a}}function t(a){s();var b=c("performanceNowSinceAppStart")(),d=q.get(a);if(d==null){q.set(a,{dropped:0,logged:[b],lastAccessed:b});return 1}a=d.dropped;var e=d.logged;d.lastAccessed=b;while(e[0]<b-o)e.shift();if(e.length<n){d.dropped=0;e.push(b);return a+1}else{d.dropped++;return null}}var u={shouldLog:function(a){return t(a.hash)}},v="RE_EXN_ID";function w(a){var b=null;a==null||typeof a!=="object"?b=k("Non-object thrown: %s",String(a)):Object.prototype.hasOwnProperty.call(a,v)?b=k("Rescript exception thrown: %s",e("JSON","stringify",!1,a)):typeof a.message!=="string"?b=k("Non-error thrown: %s, keys: %s",String(a),e("JSON","stringify",!1,Object.keys(a).sort())):Object.isExtensible&&!Object.isExtensible(a)&&(b=k("Non-extensible thrown: %s",String(a.message)));if(b!=null){b.taalOpcodes=b.taalOpcodes||[];b.taalOpcodes.push(j.PREVIOUS_FRAME);return b}return a}var x=typeof window==="undefined"?"<self.onerror>":"<window.onerror>",y;function z(a){var b=a.error!=null?w(a.error):k(a.message||"");b.fileName==null&&a.filename!=null&&(b.fileName=a.filename);b.line==null&&a.lineno!=null&&(b.line=a.lineno);b.column==null&&a.colno!=null&&(b.column=a.colno);b.guardList=[x];b.loggingSource="ONERROR";(a=y)===null||a===void 0?void 0:a.reportError(b)}var A={setup:function(a){if(typeof b.addEventListener!=="function")return;if(y!=null)return;y=a;b.addEventListener("error",z)}},B=[],C={pushGuard:function(a){B.unshift(a)},popGuard:function(){B.shift()},inGuard:function(){return B.length!==0},cloneGuardList:function(){return B.map(function(a){return a.name})},findDeferredSource:function(){for(var a=0;a<B.length;a++){var b=B[a];if(b.deferredSource!=null)return b.deferredSource}}};function D(a){if(a.type!=null)return a.type;if(a.loggingSource=="GUARDED"||a.loggingSource=="ERROR_BOUNDARY")return"fatal";if(a.name=="SyntaxError")return"fatal";return a.loggingSource=="ONERROR"&&a.message.indexOf("ResizeObserver loop")>=0?"warn":a.stack!=null&&a.stack.indexOf("chrome-extension://")>=0?"warn":"error"}var E=[],F=function(){function a(){this.metadata=[].concat(E)}var b=a.prototype;b.addEntries=function(){var a;(a=this.metadata).push.apply(a,arguments);return this};b.addEntry=function(a,b,c){this.metadata.push([a,b,c]);return this};b.isEmpty=function(){return this.metadata.length===0};b.clearEntries=function(){this.metadata=[]};b.format=function(){var a=[];this.metadata.forEach(function(b){b&&b.length&&(b=b.map(function(a){return a!=null?String(a).replace(/:/g,"_"):""}).join(":"),a.push(b))});return a};b.getAll=function(){return this.metadata};a.addGlobalMetadata=function(a,b,c){E.push([a,b,c])};a.getGlobalMetadata=function(){return E};a.unsetGlobalMetadata=function(a,b){E=E.filter(function(c){return!(Array.isArray(c)&&c[0]===a&&c[1]===b)})};return a}(),G={debug:1,info:2,warn:3,error:4,fatal:5};function d(a,b){if(Object.isFrozen(a))return;b.type&&(!a.type||G[a.type]>G[b.type])&&(a.type=b.type);var c=b.metadata;if(c!=null){var d;d=(d=a.metadata)!==null&&d!==void 0?d:new F();c!=null&&d.addEntries.apply(d,c.getAll());a.metadata=d}b.project!=null&&(a.project=b.project);b.errorName!=null&&(a.errorName=b.errorName);b.componentStack!=null&&(a.componentStack=b.componentStack);b.deferredSource!=null&&(a.deferredSource=b.deferredSource);b.blameModule!=null&&(a.blameModule=b.blameModule);b.loggingSource!=null&&(a.loggingSource=b.loggingSource);d=(c=a.messageFormat)!==null&&c!==void 0?c:a.message;c=(c=a.messageParams)!==null&&c!==void 0?c:[];if(d!==b.messageFormat&&b.messageFormat!=null){var e;d+=" [Caught in: "+b.messageFormat+"]";c.push.apply(c,(e=b.messageParams)!==null&&e!==void 0?e:[])}a.messageFormat=d;a.messageParams=c;e=b.forcedKey;d=a.forcedKey;c=e!=null&&d!=null?e+"_"+d:e!==null&&e!==void 0?e:d;a.forcedKey=c}function g(a){var b;return H((b=a.messageFormat)!==null&&b!==void 0?b:a.message,a.messageParams||[])}function H(a,b){var c=0;a=a.replace(/%s/g,function(){return c<b.length?b[c++]:"NOPARAM"});c<b.length&&(a+=" PARAMS"+e("JSON","stringify",!1,b.slice(c)));return a}function i(a){return(a!==null&&a!==void 0?a:[]).map(function(a){return String(a)})}var I={aggregateError:d,toReadableMessage:g,toStringParams:i},J=5,K=[];function L(a){K.push(a),K.length>J&&K.shift()}function M(a){var b=a.getAllResponseHeaders();b!=null&&b.indexOf("X-FB-Debug")>=0&&(b=a.getResponseHeader("X-FB-Debug"),b&&L(b))}function N(){return K}var O={add:L,addFromXHR:M,getAll:N},P="abcdefghijklmnopqrstuvwxyz012345";function Q(){var b=0;for(var c=arguments.length,d=new Array(c),e=0;e<c;e++)d[e]=arguments[e];for(var f=0;f<d.length;f++){var m=d[f];if(m!=null){var n=m.length;for(var o=0;o<n;o++)b=(b<<5)-b+m.charCodeAt(o)}}var a="";for(var p=0;p<6;p++)a=P.charAt(b&31)+a,b>>=5;return a}var R=[/\(([^\s\)\()]+):(\d+):(\d+)\)$/,/@([^\s\)\()]+):(\d+):(\d+)$/,/^([^\s\)\()]+):(\d+):(\d+)$/,/^at ([^\s\)\()]+):(\d+):(\d+)$/],S=/^\w+:\s.*?\n/g;Error.stackTraceLimit!=null&&Error.stackTraceLimit<80&&(Error.stackTraceLimit=80);function T(a){var b=a.name,c=a.message;a=a.stack;if(a==null)return null;if(b!=null&&c!=null&&c!==""){var d=b+": "+c+"\n";if(e(a,"startsWith",!0,d))return a.substr(d.length);if(a===b+": "+c)return null}if(b!=null){d=b+"\n";if(e(a,"startsWith",!0,d))return a.substr(d.length)}if(c!=null&&c!==""){b=": "+c+"\n";d=a.indexOf(b);c=a.substring(0,d);if(/^\w+$/.test(c))return a.substring(d+b.length)}return a.replace(S,"")}function U(a){a=a.trim();var b;a;var c,d,f;if(e(a,"includes",!0,"charset=utf-8;base64,"))b="<inlined-file>";else{var l;for(var m=0;m<R.length;m++){var n=R[m];l=a.match(n);if(l!=null)break}l!=null&&l.length===4?(c=l[1],d=parseInt(l[2],10),f=parseInt(l[3],10),b=a.substring(0,a.length-l[0].length)):b=a;b=b.replace(/^at /,"").trim()}n={identifier:b,script:c,line:d,column:f};n.text=ba(n);return n}function V(a){return a==null||a===""?[]:a.split(/\n\n/)[0].split("\n").map(U)}function W(a){a=T(a);return V(a)}function aa(a){if(a==null||a==="")return null;a=a.split("\n");a.splice(0,1);return a.map(function(a){return a.trim()})}function ba(a){var b=a.identifier,c=a.script,d=a.line;a=a.column;b="    at "+(b!==null&&b!==void 0?b:"<unknown>");c!=null&&d!=null&&a!=null&&(b+=" ("+c+":"+d+":"+a+")");return b}function ca(d){var e,f,w,x,a,y,z=W(d);e=(e=d.taalOpcodes)!==null&&e!==void 0?e:[];var A=d.framesToPop;if(A!=null){A=Math.min(A,z.length);while(A-->0)e.unshift(j.PREVIOUS_FRAME)}A=(A=d.messageFormat)!==null&&A!==void 0?A:d.message;f=((f=d.messageParams)!==null&&f!==void 0?f:[]).map(function(a){return String(a)});var B=aa(d.componentStack),C=B==null?null:B.map(U),E=d.metadata?d.metadata.format():new F().format();E.length===0&&(E=void 0);var G=z.map(function(a){return a.text}).join("\n");w=(w=d.errorName)!==null&&w!==void 0?w:d.name;var H=D(d),J=d.loggingSource,K=d.project;x=(x=d.lineNumber)!==null&&x!==void 0?x:d.line;a=(a=d.columnNumber)!==null&&a!==void 0?a:d.column;y=(y=d.fileName)!==null&&y!==void 0?y:d.sourceURL;var L=z.length>0;L&&x==null&&(x=z[0].line);L&&a==null&&(a=z[0].column);L&&y==null&&(y=z[0].script);C={blameModule:d.blameModule,column:a==null?null:String(a),clientTime:Math.floor(Date.now()/1e3),componentStackFrames:C,deferredSource:d.deferredSource!=null?ca(d.deferredSource):null,extra:(L=d.extra)!==null&&L!==void 0?L:{},fbtrace_id:d.fbtrace_id,guardList:(a=d.guardList)!==null&&a!==void 0?a:[],hash:Q(w,G,H,K,J),isNormalizedError:!0,line:x==null?null:String(x),loggingSource:J,message:I.toReadableMessage(d),messageFormat:A,messageParams:f,metadata:E,name:w,page_time:Math.floor(c("performanceNowSinceAppStart")()),project:K,reactComponentStack:B,script:y,serverHash:d.serverHash,stack:G,stackFrames:z,type:H,xFBDebug:O.getAll()};d.forcedKey!=null&&(C.forcedKey=d.forcedKey);e.length>0&&(C.taalOpcodes=e);L=b.location;L&&(C.windowLocationURL=L.href);for(a in C)C[a]==null&&delete C[a];return C}function da(a){return a!=null&&typeof a==="object"&&a.isNormalizedError===!0?a:null}var X={formatStackFrame:ba,normalizeError:ca,ifNormalizedError:da},ea="<global.react>",Y=[],fa=[],ga=50,ha=!1,Z={history:fa,addListener:function(a,b){b===void 0&&(b=!1),Y.push(a),b||fa.forEach(function(b){return a(b,(b=b.loggingSource)!==null&&b!==void 0?b:"DEPRECATED")})},unshiftListener:function(a){Y.unshift(a)},removeListener:function(a){c("removeFromArray")(Y,a)},reportError:function(a){a=X.normalizeError(a),Z.reportNormalizedError(a)},reportNormalizedError:function(b){if(ha)return!1;var a=C.cloneGuardList();b.componentStackFrames&&a.unshift(ea);a.length>0&&(b.guardList=a);b.deferredSource==null&&(a=C.findDeferredSource(),a!=null&&(b.deferredSource=X.normalizeError(a)));fa.length>ga&&fa.splice(ga/2,1);fa.push(b);ha=!0;for(a=0;a<Y.length;a++)try{var c;Y[a](b,(c=b.loggingSource)!==null&&c!==void 0?c:"DEPRECATED")}catch(a){}ha=!1;return!0}};Z.addListener(l.errorListener);var ia="<anonymous guard>",ja=!1,ka={applyWithGuard:function(a,b,c,d){C.pushGuard({name:((d===null||d===void 0?void 0:d.name)!=null?d.name:null)||(a.name?"func_name:"+a.name:null)||ia,deferredSource:d===null||d===void 0?void 0:d.deferredSource});if(ja)try{return a.apply(b,c)}finally{C.popGuard()}try{return Function.prototype.apply.call(a,b,c)}catch(g){try{b=d!==null&&d!==void 0?d:f["extends"]({},null);var l=b.deferredSource,m=b.onError;b=b.onNormalizedError;var n=w(g);l={deferredSource:l,loggingSource:"GUARDED",project:(l=d===null||d===void 0?void 0:d.project)!==null&&l!==void 0?l:"ErrorGuard",type:d===null||d===void 0?void 0:d.errorType};I.aggregateError(n,l);d=X.normalizeError(n);n==null&&a&&(d.extra[a.toString().substring(0,100)]="function",c!=null&&c.length&&(d.extra[e("Array","from",!1,c).toString().substring(0,100)]="args"));d.guardList=C.cloneGuardList();m&&m(n);b&&b(d);Z.reportNormalizedError(d)}catch(a){}}finally{C.popGuard()}},guard:function(a,b){function c(){for(var c=arguments.length,d=new Array(c),e=0;e<c;e++)d[e]=arguments[e];return ka.applyWithGuard(a,this,d,b)}a.__SMmeta&&(c.__SMmeta=a.__SMmeta);return c},inGuard:function(){return C.inGuard()},skipGuardGlobal:function(a){ja=a}},la=1024,ma=[],na=0;function oa(a){return String(a)}function $(a){return a==null?null:String(a)}function pa(a,b){var c={};b&&b.forEach(function(a){c[a]=!0});Object.keys(a).forEach(function(b){a[b]?c[b]=!0:c[b]&&delete c[b]});return Object.keys(c)}function qa(a){return(a!==null&&a!==void 0?a:[]).map(function(a){return{column:$(a.column),identifier:a.identifier,line:$(a.line),script:a.script}})}function ra(a){a=String(a);return a.length>la?a.substring(0,la-3)+"...":a}function sa(a,b){var c;c={appId:$(b.appId),cavalry_lid:b.cavalry_lid,access_token:m.access_token,ancestor_hash:a.hash,bundle_variant:(c=b.bundle_variant)!==null&&c!==void 0?c:null,clientTime:oa(a.clientTime),column:a.column,componentStackFrames:qa(a.componentStackFrames),events:a.events,extra:pa(a.extra,b.extra),forcedKey:a.forcedKey,frontend_env:(c=b.frontend_env)!==null&&c!==void 0?c:null,guardList:a.guardList,line:a.line,loggingFramework:b.loggingFramework,messageFormat:ra(a.messageFormat),messageParams:a.messageParams.map(ra),name:a.name,sample_weight:$(b.sample_weight),script:a.script,site_category:b.site_category,stackFrames:qa(a.stackFrames),type:a.type,page_time:$(a.page_time),project:a.project,push_phase:b.push_phase,report_source:b.report_source,report_source_ref:b.report_source_ref,rollout_hash:(c=b.rollout_hash)!==null&&c!==void 0?c:null,script_path:b.script_path,server_revision:$(b.server_revision),spin:$(b.spin),svn_rev:String(b.client_revision),additional_client_revisions:e("Array","from",!1,(c=b.additional_client_revisions)!==null&&c!==void 0?c:[]).map(oa),taalOpcodes:a.taalOpcodes==null?null:a.taalOpcodes.map(function(a){return a}),web_session_id:b.web_session_id,version:"3",xFBDebug:a.xFBDebug};b=a.blameModule;var d=a.deferredSource;b!=null&&(c.blameModule=String(b));d&&d.stackFrames&&(c.deferredSource={stackFrames:qa(d.stackFrames)});a.metadata&&(c.metadata=a.metadata);a.loadingUrls&&(c.loadingUrls=a.loadingUrls);a.serverHash!=null&&(c.serverHash=a.serverHash);a.windowLocationURL!=null&&(c.windowLocationURL=a.windowLocationURL);a.loggingSource!=null&&(c.loggingSource=a.loggingSource);return c}function ta(a,b,c){var d;na++;if(b.sample_weight===0)return!1;var f=u.shouldLog(a);if(f==null)return!1;if((d=b.projectBlocklist)!==null&&d!==void 0&&e(d,"includes",!0,a.project))return!1;d=sa(a,b);e("Object","assign",!1,d,{ancestors:ma.slice(),clientWeight:oa(f),page_position:oa(na)});ma.length<15&&ma.push(a.hash);c(d);return!0}var ua={createErrorPayload:sa,postError:ta},va=null,wa=!1;function xa(a){if(va==null)return;var b=va,c=a.reason,d;if(c!=null&&typeof c==="object"&&(c.name==null||c.name===""||c.message==null||c.message===""))try{d=k("UnhandledRejection: %s",e("JSON","stringify",!1,c)),d.loggingSource="ONUNHANDLEDREJECTION"}catch(a){d=k("UnhandledRejection: (circular) %s",Object.keys(c).join(",")),d.loggingSource="ONUNHANDLEDREJECTION"}else d=w(c),d.loggingSource||(d.loggingSource="ONUNHANDLEDREJECTION");try{c=a.promise,d.stack=String(d.stack||"")+(c!=null&&typeof c.settledStack==="string"?"\n(<promise_settled_stack_below>)\n"+c.settledStack:"")+(c!=null&&typeof c.createdStack==="string"?"\n(<promise_created_stack_below>)\n"+c.createdStack:"")}catch(a){}b.reportError(d);a.preventDefault()}function ya(a){va=a,typeof b.addEventListener==="function"&&!wa&&(wa=!0,b.addEventListener("unhandledrejection",xa))}var za={onunhandledrejection:xa,setup:ya};d={preSetup:function(a){(a==null||a.ignoreOnError!==!0)&&A.setup(Z),(a==null||a.ignoreOnUnahndledRejection!==!0)&&za.setup(Z)},setup:function(a,b){Z.addListener(function(c){ua.postError(c,a,b)})}};var Aa=function(){function a(a){this.project=a,this.events=[],this.metadata=new F(),this.taalOpcodes=[]}var b=a.prototype;b.$1=function(c,d){var v=String(d),w=this.events,x=this.project,y=this.metadata,b=this.blameModule,z=this.forcedKey,A=this.error,B;for(var C=arguments.length,D=new Array(C>2?C-2:0),E=2;E<C;E++)D[E-2]=arguments[E];if(this.normalizedError){var F={message:this.normalizedError.messageFormat+" [Caught in: "+v+"]",params:[].concat(this.normalizedError.messageParams,D),forcedKey:z};B=f["extends"]({},this.normalizedError,{message:F.message,messageFormat:F.message,messageParams:I.toStringParams(F.params),project:x,type:c,loggingSource:"FBLOGGER"})}else if(A)this.taalOpcodes.length>0&&new a("fblogger").blameToPreviousFrame().blameToPreviousFrame().warn("Blame helpers do not work with catching"),I.aggregateError(A,{messageFormat:v,messageParams:I.toStringParams(D),errorName:A.name,forcedKey:z,project:x,type:c,loggingSource:"FBLOGGER"}),B=X.normalizeError(A);else{A=new Error(v);if(A.stack===void 0)try{throw A}catch(a){}A.messageFormat=v;A.messageParams=I.toStringParams(D);A.blameModule=b;A.forcedKey=z;A.project=x;A.type=c;A.loggingSource="FBLOGGER";A.taalOpcodes=[j.PREVIOUS_FRAME,j.PREVIOUS_FRAME].concat(this.taalOpcodes);B=X.normalizeError(A);B.name="FBLogger"}if(!y.isEmpty())if(B.metadata==null)B.metadata=y.format();else{var G=B.metadata.concat(y.format()),H=new Set(G);B.metadata=e("Array","from",!1,H.values())}if(w.length>0)if(B.events!=null){var J;(J=B.events).push.apply(J,w)}else B.events=w;Z.reportNormalizedError(B);return A};b.fatal=function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];this.$1.apply(this,["fatal",a].concat(c))};b.mustfix=function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];this.$1.apply(this,["error",a].concat(c))};b.warn=function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];this.$1.apply(this,["warn",a].concat(c))};b.info=function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];this.$1.apply(this,["info",a].concat(c))};b.debug=function(a){};b.mustfixThrow=function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];var e=this.$1.apply(this,["error",a].concat(c));e||(e=k("mustfixThrow does not support catchingNormalizedError"),e.taalOpcodes=e.taalOpcodes||[],e.taalOpcodes.push(j.PREVIOUS_FRAME));throw e};b.catching=function(b){b instanceof Error?this.error=b:new a("fblogger").blameToPreviousFrame().warn("Catching non-Error object is not supported");return this};b.catchingNormalizedError=function(a){this.normalizedError=a;return this};b.event=function(a){this.events.push(a);return this};b.blameToModule=function(a){this.blameModule=a;return this};b.blameToPreviousFile=function(){this.taalOpcodes.push(j.PREVIOUS_FILE);return this};b.blameToPreviousFrame=function(){this.taalOpcodes.push(j.PREVIOUS_FRAME);return this};b.blameToPreviousDirectory=function(){this.taalOpcodes.push(j.PREVIOUS_DIR);return this};b.addToCategoryKey=function(a){this.forcedKey=a;return this};b.addMetadata=function(a,b,c){this.metadata.addEntry(a,b,c);return this};return a}();g=function(a,b){var c=new Aa(a);return b!=null?c.event(a+"."+b):c};g.addGlobalMetadata=function(a,b,c){F.addGlobalMetadata(a,b,c)};i={blameToPreviousFile:function(a){var b;a.taalOpcodes=(b=a.taalOpcodes)!==null&&b!==void 0?b:[];a.taalOpcodes.push(j.PREVIOUS_FILE);return a},blameToPreviousFrame:function(a){var b;a.taalOpcodes=(b=a.taalOpcodes)!==null&&b!==void 0?b:[];a.taalOpcodes.push(j.PREVIOUS_FRAME);return a},blameToPreviousDirectory:function(a){var b;a.taalOpcodes=(b=a.taalOpcodes)!==null&&b!==void 0?b:[];a.taalOpcodes.push(j.PREVIOUS_DIR);return a}};M={err:k,ErrorBrowserConsole:l,ErrorDynamicData:m,ErrorFilter:u,ErrorGlobalEventHandler:A,ErrorGuard:ka,ErrorGuardState:C,ErrorMetadata:F,ErrorNormalizeUtils:X,ErrorPoster:ua,ErrorPubSub:Z,ErrorSerializer:I,ErrorSetup:d,ErrorXFBDebug:O,FBLogger:g,getErrorSafe:w,getSimpleHash:Q,TAAL:i,TAALOpcode:j};h.exports=M},null);c.__d("FBLogger",["fb-error"],function(a,b,c,d,e,f,g){"use strict";g["default"]=c("fb-error").FBLogger},98);c.__d("BaseDeserializePHPQueryData",[],function(a,b,c,d,e,f){"use strict";var g=/^([-_\w]+)((?:\[[-_\w]*\])+)=?(.*)/;function h(a){return a==="hasOwnProperty"||a==="__proto__"?"\ud83d\udf56":a}function a(b,c){if(b==null||b==="")return{};var d={};b=b.replace(/%5B/gi,"[").replace(/%5D/gi,"]");b=b.split("&");var e=Object.prototype.hasOwnProperty;for(var f=0,i=b.length;f<i;f++){var a=b[f].match(g);if(!a){var j=b[f].indexOf("=");if(j===-1)d[c(b[f])]=null;else{var k=b[f].substring(0,j);j=b[f].substring(j+1);d[c(k)]=c(j)}}else{k=a[2].split(/\]\[|\[|\]/).slice(0,-1);j=a[1];a=c(a[3]||"");k[0]=j;j=d;for(var l=0;l<k.length-1;l++){var m=h(k[l]);if(m){if(!e.call(j,m)){var n=k[l+1]&&!k[l+1].match(/^\d{1,3}$/)?{}:[];j[m]=n;if(j[m]!==n)return d}j=j[m]}else k[l+1]&&!k[l+1].match(/^\d{1,3}$/)?j.push({}):j.push([]),j=j[j.length-1]}j instanceof Array&&k[k.length-1]===""?j.push(a):j[h(k[k.length-1])]=a}}return d}f.deserialize=a},66);c.__d("flattenPHPQueryData",["invariant"],function(b,c,d,e,f,g,h,i){function b(b){return a(b,"",{})}function a(b,c,d){if(b==null)d[c]=void 0;else if(typeof b==="object"){typeof b.appendChild!=="function"||i(0,2616);for(var e in b)e!=="$$typeof"&&Object.prototype.hasOwnProperty.call(b,e)&&b[e]!==void 0&&a(b[e],c?c+"["+e+"]":e,d)}else d[c]=b;return d}h["default"]=b},98);c.__d("PHPQuerySerializer",["BaseDeserializePHPQueryData","flattenPHPQueryData"],function(b,c,d,e,f,g,h){function b(a){var b=[];a=d("flattenPHPQueryData")(a);for(var c in a)if(Object.prototype.hasOwnProperty.call(a,c)){var e=i(c);a[c]===void 0?b.push(e):b.push(e+"="+i(String(a[c])))}return b.join("&")}function i(a){return encodeURIComponent(a).replace(/%5D/g,"]").replace(/%5B/g,"[")}function c(b){return e("BaseDeserializePHPQueryData").deserialize(b,a)}function a(a){try{return decodeURIComponent(a.replace(/\+/g," "))}catch(b){return a}}f={decodeComponent:a,deserialize:c,encodeComponent:i,serialize:b};g.exports=f},34);c.__d("PHPStrictQuerySerializer",["PHPQuerySerializer","flattenPHPQueryData"],function(a,b,c,d,e,f,g){function a(a){var b=[];a=c("flattenPHPQueryData")(a);for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)){var e=h(d);a[d]===void 0?b.push(e):b.push(e+"="+h(String(a[d])))}return b.join("&")}function h(a){return encodeURIComponent(a)}g.serialize=a;g.encodeComponent=h;g.deserialize=d("PHPQuerySerializer").deserialize;g.decodeComponent=d("PHPQuerySerializer").decodeComponent},98);c.__d("URIRFC3986",[],function(a,b,c,d,e,f){var g=new RegExp("^([^:/?#]+:)?(//([^\\\\/?#@]*@)?(\\[[A-Fa-f0-9:.]+\\]|[^\\/?#:]*)(:[0-9]*)?)?([^?#]*)(\\?[^#]*)?(#.*)?");function a(a){if(a.trim()==="")return null;a=a.match(g);if(a==null)return null;var b=a[2]?a[2].substr(2):null,c=a[1]?a[1].substr(0,a[1].length-1):null;a={uri:a[0]?a[0]:null,scheme:c,authority:b,userinfo:a[3]?a[3].substr(0,a[3].length-1):null,host:a[2]?a[4]:null,port:a[5]?a[5].substr(1)?parseInt(a[5].substr(1),10):null:null,path:a[6]?a[6]:null,query:a[7]?a[7].substr(1):null,fragment:a[8]?a[8].substr(1):null,isGenericURI:b===null&&!!c};return a}f.parse=a},66);c.__d("createObjectFrom",[],function(a,b,c,d,e,f){function g(a,b){if(b===void 0)return g(a,!0);var c={};if(Array.isArray(b))for(var d=a.length-1;d>=0;d--)c[a[d]]=b[d];else for(d=a.length-1;d>=0;d--)c[a[d]]=b;return c}f["default"]=g},66);c.__d("URISchemes",["createObjectFrom"],function(a,b,c,d,e,f,g){"use strict";var h=c("createObjectFrom")(["apk","accountscenter","aidemos","aistudio","blob","cmms","fb","fba","fbatwork","fb-ama","fb-internal","fb-workchat","fb-workchat-secure","fb-messenger","fb-messenger-public","fb-messenger-group-thread","fb-page-messages","fb-pma","fbcf","fbconnect","fbinternal","fbmobilehome","fbrpc","file","flipper","ftp","gtalk","http","https","mailto","wss","ms-app","intent","itms","itms-apps","itms-services","lasso","market","svn+ssh","fbstaging","tel","sms","pebblejs","sftp","whatsapp","moments","flash","fblite","chrome-extension","webcal","instagram","iglite","fb124024574287414","fb124024574287414rc","fb124024574287414master","fb1576585912599779","fb929757330408142","designpack","fbpixelcloud","fbapi20130214","fb1196383223757595","tbauth","oculus","oculus.store","oculus.feed","oculusstore","odh","com.oculus.rd","aria","skype","ms-windows-store","callto","messenger","workchat","fb236786383180508","fb1775440806014337","data","fb-mk","munki","origami-file","fb-nimble-vrsrecorder","fb-nimble-monohandtrackingvis","together","togetherbl","horizonlauncher","venues","whatsapp-consumer","whatsapp-smb","fb-ide-opener","fb-vscode","fb-vscode-insiders","editor","spark-studio","arstudio","manifold","origami-internal","origami-public","stella","mwa","mattermost","logaggregator","workrooms","pcoip","cinema","home","oculus360photos","systemux"]);function a(a){return a==null||a===""?!0:Object.prototype.hasOwnProperty.call(h,a.toLowerCase())}g.isAllowed=a},98);c.__d("isSameOrigin",[],function(a,b,c,d,e,f){"use strict";function a(a,b){return!a.getProtocol()||!a.getDomain()||!b.getProtocol()||!b.getDomain()?!1:a.getOrigin()===b.getOrigin()}f["default"]=a},66);c.__d("setHostSubdomain",[],function(a,b,c,d,e,f){"use strict";function a(a,b){a=a.split(".");a.length<3?a.unshift(b):a[0]=b;return a.join(".")}f["default"]=a},66);c.__d("URIAbstractBase",["invariant","FBLogger","PHPStrictQuerySerializer","URIRFC3986","URISchemes","isSameOrigin","setHostSubdomain"],function(b,c,d,f,g,h,i){var j,a,k=new RegExp("[\\x00-\\x2c\\x2f\\x3b-\\x40\\x5c\\x5e\\x60\\x7b-\\x7f\\uFDD0-\\uFDEF\\uFFF0-\\uFFFF\\u2047\\u2048\\uFE56\\uFE5F\\uFF03\\uFF0F\\uFF1F]"),l=new RegExp("^(?:[^/]*:|[\\x00-\\x1f]*/[\\x00-\\x1f]*/)"),m=[];b=function(){"use strict";b.parse=function(d,f,m,h){if(!f)return!0;if(f instanceof b){d.setProtocol(f.getProtocol());d.setDomain(f.getDomain());d.setPort(f.getPort());d.setPath(f.getPath());d.setQueryData(h.deserialize(h.serialize(f.getQueryData())));d.setFragment(f.getFragment());d.setIsGeneric(f.getIsGeneric());d.setForceFragmentSeparator(f.getForceFragmentSeparator());d.setOriginalRawQuery(f.getOriginalRawQuery());d.setQueryParamModified(!1);return!0}f=f.toString().trim();var i=(j||(j=c("URIRFC3986"))).parse(f)||{fragment:null,scheme:null,query:null};if(!m&&!(a||(a=c("URISchemes"))).isAllowed(i.scheme))return!1;d.setProtocol(i.scheme||"");if(!m&&k.test(i.host||""))return!1;d.setDomain(i.host||"");d.setPort(i.port||"");d.setPath(i.path||"");if(m)d.setQueryData(h.deserialize(i.query||"")||{});else try{d.setQueryData(h.deserialize(i.query||"")||{})}catch(a){return!1}d.setFragment(i.fragment||"");i.fragment===""&&d.setForceFragmentSeparator(!0);d.setIsGeneric(i.isGenericURI||!1);d.setOriginalRawQuery(i.query);d.setQueryParamModified(!1);if(i.userinfo!==null){if(m)throw new Error("URI.parse: invalid URI (userinfo is not allowed in a URI): "+f);return!1}if(!d.getDomain()&&d.getPath().indexOf("\\")!==-1){if(m)throw new Error("URI.parse: invalid URI (no domain but multiple back-slashes): "+f);return!1}if(!d.getProtocol()&&l.test(f)){if(m)throw new Error("URI.parse: invalid URI (unsafe protocol-relative URLs): "+f+"'");return!1}if(d.getDomain()&&d.getPath()&&!e(d.getPath(),"startsWith",!0,"/")){if(m)throw new Error("URI.parse: invalid URI (domain and path where path lacks leading slash): "+f);return!1}d.getProtocol()&&!d.getIsGeneric()&&!d.getDomain()&&d.getPath()!==""&&c("FBLogger")("uri").warn('URI.parse: invalid URI (protocol "'+d.getProtocol()+'" with no domain)');return!0};b.tryParse=function(a,c){var d=new b(null,c);return b.parse(d,a,!1,c)?d:null};b.isValid=function(a,c){return!!b.tryParse(a,c)};function b(a,c){c||i(0,2966),this.$9=c,this.$7="",this.$1="",this.$6="",this.$5="",this.$3="",this.$4=!1,this.$8={},this.$2=!1,b.parse(this,a,!0,c),this.$11=!1}var d=b.prototype;d.setProtocol=function(b){(a||(a=c("URISchemes"))).isAllowed(b)||i(0,11793,b);this.$7=b;return this};d.getProtocol=function(){return(this.$7||"").toLowerCase()};d.setSecure=function(a){return this.setProtocol(a?"https":"http")};d.isSecure=function(){return this.getProtocol()==="https"};d.setDomain=function(a){if(k.test(a))throw new Error("URI.setDomain: unsafe domain specified: "+a+" for url "+this.toString());this.$1=a;return this};d.getDomain=function(){return this.$1};d.setPort=function(a){this.$6=a;return this};d.getPort=function(){return this.$6};d.setPath=function(a){this.$5=a;return this};d.getPath=function(){return this.$5};d.addQueryData=function(a,b){Object.prototype.toString.call(a)==="[object Object]"?e("Object","assign",!1,this.$8,a):this.$8[a]=b;this.$11=!0;return this};d.setQueryData=function(a){this.$8=a;this.$11=!0;return this};d.getQueryData=function(){return this.$8};d.setQueryString=function(a){return this.setQueryData(this.$9.deserialize(a))};d.getQueryString=function(a,b,c){a===void 0&&(a=!1);b===void 0&&(b=function(){return!1});c===void 0&&(c=null);return this.$12(!1,a,b,c)};d.$12=function(a,b,c,d){a===void 0&&(a=!1);b===void 0&&(b=!1);c===void 0&&(c=function(){return!1});d===void 0&&(d=null);return!this.$11&&(b||c(this.getDomain()))?(b=this.$10)!=null?b:"":(a&&d?d:this.$9).serialize(this.getQueryData())};d.removeQueryData=function(a){Array.isArray(a)||(a=[a]);for(var b=0,c=a.length;b<c;++b)delete this.$8[a[b]];this.$11=!0;return this};d.setFragment=function(a){this.$3=a;this.setForceFragmentSeparator(!1);return this};d.getFragment=function(){return this.$3};d.setForceFragmentSeparator=function(a){this.$2=a;return this};d.getForceFragmentSeparator=function(){return this.$2};d.setIsGeneric=function(a){this.$4=a;return this};d.getIsGeneric=function(){return this.$4};d.getOriginalRawQuery=function(){return this.$10};d.setOriginalRawQuery=function(a){this.$10=a;return this};d.setQueryParamModified=function(a){this.$11=a;return this};d.isEmpty=function(){return!(this.getPath()||this.getProtocol()||this.getDomain()||this.getPort()||Object.keys(this.getQueryData()).length>0||this.getFragment())};d.toString=function(a,b){a===void 0&&(a=function(){return!1});b===void 0&&(b=null);return this.$13(!1,!1,a,b)};d.toStringRawQuery=function(a,b){a===void 0&&(a=function(){return!1});b===void 0&&(b=null);return this.$13(!0,!1,a,b)};d.toStringPreserveQuery=function(a,b){a===void 0&&(a=function(){return!1});b===void 0&&(b=null);return this.$13(!1,!0,a,b)};d.toStringStrictQueryEncoding=function(a){a===void 0&&(a=function(){return!1});return this.$13(!0,!1,a,c("PHPStrictQuerySerializer"))};d.$13=function(a,b,c,d){a===void 0&&(a=!1);b===void 0&&(b=!1);c===void 0&&(c=function(){return!1});d===void 0&&(d=null);var e=this;for(var f=0;f<m.length;f++)e=m[f](e);return e.$14(a,b,c,d)};d.$14=function(a,b,c,d){a===void 0&&(a=!1);b===void 0&&(b=!1);c===void 0&&(c=function(){return!1});d===void 0&&(d=null);var e="",f=this.getProtocol();f&&(e+=f+":"+(this.getIsGeneric()?"":"//"));f=this.getDomain();f&&(e+=f);f=this.getPort();f&&(e+=":"+f);f=this.getPath();f?e+=f:e&&(e+="/");f=this.$12(a,b,c,d);f&&(e+="?"+f);a=this.getFragment();a?e+="#"+a:this.getForceFragmentSeparator()&&(e+="#");return e};b.registerFilter=function(a){m.push(a)};d.getOrigin=function(){var a=this.getPort();return this.getProtocol()+"://"+this.getDomain()+(a?":"+a:"")};d.isSameOrigin=function(a){return c("isSameOrigin")(this,a)};d.getQualifiedURIBase=function(){return new b(this,this.$9).qualify()};d.qualify=function(){if(!this.getDomain()){var a=new b(window.location.href,this.$9);this.setProtocol(a.getProtocol()).setDomain(a.getDomain()).setPort(a.getPort())}return this};d.setSubdomain=function(a){var b=this.qualify();b=b.getDomain();return this.setDomain(c("setHostSubdomain")(b,a))};d.getSubdomain=function(){if(!this.getDomain())return"";var a=this.getDomain().split(".");if(a.length<=2)return"";else return a[0]};d.isSubdomainOfDomain=function(a){var c=this.getDomain();return b.isDomainSubdomainOfDomain(c,a,this.$9)};b.isDomainSubdomainOfDomain=function(a,c,d){if(c===""||a==="")return!1;if(e(a,"endsWith",!0,c)){var f=a.length,j=c.length,k=f-j-1;if(f===j||a[k]==="."){f=new b(null,d);f.setDomain(c);return b.isValid(f,d)}}return!1};return b}();g.exports=b},null);c.__d("sdk.URI",["QueryString","URIAbstractBase"],function(b,c,d,e,g,h,i){var j=/\.facebook\.com$/,a={serialize:function(a){return a?d("QueryString").encode(a):""},deserialize:function(a){return a?d("QueryString").decode(a):{}}};b=function(b){f.inheritsLoose(c,b);function c(c){return b.call(this,c,a)||this}var e=c.prototype;e.isFacebookURI=function(){return j.test(this.getDomain())};e.valueOf=function(){return this.toString()};c.isValidURI=function(b){return d("URIAbstractBase").isValid(b,a)};return c}(d("URIAbstractBase"));i["default"]=b},98);c.__d("ApiClientUtils",["ArgumentError","Assert","Log","flattenObject","sdk.URI","sprintf"],function(a,b,c,d,e,f,g){var h={get:!0,post:!0,"delete":!0,put:!0};function a(a){var b=a.shift();c("Assert").isString(b,"Invalid path");!/^https?/.test(b)&&b.charAt(0)!=="/"&&(b="/"+b);var e,f={};try{e=new(c("sdk.URI"))(b)}catch(a){throw new(c("ArgumentError"))(a.message,a)}a.forEach(function(a){return f[typeof a]=a});b=(f.string||"get").toLowerCase();c("Assert").isTrue(Object.prototype.hasOwnProperty.call(h,b),c("sprintf")("Invalid method passed to ApiClient: %s",b));a=f["function"];a||d("Log").warn("No callback passed to the ApiClient");f.object&&e.addQueryData(c("flattenObject")(f.object));var g=e.getQueryData();g.method=b;return{uri:e,callback:a,params:g}}g.parseCallDataFromArgs=a},98);c.__d("errorCode",[],function(a,b,c,d,e,f){"use strict";function a(a){throw new Error('errorCode("'+a+'"): This should not happen. Oh noes!')}f["default"]=a},66);c.__d("nullthrows",[],function(a,b,c,d,e,f){function a(a,b){b===void 0&&(b="Got unexpected null or undefined");if(a!=null)return a;a=new Error(b);a.framesToPop=1;throw a}f["default"]=a},66);c.__d("sdk.safelyParseResponse",["errorCode","nullthrows"],function(b,c,d,f,g,h,i,j){"use strict";var a=function(a,b,c,d){return k};function b(b,c,f){c===void 0&&(c=null);f===void 0&&(f=null);try{return b===null?k:e("JSON","parse",!1,d("nullthrows")(b))}catch(d){return a(d,b,c,f)}}var k={error:{code:1,error_subcode:1357046,message:"Received Invalid JSON reply.",type:"http"}};b.ERROR=k;b.setErrorHandler=function(b){a=b};c=b;i["default"]=c},98);c.__d("whitelistObjectKeys",[],function(a,b,c,d,e,f){function a(a,b){var c={};b=Array.isArray(b)?b:Object.keys(b);for(var d=0;d<b.length;d++)typeof a[b[d]]!=="undefined"&&(c[b[d]]=a[b[d]]);return c}f["default"]=a},66);c.__d("ApiBatcher",["invariant","ApiClientUtils","QueryString","sdk.safelyParseResponse","whitelistObjectKeys"],function(b,c,d,e,f,g,h,i){"use strict";var a=50,j=105440539523;b=function(){function b(a,b){this.$1=[],this.$2=[],this.$4=null,this.executeRequest=a,this.$3=b}var c=b.prototype;c.scheduleBatchCall=function(){var c=this;for(var d=arguments.length,e=new Array(d),f=0;f<d;f++)e[f]=arguments[f];var g=b.prepareBatchParams(e),h=g.body,i=g.callback,j=g.method,k=g.relative_url,l={method:j,relative_url:k};h&&(l.body=h);this.$1.push(l);this.$2.push(i);this.$1.length==a?(this.$4&&window.clearTimeout(this.$4),this.$5()):this.$4||(this.$4=window.setTimeout(function(){c.$5()},0))};b.prepareBatchParams=function(b,c){c===void 0&&(c=[]);b=e("ApiClientUtils").parseCallDataFromArgs(b);var f=b.uri,j=b.callback;b=b.params.method;var h,i=f.removeQueryData("method").toString();if(b.toLowerCase()=="post"){var a=f.getQueryData();h=d("QueryString").encode(a);a=d("whitelistObjectKeys")(a,c);i=f.setQueryData(a).toString()}return{body:h,callback:j,method:b,relative_url:i}};c.$5=function(){this.$1.length>0||i(0,4698);this.$1.length===this.$2.length||i(0,4699);var a=this.$1,b=this.$2;this.$1=[];this.$2=[];this.$4=null;if(a.length===1){var c=a[0],e=b[0],f=c.body?d("QueryString").decode(c.body):null;this.executeRequest(c.relative_url,c.method,f,e);return}this.executeRequest("/","POST",{batch:a,include_headers:!1,batch_app_id:this.$3||j},function(a){Array.isArray(a)?a.forEach(function(a,c){b[c](d("sdk.safelyParseResponse")(a&&a.body))}):b.forEach(function(a){return a({error:{message:"Fatal: batch call failed."}})})})};return b}();h["default"]=b},98);c.__d("RequestConstants",["errorCode"],function(a,b,c,d,e,f,g,h){a={code:1,error_subcode:1357045,message:"unknown error (empty response)",type:"http",status:0},g.PARSE_ERROR_TEMPLATE=a},98);c.__d("sdk.Cookie",["QueryString","sdk.Runtime","sdk.Scribe","sdk.feature"],function(b,c,d,f,g,h,i){var j=null,a=["fblo_","fbsr_","fbm_"];function k(b,c,l,m){if(!e(a,"includes",!0,b)){f("sdk.Scribe").log("jssdk_error",{appId:d("sdk.Runtime").getClientID(),error:"unknown_cookie_prefix."+b});if(d("sdk.feature")("limit_unknown_cookie_setting",!1))return}b=b+d("sdk.Runtime").getClientID();m=m?"; SameSite=None;Secure":"";var n=j!==null&&j!==".";n&&(document.cookie=b+"=; expires=Wed, 04 Feb 2004 08:00:00 GMT"+m,document.cookie=b+"=; expires=Wed, 04 Feb 2004 08:00:00 GMT;domain="+location.hostname+m);var k=new Date(l).toUTCString();document.cookie=b+"="+c+(c&&l===0?"":"; expires="+k)+"; path=/"+(n?"; domain="+((b=j)!=null?b:""):"")+m}function l(a){a=a+d("sdk.Runtime").getClientID();a=new RegExp("\\b"+a+"=([^;]*)\\b");a=document.cookie.match(a);if(a==null)return null;else return a[1]}function b(a){j=a;a=d("QueryString").encode({base_domain:j!==null&&j!=="."?j:""});var b=new Date();b.setFullYear(b.getFullYear()+1);k("fbm_",a,b.getTime(),!0)}function c(){return j}function m(){var a=l("fbm_");if(a!=null&&j===null){a=d("QueryString").decode(a);j=a.base_domain;return{base_domain:j}}return null}function g(){return l("fbsr_")}function h(a,b){if(a==="")throw new Error("Value passed to Cookie.setSignedRequestCookie was empty.");k("fbsr_",a,b,!0)}function n(){m(),k("fbsr_","",0,!0)}i.setRaw=k;i.getRaw=l;i.setDomain=b;i.getDomain=c;i.loadMeta=m;i.loadSignedRequest=g;i.setSignedRequestCookie=h;i.clearSignedRequestCookie=n},98);c.__d("CORSRequest",["Log","QueryString","RequestConstants","sdk.Cookie","sdk.safelyParseResponse","wrapFunction"],function(c,d,g,h,i,j,k){function l(c,d,e){e===void 0&&(e={withCredentials:!1});if(!a.XMLHttpRequest)return null;var f=new XMLHttpRequest(),m=function(){};((e=e)==null?void 0:e.withCredentials)&&(f.withCredentials=!0);if("withCredentials"in f)f.open(c,d,!0),f.setRequestHeader("Content-type","application/x-www-form-urlencoded");else if(a.XDomainRequest){f=new XDomainRequest();try{f.open(c,d),f.onprogress=f.ontimeout=m}catch(a){return null}}else return null;var n={send:function(a){f.send(a)}},l=g("wrapFunction")(function(){l=m,"onload"in n&&n.onload(f)},"entry","XMLHttpRequest:load"),b=g("wrapFunction")(function(){b=m,"onerror"in n&&n.onerror(f)},"entry","XMLHttpRequest:error");f.onload=function(){l()};f.onerror=function(){b()};f.onreadystatechange=function(){f.readyState==4&&(f.status==200?l():b())};return n}var b="for (;;);",m=b.length;function n(a){a.substring(0,m)==b&&(a=a.substring(m));return a}function c(c,d,m,j,k){k===void 0&&(k={withCredentials:!1});if(e(c,"includes",!0,"/../")||e(c,"includes",!0,"/..\\")||e(c,"includes",!0,"\\../")||e(c,"includes",!0,"\\..\\")){h("Log").error("CORSRequest.execute(): path traversal is not allowed.");return!1}try{if(a.document){var b=h("sdk.Cookie").getRaw("cppo");b&&(c=g("QueryString").appendToUrl(c,g("QueryString").encode({__cppo:b})))}}catch(a){}m.suppress_http_code=1;b=g("QueryString").encode(m);d!="post"&&(c=g("QueryString").appendToUrl(c,b),b="");m=l(d,c,k);if(!m)return!1;m.onload=function(a){j(g("sdk.safelyParseResponse")(n(a.responseText),c,a.status))};m.onerror=function(a){a.responseText?j(g("sdk.safelyParseResponse")(n(a.responseText),c,a.status)):j({error:f["extends"]({},h("RequestConstants").PARSE_ERROR_TEMPLATE,{status:a.status})})};m.send(b);return!0}d={execute:c};i=d;k["default"]=i},98);c.__d("ApiClient",["ApiBatcher","ApiClientUtils","Assert","CORSRequest","Log","ObservableMixin","QueryString","UrlMap","flattenObject"],function(b,c,d,g,h,i,j){var k,a,l,m=[],n=!1,o=2e3,p={fql_query:!0,fql_multiquery:!0,friends_get:!0,notifications_get:!0,stream_get:!0,users_getinfo:!0},q=["cors"],r=0,s=[],t=0,u=0,v,w=g("Log");function x(b,c,e,w){var y=t!==0&&r>=t;if(y){s.push(function(){return x(b,c,e,w)});A.inform("request.queued",b,c,e);return}r++;var z=f["extends"]({},l,e);z.pretty=z.pretty||0;z=d("flattenObject")(z);y={cors:d("CORSRequest")};var a={},B=z.access_token||k;B&&(a.access_token=B);c!=="get"&&m.forEach(function(b){a[b]=z[b]});B=Object.keys(a);B.length>0&&(b=d("QueryString").appendToUrl(b,a),delete z.access_token);B=q;for(var p=0;p<B.length;p++){var u=y[B[p]],v=f["extends"]({},z);if(u.execute(b,c,v,w,{withCredentials:n}))return}w({error:{type:"no-transport",message:"Could not find a usable transport for request"}})}function y(a,b,c,d,e,f,l,m){if(d.transport&&d.transport==="chunked"&&m===!1){a(l,!1);return}l&&l.error&&A.inform("request.error",b,c,d,l,Date.now()-e,f);A.inform("request.complete",b,c,d,l,Date.now()-e,f);r--;a&&a(l);m=s.length>0&&r<t;m&&(b=s.shift(),b())}function z(){for(var b=arguments.length,c=new Array(b),d=0;d<b;d++)c[d]=arguments[d];var f=g("ApiClientUtils").parseCallDataFromArgs(c),n=f.uri,o=f.callback,p=f.params,a=p.method;B(n,a)&&(a="post");var q=n.getProtocol()&&n.getDomain()?n.setQueryData({}).toString():g("UrlMap").resolve("graph_domain")+n.getPath(),r=u++;"_fb_domain"in p&&A.setKeptQueryParams(["_fb_domain"]);A.inform("request.prepare",q,p,r);x(q,a=="get"?"get":"post",p,e(y,"bind",!0,null,o,n.getPath(),a,p,Date.now(),r))}function b(){var b;v||(v=new(d("ApiBatcher"))(z,a));(b=v).scheduleBatchCall.apply(b,arguments)}function c(b,c){d("Assert").isObject(b);d("Assert").isString(b.method,"method missing");c||w.warn("No callback passed to the ApiClient");var f=b.method.toLowerCase().replace(".","_");b.format="json-strings";b.api_key=a;f=f in p?"api_read":"api";f=g("UrlMap").resolve(f)+"/restserver.php";var j=u++;c=e(y,"bind",!0,null,c,"/restserver.php","get",b,Date.now(),j);x(f,"get",b,c)}function h(a){return d("ApiBatcher").prepareBatchParams(a,m)}var A=e("Object","assign",!1,new(d("ObservableMixin"))(),{setAccessToken:function(a){k&&a&&k!==a&&w.error("You are overriding current access token, that means some other app is expecting different access token and you will probably break things. Please consider passing access_token directly to API parameters instead of overriding the global settings."),k=a},setAccessTokenForClientID:function(b,c){k&&a&&a!==c?w.error("Not overriding access token since it was not initialized by your application."):k=b},setWithCredentials:function(a){n=a},getWithCredentials:function(){return n},getClientID:function(){return a},getAccessToken:function(){return k},setClientID:function(b){a&&a!==b&&w.warn("Warning: Two different applications have attempted to set the client ID. Overriding the previously set client ID."),a=b},setDefaultParams:function(a){l=a},getDefaultParams:function(){return l},setDefaultTransports:function(a){q=a},setLogger:function(a){w=a},setMaxConcurrentRequests:function(a){t=a},setKeptQueryParams:function(a){m=a},getCurrentlyExecutingRequestCount:function(){return r},getQueuedRequestCount:function(){return s.length},rest:c,graph:z,scheduleBatchCall:b,prepareBatchParams:h});function B(a,b){return a.toString().length>o&&b==="get"}i=A;j["default"]=i},98);c.__d("FBEventsParamList",[],function(b,c,d,f,g,h){"use strict";var i="deep",j="shallow";b=function(){function b(){this.list=[]}var c=b.prototype;c.append=function(a,b){this.$1(encodeURIComponent(a),b,i)};c.each=function(a){var b=this.list;for(var c=0,d=b.length;c<d;c++)a(b[c][0],b[c][1])};c.toQueryString=function(){var a=[];this.each(function(b,c){a.push(b+"="+encodeURIComponent(c))});return a.join("&")};c.$1=function(b,c,d){Object(c)!==c?this.$2(b,c):d===i?this.$3(b,c):this.$2(b,a(c))};c.$2=function(a,b){b!=null&&this.list.push([a,b])};c.$3=function(a,b){for(var c in b)if(Object.prototype.hasOwnProperty.call(b,c)){var d=a+"["+encodeURIComponent(c)+"]";this.$1(d,b[c],j)}};return b}();function a(a){if(typeof JSON==="undefined"||JSON===null||!e("JSON","stringify",!1))return Object.prototype.toString.call(a);else return e("JSON","stringify",!1,a)}h["default"]=b},66);c.__d("FBEventsUtils",[],function(a,b,c,d,e,f){"use strict";var g=!("addEventListener"in document);function a(a,b,c){var d=g?"on"+b:b;b=function b(){g?a.detachEvent&&a.detachEvent(d,b):a.removeEventListener(d,b,!1),c()};g?a.attachEvent&&a.attachEvent(d,b):a.addEventListener(d,b,!1)}f.listenOnce=a},66);c.__d("FBPixelEndpoint",["invariant","FBEventsParamList","FBEventsUtils"],function(b,c,d,e,f,g,h,i){"use strict";var a="https://www.facebook.com/tr/",j=location.href,k=window.top!==window,l=document.referrer;function m(a,b,c,e){e===void 0&&(e={});var f=new(d("FBEventsParamList"))();f.append("id",a);f.append("ev",b);f.append("dl",j);f.append("rl",l);f.append("if",k);f.append("ts",new Date().valueOf());f.append("cd",c);f.append("sw",window.screen.width);f.append("sh",window.screen.height);for(a in e)f.append(a,e[a]);return f}function b(b,c,d,e){b=m(b,c,d,e),c=b.toQueryString(),2048>(a+"?"+c).length?n(a,c):o(a,b)}function n(a,b){var c=new Image();c.src=a+"?"+b}function o(a,b){var c="fb"+Math.random().toString().replace(".",""),d=document.createElement("form");d.method="post";d.action=a;d.target=c;d.acceptCharset="utf-8";d.style.display="none";a=!!(window.attachEvent&&!window.addEventListener);a=a?'<iframe name="'+c+'">':"iframe";var f=document.createElement(a);f instanceof HTMLIFrameElement||i(0,20659);f.src="javascript:false";f.id=c;f.name=c;d.appendChild(f);e("FBEventsUtils").listenOnce(f,"load",function(){b.each(function(a,b){var c=document.createElement("input");c.name=a;c.value=b;d.appendChild(c)}),e("FBEventsUtils").listenOnce(f,"load",function(){var a;(a=d.parentNode)==null?void 0:a.removeChild(d)}),d.submit()});(a=document.body)==null?void 0:a.appendChild(d)}h.sendEvent=b},98);c.__d("FBAppEvents",["ApiClient","FBPixelEndpoint"],function(a,b,c,d,e,f,g){"use strict";function a(a,b,c,e,f){var g={};e!=null&&(g.vts=e.toString());f!=null&&(g.at=f);d("FBPixelEndpoint").sendEvent(a.toString(),b,c,g)}function b(a,b,d,e){b="/"+b+"/user_properties",a={data:[{user_unique_id:a,custom_data:d}]},c("ApiClient").graph(b,"post",a,e)}g.logEvent=a;g.updateUserProperties=b},98);c.__d("Miny",[],function(a,b,c,d,e,f){var g="Miny1",h="wxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_".split("");a={encode:function(a){if(/^$|[~\\]|__proto__/.test(a))return a;a=(a=a.match(/\w+|\W+/g))!=null?a:[];var b,c=Object.create(null);for(b=0;b<a.length;b++)c[a[b]]=(c[a[b]]||0)+1;var d=Object.keys(c);d.sort(function(a,b){return parseInt(c[b],10)-parseInt(c[a],10)});for(b=0;b<d.length;b++){var e=(b-b%32)/32;c[d[b]]=e?e.toString(32)+h[b%32]:h[b%32]}e="";for(b=0;b<a.length;b++)e+=c[a[b]];d.unshift(g,d.length);d.push(e);return d.join("~")}};b=a;f["default"]=b},66);c.__d("getBlankIframeSrc",["sdk.UA"],function(a,b,c,d,e,f,g){function a(){return c("sdk.UA").ie()<10?"javascript:false":"about:blank"}g["default"]=a},98);c.__d("insertIframe",["GlobalCallback","getBlankIframeSrc","guid"],function(a,b,c,d,e,f,g){function a(b){var e=b.id!=null?b.id:c("guid")(),f=b.name!=null?b.name:c("guid")(),h=!1,g=!1,i=function(){h&&!g&&(g=!0,typeof b.onload==="function"&&b.onload(b.root.firstChild))},a=d("GlobalCallback").create(i);if(document.attachEvent){var j='<iframe id="'+e+'" name="'+f+'"'+(b.title!=null?' title="'+b.title+'"':"")+(b.className!=null?' class="'+b.className+'"':"")+' style="border:none;'+(b.width!=null?"width:"+b.width+"px;":"")+(b.height!=null?"height:"+b.height+"px;":"")+'" src="'+c("getBlankIframeSrc")()+'" frameborder="0" scrolling="no" allowtransparency="true" onload="'+a+'()"></iframe>';b.root.innerHTML='<iframe src="'+c("getBlankIframeSrc")()+'" frameborder="0" scrolling="no" style="height:1px"></iframe>';h=!0;window.setTimeout(function(){b.root.innerHTML=j,b.root.firstChild.src=b.url,typeof b.onInsert==="function"&&b.onInsert(b.root.firstChild)},0)}else a=document.createElement("iframe"),a.id=e,a.name=f,a.onload=i,a.scrolling="no",a.style.border="none",a.style.overflow="hidden",b.title!=null&&(a.title=b.title),b.className!=null&&(a.className=b.className),b.height!==void 0&&(a.style.height=b.height+"px"),b.width!==void 0&&(b.width==="100%"?a.style.width=b.width:a.style.width=b.width+"px"),b.root.appendChild(a),h=!0,a.src=b.url,b.onInsert&&b.onInsert(a)}g["default"]=a},98);c.__d("sdk.Impressions",["Miny","QueryString","UrlMap","getBlankIframeSrc","guid","insertIframe","sdk.Content","sdk.Runtime"],function(b,c,d,g,h,i,j){function k(b,c){c===void 0&&(c=!1);var e=d("sdk.Runtime").getClientID(),f=d("sdk.Runtime").isEnvironment(d("sdk.Runtime").ENVIRONMENTS.CANVAS);e&&(typeof b.api_key!=="string"||b.api_key==="")&&(b.api_key=e);b.kid_directed_site=d("sdk.Runtime").getKidDirectedSite();e=g("UrlMap").resolve("www")+"/platform/impression.php/"+d("guid")()+"/";f&&(e=g("UrlMap").resolve("www")+"/platform/canvas_impression.php/"+d("guid")()+"/");var j=d("QueryString").appendToUrl(e,b);if(j.length>2e3&&b.payload&&typeof b.payload==="string"){var k=b.payload,m=d("Miny").encode(k);m&&m.length<k.length&&(b.payload=m,j=d("QueryString").appendToUrl(e,b))}window.fetch?a(e,j,b,c||f):l(e,j,b,c||f)}function a(a,b,c,d){d===void 0&&(d=!1);var e={mode:"no-cors",credentials:"include"};d||(e.credentials="omit");if(b.length<=2e3)window.fetch(b,e);else{d=new URLSearchParams();for(b in c)if(Object.prototype.hasOwnProperty.call(c,b)){var j=c[b];j!=null&&d.set(b,j)}j=f["extends"]({method:"POST",body:d},e);window.fetch(a,j)}}function l(a,b,c,e){e===void 0&&(e=!1);if(b.length<=2e3){var f=new Image();e||(f.crossOrigin="anonymous");f.src=b}else{if(!e)return;f=d("guid")();var l=g("sdk.Content").appendHidden(document.createElement("div"));d("insertIframe")({url:d("getBlankIframeSrc")(),root:l,name:f,className:"fb_hidden fb_invisible",onload:function(){l.parentNode!=null&&l.parentNode.removeChild(l)}});g("sdk.Content").submitToTarget({url:a,target:f,params:c})}}function b(a,b){(typeof b.source!=="string"||b.source==="")&&(b.source="jssdk"),k({lid:a,payload:e("JSON","stringify",!1,b)})}j.impression=k;j.log=b},98);c.__d("sdk.AppEvents",["AppUserPropertyAPIBuiltinField","Assert","FBAppEvents","sdk.Impressions","sdk.Model","sdk.Runtime"],function(b,c,d,g,h,i,j){var k=Object.freeze({COMPLETED_REGISTRATION:"fb_mobile_complete_registration",VIEWED_CONTENT:"fb_mobile_content_view",SEARCHED:"fb_mobile_search",RATED:"fb_mobile_rate",COMPLETED_TUTORIAL:"fb_mobile_tutorial_completion",ADDED_TO_CART:"fb_mobile_add_to_cart",ADDED_TO_WISHLIST:"fb_mobile_add_to_wishlist",INITIATED_CHECKOUT:"fb_mobile_initiated_checkout",ADDED_PAYMENT_INFO:"fb_mobile_add_payment_info",ACHIEVED_LEVEL:"fb_mobile_level_achieved",UNLOCKED_ACHIEVEMENT:"fb_mobile_achievement_unlocked",PAGE_VIEW:"fb_page_view",SPENT_CREDITS:"fb_mobile_spent_credits"}),a=Object.freeze({ACTIVATED_APP:"fb_mobile_activate_app",PURCHASED:"fb_mobile_purchase"}),l=Object.freeze({APP_USER_ID:"_app_user_id",APP_VERSION:"_appVersion",CURRENCY:"fb_currency",REGISTRATION_METHOD:"fb_registration_method",CONTENT_TYPE:"fb_content_type",CONTENT_ID:"fb_content_id",SEARCH_STRING:"fb_search_string",SUCCESS:"fb_success",MAX_RATING_VALUE:"fb_max_rating_value",PAYMENT_INFO_AVAILABLE:"fb_payment_info_available",NUM_ITEMS:"fb_num_items",LEVEL:"fb_level",DESCRIPTION:"fb_description"}),m=/^[0-9a-zA-Z_][0-9a-zA-Z _-]{0,39}$/,n=40,o=m,p=n,q=100,r=100,s=100,t=100,u=e("Object","values",!1,d("AppUserPropertyAPIBuiltinField")),v=new(d("sdk.Model"))({UserID:"",Version:""});function w(a,b,c){var e=F();H(a);var f=v.getUserID();f!==""&&(c=c||{},c[l.APP_USER_ID]=f);f=v.getVersion();f!==""&&(c=c||{},c[l.APP_VERSION]=f);d("sdk.Runtime").isCanvasEnvironment()?D(e,a,b,c):E(e,a,b,c)}function b(b,c,d){var e={};e[l.CURRENCY]=c;w(a.PURCHASED,b,f["extends"]({},d,e))}function c(){w(a.ACTIVATED_APP)}function h(){w(k.PAGE_VIEW)}function i(a){J(a),v.setUserID(a)}function x(){return v.getUserID()}function y(){v.setUserID("")}function z(a){I(a),v.setVersion(a)}function A(){return v.getVersion()}function B(){v.setVersion("")}function C(a,b){var c=F(),d=x();J(d);Object.keys(a).forEach(function(b){return a[b]==null&&delete a[b]});G(a);g("FBAppEvents").updateUserProperties(d,c,a,b)}function D(a,b,c,d){b={ae:1,ev:b,vts:c,canvas:1},d&&(b.cd=d),g("sdk.Impressions").impression({api_key:a,payload:e("JSON","stringify",!1,b)})}function E(a,b,c,e){var f=d("sdk.Runtime").getAccessToken();g("FBAppEvents").logEvent(a,b,e||{},c,f)}function F(){var a=d("sdk.Runtime").getClientID();d("Assert").isTrue(a!==null&&a.length>0,"You need to call FB.init() with App ID first.");return a}function G(a){d("Assert").isTrue(Object.keys(a).length<=s,"The total number of user properties cannot exceed "+s+".");for(var b in a)d("Assert").isTrue(o.test(b)||e(u,"includes",!0,b),"Invalid user properties key name: "+b+". It must be between 1 and "+p+" characters, and must contain only alphanumerics, _, - or spaces, starting with alphanumeric or _. Or, it must be a pre-defined user property"),d("Assert").isTrue(a[b].toString().length<=t,"Invalid user properties value: "+a[b]+". It must be no longer than "+t+" characters.")}function H(a){d("Assert").isTrue(m.test(a),"Invalid event name: "+a+". It must be between 1 and "+n+" characters, and must be contain only alphanumerics, _, - or spaces, starting with alphanumeric or _.")}function I(a){d("Assert").isTrue(a.length<=r,"Invalid app version: "+a+". It must be no longer than "+r+" characters.")}function J(a){d("Assert").isTrue(a.length!==0,"User ID must be set before updateUserProperties can be called."),d("Assert").isTrue(a.length<=q,"Invalid user ID: "+a+". It must be no longer than "+q+" characters.")}b=Object.freeze({logEvent:w,logPurchase:b,activateApp:c,logPageView:h,setUserID:i,getUserID:x,clearUserID:y,updateUserProperties:C,setAppVersion:z,getAppVersion:A,clearAppVersion:B,EventNames:k,ParameterNames:l});j.assertGetValidAppID=F;j.assertValidUserProperties=G;j.assertValidEventName=H;j.assertValidAppVersion=I;j.assertValidUserID=J;j.AppEvents=b},98);c.__d("sdk.Event",[],function(b,c,d,e,f,g){"use strict";var h="event.subscribe",i="event.unsubscribe",a;function j(){a||(a={});return a}function k(a,b){var c=j();c[a]?c[a].indexOf(b)==-1&&c[a].push(b):c[a]=[b];a!=h&&a!=i&&m(h,a,c[a])}function l(a,b){var c=j()[a];c&&c.forEach(function(a,d){a===b&&c.splice(d,1)});a!=h&&a!=i&&m(i,a,c)}function b(a,b){var c=arguments;if(!b()){var d=function d(){b.apply(b,c)&&l(a,d)};k(a,d)}}function c(a){delete j()[a]}function m(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];var e=j()[a];e&&e.forEach(function(a){a&&a.apply(this,c)})}g.SUBSCRIBE=h;g.UNSUBSCRIBE=i;g.subscribers=j;g.subscribe=k;g.unsubscribe=l;g.monitor=b;g.clear=c;g.fire=m},66);c.__d("sdk.AppEvents-public",["Assert","FB","sdk.AppEvents","sdk.Event","sdk.Runtime"],function(a,b,c,d,e,f,g){function a(){d("sdk.Event").subscribe("init:post",function(a){c("sdk.Runtime").getClientID()&&(a.autoLogAppEvents!==void 0&&(c("Assert").isBoolean(a.autoLogAppEvents,"Type of property autoLogAppEvents must be boolean"),c("sdk.Runtime").setAutoLogAppEvents(a.autoLogAppEvents)),c("sdk.Runtime").getAutoLogAppEvents()&&d("sdk.AppEvents").AppEvents.logPageView())}),c("FB").provide("AppEvents",d("sdk.AppEvents").AppEvents)}b={init:a};e=b;g["default"]=e},98);c.__d("sdk.AuthState",["sdk.AuthUtils"],function(b,c,d,f,g,h,i){"use strict";var j=a();function a(){var a={igAuthResponse:null,fbAuthResponse:null,fbLoginStatus:null,igLoginStatus:null};return{currentAuthResponse:null,shouldSecondLoginRequestTimeOut:!1,mixedAuthState:a,loadState:null,timer:null,currentTimeOut:f("sdk.AuthUtils").AuthConstants.CONNECTED_REVALIDATE_PERIOD}}function b(){return e("Object","assign",!1,a(),j)}function c(b){j=e("Object","assign",!1,a(),j,b)}d={getState:b,setState:c};g=d;i["default"]=g},98);c.__d("sdk.Observable",[],function(a,b,c,d,e,f){a=function(){var a=this;this.getSubscribers=function(b){return a.$1[b]||(a.$1[b]=[])};this.clearSubscribers=function(b){b&&(a.$1[b]=[])};this.subscribe=function(b,c){b=a.getSubscribers(b),b.push(c)};this.unsubscribe=function(b,c){b=a.getSubscribers(b);for(var d=0;d<b.length;d++)if(b[d]===c){b.splice(d,1);break}};this.inform=function(b,c){b=a.getSubscribers(b);for(var d=0;d<b.length;d++){if(b[d]===null)continue;try{b[d].call(a,c)}catch(a){window.setTimeout(function(){throw a},0)}}};this.$1={}},f.Observable=a},66);c.__d("sdk.AuthUtils",["sdk.AuthState","sdk.Cookie","sdk.Observable","sdk.Runtime"],function(b,c,d,e,f,g,h){"use strict";var i=365*24*60*60*1e3;function b(a){return a!=null&&a.graphDomain!=null?a.graphDomain==="instagram":!1}function c(a){d("sdk.Runtime").getUseCookie()&&e("sdk.Cookie").getDomain()==null&&e("sdk.Cookie").setDomain("."+a)}function f(){var a={fbAuthResponse:null,fbLoginStatus:null,igAuthResponse:null,igLoginStatus:null},b=!1;d("sdk.AuthState").setState({mixedAuthState:a,shouldSecondLoginRequestTimeOut:b})}function g(a){a!=null?(d("sdk.Runtime").setGraphDomain(a),a=="instagram"&&d("sdk.Runtime").setIsVersioned(!1)):d("sdk.Runtime").setGraphDomain("")}function a(){e("sdk.Cookie").setRaw(s.LOGOUT_COOKIE_PREFIX,"y",Date.now()+i,!1)}function j(a){a===void 0&&(a=s.CONNECTED_REVALIDATE_PERIOD);var b=d("sdk.AuthState").getState().timer;b&&window.clearTimeout(b);b=window.setTimeout(function(){r.inform(s.REVALIDATE_TIMER_TIMEOUT)},a);d("sdk.AuthState").setState({timer:b});d("sdk.AuthState").setState({currentTimeOut:a})}function k(){e("sdk.Cookie").setRaw(s.LOGOUT_COOKIE_PREFIX,"",0,!1),e("sdk.Cookie").setRaw(s.LOGOUT_COOKIE_PREFIX,"",0,!0)}var l=new(e("sdk.Observable").Observable)();function m(a,b){l.inform(a,b)}function n(a,b){l.subscribe(a,b)}function o(a){l.clearSubscribers(a)}function p(a,b){l.unsubscribe(a,b)}function q(a){return l.getSubscribers(a)}var r={inform:m,subscribe:n,clearSubscribers:o,unsubscribe:p,getSubscribers:q},s={LOCAL_STORAGE_TOKEN_PREFIX:"fblst_",IG_LOCAL_STORAGE_TOKEN_PREFIX:"iglst_",SESSION_STORAGE_LOGIN_STATUS_PREFIX:"fbssls_",CONNECTED_REVALIDATE_PERIOD:60*90*1e3,DEFAULT_REVALIDATE_PERIOD:60*60*24*1e3,LOGOUT_COOKIE_PREFIX:"fblo_",CORS_FETCH_COMPLETED_EVENT:"cors_fetch_completed",XFOA_FINAL_RESPONSE_EVENT:"xfoa_final_response",LOAD_XFOA_SUBSCRIBERS:"load_xfoa_subscribers",REVALIDATE_TIMER_TIMEOUT:"revalidate_timer_timeout"};h.isInstagramLogin=b;h.setBaseDomain=c;h.resetFBAndIGLoginStatus=f;h.setGraphDomain=g;h.setLogoutState=a;h.setRevalidateTimer=j;h.removeLogoutState=k;h.AuthInternalEvent=r;h.AuthConstants=s},98);c.__d("sdk.WebStorage",["Log"],function(a,b,c,d,e,f,g){"use strict";function a(){try{return window.localStorage}catch(a){d("Log").warn("Failed to get local storage")}return null}function b(){try{var a=window.localStorage;if(a){var b="__test__"+Date.now();a.setItem(b,"");a.removeItem(b)}return a}catch(a){d("Log").warn("Failed to get local storage")}return null}function c(){try{return window.sessionStorage}catch(a){d("Log").warn("Failed to get session storage")}return null}function e(){try{var a=window.sessionStorage;if(a){var b="__test__"+Date.now();a.setItem(b,"");a.removeItem(b)}return a}catch(a){d("Log").warn("Failed to get session storage")}return null}g.getLocalStorage=a;g.getLocalStorageForRead=b;g.getSessionStorage=c;g.getSessionStorageForRead=e},98);c.__d("sdk.AuthStorageUtils",["sdk.AuthUtils","sdk.Runtime","sdk.WebStorage","sdk.feature"],function(b,c,d,f,g,h,i){"use strict";function b(a,b){if(j()&&b!=null&&b!==""){var c=f("sdk.WebStorage").getLocalStorage();c&&(a=f("sdk.AuthUtils").isInstagramLogin(a)?f("sdk.AuthUtils").AuthConstants.IG_LOCAL_STORAGE_TOKEN_PREFIX:f("sdk.AuthUtils").AuthConstants.LOCAL_STORAGE_TOKEN_PREFIX,c.setItem(a+d("sdk.Runtime").getClientID(),b))}}function c(a){var b=f("sdk.WebStorage").getLocalStorage();b&&(a==="instagram"?b.removeItem(f("sdk.AuthUtils").AuthConstants.IG_LOCAL_STORAGE_TOKEN_PREFIX+d("sdk.Runtime").getClientID()):b.removeItem(f("sdk.AuthUtils").AuthConstants.LOCAL_STORAGE_TOKEN_PREFIX+d("sdk.Runtime").getClientID()))}function g(a,b){if(!j())return;var c=f("sdk.WebStorage").getSessionStorage();c&&c.setItem(f("sdk.AuthUtils").AuthConstants.SESSION_STORAGE_LOGIN_STATUS_PREFIX+d("sdk.Runtime").getClientID(),e("JSON","stringify",!1,{authResponse:a,status:b,expiresAt:a!=null&&a.expiresIn&&a.expiresIn!==0?Date.now()+Math.min(a.expiresIn*.75*1e3,f("sdk.AuthUtils").AuthConstants.CONNECTED_REVALIDATE_PERIOD):Date.now()+f("sdk.AuthUtils").AuthConstants.DEFAULT_REVALIDATE_PERIOD}))}function j(){return d("sdk.feature")("cache_auth_response",!1)&&d("sdk.Runtime").getUseLocalStorage()&&location.protocol==="https:"}function h(){var a=null,b=null;if(d("sdk.Runtime").getUseLocalStorage()){var c=f("sdk.WebStorage").getLocalStorageForRead();c&&(a=c.getItem(f("sdk.AuthUtils").AuthConstants.LOCAL_STORAGE_TOKEN_PREFIX+d("sdk.Runtime").getClientID()),b=c.getItem(f("sdk.AuthUtils").AuthConstants.IG_LOCAL_STORAGE_TOKEN_PREFIX+d("sdk.Runtime").getClientID()))}return{fbToken:a,igToken:b}}function a(){if(!j())return null;var a=f("sdk.WebStorage").getSessionStorageForRead();if(a){a=a.getItem(f("sdk.AuthUtils").AuthConstants.SESSION_STORAGE_LOGIN_STATUS_PREFIX+d("sdk.Runtime").getClientID());if(a!=null)try{a=e("JSON","parse",!1,a);if(a!=null&&a.expiresAt!=null&&a.expiresAt>Date.now())return a}catch(a){return null}}return null}i.setLocalStorageToken=b;i.removeLocalStorageToken=c;i.setSessionStorage=g;i.getLocalStorageTokens=h;i.getCachedResponse=a},98);c.__d("Base64",[],function(b,c,d,f,g,h){var i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function j(a){a=a.charCodeAt(0)<<16|a.charCodeAt(1)<<8|a.charCodeAt(2);return String.fromCharCode(i.charCodeAt(a>>>18),i.charCodeAt(a>>>12&63),i.charCodeAt(a>>>6&63),i.charCodeAt(a&63))}var a=">___?456789:;<=_______\0\x01\x02\x03\x04\x05\x06\x07\b\t\n\v\f\r\x0e\x0f\x10\x11\x12\x13\x14\x15\x16\x17\x18\x19______\x1a\x1b\x1c\x1d\x1e\x1f !\"#$%&'()*+,-./0123";function k(b){b=a.charCodeAt(b.charCodeAt(0)-43)<<18|a.charCodeAt(b.charCodeAt(1)-43)<<12|a.charCodeAt(b.charCodeAt(2)-43)<<6|a.charCodeAt(b.charCodeAt(3)-43);return String.fromCharCode(b>>>16,b>>>8&255,b&255)}var l={encode:function(a){a=unescape(encodeURI(a));var b=(a.length+2)%3;a=(a+"\0\0".slice(b)).replace(/[\s\S]{3}/g,j);return a.slice(0,a.length+b-2)+"==".slice(b)},decode:function(a){a=a.replace(/[^A-Za-z0-9+\/]/g,"");var b=a.length+3&3;a=(a+"AAA".slice(b)).replace(/..../g,k);a=a.slice(0,a.length+b-3);try{return decodeURIComponent(escape(a))}catch(a){throw new Error("Not valid UTF-8")}},encodeObject:function(a){return l.encode(e("JSON","stringify",!1,a))},decodeObject:function(a){return e("JSON","parse",!1,l.decode(a))},encodeNums:function(a){return String.fromCharCode.apply(String,a.map(function(a){return i.charCodeAt((a|-(a>63?1:0))&-(a>0?1:0)&63)}))}};b=l;h["default"]=b},66);c.__d("sdk.SignedRequest",["Base64"],function(a,b,c,d,e,f,g){function a(a){if(a==null||a==="")return null;a=a.split(".",2)[1].replace(/\-/g,"+").replace(/\_/g,"/");return c("Base64").decodeObject(a)}g.parse=a},98);c.__d("sdk.getContextType",["sdk.Runtime","sdk.UA"],function(a,b,c,d,e,f,g){function a(){if(c("sdk.UA").nativeApp())return 3;return c("sdk.UA").mobile()?2:c("sdk.Runtime").isEnvironment(c("sdk.Runtime").ENVIRONMENTS.CANVAS)?5:1}g["default"]=a},98);c.__d("sdk.statusCORS",["Log","UrlMap","sdk.AuthState","sdk.AuthStorageUtils","sdk.AuthUtils","sdk.Impressions","sdk.Runtime","sdk.Scribe","sdk.URI","sdk.feature","sdk.getContextType"],function(b,c,d,f,g,h,i){"use strict";var j=6e4,a=114;function b(b,c,g,h){h===void 0&&(h="facebook");var i=n(c),m=Date.now();function q(){var c=new XMLHttpRequest();c&&(c.open("GET",i.toString(),!0),c.withCredentials=!0,c.onreadystatechange=function(){if(c.readyState===4){if(d("sdk.feature")("e2e_ping_tracking",!0)){var j={init:m,close:Date.now(),method:"cors"};f("Log").debug("e2e: %s",e("JSON","stringify",!1,j));f("sdk.Impressions").log(a,{payload:j})}c.status===200?k(b,(j=c.getResponseHeader("fb-s"))!=null?j:"unknown",(j=c.getResponseHeader("fb-ar"))!=null?j:"{}",h):l(b,c.status,g,h)}},c.send())}function r(){o(h)||window.setTimeout(function(){p(h==="facebook"?"instagram":"facebook"),d("sdk.AuthState").setState({shouldSecondLoginRequestTimeOut:!0})},j)}function s(){window.fetch(i.toString(),{referrer:"/",mode:"cors",credentials:"include"}).then(function(a){if(d("sdk.AuthState").getState().shouldSecondLoginRequestTimeOut){d("sdk.AuthState").setState({shouldSecondLoginRequestTimeOut:!1});return}r();if(a.status===200){var c;k(b,(c=a.headers.get("fb-s"))!=null?c:"unknown",(c=a.headers.get("fb-ar"))!=null?c:"{}",h)}else l(b,a.status,g,h)})["catch"](function(a){if(d("sdk.AuthState").getState().shouldSecondLoginRequestTimeOut){d("sdk.AuthState").setState({shouldSecondLoginRequestTimeOut:!1});return}r();l(b,0,g,h)})}typeof window.fetch==="function"?s():q()}function k(a,b,c,j){j===void 0&&(j="facebook");switch(b){case"connected":c=e("JSON","parse",!1,c);var k={accessToken:c.access_token,userID:c.user_id,expiresIn:Number(c.expires_in),signedRequest:c.signed_request,graphDomain:c.graph_domain};c.enforce_https!=null&&d("sdk.Runtime").setEnforceHttps(!0);c.data_access_expiration_time!=null&&(k.data_access_expiration_time=Number(c.data_access_expiration_time));c.base_domain!=null&&f("sdk.AuthUtils").setBaseDomain(c.base_domain);f("sdk.AuthUtils").setGraphDomain(c.graph_domain);f("sdk.AuthStorageUtils").setLocalStorageToken(k,c.long_lived_token);f("sdk.AuthUtils").removeLogoutState();c={authResponse:k,status:b,loginSource:j,cb:a};f("sdk.AuthUtils").AuthInternalEvent.inform(f("sdk.AuthUtils").AuthConstants.CORS_FETCH_COMPLETED_EVENT,c);break;case"not_authorized":case"unknown":default:k={authResponse:null,status:b,loginSource:j,cb:a},f("sdk.AuthUtils").AuthInternalEvent.inform(f("sdk.AuthUtils").AuthConstants.CORS_FETCH_COMPLETED_EVENT,k)}}function l(a,b,c,e){e===void 0&&(e="facebook"),b===0?(d("sdk.feature")("cors_status_fetch_cancel_tracking",!1)&&f("sdk.Scribe").log("jssdk_error",{appId:d("sdk.Runtime").getClientID(),error:"CORS_STATUS_FETCH_CANCELLED",extra:{message:"Status 0 returned."}}),f("Log").error("Error retrieving login status, fetch cancelled.")):(f("sdk.Scribe").log("jssdk_error",{appId:d("sdk.Runtime").getClientID(),error:"CORS_STATUS_FETCH",extra:{message:"HTTP Status Code "+b}}),f("Log").error("Error retrieving login status, HTTP status code: "+b)),b=m(),b&&b===e?(b={authResponse:c,status:d("sdk.Runtime").getLoginStatus(),loginSource:e,cb:a,shouldSetAuthResponse:!1},f("sdk.AuthUtils").AuthInternalEvent.inform(f("sdk.AuthUtils").AuthConstants.CORS_FETCH_COMPLETED_EVENT,b)):(c={authResponse:null,status:"unknown",loginSource:e,cb:a,shouldSetAuthResponse:!1},f("sdk.AuthUtils").AuthInternalEvent.inform(f("sdk.AuthUtils").AuthConstants.CORS_FETCH_COMPLETED_EVENT,c))}function m(){var a=d("sdk.AuthState").getState().currentAuthResponse;return a?f("sdk.AuthUtils").isInstagramLogin(a)?"instagram":"facebook":null}function n(a){a=new(d("sdk.URI"))(f("UrlMap").resolve("www").replace("web.","www.")+"/x/oauth/status").addQueryData("client_id",d("sdk.Runtime").getClientID()).addQueryData("input_token",a).addQueryData("redirect_uri",window.location.href).addQueryData("origin",d("sdk.getContextType")()).addQueryData("sdk","joey").addQueryData("wants_cookie_data",d("sdk.Runtime").getUseCookie());if(window.location.ancestorOrigins){var b=window.location.ancestorOrigins;if(b.length>0){var c="";for(var e=0;e<b.length;e++)c+=b[e],c+=",";a.addQueryData("ancestor_origins",c.slice(0,-1))}}return a}function o(a){var b=d("sdk.AuthState").getState().mixedAuthState;switch(a){case"facebook":return(b==null?void 0:b.fbLoginStatus)===null&&(b==null?void 0:b.igLoginStatus)!==null;case"instagram":return(b==null?void 0:b.igLoginStatus)===null&&(b==null?void 0:b.fbLoginStatus)!==null;default:return!1}}function p(a){var b=d("sdk.AuthState").getState().mixedAuthState;if((b==null?void 0:b.fbLoginStatus)!=null&&(b==null?void 0:b.igLoginStatus)!=null)return;b={authResponse:null,status:"unknown",loginSource:a};f("sdk.AuthUtils").AuthInternalEvent.inform("xFoAFetchCompleted",b)}c={getLoginStatusCORS:b};g=c;i["default"]=g},98);c.__d("sdk.Auth.LoginStatus",["Log","QueryString","sdk.Auth","sdk.AuthState","sdk.AuthStorageUtils","sdk.AuthUtils","sdk.Cookie","sdk.Runtime","sdk.Scribe","sdk.SignedRequest","sdk.feature","sdk.statusCORS"],function(b,c,d,f,g,h,i){"use strict";var j=/^https?:\/\/([\w\.]+)?\.facebook\.com\/?/;d("sdk.Runtime").subscribe("AccessToken.change",function(a){!a&&d("sdk.Runtime").getLoginStatus()==="connected"&&m(null,!0)});function b(a){a.legacyStatusInit?o.getLoginStatus(function(a){a!=null&&a.status==="connected"&&f("sdk.Scribe").log("jssdk_error",{appId:d("sdk.Runtime").getClientID(),error:"legacy_status_init_success"})}):a.status&&o.getLoginStatus();if(d("sdk.Runtime").getClientID()&&d("sdk.Runtime").getUseCookie()){d("sdk.feature")("log_cookies_usage",!1)&&f("sdk.Scribe").log("jssdk_error",{appId:d("sdk.Runtime").getClientID(),error:"jssdk_cookie_toggled_on"});a=f("sdk.Cookie").loadSignedRequest();var b;if(a){try{b=f("sdk.SignedRequest").parse(a)}catch(a){f("sdk.Cookie").clearSignedRequestCookie()}b!=null&&b.user_id!=null&&d("sdk.Runtime").setCookieUserID(b.user_id)}}}function a(a){window.location.protocol!=="https:"&&n(a);var b=d("sdk.AuthState").getState().timer;b&&(window.clearTimeout(b),d("sdk.AuthState").setState({timer:null}));f("sdk.AuthUtils").resetFBAndIGLoginStatus();b=f("sdk.Cookie").getRaw(f("sdk.AuthUtils").AuthConstants.LOGOUT_COOKIE_PREFIX)==="y";var c=l(a),i=c.access_token;c=c.redirect_cancelled;if(b||c){n(a);return}b=f("sdk.AuthStorageUtils").getLocalStorageTokens();c=b.fbToken;b=b.igToken;i!=null&&(e(i,"startsWith",!0,"IG")?b=i:c=i);d("sdk.Runtime").getShouldLoadFamilyLogin()&&d("sdk.feature")("should_enable_ig_login_status_fetch",!1)?d("sdk.Runtime").getFamilyLoginLoaded()?f("sdk.AuthUtils").AuthInternalEvent.inform(f("sdk.AuthUtils").AuthConstants.LOAD_XFOA_SUBSCRIBERS):d("sdk.Runtime").subscribe("FamilyLoginLoaded.change",function(a){a&&f("sdk.AuthUtils").AuthInternalEvent.inform(f("sdk.AuthUtils").AuthConstants.LOAD_XFOA_SUBSCRIBERS)}):f("sdk.AuthUtils").AuthInternalEvent.subscribe(f("sdk.AuthUtils").AuthConstants.CORS_FETCH_COMPLETED_EVENT,d("sdk.Auth").setFinalAuthResponse);k(c,b,a)}function k(a,b,c){d("sdk.statusCORS").getLoginStatusCORS(c,a,d("sdk.AuthState").getState().currentAuthResponse,"facebook"),d("sdk.Runtime").getShouldLoadFamilyLogin()&&d("sdk.feature")("should_enable_ig_login_status_fetch",!1)&&(b!=null?d("sdk.statusCORS").getLoginStatusCORS(c,b,d("sdk.AuthState").getState().currentAuthResponse,"instagram"):(a={authResponse:null,status:"unknown",loginSource:"instagram",cb:c},f("sdk.AuthUtils").AuthInternalEvent.inform(f("sdk.AuthUtils").AuthConstants.CORS_FETCH_COMPLETED_EVENT,a)))}function l(b){var c=null,e=!1;if(d("sdk.Runtime").getLoginStatus()!=="connected"&&(document.referrer===""||j.test(document.referrer))){var l=location.hash.substr(1);if(l!==""){var m=d("QueryString").decode(l,!0);c=m.access_token;var a=m.signed_request;c!=null&&f("sdk.AuthUtils").removeLogoutState();if(window==top&&c!=null){var n=b;b=function(e){var b;e!=null&&e.status==="connected"&&((b=e.authResponse)==null?void 0:b.accessToken)===c&&(delete m.access_token,delete m.code,delete m.signed_request,location.hash=d("QueryString").encode(m),a!=null&&e.authResponse!=null&&(e.authResponse.signedRequest=a));n!=null&&n(e)}}}l=d("QueryString").decode(location.search);l.error==="access_denied"&&(e=!0)}return{access_token:c,redirect_cancelled:e}}function m(b,c){c===void 0&&(c=!1);var e=d("sdk.Runtime").getClientID();if(e==null||e===""){f("Log").warn("FB.getLoginStatus() called before calling FB.init().");n(b);return}if(!(typeof e==="number"||typeof e==="string")||e===0||typeof e==="string"&&(e==="0"||!/^\d+$/.test(e))){f("Log").warn("FB.getLoginStatus() not checked for an invalid client ID "+e);n(b);return}e=d("sdk.Runtime").getLoginStatus()!=="connected"&&j.test(document.referrer)&&location.hash.indexOf("cb=")>-1;if(!e&&!c){e=f("sdk.AuthStorageUtils").getCachedResponse();if(e!=null){var i;d("sdk.AuthState").setState({loadState:"loaded"});d("sdk.Auth").setAuthResponse(e.authResponse,(i=e.status)!=null?i:"unknown","facebook",!0);f("sdk.AuthUtils").setRevalidateTimer(e.status==="connected"?f("sdk.AuthUtils").AuthConstants.CONNECTED_REVALIDATE_PERIOD:f("sdk.AuthUtils").AuthConstants.DEFAULT_REVALIDATE_PERIOD)}}if(!c)if(d("sdk.AuthState").getState().loadState==="loaded"){b&&(i={authResponse:d("sdk.Auth").getAuthResponse(),status:d("sdk.Runtime").getLoginStatus()},b(i));return}else if(d("sdk.AuthState").getState().loadState==="loading"){b&&f("sdk.AuthUtils").AuthInternalEvent.subscribe("FB.loginStatus",b);return}b&&f("sdk.AuthUtils").AuthInternalEvent.subscribe("FB.loginStatus",b);d("sdk.AuthState").setState({loadState:"loading"});e=function(a){d("sdk.AuthState").setState({loadState:"loaded"}),f("sdk.AuthUtils").AuthInternalEvent.inform("FB.loginStatus",a),f("sdk.AuthUtils").AuthInternalEvent.clearSubscribers("FB.loginStatus")};a(e)}function n(a){var b="unknown";d("sdk.Auth").setAuthResponse(null,b,null);b={authResponse:null,status:b,loginSource:null};a&&a(b)}var o={getLoginStatus:m,fetchLoginStatus:a,onSDKInit:b};c=o;i["default"]=c},98);c.__d("sdk.LoggingUtils",["sdk.Impressions","sdk.feature"],function(b,c,d,e,g,h,i){"use strict";var j={buttonLoad:"client_login_button_load",buttonClick:"client_login_click",loginSuccess:"client_login_success",loginCancel:"client_login_cancel",popupHide:"client_login_popup_hide_xfoa",popupShow:"client_login_popup_show_xfoa",loginEnd:"client_login_end",loginStart:"client_login_start",loginCompleteHeartbeat:"client_login_complete_heartbeat",loginStatusPopupShowXfoa:"client_login_status_popup_show_xfoa",loginStatusPopupHideXfoa:"client_login_status_popup_hide_xfoa",loginStatusPopupClickXfoa:"client_login_status_popup_click_xfoa",loginStatusPopupErrorXfoa:"client_login_status_popup_error_xfoa"};function a(a,b,c){e("sdk.Impressions").log(117,{payload:f["extends"]({},c||{},{logger_id:a,action:b,client_funnel_version:d("sdk.feature")("oauth_funnel_logger_version",1)})})}function b(b,c){var d=b&&b.cbt!==void 0?Number(b.cbt):0;a(b==null?void 0:b.logger_id,c,{cbt_delta:Date.now()-d})}function c(b,c){c!==void 0&&a(b,c)}function g(b,c){b!==void 0&&a(c,j.loginStatusPopupErrorXfoa,{message:b})}i.logEventName=j;i.logEvent=a;i.logLoginEvent=b;i.logPopupEvent=c;i.logDisambiguationTrayEvent=g},98);c.__d("sdk.Auth",["Log","UrlMap","sdk.AuthState","sdk.AuthStorageUtils","sdk.AuthUtils","sdk.Cookie","sdk.Frictionless","sdk.LoggingUtils","sdk.Runtime","sdk.Scribe","sdk.SignedRequest","sdk.URI","sdk.ui"],function(b,c,d,g,h,i,j){c("sdk.Frictionless");var k=5*1e3;function b(a,b){b&&b.perms&&!b.scope&&(b.scope=b.perms,delete b.perms,g("Log").warn("OAuth2 specification states that 'perms' should now be called 'scope'.  Please update."));var c=d("sdk.Runtime").isEnvironment(d("sdk.Runtime").ENVIRONMENTS.CANVAS)||d("sdk.Runtime").isEnvironment(d("sdk.Runtime").ENVIRONMENTS.PAGETAB);d("sdk.ui")(f["extends"]({method:"permissions.oauth",display:c?"async":"popup",domain:location.hostname},b||{}),a)}function a(a){switch(a){case"connected":return"connected";case"not_authorized":return"not_authorized";default:return"unknown"}}function h(b){(b==null?void 0:b.shouldSetAuthResponse)!==!1&&((b==null?void 0:b.status)==="connected"&&g("sdk.AuthUtils").setRevalidateTimer(),l(b==null?void 0:b.authResponse,a(b==null?void 0:b.status),b==null?void 0:b.loginSource));var c=b==null?void 0:b.cb;c!=null&&(b={authResponse:b==null?void 0:b.authResponse,status:a(b==null?void 0:b.status),loginSource:b==null?void 0:b.loginSource},c(b));g("sdk.AuthUtils").AuthInternalEvent.clearSubscribers(g("sdk.AuthUtils").AuthConstants.CORS_FETCH_COMPLETED_EVENT);g("sdk.AuthUtils").AuthInternalEvent.clearSubscribers(g("sdk.AuthUtils").AuthConstants.XFOA_FINAL_RESPONSE_EVENT)}function l(b,c,e,f){e===void 0&&(e="facebook");f===void 0&&(f=!1);var o=d("sdk.Runtime").getUserID(),p=d("sdk.Runtime").getLoginStatus(),a="";if(b!=null){d("sdk.AuthState").setState({loadState:"loaded"});if(b.userID!=null&&b.userID!=="")a=b.userID;else if(b.signedRequest!=null&&b.signedRequest!==""){var l=g("sdk.SignedRequest").parse(b.signedRequest);l!=null&&l!==""&&l.user_id!=null&&l.user_id!==""&&(a=l.user_id)}d("sdk.Runtime").getUseCookie()&&(l=b.expiresIn===0?0:Date.now()+b.expiresIn*1e3,g("sdk.Cookie").setSignedRequestCookie(b.signedRequest,l))}else d("sdk.Runtime").getUseCookie()&&g("sdk.Cookie").clearSignedRequestCookie(),d("sdk.Runtime").getUseLocalStorage()&&g("sdk.AuthStorageUtils").removeLocalStorageToken((l=e)!=null?l:"facebook");l=p==="unknown"&&b!=null||d("sdk.Runtime").getUseCookie()&&d("sdk.Runtime").getCookieUserID()!==a;var m=o!=null&&o!==""&&b==null;o=b!=null&&o!=null&&o!==""&&o!=a;var n=b!=d("sdk.AuthState").getState().currentAuthResponse;p=c!=p;d("sdk.Runtime").setLoginStatus(c);d("sdk.Runtime").setAccessToken(b&&b.accessToken||null);d("sdk.Runtime").setUserID(a);d("sdk.Runtime").setGraphDomain(b&&b.graphDomain||"");d("sdk.AuthState").setState({currentAuthResponse:b});a={authResponse:b,status:c,loginSource:e};(m||o)&&g("sdk.AuthUtils").AuthInternalEvent.inform("logout",a);(l||o)&&g("sdk.AuthUtils").AuthInternalEvent.inform("login",a);n&&g("sdk.AuthUtils").AuthInternalEvent.inform("authresponse.change",a);p&&g("sdk.AuthUtils").AuthInternalEvent.inform("status.change",a);f||g("sdk.AuthStorageUtils").setSessionStorage(b,c);return a}function m(){return d("sdk.AuthState").getState().currentAuthResponse}function i(a){var b=m(),c=g("sdk.AuthUtils").isInstagramLogin(b)?"instagram":"facebook";l(null,"unknown",c);g("sdk.AuthUtils").setLogoutState();if(b!=null&&b.accessToken!=null){c=new(d("sdk.URI"))(g("UrlMap").resolve("www").replace("web.","www.")+"/x/oauth/logout").addQueryData("access_token",b.accessToken);var e=new XMLHttpRequest(),f=!1;e&&(e.open("GET",c.toString(),!0),e.withCredentials=!0,a&&(e.onreadystatechange=function(){if(e.readyState>=2){if(f)return;a({authResponse:m(),status:d("sdk.Runtime").getLoginStatus()});f=!0}}),e.send())}g("sdk.Scribe").log("jssdk_error",{appId:d("sdk.Runtime").getClientID(),error:"PLATFORM_AUTH_LOGOUT",extra:{args:{fblo:!0}}})}function n(a,b,c,n){return function(c){if(c&&c.access_token){var i=g("sdk.SignedRequest").parse(c.signed_request);i=i!=null?i.user_id!=null?i.user_id:null:null;b={accessToken:c.access_token,userID:i,expiresIn:Number(c.expires_in),signedRequest:c.signed_request,graphDomain:c.graph_domain};c.asset_scopes&&(b=f["extends"]({},b,{asset_scopes:e("JSON","parse",!1,c.asset_scopes)}));b=p(b,c);g("sdk.AuthUtils").removeLogoutState();i="connected";l(b,i);o(n)}else if(c&&c.asset_scopes)b={asset_scopes:e("JSON","parse",!1,c.asset_scopes)},b=p(b,c),g("sdk.AuthUtils").removeLogoutState(),i="connected",l(b,i),o(n);else if(c&&(c.error||c.error_message||c.error_description||c.error_code||c.error_reason||c.result&&c.result.closeWindow)){g("sdk.AuthUtils").setLogoutState();i="unknown";l(null,i);var k=c.error_message||c.error_description;i={authResponse:m(),status:i,message:k};c.error==="access_denied"||c.result&&c.result.closeWindow?g("sdk.AuthUtils").AuthInternalEvent.inform("loginDenied",i):g("sdk.AuthUtils").AuthInternalEvent.inform("loginError",i)}else c&&c.result&&(g("sdk.AuthUtils").removeLogoutState(),b=c.result.authResponse);a&&(k={authResponse:b,status:d("sdk.Runtime").getLoginStatus()},a(k));return b}}function o(a){if(a&&a.tp&&a.tp!=="unspecified")return;g("sdk.LoggingUtils").logLoginEvent(a,g("sdk.LoggingUtils").logEventName.loginEnd);window.setTimeout(function(){g("sdk.LoggingUtils").logLoginEvent(a,g("sdk.LoggingUtils").logEventName.loginCompleteHeartbeat)},k)}function p(a,b){b.granted_scopes&&(a=f["extends"]({},a,{grantedScopes:b.granted_scopes}));b.data_access_expiration_time&&(a=f["extends"]({},a,{data_access_expiration_time:Number(b.data_access_expiration_time)}));b.base_domain!=null&&g("sdk.AuthUtils").setBaseDomain(b.base_domain);g("sdk.AuthUtils").setGraphDomain(b.graph_domain);b.enforce_https&&d("sdk.Runtime").setEnforceHttps(!0);b.referred&&(a=f["extends"]({},a,{referred:b.referred}));g("sdk.AuthStorageUtils").setLocalStorageToken(a,b.long_lived_token);return a}c={setFinalAuthResponse:h,login:b,logout:i,setAuthResponse:l,getAuthResponse:m,parseSignedRequest:g("sdk.SignedRequest").parse,xdResponseWrapper:n,subscribe:g("sdk.AuthUtils").AuthInternalEvent.subscribe,unsubscribe:g("sdk.AuthUtils").AuthInternalEvent.unsubscribe};h=c;j["default"]=h},98);c.__d("dedupString",[],function(a,b,c,d,e,f){"use strict";function a(a){var b;return Object.keys((b={},b[a]=0,b))[0]}f["default"]=a},66);c.__d("emptyFunction",[],function(a,b,c,d,e,f){function a(a){return function(){return a}}b=function(){};b.thatReturns=a;b.thatReturnsFalse=a(!1);b.thatReturnsTrue=a(!0);b.thatReturnsNull=a(null);b.thatReturnsThis=function(){return this};b.thatReturnsArgument=function(a){return a};c=b;f["default"]=c},66);c.__d("passiveEventListenerUtil",[],function(a,b,c,d,e,f){"use strict";b=!1;try{c=Object.defineProperty({},"passive",{get:function(){b=!0}}),window.addEventListener("test",null,c)}catch(a){}var g=b;function a(a){return g?a:typeof a==="boolean"?a:a.capture||!1}f.isPassiveEventListenerSupported=g;f.makeEventOptions=a},66);c.__d("DOMEventListener",["invariant","dedupString","emptyFunction","passiveEventListenerUtil","wrapFunction"],function(b,c,d,e,f,g,h){var i=c("passiveEventListenerUtil").isPassiveEventListenerSupported,a,j;window.addEventListener?(a=function(a,b,d,e){e===void 0&&(e=!1),d.wrapper=c("wrapFunction")(d,"entry",c("dedupString")("DOMEventListener.add "+b)),a.addEventListener(b,d.wrapper,i?e:!1)},j=function(a,b,c,d){d===void 0&&(d=!1),a.removeEventListener(b,c.wrapper,i?d:!1)}):window.attachEvent?(a=function(a,b,d,e){e===void 0,d.wrapper=c("wrapFunction")(d,"entry","DOMEventListener.add "+b),a.attachEvent||h(0,2798),a.attachEvent("on"+b,d.wrapper)},j=function(a,b,c,d){d===void 0,a.detachEvent||h(0,2799),a.detachEvent("on"+b,c.wrapper)}):j=a=c("emptyFunction");b={add:function(b,c,d,e){e===void 0&&(e=!1);a(b,c,d,e);return{remove:function(){j(b,c,d,e)}}},remove:j};f.exports=b},null);c.__d("JSONRPC",["Log"],function(a,b,c,d,f,g){a=function(){"use strict";function a(a){var b=this;this.$1=0;this.$2={};this.remote=function(a){b.$3=a;return b.remote};this.local={};this.$4=a}var c=a.prototype;c.stub=function(a){var b=this;this.remote[a]=function(){var c={jsonrpc:"2.0",method:a};for(var d=arguments.length,f=new Array(d),g=0;g<d;g++)f[g]=arguments[g];typeof f[f.length-1]==="function"&&(c.id=++b.$1,b.$2[c.id]=f.pop());c.params=f;b.$4(e("JSON","stringify",!1,c),b.$3||{method:a})}};c.read=function(a,c){a=e("JSON","parse",!1,a);var d=a.id;if(!a.method){if(!this.$2[d]){b("Log").warn("Could not find callback %s",d);return}var f=this.$2[d];delete this.$2[d];delete a.id;delete a.jsonrpc;f(a);return}var g=this;f=this.local[a.method];var h;d?h=function(a,b){var f={jsonrpc:"2.0",id:d};f[a]=b;window.setTimeout(function(){g.$4(e("JSON","stringify",!1,f),c)},0)}:h=function(){};if(!f){b("Log").error('Method "%s" has not been defined',a.method);h("error",{code:-32601,message:"Method not found",data:a.method});return}a.params.push(e(h,"bind",!0,null,"result"));a.params.push(e(h,"bind",!0,null,"error"));try{f=f.apply(c||null,a.params),typeof f!=="undefined"&&h("result",f)}catch(c){b("Log").error("Invokation of RPC method %s resulted in the error: %s",a.method,c.message),h("error",{code:-32603,message:"Internal error",data:c.message})}};return a}(),f.exports=a},null);c.__d("Queue",[],function(a,b,c,d,e,f){var g={};a=function(){function a(a){this._timeout=null,this._interval=(a==null?void 0:a.interval)||0,this._processor=a==null?void 0:a.processor,this._queue=[],this._stopped=!0}var b=a.prototype;b._dispatch=function(a){var b=this;a===void 0;if(this._stopped||this._queue.length===0)return;a=this._processor;if(a==null){this._stopped=!0;throw new Error("No processor available")}var c=this._interval;if(c!=null)a.call(this,this._queue.shift()),this._timeout=setTimeout(function(){return b._dispatch()},c);else while(this._queue.length)a.call(this,this._queue.shift())};b.enqueue=function(a){this._processor&&!this._stopped?this._processor(a):this._queue.push(a);return this};b.start=function(a){a&&(this._processor=a);this._stopped=!1;this._dispatch();return this};b.isStarted=function(){return!this._stopped};b.dispatch=function(){this._dispatch(!0)};b.stop=function(a){this._stopped=!0;a&&this._timeout!=null&&clearTimeout(this._timeout);return this};b.merge=function(a,b){b?(b=this._queue).unshift.apply(b,a._queue):(b=this._queue).push.apply(b,a._queue);a._queue=[];this._dispatch();return this};b.getLength=function(){return this._queue.length};a.get=function(b,c){var d;b in g?d=g[b]:d=g[b]=new a(c);return d};a.exists=function(a){return a in g};a.remove=function(a){return delete g[a]};return a}();f["default"]=a},66);c.__d("sdk.RPC",["Assert","JSONRPC","Queue"],function(b,c,d,f,g,h,i){var j=new(d("Queue"))(),a=new(d("JSONRPC"))(function(a){j.enqueue(a)});b={local:a.local,remote:a.remote,stub:e(a.stub,"bind",!0,a),setInQueue:function(b){d("Assert").isInstanceOf(d("Queue"),b),b.start(function(b){a.read(b)})},getOutQueue:function(){return j}};c=b;i["default"]=c},98);c.__d("sdk.Canvas.Environment",["sdk.RPC"],function(a,b,c,d,e,f,g){function a(a){c("sdk.RPC").remote.getPageInfo(function(b){a(b.result)})}function b(a,b){c("sdk.RPC").remote.scrollTo({x:a||0,y:b||0})}c("sdk.RPC").stub("getPageInfo");c("sdk.RPC").stub("scrollTo");d={getPageInfo:a,scrollTo:b};e=d;g["default"]=e},98);c.__d("sdk.DialogUtils",["DOMEventListener","sdk.Content","sdk.DOM","sdk.UA"],function(b,c,d,e,f,g,h){"use strict";var i=590,a=240,j=575;function b(){return window.innerWidth<window.innerHeight}function c(a,b,c){var d=null;return e("DOMEventListener").add(a,"click",function(){d!==null&&(window.clearTimeout(d),d=null,b()),d=window.setTimeout(function(){d=null},c)})}function f(a,b,c){var d,f,i=function(){d=window.setTimeout(b,c)};i();return e("DOMEventListener").add(a,"mouseenter",function(){window.clearTimeout(d),f||(f=e("DOMEventListener").add(a,"mouseleave",function(){i()}))})}function g(a){if(!d("sdk.UA").mobile())return null;var b="onorientationchange"in window?"orientationchange":"resize",c=function(b){return window.setTimeout(function(b){return a(b)},50)};return e("DOMEventListener").add(window,b,c)}function k(a){if(a==null)return;var b=e("sdk.DOM").getViewportInfo();a.style.minHeight=b.height?b.height+"px":"";a.style.top=b.scrollTop?b.scrollTop+"px":""}function l(b,c,f){var o,p,k,l=function(a){return typeof a==="number"?a:parseInt(a,10)},m=e("sdk.DOM").getViewportInfo(),n=l(b.offsetWidth);l=l(b.offsetHeight);o=(o=m.scrollLeft)!=null?o:0+((o=m.width)!=null?o:j-n)/2;n=((n=m.height)!=null?n:a-l)/2.5;o<n&&(n=o);p=(p=m.height)!=null?p:a-l-n;k=((k=m.height)!=null?k:a-l)/2;f&&(k=f.scrollTop-f.offsetTop+(f.clientHeight-l)/2);k<n?k=n:k>p&&(k=p);k+=(f=m.scrollTop)!=null?f:0;d("sdk.UA").mobile()&&(n=100,c?(n+=((p=m.height)!=null?p:i-l)/2,e("sdk.DOM").addCss(document.body,"fb_reposition")):(e("sdk.DOM").addCss(document.body,"fb_hidden"),document.body.style.width="auto",k=1e4),f=e("sdk.DOM").getByClass("fb_dialog_padding",b),f.length&&(f[0].style.height=n+"px"));b.style.left=(o>0?o:0)+"px";b.style.top=(k>0?k:0)+"px"}function m(a,b,c){l(a,b,c),b=e("sdk.DOM").getViewportInfo(),b=(c=b.scrollTop)!=null?c:0+((c=b.height)!=null?c:i-a.offsetHeight)*.05,e("sdk.DOM").setStyle(a,"top",b+"px")}function n(){var a=document.createElement("div");a.setAttribute("id","fb_dialog_ipad_overlay");k(a);return a}function o(a){a=a||{};var b=document.createElement("div"),c=a;c=c.onClose;if(a.closeIcon&&c){var f=document.createElement("a");f.className="fb_dialog_close_icon";e("DOMEventListener").add(f,"click",c);b.appendChild(f)}c="fb_dialog";c+=" "+(a.classes||"");c+=d("sdk.UA").mobile()?" fb_dialog_mobile":" fb_dialog_advanced";b.className=c;a.width&&(f=parseInt(a.width,10),isNaN(f)||(b.style.width=f+"px"));c=document.createElement("div");a.content&&e("sdk.Content").append(a.content,c);c.className="fb_dialog_content";b.appendChild(c);d("sdk.UA").mobile()&&(f=document.createElement("div"),f.className="fb_dialog_padding",b.appendChild(f));return{dialogElement:b,contentRoot:c}}function p(a){var b=document.body;a?e("sdk.DOM").removeCss(b,"fb_reposition"):e("sdk.DOM").removeCss(b,"fb_hidden")}h.isOrientationPotrait=b;h.addDoubleClickAction=c;h.addIdleDesktopAction=f;h.addMobileOrientationChangeAction=g;h.applyScreenDimensions=k;h.setDialogPositionToCenter=l;h.setDialogPositionToTop=m;h.setupNewDarkOverlay=n;h.setupNewDialog=o;h.onDialogHideCleanup=p},98);c.__d("sdk.fbt",[],function(a,b,c,d,e,f){a=function(){},a._=function(a){var b=typeof a==="string"?a:a[0];b==null&&typeof a==="object"&&a!==null&&!Array.isArray(a)&&"*"in a&&(b=a["*"]);return b},b=a,f["default"]=b},66);c.__d("sdk.Dialog",["DOMEventListener","ObservableMixin","Type","sdk.Canvas.Environment","sdk.Content","sdk.DOM","sdk.DialogUtils","sdk.Runtime","sdk.UA","sdk.fbt"],function(b,c,d,f,g,h,i){var j=30,a=590,k=500,l=240,m=575;function n(){var b=f("sdk.DOM").getViewportInfo(),c=b.height;b=b.width;return c!=null&&b!=null?{width:Math.min(b,k),height:Math.min(c,a)}:null}var o=d("Type").extend({constructor:function(a,b){this.parent(),this.id=a,this.display=b,this._e2e={},p._dialogs||(p._dialogs={},p._addOrientationHandler()),p._dialogs[a]=this,this.trackEvent("init")},trackEvent:function(a,b){if(this._e2e[a])return this;this._e2e[a]=b||Date.now();a=="close"&&this.inform("e2e:end",this._e2e);return this},trackEvents:function(a){typeof a==="string"&&(a=e("JSON","parse",!1,a));for(var b in a)Object.prototype.hasOwnProperty.call(a,b)&&this.trackEvent(b,a[b]);return this}},d("ObservableMixin")),p={newInstance:function(a,b){return new o(a,b)},_dialogs:null,_lastYOffset:0,_availScreenWidth:null,_overlayListeners:[],_loaderEl:null,_overlayEl:null,_stack:[],_active:null,get:function(a){return p._dialogs[a]},_findRoot:function(a){a=a;while(a){if(f("sdk.DOM").containsCss(a,"fb_dialog"))return a;a.parentElement instanceof HTMLElement&&(a=a.parentElement)}},_createWWWLoader:function(a){a=a?a:"460";var b=document.createElement("div");b.innerHTML='<div class="dialog_title">  <a id="fb_dialog_loader_close">    <div class="fb_dialog_close_icon"></div>  </a>  <span>Facebook</span>  <div style="clear:both;"></div></div><div class="dialog_content"></div><div class="dialog_footer"></div>';return p.create({content:b,width:a})},_createMobileLoader:function(){var a=document.createElement("div");d("sdk.UA").nativeApp()?a.innerHTML='<div class="dialog_header"></div>':p.isTabletStyle()?a.innerHTML='<div class="overlayLoader"><div id="fb_dialog_loader_spinner"></div><a id="fb_dialog_loader_close" href="#">'+d("sdk.fbt")._("Cancel")+"</a></div>":a.innerHTML='<div class="dialog_header"><table>  <tbody>    <tr>      <td class="header_left">        <label class="touchable_button">          <input type="submit" value="'+d("sdk.fbt")._("Cancel")+'"            id="fb_dialog_loader_close"/>        </label>      </td>      <td class="header_center">        <div>         '+d("sdk.fbt")._("Loading...")+'        </div>      </td>      <td class="header_right">      </td>    </tr>  </tbody></table></div>';return p.create({classes:"loading"+(p.isTabletStyle()?" centered":""),content:a})},_setDialogOverlayStyle:function(){p._overlayEl!=null&&f("sdk.DialogUtils").applyScreenDimensions(p._overlayEl)},_showTabletOverlay:function(a){if(!p.isTabletStyle())return;p._overlayEl==null?(a=f("sdk.DialogUtils").setupNewDarkOverlay(),a.className="",p._overlayEl=a,f("sdk.Content").append(p._overlayEl,null)):p._overlayEl.className=""},_hideTabletOverlay:function(){p.isTabletStyle()&&(p._overlayEl!=null&&(p._overlayEl.className="hidden"),p._overlayListeners.forEach(function(a){return a.remove()}),p._overlayListeners=[])},showLoader:function(a,b){a||(a=function(){});var c=function(){p._hideLoader(),f("sdk.DialogUtils").onDialogHideCleanup(p.isTabletStyle()),p._hideTabletOverlay(),a!=null&&a()};p._showTabletOverlay(c);p._loaderEl||(p._loaderEl=p._findRoot(d("sdk.UA").mobile()?p._createMobileLoader():p._createWWWLoader(b)));b=document.getElementById("fb_dialog_loader_close");b&&(f("sdk.DOM").removeCss(b,"fb_hidden"),b=f("DOMEventListener").add(b,"click",c),p._overlayListeners.push(b));p._loaderEl!=null&&p._makeActive(p._loaderEl)},_hideLoader:function(){p._loaderEl&&p._loaderEl==p._active&&(p._loaderEl.style.top="-10000px")},_makeActive:function(a){p._setDialogSizes(),p._lowerActive(),p._active=a,d("sdk.Runtime").isEnvironment(d("sdk.Runtime").ENVIRONMENTS.CANVAS)&&d("sdk.Canvas.Environment").getPageInfo(function(a){p._centerActive(a)}),p._centerActive()},_lowerActive:function(){if(!p._active)return;p._active.style.top="-10000px";p._active=null},_removeStacked:function(a){p._stack=p._stack.filter(function(b){return b!=a})},_centerActive:function(a){var b=p._active;if(!b)return;f("sdk.DialogUtils").setDialogPositionToCenter(b,p.isTabletStyle(),a)},_setDialogSizes:function(a){a===void 0&&(a=!1);if(!d("sdk.UA").mobile())return;for(var b in p._dialogs)if(Object.prototype.hasOwnProperty.call(p._dialogs,b)){var c=document.getElementById(b);c&&(c.style.width=p.getDefaultSize().width+"px",a||(c.style.height=p.getDefaultSize().height+"px"))}},getDefaultSize:function(){if(d("sdk.UA").mobile()){var b=n();if(b){var c;((c=(c=f("sdk.DOM").getViewportInfo())==null?void 0:c.width)!=null?c:k<=b.width)&&(b.width=(c=(c=f("sdk.DOM").getViewportInfo())==null?void 0:c.width)!=null?c:k-j);((c=(c=f("sdk.DOM").getViewportInfo())==null?void 0:c.height)!=null?c:a<=b.height)&&(b.height=(c=(c=f("sdk.DOM").getViewportInfo())==null?void 0:c.height)!=null?c:a-j);return b}if(d("sdk.UA").ipad())return{width:k,height:a};if(d("sdk.UA").android())return{width:screen.availWidth,height:screen.availHeight};else{c=window.innerWidth;b=window.innerHeight;var e=c/b>1.2;return{width:c,height:Math.max(b,e?screen.width:screen.height)}}}return{width:m,height:l}},_handleOrientationChange:function(){var a;p._availScreenWidth=(a=(a=f("sdk.DOM").getViewportInfo())==null?void 0:a.width)!=null?a:k;if(p.isTabletStyle())p._setDialogSizes(!0),p._centerActive(),p._setDialogOverlayStyle();else{a=p.getDefaultSize().width;for(var b in p._dialogs)if(Object.prototype.hasOwnProperty.call(p._dialogs,b)){var c=document.getElementById(b);c&&(c.style.width=a+"px")}}},_addOrientationHandler:function(){var a;if(!d("sdk.UA").mobile())return;p._availScreenWidth=(a=(a=f("sdk.DOM").getViewportInfo())==null?void 0:a.width)!=null?a:k;f("sdk.DialogUtils").addMobileOrientationChangeAction(p._handleOrientationChange)},create:function(a){var b=f("sdk.DialogUtils").setupNewDialog(a);f("sdk.Content").append(b.dialogElement);a.visible&&p.show(b.dialogElement);typeof a.styles==="object"&&e("Object","assign",!1,b.dialogElement.style,a.styles);return b.contentRoot},show:function(a){var b=p._findRoot(a);b!=null&&(p._removeStacked(b),p._hideLoader(),p._makeActive(b),p._stack.push(b),"fbCallID"in a&&p.get(a.fbCallID).inform("iframe_show").trackEvent("show"))},hide:function(a){var b=p._findRoot(a);p._hideLoader();b==p._active&&(p._lowerActive(),f("sdk.DialogUtils").onDialogHideCleanup(p.isTabletStyle()),p._hideTabletOverlay(),"fbCallID"in a&&p.get(a.fbCallID).inform("iframe_hide").trackEvent("hide"))},remove:function(a){var b=p._findRoot(a);b&&(a=p._active==b,p._removeStacked(b),a?(p._hideLoader(),p._stack.length>0?p.show(p._stack.pop()):(p._lowerActive(),f("sdk.DialogUtils").onDialogHideCleanup(p.isTabletStyle()),p._hideTabletOverlay())):p._active===null&&p._stack.length>0&&p.show(p._stack.pop()),window.setTimeout(function(){var a;(a=b.parentNode)==null?void 0:a.removeChild(b)},3e3))},isActive:function(a){a=p._findRoot(a);return a!=null&&a===p._active},isTabletStyle:function(){if(!d("sdk.UA").mobile())return!1;var b=n();return b!=null&&(b.height>=a||b.width>=k)}};b=p;i["default"]=b},98);c.__d("sdk.PlatformVersioning",["ManagedError","sdk.Runtime"],function(a,b,c,d,e,f,g){var h=/^v\d+\.\d\d?$/;function a(){if(!c("sdk.Runtime").getVersion())throw new(c("ManagedError"))("init not called with valid version")}function b(a){if(!h.test(a))throw new(c("ManagedError"))("invalid version specified")}g.REGEX=h;g.assertVersionIsSet=a;g.assertValidVersion=b},98);c.__d("sdk.warnInsecure",["Log","sdk.Runtime","sdk.Scribe","sdk.feature"],function(b,c,d,e,f,g,h){"use strict";var i=d("sdk.feature")("https_only_learn_more",""),a={};function b(b){window.location.protocol!=="https:"&&(e("Log").log("error",-1,"The method FB.%s can no longer be called from http pages. %s",b,i),d("sdk.feature")("https_only_scribe_logging",!0)&&!Object.prototype.hasOwnProperty.call(a,b)&&(e("sdk.Scribe").log("jssdk_error",{appId:d("sdk.Runtime").getClientID(),error:"HttpsOnly",extra:{message:b}}),a[b]=!0));return!0}h["default"]=b},98);c.__d("sdk.api",["ApiClient","sdk.PlatformVersioning","sdk.Runtime","sdk.URI","sdk.warnInsecure"],function(a,b,c,d,e,f,g){function a(a){for(var b=arguments.length,e=new Array(b>1?b-1:0),f=1;f<b;f++)e[f-1]=arguments[f];c("sdk.warnInsecure")("api");if(typeof a==="string")if(c("sdk.Runtime").getIsVersioned()){d("sdk.PlatformVersioning").assertVersionIsSet();var g=a;!/https?/.test(g)&&g.charAt(0)!=="/"&&(g="/"+g);g=new(c("sdk.URI"))(g).setDomain("").setProtocol("").toString();d("sdk.PlatformVersioning").REGEX.test(g.substring(1,g.indexOf("/",1)))||(g="/"+c("sdk.Runtime").getVersion()+g);var h=[g].concat(Array.prototype.slice.call(arguments,1));c("ApiClient").graph.apply(c("ApiClient"),h)}else c("ApiClient").graph.apply(c("ApiClient"),arguments);else c("ApiClient").rest.apply(c("ApiClient"),arguments)}g["default"]=a},98);c.__d("sdk.Frictionless",["sdk.Auth.LoginStatus","sdk.Dialog","sdk.Event","sdk.api"],function(a,b,c,d,e,f,g){var h={_allowedRecipients:{},_useFrictionless:!1,_updateRecipients:function(){h._allowedRecipients={},c("sdk.api")("/me/apprequestformerrecipients",function(a){if(!a||(a==null?void 0:a.error))return;a.data.forEach(function(a){h._allowedRecipients[a.recipient_id]=!0})})},init:function(){h._useFrictionless=!0,c("sdk.Auth.LoginStatus").getLoginStatus(function(a){(a==null?void 0:a.status)=="connected"&&h._updateRecipients()}),d("sdk.Event").subscribe("auth.login",function(a){a.authResponse&&h._updateRecipients()})},_processRequestResponse:function(a,b){return function(d){var e=d&&d.updated_frictionless;h._useFrictionless&&e!==null&&h._updateRecipients();d&&(!b&&d.frictionless!==null&&c("sdk.Dialog")._hideLoader(),delete d.frictionless,delete d.updated_frictionless);a&&a(d)}},isAllowed:function(a){a=a;if(!a)return!1;if(typeof a==="number")return a in h._allowedRecipients;typeof a==="string"&&(a=a.split(","));a=a.map(function(a){return String(a).trim()});var b=!0,c=!1;a.forEach(function(a){b=b&&a in h._allowedRecipients,c=!0});return b&&c}};a=h;g["default"]=a},98);c.__d("resolveURI",[],function(a,b,c,d,e,f){function a(a){if(a==null||a==="")return window.location.href;var b=document.createElement("a");b.href=a;return b.href}f["default"]=a},66);c.__d("sdk.NativeExtensions",["DOMEventListener","Log","sdk.UA"],function(b,c,d,e,f,g,h){var i="fbNativeExtensionsReady";function a(){return window._FBSdkExtensions&&window._FBSdkExtensions.jsonRPC&&window._FBSdkExtensions.initializeCallbackHandler&&window._FBSdkExtensions.supportsDialog?window._FBSdkExtensions:null}function b(b){if(!d("sdk.UA").facebookInAppBrowser()){e("Log").error("FB.NativeExtensions.onReady only works when the page is rendered in a WebView of the native Facebook app.");return}var c=a();if(c){b(c);return}var f=!1;c=function c(){var d=a();if(f||!d)return;f=!0;b(d);e("DOMEventListener").remove(window,i,c)};e("DOMEventListener").add(window,i,c)}h.onReady=b},98);c.__d("sdk.Extensions",["JSONRPC","Queue","sdk.NativeExtensions","sdk.UA"],function(b,c,d,f,g,h,i){"use strict";var j=new(d("Queue"))(),a=new(d("JSONRPC"))(function(a){j.enqueue(a)}),k=new(d("Queue"))();k.start(function(b){a.read(b)});var l=null;d("sdk.UA").facebookInAppBrowser()&&f("sdk.NativeExtensions").onReady(function(a){l=a,window._FBBrowserCallbackHandler=function(a){k.enqueue(e("JSON","stringify",!1,a))},a.initializeCallbackHandler(e("JSON","stringify",!1,{name:"_FBBrowserCallbackHandler"})),j.start(function(b){a.jsonRPC(b)})});c=a.local;g=a.remote;h=e(a.stub,"bind",!0,a);function b(a){return!!l&&l.supportsDialog(a)}i.local=c;i.remote=g;i.stub=h;i.supportsDialog=b},98);c.__d("sdk.Native",["Log","sdk.UA"],function(a,b,c,d,f,g,h){var i="fbNativeReady";a={onready:function(a){if(!c("sdk.UA").nativeApp()){d("Log").error("FB.Native.onready only works when the page is rendered in a WebView of the native Facebook app. Test if this is the case calling FB.UA.nativeApp()");return}window.__fbNative&&!this.nativeReady&&e("Object","assign",!1,this,window.__fbNative);if(this.nativeReady)a();else{var b=function b(){window.removeEventListener(i,b),this.onready(a)};window.addEventListener(i,b,!1)}}};b=a;h["default"]=b},98);c.__d("sdk.Popup",["sdk.Content","sdk.Runtime","sdk.Scribe","sdk.UA","sdk.feature"],function(a,b,c,d,e,f,g){"use strict";function a(a,b){var e=h({name:a.name,height:a.size.height,width:a.size.width,isOAuth:b}),f;a.post?(f=window.open("about:blank",a.id,e),f&&d("sdk.Content").submitToTarget({url:a.url,target:a.id,params:a.params})):f=window.open(a.url,a.id,e);!f&&c("sdk.feature")("popup_blocker_scribe_logging",!0)&&(e=b?"POPUP_MAYBE_BLOCKED_OAUTH":"POPUP_MAYBE_BLOCKED",d("sdk.Scribe").log("jssdk_error",{appId:c("sdk.Runtime").getClientID(),error:e,extra:{call:a.name}}));return f}function h(a){var b=window.screenX,d=window.screenY,e=window.outerWidth,f=window.outerHeight,g=c("sdk.UA").mobile()?0:a.width,h=c("sdk.UA").mobile()?0:a.height;b=b<0?window.screen.width+b:b;b=b+(e-g)/2;e=d+(f-h)/2.5;d=[];g!==null&&d.push("width="+g);h!==null&&d.push("height="+h);d.push("left="+b);d.push("top="+e);d.push("scrollbars=1");a.isOAuth&&(d.push("toolbar=0"),(!c("sdk.UA").chrome()||c("sdk.UA").chrome()<59)&&d.push("location=1"));return d.join(",")}g.popup=a},98);c.__d("isFacebookDotNetURI",[],function(a,b,c,d,e,f){"use strict";function a(a){if(a.getProtocol()!=="http"&&a.getProtocol()!=="https")return!1;var b=Number(a.getPort());return!!b&&b!==80&&b!==443?!1:a.isSubdomainOfDomain("facebook.net")?!0:!1}f["default"]=a},66);c.__d("isFacebookURI",[],function(a,b,c,d,e,f){var g=null,h=["http","https"];function a(a){g||(g=new RegExp("(^|\\.)facebook\\.com$","i"));return a.isEmpty()&&a.toString()!=="#"?!1:!a.getDomain()&&!a.getProtocol()?!0:h.indexOf(a.getProtocol())!==-1&&g.test(a.getDomain())}a.setRegex=function(a){g=a};f["default"]=a},66);c.__d("isInstagramURI",[],function(a,b,c,d,e,f){var g=null;function a(a){if(a.isEmpty()&&a.toString()!=="#")return!1;if(!a.getDomain()&&!a.getProtocol())return!1;if(a.getProtocol()!=="https")return!1;g||(g=new RegExp("(^|\\.)instagram\\.com$","i"));return g.test(a.getDomain())}f["default"]=a},66);c.__d("resolveWindow",[],function(a,b,c,d,e,f){function a(a){if(a==null)return null;var b=window;a=a.split(".");try{for(var c=0;c<a.length;c++){var d=a[c],e=/^frames\[[\'\"]?([a-zA-Z0-9\-_]+)[\'\"]?\]$/.exec(d);if(e)b=b.frames[e[1]];else if(d==="opener"||d==="parent"||d==="top")b=b[d];else return null}}catch(a){return null}return b}f["default"]=a},66);c.__d("sdk.XD",["JSSDKXDConfig","Log","QueryString","Queue","UrlMap","guid","isFacebookDotNetURI","isFacebookURI","isInstagramURI","resolveWindow","sdk.Event","sdk.RPC","sdk.Runtime","sdk.Scribe","sdk.URI","sdk.feature"],function(b,c,d,f,g,h,i){var j=new(d("Queue"))(),a="parent",k=null,l=/^https:\/\/.*\.(facebook|instagram)\.(com|net)$/;b=f("JSSDKXDConfig").useCdn?"cdn":"www";var m=f("UrlMap").resolve(b)+f("JSSDKXDConfig").XXdUrl;c=function(){if("origin"in location)if(location.origin&&location.origin!="null")return location.origin;else if(window!==window.parent)try{var a=parent.location.origin;if(a&&a!="null")return a}catch(a){}return location.protocol+"//"+location.host};var n=d("guid")(),o=c(),p=!1,q=new(d("Queue"))();d("sdk.RPC").setInQueue(q);function r(a){f("Log").info("Remote XD can talk to facebook.com (%s)",a),d("sdk.Runtime").setEnvironment(a==="canvas"?d("sdk.Runtime").ENVIRONMENTS.CANVAS:d("sdk.Runtime").ENVIRONMENTS.PAGETAB)}function s(a,b){if(!b){f("Log").error("No senderOrigin");throw new Error()}switch(a.xd_action){case"plugin_ready":if(typeof a.name==="string"){var c=a.name;f("Log").info("Plugin %s ready from %s",c,b);if(l.test(b)){var e=d("Queue").get(c,{});e.start(function(a){if(a==null){f("Log").warn("Discarding null message from %s to %s on %s",b,c,o);return}window.frames[c]!=null?window.frames[c].postMessage({xdArbiterHandleMessage:!0,message:a,origin:o},b):f("Log").info("Message discarded for plugin at window.frames[%s] which may have been removed by a new XFBML.parse() call.",c)})}else{f("Log").error("Plugin attempted to register from non-Facebook domain %s",b);return}}else f("Log").error("plugin_ready message received without a name");break}a.data!=null&&(typeof a.data==="object"||typeof a.data==="string")&&t(a.data,b)}function t(a,b){var c=new(d("sdk.URI"))(b);if(b!=null&&b!=="native"&&!d("isFacebookURI")(c)&&!d("isFacebookDotNetURI")(c)&&!d("isInstagramURI")(c))return;if(typeof a==="string"){if(/^FB_RPC:/.test(a)){q.enqueue(a.substring(7));return}if(a.substring(0,1)=="{")try{a=e("JSON","parse",!1,a)}catch(b){f("Log").warn("Failed to decode %s as JSON",a);return}else a=d("QueryString").decode(a)}c=a;if(c.xd_action){s(c,b);return}typeof c.cb==="string"&&(a=x._callbacks[c.cb],x._forever[c.cb]||delete x._callbacks[c.cb],a&&a(c))}function u(b,c){b=="facebook"?(c.relation=a,j.enqueue(c),!d("sdk.Runtime").isCanvasEnvironment()&&!j.isStarted()&&w(k)):d("Queue").get(b,{}).enqueue(c)}d("sdk.RPC").getOutQueue().start(function(a){j.enqueue("FB_RPC:"+a)});function v(a){if(p)return;p=!0;window.addEventListener("message",function(a){var b=a.data,c=a.origin||"native";if(!/^(https?:\/\/|native$)/.test(c)){f("Log").debug("Received message from invalid origin type: %s",c);return}if(!l.test(c))return;if(typeof b==="string")t(b,c);else{if(a.source==parent&&a.data.xdArbiterRegisterAck&&l.test(c)){typeof a.data.xdArbiterRegisterAck==="string"&&a.data.xdArbiterRegisterAck!==""&&r(a.data.xdArbiterRegisterAck);j.isStarted()||j.start(function(a){if(a==null){f("Log").warn("Discarding null message from %s to %s",o,c);return}var b=parent;typeof a==="object"&&typeof a.relation==="string"&&(b=d("resolveWindow")(a.relation));((b=b)!=null?b:parent).postMessage({xdArbiterHandleMessage:!0,message:a,origin:o},c)});return}return}});d("sdk.Runtime").isCanvasEnvironment()&&w(a)}function w(b){var c;p||v();if(window.parent!=top){f("Log").warn("cannot deliver messages to facebook unless window.parent is top and facebook.com.");return}var e=(c=d("sdk.feature")("xd_timeout",6e4))!=null?c:6e4;c=200;var k=0,l=e/c,a=function(){return parent.postMessage({xdArbiterRegister:!0,xdProxyName:b,origin:o},"*")};k=window.setInterval(function(){if(!j.isStarted()&&l>0)l--,f("Log").debug("resending xdArbiterRegister"),a();else{window.clearInterval(k);if(l===0){f("sdk.Scribe").log("jssdk_error",{appId:d("sdk.Runtime").getClientID(),error:"XD_FB_QUEUE_INITIALIZATION",extra:{message:"Failed to initialize in "+e+"ms"}});f("Log").error("xdAbiterRegisterAck not received");return}}},c)}var x={rpc:d("sdk.RPC"),_callbacks:{},_forever:{},_channel:n,_origin:o,onMessage:t,init:v,sendToFacebook:u,inform:function(a,b,c,d){u("facebook",{method:a,params:e("JSON","stringify",!1,b||{}),behavior:d||"p",relation:c})},handler:function(a,b,c,e){a="#"+d("QueryString").encode({cb:x.registerCallback(a,c,e),origin:o+"/"+n,domain:location.hostname,relation:b||"opener",is_canvas:d("sdk.Runtime").isCanvasEnvironment()});return m+a},registerCallback:function(a,b,c){c=c||d("guid")();b&&(x._forever[c]=!0);x._callbacks[c]=a;return c}};f("sdk.Event").subscribe("init:post",function(a){k=a.xdProxyName,v(a.xdProxyName)});h.exports=x},34);c.__d("sdk.modFeatureCheck",["JSSDKConfig"],function(a,b,c,d,e,f,g){function a(a,b,c){c===void 0&&(c=!1);if(d("JSSDKConfig").features&&a in d("JSSDKConfig").features){var e=d("JSSDKConfig").features[a];if(typeof e==="object"&&Array.isArray(e))return b.some(function(a){return e.some(function(b){return a%b===0})})}return c}g.forIDs=a},98);c.__d("sdk.openMessenger",["sdk.UA"],function(b,c,d,e,f,g,h){"use strict";var i="https://itunes.apple.com/us/app/messenger/id454638411",a="https://play.google.com/store/apps/details?id=com.facebook.orca",j=3e3;function b(b){var c,e,f=b.link;b=b.app_id;d("sdk.UA").android()?(c="intent://share/#Intent;package=com.facebook.orca;scheme=fb-messenger;S.android.intent.extra.TEXT="+encodeURIComponent(f)+";S.trigger=send_plugin;",b&&(c+="S.platform_app_id="+encodeURIComponent(b)+";"),c+="end",e=a):(c="fb-messenger://share?link="+encodeURIComponent(f),b&&(c+="&app_id="+encodeURIComponent(b)),e=i);setTimeout(function(){window.location.href=e},j);window.location.href=c}h["default"]=b},98);c.__d("sdk.UIServer",["Log","QueryString","UrlMap","createObjectFrom","flattenObject","guid","insertIframe","resolveURI","sdk.Auth","sdk.Auth.LoginStatus","sdk.Content","sdk.DOM","sdk.Dialog","sdk.Event","sdk.Extensions","sdk.Frictionless","sdk.LoggingUtils","sdk.Native","sdk.Popup","sdk.RPC","sdk.Runtime","sdk.UA","sdk.XD","sdk.api","sdk.fbt","sdk.feature","sdk.getContextType","sdk.modFeatureCheck","sdk.openMessenger"],function(b,c,d,g,h,i,j){var k={transform:function(a){if(a.params.display==="touch"&&r.canIframe(a.params)&&window.postMessage){a.params.channel=r._xdChannelHandler(a.id,"parent");d("sdk.UA").nativeApp()||(a.params.in_iframe=1);return a}else return r.genericTransform(a)},getXdRelation:function(a){var b=a.display;return b==="touch"&&window.postMessage&&a.in_iframe?"parent":r.getXdRelation(a)}};function a(a){return r.isOAuth(a)&&g("sdk.Extensions").supportsDialog("oauth")}function l(a){return r.isOAuth(a)&&(a.is_account_link===!0||a.is_account_link==="true")&&g("sdk.Extensions").supportsDialog("accountLink")}function m(a){if(!d("sdk.Runtime").getClientID()){g("Log").error("FB.login() called before FB.init().");return}if(d("sdk.Auth").getAuthResponse()&&!a.params.scope&&!a.params.asset_scope&&!a.params.auth_type){a.params.plugin_prepare||(g("Log").error("FB.login() called when user is already connected."),a.cb&&(a==null?void 0:a.cb({status:d("sdk.Runtime").getLoginStatus(),authResponse:d("sdk.Auth").getAuthResponse()})));return}var b=a.cb,c=a.id;delete a.cb;a&&a.params&&!a.params.logger_id&&(a.params.logger_id=d("guid")());a&&a.params&&!a.params.cbt&&(a.params.cbt=Date.now());(a.params.fx_app==="instagram"||a.params.fx_app==="ig_single")&&!a.params.scope&&(a.params.scope="public_profile");var f=a.params.auth_type;f=f&&e(f,"includes",!0,"reauthenticate");var k={token:!0,signed_request:!0,graph_domain:!0};k=Object.keys(e("Object","assign",!1,a.params.response_type?d("createObjectFrom")(a.params.response_type.split(",")):{},k)).join(",");a.params.display==="async"?(e("Object","assign",!1,a.params,{client_id:d("sdk.Runtime").getClientID(),origin:d("sdk.getContextType")(),response_type:k,domain:location.hostname}),a.cb=d("sdk.Auth").xdResponseWrapper(b,d("sdk.Auth").getAuthResponse(),"permissions.oauth",a.params)):(f&&r._xdNextHandler(function(a){b({authResponse:null,status:"not_authorized"})},c,a.params.plugin_prepare?"opener.parent":"opener",!0),e("Object","assign",!1,a.params,{client_id:d("sdk.Runtime").getClientID(),redirect_uri:d("resolveURI")(r.xdHandler(b,c,a.params.plugin_prepare?"opener.parent":"opener",d("sdk.Auth").getAuthResponse(),"permissions.oauth",!f,a.params)),origin:d("sdk.getContextType")(),response_type:k,domain:location.hostname}));c=a.params&&a.params.tp&&a.params.tp!=="unspecified";!a.params.plugin_prepare&&!c&&g("sdk.LoggingUtils").logEvent(a.params.logger_id,g("sdk.LoggingUtils").logEventName.loginStart,{cbt_delta:0});return a}b={"stream.share":{size:{width:670,height:340},url:"sharer.php",transform:function(a){a.params.u||(a.params.u=window.location.toString());a.params.display="popup";return a}},gaming_friendfinder:{url:"gaming/me/friendfinder/",transform:function(a){if(!d("sdk.Runtime").getClientID()){g("Log").error("FriendFinder called before FB.init().");return}a.url+=d("sdk.Runtime").getClientID();a.size={width:400,height:800};return a}},gaming_media_library:{url:"gaming/me/media_asset/",transform:function(a){a.url+=a.params.media_id;a.size={width:400,height:800};return a}},apprequests:{transform:function(a){a=k.transform(a);a.size={width:445,height:635};a.params.display="popup";a.params.in_iframe=!1;a.params.frictionless=d("sdk.Frictionless")&&d("sdk.Frictionless")._useFrictionless;a.params.frictionless&&(d("sdk.Frictionless").isAllowed(a.params.to)&&(a.hideLoader=!0),a.cb=d("sdk.Frictionless")._processRequestResponse(a.cb,a.hideLoader));a.closeIcon=!1;return a},getXdRelation:k.getXdRelation},"permissions.oauth":{url:"dialog/oauth",size:{width:d("sdk.UA").mobile()?null:600,height:d("sdk.UA").mobile()?null:679},transform:function(a){return m(a)}},"permissions.ig_oauth":{url:"oauth/authorize",size:{width:d("sdk.UA").mobile()?null:600,height:d("sdk.UA").mobile()?null:679},transform:function(a){return m(a)}},photo_picker:{url:"dialog/photo_picker",size:{width:d("sdk.UA").mobile()?null:600,height:d("sdk.UA").mobile()?null:679},transform:function(a){if(!d("sdk.Runtime").getClientID()){g("Log").error("Photo Picker was called before FB.init().");return}var b=a.cb,c=a.id;delete a.cb;e("Object","assign",!1,a.params,{client_id:d("sdk.Runtime").getClientID(),redirect_uri:d("resolveURI")(r.xdHandlerPhotoPicker(b,c,a.params.plugin_prepare?"opener.parent":"opener","photo_picker",a.params)),origin:d("sdk.getContextType")(),domain:location.hostname});return a}},"auth.logout":{transform:function(a){d("sdk.Runtime").getClientID()?d("sdk.Auth").getAuthResponse()?d("sdk.Auth").logout(a.cb):g("Log").error("FB.logout() called without an access token."):g("Log").error("FB.logout() called before calling FB.init().")}},"login.status":{transform:function(a){d("sdk.Auth.LoginStatus").getLoginStatus(a.cb)}},pay:{size:{width:555,height:120},connectDisplay:"popup"},live_broadcast:{transform:function(a){a.params.phase==="create"&&(a.size={width:480,height:280});a.params.phase==="publish"&&(a.size={width:772,height:540});return a},require_access_token:!0},boost:{transform:function(a){a.size={width:960,height:760};a.params.display="popup";return a}},share_referral:{size:{width:482,height:725}}};var n={},o=0;function p(a,b){n[b]=!0;return function(c){delete n[b],a(c)}}function q(a){var b=a.method.toLowerCase();return b==="pay"&&a.display==="async"?!0:!1}var r={Methods:b,_oauthMethodNameSet:new Set(["permissions.oauth","permissions.request","permissions.ig_oauth"]),_loadedNodes:{},_defaultCb:{},_resultToken:'"xxRESULTTOKENxx"',_popupInterval:null,genericTransform:function(a){(a.params.display=="dialog"||a.params.display=="iframe")&&e("Object","assign",!1,a.params,{display:"iframe",channel:r._xdChannelHandler(a.id,"parent.parent")},!0);return a},isOAuth:function(a){return r._oauthMethodNameSet.has(a.method)||a.method=="oauth"},checkOauthDisplay:function(a){var b=a.scope||a.perms||d("sdk.Runtime").getScope();return b?"popup":a.display},prepareCall:function(a,b){var c=a.method.toLowerCase(),l=Object.prototype.hasOwnProperty.call(r.Methods,c)?f["extends"]({},r.Methods[c]):{},m=a.id||d("guid")(),k=!0;e("Object","assign",!1,a,{app_id:d("sdk.Runtime").getClientID(),locale:d("sdk.Runtime").getLocale(),sdk:"joey",access_token:k&&d("sdk.Runtime").getAccessToken()||void 0});a.display=r.getDisplayMode(l,a);l.url||(l.url="dialog/"+c);(l.url=="dialog/oauth"||l.url=="dialog/permissions.request")&&(a.display=="iframe"||a.display=="touch"&&a.in_iframe)&&(a.display=r.checkOauthDisplay(a));if(l.url=="dialog/oauth"){if(o>=((k=d("sdk.feature")("max_oauth_dialog_retries",100))!=null?k:100)){g("Log").error("Your request to oauth has exceeded the rate limit, please try again later");return}o++}a.display=="popup"&&!l.require_access_token&&delete a.access_token;d("sdk.Runtime").getIsVersioned()&&l.url.substring(0,7)==="dialog/"&&(k=a.version||d("sdk.Runtime").getVersion(),k!=null&&k!==""&&k!=="null"&&(l.url=k+"/"+l.url));if(q(a)){if(n[c]){k='Dialog "'+c+'" is trying to run more than once.';g("Log").warn(k);b({error_code:-100,error_message:k});return}b=p(b,c)}k={cb:b,id:m,size:l.size||r.getDefaultSize(),url:g("UrlMap").resolve(a.fx_app==="instagram"||a.fx_app==="ig_single"?"www_instagram":a.display=="touch"?"m":"www")+"/"+l.url,params:a,name:c,dialog:d("sdk.Dialog").newInstance(m,a.display)};b=l.transform?l.transform:r.genericTransform;if(b){k=b(k);if(!k)return}a.display==="touch"&&a.in_iframe&&(k.params.parent_height=window.innerHeight);c=l.getXdRelation||r.getXdRelation;b=c(k.params);!(k.id in r._defaultCb)&&!("next"in k.params)&&!("redirect_uri"in k.params)&&(k.params.next=r._xdResult(k.cb,k.id,b,!0));(b==="parent"||b==="opener")&&e("Object","assign",!1,k.params,{channel_url:r._xdChannelHandler(m,b==="parent"?"parent.parent":"opener")},!0);k=r.prepareParams(k);return k},prepareParams:function(a){a.params.display!=="async"&&delete a.params.method;a.params.kid_directed_site=d("sdk.Runtime").getKidDirectedSite()||a.params.kid_directed_site;a.params=d("flattenObject")(a.params);var b=d("QueryString").encode(a.params);!d("sdk.UA").nativeApp()&&r.urlTooLongForIE(a.url+"?"+b)?a.post=!0:b&&(a.url+="?"+b);return a},urlTooLongForIE:function(a){return d("sdk.UA").ie()!=null&&d("sdk.UA").ie()<=8&&a.length>2048},getDisplayMode:function(b,c){if(c.display==="hidden"||c.display==="none"||c.display==="native")return c.display;var e=d("sdk.Runtime").isEnvironment(d("sdk.Runtime").ENVIRONMENTS.CANVAS)||d("sdk.Runtime").isEnvironment(d("sdk.Runtime").ENVIRONMENTS.PAGETAB);if(e&&(g("sdk.modFeatureCheck").forIDs("force_popup_to_canvas_apps_with_id",[d("sdk.Runtime").getClientID()])||d("sdk.feature")("force_popup_to_all_canvas_app",!1)))return"popup";if(e&&!c.display)return"async";if(a(c)||l(c))return"async";if(d("sdk.UA").mobile()||c.display==="touch")return"touch";if((c.display=="iframe"||c.display=="dialog")&&!r.canIframe(c)){g("Log").error('"dialog" mode can only be used when the user is connected.');return"popup"}return b.connectDisplay&&!e?b.connectDisplay:c.display||(r.canIframe(c)?"dialog":"popup")},canIframe:function(a){return d("sdk.Runtime").getAccessToken()},getXdRelation:function(a){a=a.display;if(a==="popup"||a==="touch")return"opener";return a==="dialog"||a==="iframe"||a==="hidden"||a==="none"?"parent":a==="async"?"parent.frames["+window.name+"]":""},popup:function(a){var b=g("sdk.Popup").popup(a,r.isOAuth({method:a.name}));b&&(r.setLoadedNode(a,b,"popup"),a.id in r._defaultCb&&r._popupMonitor())},setLoadedNode:function(a,b,c){c==="iframe"&&(b.fbCallID=a.id),b={node:b,type:c,fbCallID:a.id,method:a.name,params:a.params},r._loadedNodes[a.id]=b},getLoadedNode:function(a){a=typeof a==="object"?a.id:a;a=r._loadedNodes[a];return a?a.node:null},hidden:function(a){a.className="FB_UI_Hidden",a.root=g("sdk.Content").appendHidden(document.createElement("div")),r._insertIframe(a)},iframe:function(a){a.className="FB_UI_Dialog";var b=function(){var b=e("JSON","stringify",!1,{error_code:4201,error_message:d("sdk.fbt")._("User canceled the Dialog flow")});r._triggerDefault(a.id,b)},c={onClose:b,closeIcon:a.closeIcon===void 0?!0:a.closeIcon,classes:d("sdk.Dialog").isTabletStyle()?"centered":""};a.root=d("sdk.Dialog").create(c);a.hideLoader||d("sdk.Dialog").showLoader(b,a.size.width);g("sdk.DOM").addCss(a.root,"fb_dialog_iframe");r._insertIframe(a)},touch:function(a){a.params&&a.params.in_iframe?a.ui_created?d("sdk.Dialog").showLoader(function(){r._triggerDefault(a.id,null)},0):r.iframe(a):d("sdk.UA").nativeApp()&&!a.ui_created?(a.frame=a.id,d("sdk.Native").onready(function(){r.setLoadedNode(a,d("sdk.Native").open(a.url+"#cb="+a.frameName),"native")}),r._popupMonitor()):a.ui_created||r.popup(a)},async:function(b){b.params.redirect_uri=location.protocol+"//"+location.host+location.pathname;delete b.params.access_token;b.params.is_canvas=d("sdk.Runtime").isCanvasEnvironment();var c=function(c){c=c.result;if(c&&c.e2e){var a=d("sdk.Dialog").get(b.id);a.trackEvents(c.e2e);a.trackEvent("close");delete c.e2e}b.cb(c)};a(b.params)||l(b.params)?(b.params.method="oauth",b.params.redirect_uri=b.params.next,g("sdk.Extensions").remote.showDialog(b.params,c)):d("sdk.RPC").remote.showDialog(b.params,c)},"native":function(a){d("sdk.openMessenger")(a.params)},getDefaultSize:function(){return d("sdk.Dialog").getDefaultSize()},_insertIframe:function(a){r._loadedNodes[a.id]=!1;var b=function(b){a.id in r._loadedNodes&&r.setLoadedNode(a,b,"iframe")};a.post?d("insertIframe")({url:"about:blank",root:a.root,className:a.className,width:a.size.width,height:a.size.height,id:a.id,onInsert:b,onload:function(b){g("sdk.Content").submitToTarget({url:a.url,target:b.name,params:a.params})}}):d("insertIframe")({url:a.url,root:a.root,className:a.className,width:a.size.width,height:a.size.height,id:a.id,name:a.frameName,onInsert:b})},_handleResizeMessage:function(a,b){a=r.getLoadedNode(a);if(!a)return;b.height&&(a.style.height=b.height+"px");b.width&&b.width!=0&&(a.style.width=b.width+"px");d("sdk.XD").inform("resize.ack",b||{},"parent.frames["+a.name+"]");d("sdk.Dialog").isActive(a)?d("sdk.Dialog")._centerActive():d("sdk.Dialog").show(a)},_triggerDefault:function(a,b){var c={frame:a,result:""};b&&(c.result=b);r._xdRecv(c,r._defaultCb[a]||function(){})},_popupMonitor:function(){var a;for(var b in r._loadedNodes)if(Object.prototype.hasOwnProperty.call(r._loadedNodes,b)&&b in r._defaultCb){var c=function(){var c=r._loadedNodes[b];if(c.type!="popup"&&c.type!="native")return"continue";var e=c.node;try{e.closed?r.isOAuth(c)?d("sdk.Auth.LoginStatus").getLoginStatus(function(a){(a==null?void 0:a.status)==="connected"&&c.params!=null&&c.params.return_scopes?d("sdk.api")("/me/permissions",function(c){(!c||c.error)&&r._triggerDefault(b,a);var d="";c=c&&c.data?c.data:[];for(var e=0;e<c.length;e++)c[e].status==="granted"&&(d!==""&&(d+=","),d+=c[e].permission);a.authResponse&&a.authResponse.grantedScopes&&(a.authResponse.grantedScopes=d);r._triggerDefault(b,a)}):((a==null?void 0:a.status)!=="connected"&&(a.closeWindow=!0),r._triggerDefault(b,a))},!0):r._triggerDefault(b,null):a=!0}catch(a){}}();if(c==="continue")continue}a&&!r._popupInterval?r._popupInterval=window.setInterval(r._popupMonitor,100):!a&&r._popupInterval&&(window.clearInterval(r._popupInterval),r._popupInterval=null)},_xdChannelHandler:function(a,b){return d("sdk.XD").handler(function(b){var c=r.getLoadedNode(a);if(!c)return;b.type=="resize"?r._handleResizeMessage(a,b):b.type=="hide"?d("sdk.Dialog").hide(c):b.type=="rendered"?(c=d("sdk.Dialog")._findRoot(c),d("sdk.Dialog").show(c)):b.type=="fireevent"&&g("sdk.Event").fire(b.event,b)},b,!0,null)},_xdNextHandler:function(a,b,c,e){e&&(r._defaultCb[b]=a);return d("sdk.XD").handler(function(b){r._xdRecv(b,a)},c)+"&frame="+b},_xdRecv:function(a,b){var c=r.getLoadedNode(a.frame);if(c)if(c.close)try{c.close(),/iPhone.*Version\/(5|6)/.test(navigator.userAgent)&&RegExp.$1!=="5"&&window.focus(),r._popupCount--}catch(a){}else g("sdk.DOM").containsCss(c,"FB_UI_Hidden")?window.setTimeout(function(){c.parentNode.parentNode.removeChild(c.parentNode)},3e3):g("sdk.DOM").containsCss(c,"FB_UI_Dialog")&&d("sdk.Dialog").remove(c);delete r._loadedNodes[a.frame];delete r._defaultCb[a.frame];if(a.e2e){var e=d("sdk.Dialog").get(a.frame);e.trackEvents(a.e2e);e.trackEvent("close");delete a.e2e}b(a)},_xdResult:function(a,b,c,d){return r._xdNextHandler(function(b){a&&a(b.result&&b.result!=r._resultToken&&e("JSON","parse",!1,b.result))},b,c,d)+"&result="+encodeURIComponent(r._resultToken)},xdHandler:function(a,b,c,e,f,l,m){return r._xdNextHandler(d("sdk.Auth").xdResponseWrapper(a,e,f,m),b,c,l)},xdHandlerPhotoPicker:function(a,b,c,d,e){return r._xdNextHandler(r.xdResponseWrapperPhotoPicker(a),b,c,!1)},xdResponseWrapperPhotoPicker:function(a){return function(b){var c;b&&b.result&&b.result.closeWindow?c="Photo picker call was cancelled by the user":c=b.photos;a&&a(c);return null}}};g("sdk.Extensions").stub("showDialog");d("sdk.RPC").stub("showDialog");c=r;j["default"]=c},98);c.__d("sdk.ui",["Assert","Log","sdk.Impressions","sdk.PlatformVersioning","sdk.Runtime","sdk.UIServer","sdk.feature"],function(a,b,c,d,g,h,i){function a(a,b){c("Assert").isObject(a);c("Assert").maybeFunction(b);c("sdk.Runtime").getIsVersioned()&&(d("sdk.PlatformVersioning").assertVersionIsSet(),a.version?d("sdk.PlatformVersioning").assertValidVersion(a.version):a.version=c("sdk.Runtime").getVersion());a=f["extends"]({},a);if(!a.method){d("Log").error('"method" is a required parameter for FB.ui().');return null}a.method=="pay.prompt"&&(a.method="pay");var g=a.method;a.redirect_uri&&(d("Log").warn("When using FB.ui, you should not specify a redirect_uri."),delete a.redirect_uri);a.fallback_redirect_uri||(a.fallback_redirect_uri=document.location.href);c("sdk.UIServer").isOAuth(g)&&(a.display=="iframe"||a.display=="dialog")&&(a.display=c("sdk.UIServer").checkOauthDisplay(a));if(a.display==="native"&&g!=="send"){d("Log").error('display type "native" not supported');return null}var h=c("sdk.feature")("e2e_tracking",!0);h&&(a.e2e={});a=c("sdk.UIServer").prepareCall(a,b||function(){});if(!a)return null;var i=a.params.display;i==="dialog"?i="iframe":i==="none"&&(i="hidden");b=c("sdk.UIServer")[i];if(!b){d("Log").error('"display" must be one of "popup", "dialog", "iframe", "touch", "async", "hidden", or "none"');return null}h&&a.dialog.subscribe("e2e:end",function(a){a.method=g,a.display=i,d("Log").debug("e2e: %s",e("JSON","stringify",!1,a)),d("sdk.Impressions").log(114,{payload:a})});b(a);return a.dialog}i["default"]=a},98);c.__d("sdk.Auth-public",["FB","sdk.Auth","sdk.Auth.LoginStatus","sdk.AuthUtils","sdk.Event","sdk.Runtime","sdk.ui","sdk.warnInsecure"],function(a,b,c,d,f,g,h){"use strict";function a(){c("FB").provide("",{getLoginStatus:function(){c("sdk.warnInsecure")("getLoginStatus");return c("sdk.Auth.LoginStatus").getLoginStatus.apply(c("sdk.Auth"),arguments)},getAuthResponse:function(){c("sdk.warnInsecure")("getAuthResponse");return c("sdk.Auth").getAuthResponse()},getAccessToken:function(){c("sdk.warnInsecure")("getAccessToken");return c("sdk.Runtime").getAccessToken()||null},getUserID:function(){c("sdk.warnInsecure")("getUserID");return c("sdk.Runtime").getUserID()||c("sdk.Runtime").getCookieUserID()},login:function(a,b){c("sdk.warnInsecure")("login"),c("sdk.Auth").login(a,b)},logout:function(a){c("sdk.ui")({method:"auth.logout",display:"hidden"},a)}}),c("sdk.Auth").subscribe("logout",e(d("sdk.Event").fire,"bind",!0,d("sdk.Event"),"auth.logout")),c("sdk.Auth").subscribe("login",e(d("sdk.Event").fire,"bind",!0,d("sdk.Event"),"auth.login")),c("sdk.Auth").subscribe("authresponse.change",e(d("sdk.Event").fire,"bind",!0,d("sdk.Event"),"auth.authResponseChange")),c("sdk.Auth").subscribe("status.change",e(d("sdk.Event").fire,"bind",!0,d("sdk.Event"),"auth.statusChange")),c("sdk.Auth").subscribe("loginDenied",e(d("sdk.Event").fire,"bind",!0,d("sdk.Event"),"auth.denied")),c("sdk.Auth").subscribe("loginError",e(d("sdk.Event").fire,"bind",!0,d("sdk.Event"),"auth.error")),c("sdk.Runtime").subscribe("AccessToken.change",function(a){!a&&c("sdk.Runtime").getLoginStatus()==="connected"&&c("sdk.Auth.LoginStatus").getLoginStatus(null,!0)}),d("sdk.AuthUtils").AuthInternalEvent.subscribe(d("sdk.AuthUtils").AuthConstants.REVALIDATE_TIMER_TIMEOUT,function(a){c("sdk.Auth.LoginStatus").fetchLoginStatus(function(){})}),d("sdk.Event").subscribe("init:post",function(a){c("sdk.Auth.LoginStatus").onSDKInit(a)})}b={init:a};f=b;h["default"]=f},98);c.__d("sdk.Canvas.IframeHandling",["DOMWrapper","sdk.RPC"],function(b,c,d,e,f,g,h){var i=null,a;function j(){var a=e("DOMWrapper").getWindow().document,b=a.body;a=a.documentElement;var c=Math.max(b.offsetTop,0),d=Math.max(a.offsetTop,0),f=b.scrollHeight+c;b=b.offsetHeight+c;c=a.scrollHeight+d;a=a.offsetHeight+d;return Math.max(f,b,c,a)}function k(b){typeof b!=="object"&&(b={});var c=0,e=0;b.height||(b.height=j(),c=16,e=4);b.frame||(b.frame=window.name||"iframe_canvas");if(a){var f=a.height;f=b.height-f;if(f<=e&&f>=-c)return!1}a=b;d("sdk.RPC").remote.setSize(b);return!0}function b(a,b){b===void 0&&typeof a==="number"&&(b=a,a=!0),a||a===void 0?(i===null&&(i=window.setInterval(function(){k()},b||100)),k()):i!==null&&(window.clearInterval(i),i=null)}d("sdk.RPC").stub("setSize");c={setSize:k,setAutoGrow:b};f=c;h["default"]=f},98);c.__d("sdk.Canvas.Navigation",["sdk.RPC"],function(a,b,c,d,e,f){function a(a){b("sdk.RPC").local.navigate=function(b){a({path:b})},b("sdk.RPC").remote.setNavigationEnabled(!0)}b("sdk.RPC").stub("setNavigationEnabled");c={setUrlHandler:a};d=c;f["default"]=d},66);c.__d("sdk.Canvas.Plugin",["Log","sdk.Runtime","sdk.UA","sdk.api"],function(b,c,d,f,g,h,i){var j="CLSID:D27CDB6E-AE6D-11CF-96B8-444553540000",a="CLSID:444785F1-DE89-4295-863A-D46C3A781394",k=null;h=d("sdk.UA").osx()&&d("sdk.UA").osx.getVersionParts();var l=!(h&&h[0]>10&&h[1]>10&&(d("sdk.UA").chrome()>=31||d("sdk.UA").webkit()>=537.71||d("sdk.UA").firefox()>=25));function m(a){a._hideunity_savedstyle={},a._hideunity_savedstyle.left=a.style.left,a._hideunity_savedstyle.position=a.style.position,a._hideunity_savedstyle.width=a.style.width,a._hideunity_savedstyle.height=a.style.height,a.style.left="-10000px",a.style.position="absolute",a.style.width="1px",a.style.height="1px"}function n(a){a._hideunity_savedstyle&&(a.style.left=a._hideunity_savedstyle.left,a.style.position=a._hideunity_savedstyle.position,a.style.width=a._hideunity_savedstyle.width,a.style.height=a._hideunity_savedstyle.height)}function o(a){a._old_visibility=a.style.visibility,a.style.visibility="hidden"}function p(a){a.style.visibility=a._old_visibility||"",delete a._old_visibility}function q(a){var b=a.type?a.type.toLowerCase():null;b=b==="application/x-shockwave-flash"||a.classid&&a.classid.toUpperCase()==j;if(!b)return!1;b=/opaque|transparent/i;if(b.test(a.getAttribute("wmode")))return!1;for(var c=0;c<a.childNodes.length;c++){var d=a.childNodes[c];if(/param/i.test(d.nodeName)&&/wmode/i.test(d.name)&&b.test(d.value))return!1}return!0}function r(b){var c=b.type?b.type.toLowerCase():null;return c==="application/vnd.unity"||b.classid&&b.classid.toUpperCase()==a}function b(a){var b=e("Array","from",!1,window.document.getElementsByTagName("object"));b=b.concat(e("Array","from",!1,window.document.getElementsByTagName("embed")));var c=!1,h=!1;b.forEach(function(b){var d=q(b),e=l&&r(b);if(!d&&!e)return;c=c||d;h=h||e;e=function(){a.state==="opened"?d?o(b):m(b):d?p(b):n(b)};if(k){f("Log").info("Calling developer specified callback");var g={state:a.state,elem:b};k(g);window.setTimeout(e,200)}else e()});Math.random()<=1/1e3&&(b={unity:h,flash:c},d("sdk.api")(d("sdk.Runtime").getClientID()+"/occludespopups","post",b))}function c(){o(),m()}function g(){p(),n()}h={_setHidePluginCallback:function(a){k=a},hidePluginCallback:b,hidePluginElement:c,showPluginElement:g};b=h;i["default"]=b},98);c.__d("sdk.Canvas.Prefetcher",["JSSDKCanvasPrefetcherConfig","sdk.Runtime","sdk.api"],function(b,c,d,f,g,h,i){var j={AUTOMATIC:0,MANUAL:1},a=(h=f("JSSDKCanvasPrefetcherConfig").excludedAppIds)!=null?h:[],k=j.AUTOMATIC,l=[];function m(){var a={object:"data",link:"href",script:"src"};k==j.AUTOMATIC&&Object.keys(a).forEach(function(b){var c=a[b];e("Array","from",!1,document.getElementsByTagName(b)).forEach(function(a){a[c]&&l.push(a[c])})});if(l.length===0)return;d("sdk.api")(d("sdk.Runtime").getClientID()+"/staticresources","post",{urls:e("JSON","stringify",!1,l),is_https:location.protocol==="https:"});l=[]}function b(){if(!d("sdk.Runtime").isEnvironment(d("sdk.Runtime").ENVIRONMENTS.CANVAS)||!d("sdk.Runtime").getClientID()||!f("JSSDKCanvasPrefetcherConfig").sampleRate)return;if(Math.random()>=1/f("JSSDKCanvasPrefetcherConfig").sampleRate||!f("JSSDKCanvasPrefetcherConfig").enabled||e(a,"includes",!0,d("sdk.Runtime").getClientID()))return;setTimeout(m,3e4)}function c(a){k=a}function g(a){l.push(a)}h={COLLECT_AUTOMATIC:j.AUTOMATIC,COLLECT_MANUAL:j.MANUAL,addStaticResource:g,setCollectionMode:c,_maybeSample:b};g=h;i["default"]=g},98);c.__d("sdk.Canvas.Tti",["sdk.RPC","sdk.Runtime"],function(a,b,c,d,e,f,g){function h(a,b){b={appId:c("sdk.Runtime").getClientID(),time:Date.now(),name:b},b=[b],a&&b.push(function(b){a(b.result)}),c("sdk.RPC").remote.logTtiMessage.apply(null,b)}function a(){h(null,"StartIframeAppTtiTimer")}function b(a){h(a,"StopIframeAppTtiTimer")}function d(a){h(a,"RecordIframeAppTti")}c("sdk.RPC").stub("logTtiMessage");e={setDoneLoading:d,startTimer:a,stopTimer:b};f=e;g["default"]=f},98);c.__d("sdk.Canvas-public",["Assert","FB","Log","sdk.Canvas.Environment","sdk.Canvas.IframeHandling","sdk.Canvas.Navigation","sdk.Canvas.Plugin","sdk.Canvas.Prefetcher","sdk.Canvas.Tti","sdk.Event","sdk.RPC","sdk.Runtime"],function(a,b,c,d,f,g,h){function a(){c("FB").provide("Canvas",{setSize:function(a){c("Assert").maybeObject(a,"Invalid argument");return c("sdk.Canvas.IframeHandling").setSize.apply(null,arguments)},setAutoGrow:function(){return c("sdk.Canvas.IframeHandling").setAutoGrow.apply(null,arguments)},getPageInfo:function(a){c("Assert").isFunction(a,"Invalid argument");return c("sdk.Canvas.Environment").getPageInfo.apply(null,arguments)},scrollTo:function(a,b){c("Assert").maybeNumber(a,"Invalid argument");c("Assert").maybeNumber(b,"Invalid argument");return c("sdk.Canvas.Environment").scrollTo.apply(null,arguments)},setDoneLoading:function(a){c("Assert").maybeFunction(a,"Invalid argument");return c("sdk.Canvas.Tti").setDoneLoading.apply(null,arguments)},startTimer:function(){return c("sdk.Canvas.Tti").startTimer.apply(null,arguments)},stopTimer:function(a){c("Assert").maybeFunction(a,"Invalid argument");return c("sdk.Canvas.Tti").stopTimer.apply(null,arguments)},setUrlHandler:function(a){c("Assert").isFunction(a,"Invalid argument");return c("sdk.Canvas.Navigation").setUrlHandler.apply(null,arguments)}}),c("sdk.RPC").local.fireEvent=e(d("sdk.Event").fire,"bind",!0,d("sdk.Event")),d("sdk.Event").subscribe("init:post",function(a){c("sdk.Runtime").isEnvironment(c("sdk.Runtime").ENVIRONMENTS.CANVAS)&&(c("Assert").isTrue(!a.hideFlashCallback||!a.hidePluginCallback,"cannot specify deprecated hideFlashCallback and new hidePluginCallback"),c("sdk.Canvas.Plugin")._setHidePluginCallback(a.hidePluginCallback||a.hideFlashCallback))})}function i(){c("sdk.RPC").local.hidePluginObjects=function(){d("Log").info("hidePluginObjects called"),c("sdk.Canvas.Plugin").hidePluginCallback({state:"opened"})},c("sdk.RPC").local.showPluginObjects=function(){d("Log").info("showPluginObjects called"),c("sdk.Canvas.Plugin").hidePluginCallback({state:"closed"})},c("sdk.RPC").local.showFlashObjects=c("sdk.RPC").local.showPluginObjects,c("sdk.RPC").local.hideFlashObjects=c("sdk.RPC").local.hidePluginObjects}function b(){i(),c("FB").provide("Canvas.Plugin",c("sdk.Canvas.Plugin"))}function f(){c("FB").provide("Canvas.Prefetcher",c("sdk.Canvas.Prefetcher")),d("sdk.Event").subscribe("init:post",function(a){c("sdk.Runtime").isEnvironment(c("sdk.Runtime").ENVIRONMENTS.CANVAS)&&c("sdk.Canvas.Prefetcher")._maybeSample()})}function g(){d("sdk.Event").subscribe(d("sdk.Event").SUBSCRIBE,a);d("sdk.Event").subscribe(d("sdk.Event").UNSUBSCRIBE,b);c("sdk.RPC").stub("useFriendsOnline");function a(a,b){if(a!="canvas.friendsOnlineUpdated")return;b.length===1&&c("sdk.RPC").remote.useFriendsOnline(!0)}function b(a,b){if(a!="canvas.friendsOnlineUpdated")return;b.length===0&&c("sdk.RPC").remote.useFriendsOnline(!1)}}a={init:a,initCanvasPlugin:b,initCanvasPrefetcher:f,initCanvasPresence:g,initRPC:i};b=a;h["default"]=b},98);c.__d("sdk.Event-public",["FB","Log","sdk.Event"],function(a,b,c,d,f,g,h){function a(){var a=function(a){return d("Log").error("FB.Event."+a+"() has been deprecated")};c("FB").provide("Event",{subscribe:function(a,b){return d("sdk.Event").subscribe(a,b)},unsubscribe:e(d("sdk.Event").unsubscribe,"bind",!0,d("sdk.Event")),clear:e(a,"bind",!0,null,"clear"),fire:e(a,"bind",!0,null,"fire")})}b={init:a};f=b;h["default"]=f},98);c.__d("sdk.Frictionless-public",["FB","sdk.Event","sdk.Frictionless"],function(a,b,c,d,e,f,g){"use strict";function a(){d("sdk.Event").subscribe("init:post",function(a){a.frictionlessRequests&&c("sdk.Frictionless").init()}),c("FB").provide("Frictionless",c("sdk.Frictionless"))}b={init:a};e=b;g["default"]=e},98);c.__d("sdk.GamingServices",["sdk.api","sdk.ui"],function(a,b,c,d,e,f,g){function a(a){c("sdk.ui")({display:"touch",method:"gaming_friendfinder"},a)}function b(a,b,d,e){c("sdk.api")("me/photos","POST",{caption:b,url:a},function(a){if(d===!1||!a||a.error)e!==null&&e(a);else{var b=a.id;c("sdk.ui")({display:"touch",method:"gaming_media_library",media_id:b},function(b){e!==null&&e(a)})}})}d={friendFinder:a,uploadImageToMediaLibrary:b};e=d;g["default"]=e},98);c.__d("sdk.GamingServices-public",["FB","sdk.GamingServices"],function(a,b,c,d,e,f,g){"use strict";function a(){c("FB").provide("",{gamingservices:c("sdk.GamingServices")})}b={init:a};d=b;g["default"]=d},98);c.__d("sdk.PluginUtils",["resolveURI","sdk.Event"],function(b,c,d,e,f,g,h){var i={string:function(a){return a},bool:function(a){return a!=null?/^(?:true|1|yes|on)$/i.test(a):void 0},url:function(a){return d("resolveURI")(a)},url_maybe:function(a){return a!=null&&a!==""?d("resolveURI")(a):void 0},hostname:function(a){return a!=null&&a!==""?a:"window.location.hostname"},px:function(a){if(typeof a==="string"){var b=a.match(/^(\d+)(?:px)?$/);return b!=null?parseInt(b[0],10):void 0}else if(typeof a==="number")return a;else return void 0},text:function(a){return a}};function a(a,b){var c;return(c=(c=(c=(c=(c=(c=a[b])!=null?c:a[b.replace(/_/g,"-")])!=null?c:a[b.replace(/_/g,"")])!=null?c:a["data-"+b])!=null?c:a["data-"+b.replace(/_/g,"-")])!=null?c:a["data-"+b.replace(/_/g,"")])!=null?c:void 0}function b(b,c,d,e){Object.keys(b).forEach(function(f){if(b[f]==="text"&&!d[f]){var h;d[f]=(h=(h=c.textContent)!=null?h:c.innerText)!=null?h:void 0;c.setAttribute(f,d[f])}e[f]=i[b[f]](a(d,f))})}function j(a,b,c){b==="100%"?a.style.width="100%":b!=null&&b!==""&&(a.style.width=b+"px"),(c!=null&&c!==""||c===0)&&(a.style.height=c+"px")}function c(a){return function(b){b={width:b.width,height:b.height,pluginID:a},e("sdk.Event").fire("xfbml.resize",b)}}function f(a){return a==="100%"?"100%":a!=null?parseInt(a,10):void 0}function g(a){a!=null&&j(a,0,0)}var k={skin:"string",font:"string",width:"string",height:"px",ref:"string",lazy:"bool",color_scheme:"string"};h.getVal=a;h.validate=b;h.resize=j;h.resizeBubbler=c;h.parse=f;h.collapseIframe=g;h.baseParams=k},98);c.__d("isNumberLike",[],function(a,b,c,d,e,f){function a(a){return!isNaN(parseFloat(a))&&isFinite(a)}f["default"]=a},66);c.__d("sdk.createIframe",["DOMEventListener","getBlankIframeSrc","guid","isNumberLike"],function(a,b,c,d,f,g,h){function a(b){var f=e("Object","assign",!1,{},b),i,g=f.name||c("guid")(),h=f.root,j=f.style||{border:"none"},a=f.url,k=f.onload,l=f.onerror;i=document.createElement("iframe");i.name=g;delete f.style;delete f.name;delete f.url;delete f.root;delete f.onload;delete f.onerror;delete f.height;delete f.width;f.frameBorder===void 0&&(f.frameBorder=0);f.allowTransparency===void 0&&(f.allowTransparency=!0);f.allowFullscreen===void 0&&(f.allowFullscreen=!0);f.scrolling===void 0&&(f.scrolling="no");f.allow===void 0&&(f.allow="encrypted-media");f.lazy&&(f.loading="lazy",j.visibility&&delete j.visibility);delete f.lazy;b.width!=null&&c("isNumberLike")(b.width)&&(i.width=b.width+"px");b.height!=null&&c("isNumberLike")(b.height)&&(i.height=b.height+"px");f.testid&&i.dataset!=null&&(i.dataset.testid=f.testid,delete f.testid);for(g in f)Object.prototype.hasOwnProperty.call(f,g)&&i.setAttribute(g,f[g]);e("Object","assign",!1,i.style,j);i.src=c("getBlankIframeSrc")();h!=null&&h.appendChild(i);if(k)var m=d("DOMEventListener").add(i,"load",function(){m.remove(),k()});if(l)var n=d("DOMEventListener").add(i,"error",function(){n.remove(),l()});i.src=a;return i}h["default"]=a},98);c.__d("IframePlugin",["Log","ObservableMixin","QueryString","Type","UrlMap","guid","sdk.Auth.LoginStatus","sdk.AuthUtils","sdk.DOM","sdk.Event","sdk.PlatformVersioning","sdk.PluginUtils","sdk.Runtime","sdk.UA","sdk.URI","sdk.XD","sdk.createIframe"],function(a,b,c,d,g,h,i){var j=c("Type").extend({constructor:function(b,f,g,h){var i=this;this.parent();g=g.replace(/-/g,"_");var j=d("sdk.PluginUtils").getVal(h,"plugin_id");this.subscribe("xd.resize",d("sdk.PluginUtils").resizeBubbler(j));this.subscribe("xd.resize.flow",d("sdk.PluginUtils").resizeBubbler(j));this.subscribe("xd.resize.flow",function(a){e("Object","assign",!1,i._iframeOptions.root.style,{verticalAlign:"bottom",overflow:""}),d("sdk.PluginUtils").resize(i._iframeOptions.root,d("sdk.PluginUtils").parse(a.width),d("sdk.PluginUtils").parse(a.height)),i.updateLift(),window.clearTimeout(i._timeoutID)});this.subscribe("xd.resize",function(a){e("Object","assign",!1,i._iframeOptions.root.style,{verticalAlign:"bottom",overflow:""}),d("sdk.PluginUtils").resize(i._iframeOptions.root,d("sdk.PluginUtils").parse(a.width),d("sdk.PluginUtils").parse(a.height)),d("sdk.PluginUtils").resize(i._iframe,d("sdk.PluginUtils").parse(a.width),d("sdk.PluginUtils").parse(a.height)),i._isIframeResized=!0,i.updateLift(),window.clearTimeout(i._timeoutID)});this.subscribe("xd.resize.iframe",function(a){d("sdk.PluginUtils").resize(i._iframe,d("sdk.PluginUtils").parse(a.width),d("sdk.PluginUtils").parse(a.height)),i._isIframeResized=!0,i.updateLift(),window.clearTimeout(i._timeoutID)});this.subscribe("xd.sdk_event",function(a){var c=e("JSON","parse",!1,a.data);c.pluginID=j;d("sdk.Event").fire(a.event,c,b)});var a=d("UrlMap").resolve("www")+"/plugins/"+g+".php?",k={};d("sdk.PluginUtils").validate(this.getParams(),b,h,k);d("sdk.PluginUtils").validate(d("sdk.PluginUtils").baseParams,b,h,k);e("Object","assign",!1,k,{app_id:c("sdk.Runtime").getClientID(),locale:c("sdk.Runtime").getLocale(),sdk:"joey",kid_directed_site:c("sdk.Runtime").getKidDirectedSite(),channel:d("sdk.XD").handler(function(a){a!=null&&i.inform("xd."+a.type,a)},"parent.parent",!0)});this.shouldIgnoreWidth()&&(k.width=void 0);k.container_width=b.offsetWidth;d("sdk.DOM").addCss(b,"fb_iframe_widget");var l=c("guid")();this.subscribe("xd.verify",function(a){d("sdk.XD").sendToFacebook(l,{method:"xd/verify",params:e("JSON","stringify",!1,a.token)})});this.subscribe("xd.refreshLoginStatus",function(){d("sdk.AuthUtils").removeLogoutState(),c("sdk.Auth.LoginStatus").getLoginStatus(e(i.inform,"bind",!0,i,"login.status"),!0)});h=document.createElement("span");e("Object","assign",!1,h.style,{verticalAlign:"top",width:k.lazy?"1px":"0px",height:k.lazy?"1px":"0px",overflow:"hidden"});this._element=b;this._ns=f;this._tag=g;this._params=k;this._config=this.getConfig();this._iframeOptions={root:h,url:a+c("QueryString").encode(k),name:l,width:this._config.mobile_fullsize&&c("sdk.UA").mobile()?void 0:k.width||1e3,height:k.height||1e3,style:{border:"none",visibility:"hidden"},title:this._ns+":"+this._tag+" Facebook Social Plugin",testid:this._ns+":"+this._tag+" Facebook Social Plugin",onload:function(){return i.inform("render")},onerror:function(){return d("sdk.PluginUtils").collapseIframe(i._iframe)},lazy:k.lazy};this.isFluid()&&k.width!=="auto"&&(d("sdk.DOM").addCss(this._element,"fb_iframe_widget_fluid_desktop"),!k.width&&this._config.full_width&&(this._element.style.width="100%",this._iframeOptions.root.style.width="100%",this._iframeOptions.style.width="100%",this._params.container_width=this._element.offsetWidth,this._iframeOptions.url=a+c("QueryString").encode(this._params)))},shouldIgnoreWidth:function(){return c("sdk.UA").mobile()&&this.getConfig().mobile_fullsize},useInlineHeightForMobile:function(){return!0},process:function(){var a=this;if(c("sdk.Runtime").getIsVersioned()){d("sdk.PlatformVersioning").assertVersionIsSet();var b=new(c("sdk.URI"))(this._iframeOptions.url);this._iframeOptions.url=b.setPath("/"+c("sdk.Runtime").getVersion()+b.getPath()).toString()}b=f["extends"]({},this._params);delete b.channel;var i=c("QueryString").encode(b);if(this._element.getAttribute("fb-iframe-plugin-query")==i){d("Log").info("Skipping render: %s:%s %s",this._ns,this._tag,i);this.inform("render");return}this._element.setAttribute("fb-iframe-plugin-query",i);this.subscribe("render",function(){d("sdk.Event").fire("iframeplugin:onload"),a._iframe.style.visibility="visible",a._isIframeResized||d("sdk.PluginUtils").collapseIframe(a._iframe)});while(this._element.firstChild)this._element.removeChild(this._element.firstChild);this._element.appendChild(this._iframeOptions.root);var j=c("sdk.UA").mobile()?120:45;this._timeoutID=window.setTimeout(function(){d("sdk.PluginUtils").collapseIframe(a._iframe),d("Log").warn("%s:%s failed to resize in %ss",a._ns,a._tag,j)},j*1e3);this._iframe=c("sdk.createIframe")(this._iframeOptions);d("sdk.Event").fire("iframeplugin:create");(c("sdk.UA").mobile()||b.width==="auto")&&(this.useInlineHeightForMobile()&&d("sdk.DOM").addCss(this._element,"fb_iframe_widget_fluid"),this._iframeOptions.width||(e("Object","assign",!1,this._element.style,{display:"block",width:"100%",height:"auto"}),e("Object","assign",!1,this._iframeOptions.root.style,{width:"100%",height:"auto"}),i={height:"auto",position:"static",width:"100%"},(c("sdk.UA").iphone()||c("sdk.UA").ipad())&&e("Object","assign",!1,i,{width:"220px","min-width":"100%"}),e("Object","assign",!1,this._iframe.style,i)))},getConfig:function(){return{}},isFluid:function(){var a=this.getConfig();return a.fluid},updateLift:function(){var a=this._iframe.style.width===this._iframeOptions.root.style.width&&this._iframe.style.height===this._iframeOptions.root.style.height;d("sdk.DOM")[a?"removeCss":"addCss"](this._iframe,"fb_iframe_widget_lift")}},c("ObservableMixin"));j.withParams=function(a,b){return j.extend({getParams:function(){return a},getConfig:function(){return b?b:{}}})};a=j;i["default"]=a},98);c.__d("PluginConfig",["sdk.feature"],function(a,b,c,d,e,f,g){a={mobile_fullsize:!0},b={mobile_fullsize:!0},d={mobile_fullsize:!0},e={mobile_fullsize:!0},f={mobile_fullsize:!0},c={fluid:c("sdk.feature")("fluid_embed",!1),mobile_fullsize:!0},a={comment_embed:a,messengerpreconfirmation:b,messengeraccountconfirmation:d,messengerbusinesslink:e,messengertoggle:f,post:c},b=a,g["default"]=b},98);c.__d("PluginAttrTypes",[],function(a,b,c,d,e,f){"use strict";a="string",b="bool",c="url",f.string=a,f.bool=b,f.url=c},66);c.__d("PluginTags",["PluginAttrTypes"],function(b,c,d,e,f,g,h){var i={ad_library_spend_tracker:{country:e("PluginAttrTypes").string,time_preset:e("PluginAttrTypes").string,custom_start_date:e("PluginAttrTypes").string,custom_end_date:e("PluginAttrTypes").string,race_type:e("PluginAttrTypes").string,state:e("PluginAttrTypes").string,district:e("PluginAttrTypes").string,page_ids:e("PluginAttrTypes").string,include_vps:e("PluginAttrTypes").bool},comment_embed:{href:e("PluginAttrTypes").url,include_parent:e("PluginAttrTypes").bool},composer:{action_type:e("PluginAttrTypes").string,action_properties:e("PluginAttrTypes").string},create_event_button:{},group:{href:e("PluginAttrTypes").url,show_social_context:e("PluginAttrTypes").bool,show_group_info:e("PluginAttrTypes").bool,show_metadata:e("PluginAttrTypes").bool},like:{href:e("PluginAttrTypes").url,layout:e("PluginAttrTypes").string,show_faces:e("PluginAttrTypes").bool,share:e("PluginAttrTypes").bool,action:e("PluginAttrTypes").string,send:e("PluginAttrTypes").bool,size:e("PluginAttrTypes").string},like_box:{href:e("PluginAttrTypes").string,show_faces:e("PluginAttrTypes").bool,header:e("PluginAttrTypes").bool,stream:e("PluginAttrTypes").bool,force_wall:e("PluginAttrTypes").bool,show_border:e("PluginAttrTypes").bool,id:e("PluginAttrTypes").string,connections:e("PluginAttrTypes").string,profile_id:e("PluginAttrTypes").string,name:e("PluginAttrTypes").string},page:{href:e("PluginAttrTypes").string,hide_cta:e("PluginAttrTypes").bool,hide_cover:e("PluginAttrTypes").bool,small_header:e("PluginAttrTypes").bool,adapt_container_width:e("PluginAttrTypes").bool,show_facepile:e("PluginAttrTypes").bool,show_posts:e("PluginAttrTypes").bool,tabs:e("PluginAttrTypes").string},page_events:{href:e("PluginAttrTypes").url},post:{href:e("PluginAttrTypes").url,show_text:e("PluginAttrTypes").bool},profile_pic:{uid:e("PluginAttrTypes").string,linked:e("PluginAttrTypes").bool,href:e("PluginAttrTypes").string,size:e("PluginAttrTypes").string,facebook_logo:e("PluginAttrTypes").bool},send_to_mobile:{max_rows:e("PluginAttrTypes").string,show_faces:e("PluginAttrTypes").bool,size:e("PluginAttrTypes").string}},a={fan:"like_box",likebox:"like_box"};Object.keys(a).forEach(function(b){i[b]=i[a[b]]});b=i;h["default"]=b},98);c.__d("runOnce",[],function(a,b,c,d,e,f){function a(a){var b=!1,c;return function(){b||(b=!0,c=a());return c}}f["default"]=a},66);c.__d("XFBML",["Assert","Log","runOnce","sdk.Observable"],function(b,c,d,f,g,h,i){var j={},a={},k=0,l=new(f("sdk.Observable").Observable)();function m(a,b){return(a[b]+"").trim()}function n(a){return j[m(a,"nodeName").toLowerCase()]}function o(b){var c=m(b,"className").split(/\s+/).filter(function(b){return Object.prototype.hasOwnProperty.call(a,b)});if(c.length===0)return void 0;if(b.getAttribute("fb-xfbml-state")||!b.childNodes||b.childNodes.length===0||b.childNodes.length===1&&b.childNodes[0].nodeType===3||b.children.length===1&&m(b.children[0],"className")==="fb-xfbml-parse-ignore")return a[c[0]]}function p(a){var b={};e("Array","from",!1,a.attributes).forEach(function(a){b[m(a,"name")]=m(a,"value")});return b}function q(b,c,q){d("Assert").isTrue(b&&b.nodeType&&b.nodeType===1&&!!b.getElementsByTagName,"Invalid DOM node passed to FB.XFBML.parse()");d("Assert").isFunction(c,"Invalid callback passed to FB.XFBML.parse()");if(b==null)return;var h=++k;f("Log").info("XFBML Parsing Start %s",h);var i=1,j=0,a=function(){i--,i===0&&(f("Log").info("XFBML Parsing Finish %s, %s tags found",h,j),c!=null&&c(),l.inform("render",[h,j])),d("Assert").isTrue(i>=0,"onrender() has been called too many times")};e("Array","from",!1,b.getElementsByTagName("*")).forEach(function(b){if(q!==!0&&b.getAttribute("fb-xfbml-state"))return;if(b.nodeType!==1)return;var c=n(b)||o(b);if(c==null)return;i++;j++;var e=new c.ctor(b,c.xmlns,c.localName,p(b));e.subscribe("render",d("runOnce")(function(){b.setAttribute("fb-xfbml-state","rendered"),a()}));c=function a(){b.getAttribute("fb-xfbml-state")=="parsed"?l.subscribe("render.queue",a):(b.setAttribute("fb-xfbml-state","parsed"),e.process())};c()});l.inform("parse",[h,j]);var m=3e4;window.setTimeout(function(){i>0&&f("Log").warn("%s tags failed to render in %s ms",i,m)},m);a()}l.subscribe("render",function(){var a=l.getSubscribers("render.queue");l.clearSubscribers("render.queue");a.forEach(function(a){a([])})});b={registerTag:function(b){var c=b.xmlns+":"+b.localName;if(c==="fb:customerchat"&&j[c]!=null)return;d("Assert").isUndefined(j[c],c+" already registered");j[c]=b;a[b.xmlns+"-"+b.localName]=b},parse:function(a,b){q((a=a)!=null?a:document.body,(a=b)!=null?a:function(){},!0)},parseNew:function(){q(document.body,function(){},!1)},subscribe:l.subscribe,unsubscribe:l.unsubscribe};c=b;i["default"]=c},98);c.__d("sdk.XFBML.Comments",["IframePlugin","QueryString","UrlMap","sdk.DOM","sdk.Event","sdk.PluginUtils","sdk.Runtime","sdk.UA","sdk.URI"],function(b,c,d,g,h,i,j){var k=320,a=f["extends"]({numposts:"string",href:"url",permalink:"bool",order_by:"string",mobile:"bool",version:"string",hide_post_profile:"bool",limit:"string",offset:"string",view:"string",fb_comment_id:"string",from_mod_tool:"bool",migrated:"string",xid:"string",title:"string",url:"string",quiet:"string",reverse:"string",simple:"string",css:"string",notify:"string",count:"bool",skin:"string",font:"string",width:"string",height:"px",ref:"string",lazy:"bool",color_scheme:"string"},g("sdk.PluginUtils").baseParams);function l(b,c){Object.keys(a).forEach(function(a){var d=g("sdk.DOM").getAttr(b,a);d!==null&&(c[a]=d)});Object.keys(c).forEach(function(a){e(a,"startsWith",!0,"data-")&&delete c[a]});d("sdk.UA").mobile()&&c.mobile!==!1&&(c.mobile=!0);c.skin||(c.skin=c.colorscheme);if(!c.href){c.title=c.title||document.title;c.url=c.url||document.URL;if(!c.xid){var f=document.URL.indexOf("#");f>0?c.xid=encodeURIComponent(document.URL.substring(0,f)):c.xid=encodeURIComponent(document.URL)}c.migrated&&(c.href=g("UrlMap").resolve("www")+"/plugins/comments_v1.php?app_id="+d("sdk.Runtime").getClientID()+"&xid="+encodeURIComponent(c.xid)+"&url="+encodeURIComponent(c.url))}else f=c.fb_comment_id,f||(f=d("QueryString").decode(document.URL.substring(document.URL.indexOf("?")+1)).fb_comment_id,f&&f.indexOf("#")>0&&(f=f.substring(0,f.indexOf("#")))),f&&(c.fb_comment_id=f);c.version||(c.version=d("sdk.Runtime").getVersion());c.permalink||(c.width=c.mobile||c.width==="auto"||c.width==="100%"?"":c.width?Math.max(c.width,k):550,c.height=100);c.href!=null&&(f=new(d("sdk.URI"))(c.href),f.getProtocol()||(c.href=f.setProtocol("http").toString()));return c}b=d("IframePlugin").extend({constructor:function(a,b,c,d){d=l(a,d),this.parent(a,b,c,d),this.subscribe("xd.sdk_event",function(a){g("sdk.Event").fire(a.event,e("JSON","parse",!1,a.data))})},getConfig:function(){return{fluid:!0,full_width:!0}},getParams:function(){return a}});c=b;j["default"]=c},98);c.__d("sdk.XFBML.CommentsCount",["sdk.DOM","sdk.XFBML.Comments","sprintf"],function(a,b,c,d,f,g,h){a=c("sdk.XFBML.Comments").extend({constructor:function(a,b,f,h){d("sdk.DOM").addCss(a,"fb_comments_count_zero"),h.count=1,this.parent(a,b,"comments",h),this.subscribe("xd.comment_count",function(b){b=e("JSON","parse",!1,b.data),d("sdk.DOM").html(a,c("sprintf")('<span class="fb_comments_count">%s</span>',b.count)),b.count>0&&d("sdk.DOM").removeCss(a,"fb_comments_count_zero"),d("sdk.DOM").removeCss(a,"fb_iframe_widget")})}}),b=a,h["default"]=b},98);c.__d("$InternalEnum",[],function(b,c,d,e,f,g){"use strict";var h=Object.prototype.hasOwnProperty,i=typeof WeakMap==="function"?new WeakMap():new Map();function a(a){var b=i.get(a);if(b!==void 0)return b;var c=new Map();Object.getOwnPropertyNames(a).forEach(function(b){c.set(a[b],b)});try{i.set(a,c)}catch(a){}return c}var j=Object.freeze(Object.defineProperties(Object.create(null),{isValid:{value:function(b){return a(this).has(b)}},cast:{value:function(a){return this.isValid(a)?a:void 0}},members:{value:function(){return a(this).keys()}},getName:{value:function(b){return a(this).get(b)}}}));function b(a){var b=Object.create(j);for(var c in a)h.call(a,c)&&Object.defineProperty(b,c,{value:a[c]});return Object.freeze(b)}var k=Object.freeze(Object.defineProperties(Object.create(null),{isValid:{value:function(a){return typeof a==="string"?h.call(this,a):!1}},cast:{value:j.cast},members:{value:function(){return Object.getOwnPropertyNames(this).values()}},getName:{value:function(a){return a}}}));b.Mirrored=function(a){var b=Object.create(k);for(var c=0,d=a.length;c<d;++c)Object.defineProperty(b,a[c],{value:a[c]});return Object.freeze(b)};Object.freeze(b.Mirrored);f.exports=Object.freeze(b)},null);c.__d("ChatPluginEntryPointIconEnum",["$InternalEnum"],function(a,b,c,d,e,f){a=b("$InternalEnum")({MESSENGER_ICON:"messenger_icon",CHAT_ROUND_ICON:"chat_round_icon",CHAT_ANGULAR_ICON:"chat_angular_icon",NONE:"none"}),c=a,f["default"]=c},66);c.__d("ChatPluginEntryPointLabelEnum",["$InternalEnum"],function(a,b,c,d,e,f){a=b("$InternalEnum")({NONE:"none",CHAT:"chat",HELP:"help",ASK_US:"ask_us"}),c=a,f["default"]=c},66);c.__d("ChatPluginEntryPointSizeEnum",[],function(a,b,c,d,e,f){a=Object.freeze({STANDARD:"standard",COMPACT:"compact"}),f["default"]=a},66);c.__d("performanceAbsoluteNow",["performance"],function(b,c,d,e,f,g,h){var i=function(){return Date.now()};function b(a){i=a}if(d("performance").now&&d("performance").timing&&d("performance").timing.navigationStart){var a=d("performance").timing.navigationStart;c=function(){return d("performance").now()+a}}else c=function(){return i()};c.setFallback=b;e=c;h["default"]=e},98);c.__d("ChatPluginSDKPreLoggingUtils",["CORSRequest","UrlMap","performanceAbsoluteNow"],function(a,b,c,d,e,g,h){"use strict";function a(b,e,i,g){var h;g===void 0&&(g=!0);var j=c("performanceAbsoluteNow")();b=b?d("UrlMap").resolve("social_plugin")+"/customer_chat/SDK/":d("UrlMap").resolve("www")+"/plugins/customer_chat/SDK/";h=(h=i.request_time)!=null?h:void 0;var a=0;typeof h==="number"&&g&&(a=j-h);i.request_time=j;c("CORSRequest").execute(b,"get",f["extends"]({},i,{event_name:e,loading_time:a}),function(){return null})}h.preLogging=a},98);c.__d("LiveChatPluginConstants",["$InternalEnum"],function(b,c,d,e,f,g){"use strict";b="LiveChatEvent/";d="mqtt";e=c("$InternalEnum")({CONNECTED:"Connected",CONNECTING:"Connecting",DISCONNECTED:"Disconnected"});f=250;c=b+"close_timestamps";var h=b+"reset_badging",i=b+"state",a=b+"switch_account",j=b+"typing",k=b+"unread_count",l=b+"update_message_list",m="platform/plugins/connect/guest",n=b+"guest_upgrade_success",o=b+"guest_upgrade_success_incognito",p=b+"navigate_to_welcome_page",q="platform/plugins/connect/access_token";b=b+"start_re_engagement";var r=124,s=187,t=24,u=424,v=288,w=313,x=219,y=40,z=36,A=24,B=18,C=708,D=540,E=244,F=202,G=509,H=430,I=6,J=0,K=1,L=-1,M="messaging_plugin",N=8634e4,O="#8A8D91",P="entrypoint:customer_chat_plugin",Q=0,R=1,S=2,T=3,U="new_message_update",V="initial_fetch",W=5e3,aa="https://www.facebook.com/business/help/****************";g.MQTT=d;g.MQTTGatewayConnectionState=e;g.LOGIN_CHECK_INTERVAL=f;g.CLOSE_TIMESTAMPS=c;g.RESET_BADGING=h;g.STATE_UPDATE=i;g.SWITCH_ACCOUNT=a;g.TYPING_UPDATE=j;g.UNREAD_COUNT_UPDATE=k;g.UPDATE_MESSAGE_LIST=l;g.GUEST_MODE_CONNECT=m;g.GUEST_UPGRADE_SUCCESS=n;g.GUEST_UPGRADE_SUCCESS_INCOGNITO=o;g.NAVIGATE_TO_WELCOME_PAGE=p;g.ACCESS_TOKEN_LOGIN=q;g.START_RE_ENGAGEMENT=b;g.PROMPT_FALLBACK_HEIGHT=r;g.PROMPT_REDESIGN_FALLBACK_HEIGHT=s;g.PROMPT_CONTAINER_PADDING_HEIGHT=t;g.WELCOME_PAGE_GUEST_FALLBACK_HEIGHT=u;g.WELCOME_PAGE_NO_GUEST_FALLBACK_HEIGHT=v;g.WELCOME_PAGE_GUEST_FALLBACK_HEIGHT_WITH_COMPACT=w;g.WELCOME_PAGE_NO_GUEST_FALLBACK_HEIGHT_WITH_COMPACT=x;g.WELCOME_PAGE_ATTRIBUTION_OFFEST_HEIGHT=y;g.WELCOME_PAGE_ATTRIBUTION_OFFSET_HEIGHT_WITH_COMPACT=z;g.MAIN_IFRAME_PADDING_HEIGHT=A;g.MAIN_IFRAME_PADDING_HEIGHT_WITH_COMPACT=B;g.THREAD_PAGE_HEIGHT=C;g.THREAD_PAGE_HEIGHT_COMPACT=D;g.RE_ENGAGEMENT_COLLAPSED_DIALOG_HEIGHT=E;g.RE_ENGAGEMENT_COLLAPSED_DIALOG_HEIGHT_COMPACT=F;g.RE_ENGAGEMENT_EXPANDED_DIALOG_HEIGHT=G;g.RE_ENGAGEMENT_EXPANDED_DIALOG_HEIGHT_COMPACT=H;g.GREETING_TEXT_BOTTOM_SPACING_OFFEST=I;g.LOGGED_IN_CHAT_MODE=J;g.GUEST_CHAT_MODE=K;g.INVALID_CHAT_MODE=L;g.MESSENGING_PLUGIN=M;g.GUEST_SESSION_STORAGE_VALIDITY_MS=N;g.GUEST_SEND_BUTTON_COLOR_EMPTY_INPUT=O;g.LIVE_CHAT_ENTRYPOINT_ATTRIBUTION_TAG=P;g.ITP_CONSISTENCY_UNKNOWN_LOGGED_OUT=Q;g.ITP_CONSISTENCY_CONSISTENT_LOGGED_IN=R;g.ITP_CONSISTENCY_INCONSISTENT=S;g.ITP_CONSISTENCY_CONSISTENT_NO_ITP=T;g.NEW_MESSAGE_UPDATE=U;g.INITIAL_FETCH=V;g.PLUGIN_FADE_DELAY=W;g.HELP_DEX_LINK=aa},66);c.__d("ChatPluginStyleUtils",["LiveChatPluginConstants"],function(b,c,d,g,h,i,j){var k=44,a=36,l=17,m=15,n=22,o=20,p=6,q=4,r=16,s=14,t=60,u=44,v=70,w=86,x=63,y=76,z=99,A=114,B=85,C=98,D=12;function b(){var a=screen.width;a="width: "+a+"px;";return"padding: 0; position: fixed; z-index: 2147483646;box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);border-radius: 16px;bottom: 24px; top: auto; right:0;"+a}function c(a){a instanceof HTMLElement&&(a.style.marginTop=window.innerHeight-a.clientHeight+"px")}function E(a,b,c,d,k,l,m){b=T(b,d,k);d=U(l,m);k=V(a,c);l=f["extends"]({},b,d,k);return Object.fromEntries(e("Object","entries",!1,l).sort())}function d(a,b,c,d,f,k,l){a=E(a,b,c,d,f,k,l);return e("JSON","stringify",!1,a)}function h(b,c,d,i,j){d=d+(b=="right"?-2:43);c+t-16;c=c-16;i==="none"?j==="compact"?c+=u:c+=t:j==="compact"?c+=a:c+=k;i={bottom:c.toString()+"px",position:"fixed",width:"20px",height:"24px",zIndex:2147483645,borderRadius:"4pt",background:"none"};j={};switch(b){case"right":j=f["extends"]({},i,{right:d+"px"});break;case"left":j=f["extends"]({},i,{left:d+"px"});break}c=Object.fromEntries(e("Object","entries",!1,j).sort());return e("JSON","stringify",!1,c)}function i(b,c,d,l,m,n,o,p){var a=d;c=c-2.5;b==="left"&&(a=d-15,l==="none"?n==="compact"?a+=u:a+=t:m==="none"?l==="ask_us"?n==="compact"?a+=y:a+=w:n==="compact"?a+=x:a+=v:l==="ask_us"?n==="compact"?a+=C:a+=A:n==="compact"?a+=B:a+=z);d={bottom:c.toString()+"px",position:"fixed",zIndex:2147483646,height:"15px",width:"15px",borderRadius:"50%"};m={};switch(b){case"right":m=f["extends"]({},d,{right:a+"px"});break;case"left":m=f["extends"]({},d,{left:a+"px"});break}o===!1&&(m=f["extends"]({},m,{animation:p===!0?"slideInFromBottomDelay 3.5s ease-out":null,backgroundColor:"#31CC46",border:"2.5px solid white",height:"10px",width:"10px"}));return Object.fromEntries(e("Object","entries",!1,m).sort())}function F(a){var b="padding: 0; position: fixed; z-index: 2147483646;box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);border-radius: 16px;bottom: 24px; top: auto; right:0; margin: 0 12px; width: calc(100% - 24px);";a=a===!0?" height: 60px;":" height: 0px;";b+=a;return b}function G(b,c,d,e,f,n,o){d=d-D;t;f==="none"?n==="compact"?f=u:f=t:n==="compact"?f=a:f=k;n=f+c;f=" bottom: "+n.toString()+"px;";c="padding: 0; position: fixed; z-index: 2147483646; border-radius: 16px; top: auto; background: none; minHeight: 300px;";var p=" width: 399px;",q=" width: 324px;";c=c+(o==="compact"?q:p)+f;e?c+=" max-height: calc(100% - "+n.toString()+"px);":c+=" max-height: 0;";switch(b){case"right":c+=" right: "+(d+4)+"px;";break;case"left":c+=" left: "+(d-4)+"px;";break}return c}function H(a){var b="position: fixed; z-index: 2147483646; box-shadow: none; border-radius: 0; top: 0px; right: 0px; width: 100%;";a=a===!0?" height: 100%; max-height: 100%;":" max-height: 0px;";b+=a;return b}function I(a,b,c,d,e){b=J(b,c,d);a||(c=e?g("LiveChatPluginConstants").RE_ENGAGEMENT_COLLAPSED_DIALOG_HEIGHT_COMPACT:g("LiveChatPluginConstants").RE_ENGAGEMENT_COLLAPSED_DIALOG_HEIGHT,d=e?g("LiveChatPluginConstants").MAIN_IFRAME_PADDING_HEIGHT_WITH_COMPACT:g("LiveChatPluginConstants").MAIN_IFRAME_PADDING_HEIGHT,a=c+d,b+=a-g("LiveChatPluginConstants").GREETING_TEXT_BOTTOM_SPACING_OFFEST);return b}function J(b,c,d){var e=D;c==="none"?d==="compact"?e+=u:e+=t:d==="compact"?e+=a:e+=k;return e+b}function K(a,b,c,d,i){c=c-D;b=J(b,d,i);d={padding:0,position:"fixed",zIndex:2147483646,borderRadius:"16px",top:"auto",width:"247px",maxHeight:"calc(100% - 80px)",background:"none",height:"72px"};({});i=f["extends"]({},d,{bottom:b+"px"});switch(a){case"right":i=f["extends"]({},i,{right:"6px",marginRight:c+"px"});break;case"left":i=f["extends"]({},i,{left:"2px",marginLeft:c+"px"});break}i=f["extends"]({},i,{animation:"slideInFromBottomDelay 3s ease-out"});d=Object.fromEntries(e("Object","entries",!1,i).sort());return e("JSON","stringify",!1,d)}function L(a,b,c,d,e,f){c=c-D;b=J(b,e,f);e=" bottom: "+b.toString()+"px;";f="padding: 0; position: fixed; z-index: 2147483646;border-radius: 16px; top: auto; width: 247px; max-height: calc(100% - 80px); background: none;"+e;b=f;switch(a){case"right":b+=" right: 6px; margin-right: "+c+"px;";break;case"left":b+=" left: 2px; margin-left: "+c+"px;";break}b+=Boolean(d)?" animation: slideInFromBottomDelay 6s ease-out;":" animation: slideInFromBottomDelay 3s ease-out;";return b}function M(b,c,d,e,f,m){e=m==="compact"?u:t;var n="position: fixed; z-index: 2147483646; box-shadow: none; border-radius: 18px 0px 18px 18px; right: 0px; width: calc(80% - 40px); bottom: 24px;";if(b!=null&&typeof d=="number"){n+=" height: "+b+"px;";var o=d;b<e&&(o=d+12);n+=" bottom: "+o+"px;"}else n+=" height: 60px;";n+=c==="left"?" left: calc(84px + 5%);":" left: 5%;";if(f!=="none"&&typeof d==="number"){b=m==="compact"?a:k;e=d+b+12;n+=" bottom: "+e+"px;";switch(c){case"right":n+=" right: 20px; left: auto;";break;case"left":n+=" left: 20px; right: auto;";break}}return n}function N(b,c,d,e,f){d="position: fixed; z-index: 2147483646; box-shadow: none; border-radius: 18px 0px 18px 18px;width: 306px;bottom: 24px; right: 20px;";typeof c==="number"&&(f=f==="compact"?a:k,c=c+f+12,d+=" bottom: "+c+"px;");if(typeof e==="number"){f=e-D;switch(b){case"right":d+=" right: "+(f+4)+"px; left:auto";break;case"left":d+=" left: "+(f-4)+"px; right:auto";break}}return d}function O(a,b){var c={alignItems:"flex-start",background:"#FFFFFF",borderRadius:"18px",bottom:0,boxShadow:"0 4px 12px 0 rgba(0, 0, 0, 0.15)",display:"flex",flexDirection:"row",position:"absolute"},d=f["extends"]({},c,{marginLeft:"12px",padding:"8px 0px 8px 8px",width:"90%"});c=f["extends"]({},c,{padding:"8px 0px 8px 12px",width:"93%"});a=a?d:c;b==="desktop"&&(a=f["extends"]({},a,{cursor:"pointer"}));return Object.fromEntries(e("Object","entries",!1,a).sort())}function P(a,b){var c={display:"inline-block",fontSize:"17px",lineHeight:"22px",textAlign:"left",width:"90%",wordWrap:"break-word"},d=f["extends"]({},c,{paddingRight:"18px"});b==="mobile"&&(d=f["extends"]({},d,{paddingLeft:"12px"}));b=f["extends"]({},c,{paddingRight:"12px",paddingLeft:"12px"});c=a?b:d;return Object.fromEntries(e("Object","entries",!1,c).sort())}function Q(a,b){var c={position:"absolute",top:"-2px",width:"38px"};b==="desktop"&&(c=f["extends"]({},c,{cursor:"pointer"}));b=f["extends"]({},c,{left:"-4px"});c=f["extends"]({},c,{right:"-4px"});a=a?b:c;return Object.fromEntries(e("Object","entries",!1,a).sort())}function R(a){a={visibility:a?"hidden":"visible"};return Object.fromEntries(e("Object","entries",!1,a).sort())}function S(){var a={outline:"none"};return Object.fromEntries(e("Object","entries",!1,a).sort())}function T(a,b,c){return{animation:W(b,c),background:"none",bottom:a.toString()+"px",display:"block",margin:"0 12px 0 12px",overflow:"visible",padding:"0",position:"fixed",top:"auto",zIndex:2147483644}}function U(b,c){if(b!=="none"){b=c==="compact"?a:k;return{borderRadius:"60px",boxShadow:null,height:b+"px",width:"auto"}}else{b=c==="compact"?u:t;return{borderRadius:"60px",boxShadow:"0 4px 12px 0 rgba(0, 0, 0, 0.15)",height:b+"px",width:b+"px"}}}function V(a,b){b=b-D;switch(a){case"right":return{right:b+"px"};case"left":return{left:b+"px"}}}function W(a,b){if(Boolean(a))return null;if(Boolean(b))return"slideInFromBottomDelay 3s ease-out";else return"slideInFromBottom 0.3s ease-out"}j.LABELED_ENTRY_POINT_STANDARD_HEIGHT=k;j.LABELED_ENTRY_POINT_COMPACT_HEIGHT=a;j.LABELED_ENTRY_POINT_STANDARD_FONT_SIZE=l;j.LABELED_ENTRY_POINT_COMPACT_FONT_SIZE=m;j.LABELED_ENTRY_POINT_STANDARD_LINE_HEIGHT=n;j.LABELED_ENTRY_POINT_COMPACT_LINE_HEIGHT=o;j.LABELED_ENTRY_POINT_STANDARD_ICON_MARGIN_RIGHT=p;j.LABELED_ENTRY_POINT_COMPACT_ICON_MARGIN_RIGHT=q;j.LABELED_ENTRY_POINT_STANDARD_PADDING_HORIZONTAL=r;j.LABELED_ENTRY_POINT_COMPACT_PADDING_HORIZONTAL=s;j.ICON_ENTRY_POINT_STANDARD_HEIGHT=t;j.ICON_ENTRY_POINT_COMPACT_HEIGHT=u;j.LABELED_ONLY_ENTRY_POINT_STANDARD_WIDTH_SHORT=v;j.LABELED_ONLY_ENTRY_POINT_STANDARD_WIDTH_LONG=w;j.LABELED_ONLY_ENTRY_POINT_COMPACT_WIDTH_SHORT=x;j.LABELED_ONLY_ENTRY_POINT_COMPACT_WIDTH_LONG=y;j.LABELED_WITH_ICON_ENTRY_POINT_STANDARD_WIDTH_SHORT=z;j.LABELED_WITH_ICON_ENTRY_POINT_STANDARD_WIDTH_LONG=A;j.LABELED_WITH_ICON_ENTRY_POINT_COMPACT_WIDTH_SHORT=B;j.LABELED_WITH_ICON_ENTRY_POINT_COMPACT_WIDTH_LONG=C;j.getMobileStyleText=b;j.positionElementAtWindowFooter=c;j.getEntryPointStyle=E;j.getIconStyleText=d;j.getUnreadCountStyleText=h;j.getAvailabilityStatusStyleText=i;j.getMobileLandingStyleText=F;j.getDesktopStyleText=G;j.getMobileFullScreenStyleText=H;j.getDesktopGreetingBottomSpacingWithReEngagementDialog=I;j.getDesktopGreetingBottomSpacing=J;j.getDesktopGreetingStyleText=K;j.getDesktopWelcomeMessageStyleText=L;j.getMobileWelcomeMessageStyleText=M;j.getMobileReengagementCollapsedDialogStyleText=N;j.getWelcomeMessageBubbleStyle=O;j.getWelcomeMessageTextStyle=P;j.getMinusButtonStyle=Q;j.getMinusButtonVisibility=R;j.getNoOutlineFocus=S},98);c.__d("DOMPlugin",["JSSDKShadowCssConfig","Log","QueryString","sdk.DOM","sdk.Observable","sdk.PluginUtils","sdk.Runtime","sdk.XD","sdk.feature"],function(a,b,c,d,g,h,i){a=function(a){f.inheritsLoose(g,a);var b=g.prototype;b.render=function(a){};function g(f,g,h,i,j,b){var k;k=a.call(this)||this;k.shadowCss=[];k.element=f;k.tag=h.replace(/-/g,"_");k.ns=g;k.config=(h=b)!=null?h:{};k.params={};d("sdk.PluginUtils").validate(j,f,i,k.params);d("sdk.PluginUtils").validate(d("sdk.PluginUtils").baseParams,f,i,k.params);e("Object","assign",!1,k.params,{app_id:c("sdk.Runtime").getClientID(),locale:c("sdk.Runtime").getLocale(),sdk:"joey",kid_directed_site:c("sdk.Runtime").getKidDirectedSite(),channel:d("sdk.XD").handler(function(a){a!=null&&k.inform("xd."+a.type,a)},"parent.parent",!0)});return k}b.process=function(){var a=f["extends"]({},this.params);delete a.channel;a=c("QueryString").encode(a);if(this.element.getAttribute("fb-iframe-plugin-query")===a){d("Log").info("Skipping render: %s:%s %s",this.ns,this.tag,a);this.inform("render");return}this.element.setAttribute("fb-iframe-plugin-query",a);j(this.element,e(this.render,"bind",!0,this),this.shadowCss);this.inform("render")};return g}(d("sdk.Observable").Observable);function j(a,b,e){e===void 0&&(e=[]);while(a.firstChild)a.removeChild(a.firstChild);if(typeof a.attachShadow==="function"){var f=document.createElement("div");a.appendChild(f);var j=f.attachShadow({mode:c("sdk.feature")("shadow_dom_plugin_mode","closed")});e.forEach(function(a){return d("sdk.DOM").addCssRules(c("JSSDKShadowCssConfig")[a],[a],j)});j.appendChild(b(j))}else e.forEach(function(a){return d("sdk.DOM").addCssRules(c("JSSDKShadowCssConfig")[a],[a])}),a.appendChild(b(document))}i.DOMPlugin=a;i.maybeCreateShadowRootAndRenderInDOM=j},98);c.__d("getJSEnumSafe",[],function(a,b,c,d,e,f){"use strict";function a(a,b){if(b==null)return null;if(!Object.prototype.hasOwnProperty.call(a,b))return null;b=b;return a[b]}f["default"]=a},66);c.__d("nativeRequestAnimationFrame",[],function(a,b,c,d,e,f){b=a.__fbNativeRequestAnimationFrame||a.requestAnimationFrame||a.webkitRequestAnimationFrame||a.mozRequestAnimationFrame||a.oRequestAnimationFrame||a.msRequestAnimationFrame,c=b,f["default"]=c},66);c.__d("requestAnimationFramePolyfill",["nativeRequestAnimationFrame","performanceNow"],function(a,b,c,d,e,f,g){var h=0;b=c("nativeRequestAnimationFrame")||function(b){var d=c("performanceNow")(),e=Math.max(0,16-(d-h));h=d+e;return a.setTimeout(function(){b(c("performanceNow")())},e)};d=b;g["default"]=d},98);c.__d("IdleCallbackImplementation",["performanceNow","requestAnimationFramePolyfill"],function(b,c,d,e,f,g,h){var i=[],a=0,j=0,k=-1,l=!1,m=1e3/60,n=2;function o(a){return a}function p(a){return a}function c(a,c){var d=j++;i[d]=a;r();if(c!=null&&c.timeout>0){var e=o(d);b.setTimeout(function(){return x(e)},c.timeout)}return o(d)}function q(a){a=p(a),i[a]=null}function r(){l||(l=!0,d("requestAnimationFramePolyfill")(function(a){l=!1,t(d("performanceNow")()-a)}))}function s(a){var b=m-n;if(a<b)return b-a;a=a%m;if(a>b||a<n)return 0;else return b-a}function t(a){var b=d("performanceNow")();b>k&&(a=s(a),a>0&&(b=b+a,w(b),k=b));u()&&r()}function u(){return a<i.length}function v(){while(u()){var b=i[a];a++;if(b)return b}return null}function w(a){var b;while(d("performanceNow")()<a&&(b=v()))b(new y(a))}function x(a){var b=p(a);b=i[b];b&&(q(a),b(new y(null)))}var y=function(){function a(a){this.didTimeout=a==null,this.$1=a}var b=a.prototype;b.timeRemaining=function(){var a=this.$1;if(a!=null){var b=d("performanceNow")();if(b<a)return a-b}return 0};return a}();h.requestIdleCallback=c;h.cancelIdleCallback=q},98);c.__d("sdk.IdleCallback",["IdleCallbackImplementation","sdk.feature"],function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b,d){d===void 0&&(d=c("sdk.feature")("idle_callback_wait_time_ms",2e3)),this.$1=d,this.$3=b,this.$2=a}var b=a.prototype;b.start=function(){var a=this;if(this.$1===0||isNaN(this.$1)||this.$2===0||isNaN(this.$2)){this.$3();return null}var b=Date.now(),c=this.isBrowserCompatible()?window.requestIdleCallback:d("IdleCallbackImplementation").requestIdleCallback,e=0,f=0,g=function d(g){if(e>a.$1){a.$3();return}var h=Date.now();if(h-b>a.$2){a.$3();return}h=g.timeRemaining();e+=h>=49&&f>=49?h:0;f=h;c(d)};return c(g)};b.isBrowserCompatible=function(){return typeof window==="undefined"?!1:typeof window.requestIdleCallback==="function"};return a}(),g["default"]=a},98);c.__d("uuid",[],function(b,c,d,e,f,g){"use strict";function b(){var b;b=(b=a)==null?void 0:(b=b.crypto)==null?void 0:b.randomUUID;return typeof b==="function"?a.crypto.randomUUID():"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(a){var b=Math.random()*16|0;a=a==="x"?b:b&3|8;return a.toString(16)})}g["default"]=b},66);c.__d("sdk.XFBML.ChatDOM",["$InternalEnum","CORSRequest","ChatPluginEntryPointIconEnum","ChatPluginEntryPointLabelEnum","ChatPluginEntryPointSizeEnum","ChatPluginSDKPreLoggingUtils","ChatPluginStyleUtils","DOMPlugin","Log","UrlMap","getJSEnumSafe","performanceAbsoluteNow","sdk.Content","sdk.DOM","sdk.IdleCallback","sdk.XFBML.CustomerChatNew","sdk.fbt","sdk.feature","uuid"],function(a,b,c,d,g,h,i){"use strict";var j=b("$InternalEnum").Mirrored(["WAITING","LOADING","LOADED"]);a=function(a){f.inheritsLoose(b,a);function b(f,g,h,i){var b;b=a.call(this,f,g,h,i,{allow_guests:"bool",attribution:"string",greeting_dialog_display:"string",greeting_dialog_delay:"string",logged_in_greeting:"string",logged_out_greeting:"string",minimized:"bool",page_id:"string",theme_color:"string",override:"string",attribution_version:"string",is_loaded_by_facade:"bool"})||this;g=c("performanceAbsoluteNow")();h=c("uuid")();var k=window.location.href;e("Object","assign",!1,b.params,{current_url:k,log_id:h,request_time:g,is_loaded_by_facade:!0,should_use_new_domain:i.should_use_new_domain});d("ChatPluginSDKPreLoggingUtils").preLogging(Boolean(i.should_use_new_domain),"chat_plugin_sdk_facade_create",b.params,!1);d("sdk.DOM").remove(f);d("sdk.Content").append(f);b.$ChatDOMFacade3=document.createElement("div");b.$ChatDOMFacade1=j.WAITING;b.$ChatDOMFacade2=!1;b.$ChatDOMFacade6=!1;b.$ChatDOMFacade8="standard";b.$ChatDOMFacade9="none";b.$ChatDOMFacade10="none";b.$ChatDOMFacade11="left";b.$ChatDOMFacade12=0;b.$ChatDOMFacade13=0;b.$ChatDOMFacade14=!1;b.$ChatDOMFacade15=Boolean(i.should_use_new_domain);b.shadowCss=["css:fb.shadow.css.chatdom"];b.$ChatDOMFacade4=c("performanceAbsoluteNow")();d("Log").info("facadeperf: Started browser idle loader.");b.$ChatDOMFacade5=new(c("sdk.IdleCallback"))(c("sdk.feature")("chat_plugin_facade_timeout_ms",8e3),function(){var a=c("performanceAbsoluteNow")();d("Log").info("facadeperf: Idle callback starts full load in %sms.",a-b.$ChatDOMFacade4);b.$ChatDOMFacade16(!1,!1)}).start();return b}var g=b.prototype;g.render=function(a){var b=this;this.$ChatDOMFacade3.classList.add("container");a=this.$ChatDOMFacade15?d("UrlMap").resolve("social_plugin")+"/customer_chat/facade/":d("UrlMap").resolve("www")+"/plugins/customer_chat/facade/";c("CORSRequest").execute(a,"get",this.params,function(a){var f,i=c("performanceAbsoluteNow")();d("Log").info("facadeperf: CORS request completed in %sms.",i-b.$ChatDOMFacade4);if(a.error)return;b.$ChatDOMFacade11=a.alignment;b.$ChatDOMFacade12=a.bottom_spacing;b.$ChatDOMFacade13=a.side_spacing;i=a.theme_color;b.$ChatDOMFacade8=(f=c("getJSEnumSafe")(c("ChatPluginEntryPointSizeEnum"),(f=a.entry_point_size)==null?void 0:f.toUpperCase()))!=null?f:"standard";b.$ChatDOMFacade9=(f=c("ChatPluginEntryPointLabelEnum").cast(a.entry_point_label))!=null?f:"none";b.$ChatDOMFacade10=(f=c("ChatPluginEntryPointIconEnum").cast(a.entry_point_icon_enum))!=null?f:"none";b.$ChatDOMFacade14=a.away_hours_enabled&&!a.is_page_away;b.$ChatDOMFacade9==="none"?(f=document.createElement("div"),d("sdk.DOM").html(f,a.entry_point_icon_svg),f=f==null?void 0:f.outerHTML,d("sdk.DOM").html(b.$ChatDOMFacade3,f)):(f=a.entry_point_icon_svg,d("sdk.DOM").html(b.$ChatDOMFacade3,b.$ChatDOMFacade17(f)));f=d("ChatPluginStyleUtils").getEntryPointStyle(a.alignment,a.bottom_spacing,a.side_spacing,!1,!0,a.entry_point_label,b.$ChatDOMFacade8);e("Object","assign",!1,b.$ChatDOMFacade3.style,f);b.$ChatDOMFacade3.style.backgroundColor=i;b.$ChatDOMFacade14&&(f=d("ChatPluginStyleUtils").getAvailabilityStatusStyleText(a.alignment,a.bottom_spacing,a.side_spacing,a.entry_point_label,a.entry_point_icon_enum,b.$ChatDOMFacade8,!1,!0),i=document.createElement("div"),e("Object","assign",!1,i.style,f),b.$ChatDOMFacade3.append(i));d("ChatPluginSDKPreLoggingUtils").preLogging(b.$ChatDOMFacade15,"chat_plugin_sdk_facade_load",b.params)});this.$ChatDOMFacade3.addEventListener("click",function(a){b.$ChatDOMFacade16(!0,!0)});return this.$ChatDOMFacade3};g.$ChatDOMFacade18=function(a){switch(a){case"chat":return c("sdk.fbt")._("Chat");case"help":return c("sdk.fbt")._("Help");case"ask_us":return c("sdk.fbt")._("Ask us");case"none":return""}};g.$ChatDOMFacade19=function(){var a;(a=this.element.parentNode)==null?void 0:a.removeChild(this.element)};g.$ChatDOMFacade20=function(){if(this.$ChatDOMFacade2||this.$ChatDOMFacade1===j.LOADED)return;if(this.$ChatDOMFacade9==="none"){var a=this.$ChatDOMFacade8==="compact"?24:36;a=this.$ChatDOMFacade21(a);d("sdk.DOM").html(this.$ChatDOMFacade3,'\n          <div class="centered-container">\n            '+a+"\n          </div>\n        ")}else{a=this.$ChatDOMFacade8==="compact"?20:24;a=this.$ChatDOMFacade21(a);if(this.$ChatDOMFacade10==="none"){var b=this.$ChatDOMFacade8==="compact"?d("ChatPluginStyleUtils").LABELED_ENTRY_POINT_COMPACT_ICON_MARGIN_RIGHT:d("ChatPluginStyleUtils").LABELED_ENTRY_POINT_STANDARD_ICON_MARGIN_RIGHT;d("sdk.DOM").html(this.$ChatDOMFacade3.children[0],this.$ChatDOMFacade22(b,a)+this.$ChatDOMFacade3.children[0].innerHTML);if(this.$ChatDOMFacade14){b=d("ChatPluginStyleUtils").getAvailabilityStatusStyleText(this.$ChatDOMFacade11,this.$ChatDOMFacade12,this.$ChatDOMFacade13,this.$ChatDOMFacade9,"chat_round_icon",this.$ChatDOMFacade8,!1,!1);var c=document.createElement("div");e("Object","assign",!1,c.style,b);d("sdk.DOM").remove(this.$ChatDOMFacade3.children[1]);this.$ChatDOMFacade3.append(c)}}else d("sdk.DOM").html(this.$ChatDOMFacade3.children[0].children[0],a)}this.$ChatDOMFacade2=!0};g.$ChatDOMFacade17=function(a){var b=this.$ChatDOMFacade8==="compact"?d("ChatPluginStyleUtils").LABELED_ENTRY_POINT_COMPACT_HEIGHT:d("ChatPluginStyleUtils").LABELED_ENTRY_POINT_STANDARD_HEIGHT,c=this.$ChatDOMFacade8==="compact"?d("ChatPluginStyleUtils").LABELED_ENTRY_POINT_COMPACT_FONT_SIZE:d("ChatPluginStyleUtils").LABELED_ENTRY_POINT_STANDARD_FONT_SIZE,e=this.$ChatDOMFacade8==="compact"?d("ChatPluginStyleUtils").LABELED_ENTRY_POINT_COMPACT_LINE_HEIGHT:d("ChatPluginStyleUtils").LABELED_ENTRY_POINT_STANDARD_LINE_HEIGHT,f=this.$ChatDOMFacade8==="compact"?d("ChatPluginStyleUtils").LABELED_ENTRY_POINT_COMPACT_ICON_MARGIN_RIGHT:d("ChatPluginStyleUtils").LABELED_ENTRY_POINT_STANDARD_ICON_MARGIN_RIGHT,j=this.$ChatDOMFacade8==="compact"?d("ChatPluginStyleUtils").LABELED_ENTRY_POINT_COMPACT_PADDING_HORIZONTAL:d("ChatPluginStyleUtils").LABELED_ENTRY_POINT_STANDARD_PADDING_HORIZONTAL;return'\n      <div class="label-container" style="\n        height: '+b+"px;\n        padding: 0 "+j+'px\n      ">\n        '+(a&&'\n            <div class="label-container-icon" style="\n              margin-right: '+f+'px;\n            ">\n              '+a+"\n            </div>\n          ")+'\n        <div\n          class="label-container-label"\n          style="\n            font-size: '+c+"px;\n            line-height: "+e+'px;\n        ">'+this.$ChatDOMFacade18(this.$ChatDOMFacade9).toString()+"</div>\n      </div>\n    "};g.$ChatDOMFacade22=function(a,b){return'\n      <div class="label-container-icon" style="\n        margin-right: '+a+'px;\n      ">\n        '+b+"\n      </div>\n    "};g.$ChatDOMFacade21=function(a){return'\n      <svg class="spinning" x="0" y="0" width="'+a+'" height="'+a+'" viewbox="0 0 60 60">\n        <circle class="path" cx="30" cy="30" r="24" fill="none" stroke-width="6"></circle>\n      </svg>\n    '};g.$ChatDOMFacade23=function(a){if(a&&this.$ChatDOMFacade7&&!this.$ChatDOMFacade6){this.$ChatDOMFacade6=!0;var b=this.$ChatDOMFacade7;b.subscribe("iframes_loaded",function(a){b.showDialog()})}};g.$ChatDOMFacade16=function(a,b){var e=this;b&&this.$ChatDOMFacade20();this.$ChatDOMFacade23(a);if(this.$ChatDOMFacade1!==j.WAITING)return;this.$ChatDOMFacade1=j.LOADING;b=document.createElement("div");d("sdk.Content").append(b,this.element);b=new(c("sdk.XFBML.CustomerChatNew"))(b,"fb","customerchat",this.params);this.$ChatDOMFacade7=b;b.subscribe("xd.mpn.setupIconIframe",function(a){e.$ChatDOMFacade1=j.LOADED,e.$ChatDOMFacade19()});this.$ChatDOMFacade23(a);b.process()};return b}(d("DOMPlugin").DOMPlugin);i["default"]=a},98);c.__d("IframePluginClass",["Log","QueryString","UrlMap","guid","sdk.Auth.LoginStatus","sdk.AuthUtils","sdk.DOM","sdk.Event","sdk.Observable","sdk.PlatformVersioning","sdk.PluginUtils","sdk.Runtime","sdk.UA","sdk.URI","sdk.XD","sdk.createIframe"],function(a,b,c,d,g,h,i){a=function(a){f.inheritsLoose(b,a);function b(j,g,h,i,k){var b;k===void 0&&(k=null);b=a.call(this)||this;h=h.replace(/-/g,"_");b.$IframePluginClass2=!1;b.config=k!=null?k:{fluid:!1,mobile_fullsize:!1,full_width:!1};var l=d("sdk.PluginUtils").getVal(i,"plugin_id");b.subscribe("xd.resize",d("sdk.PluginUtils").resizeBubbler(l));b.subscribe("xd.resize.flow",d("sdk.PluginUtils").resizeBubbler(l));b.subscribe("xd.resize.flow",function(a){e("Object","assign",!1,b.iframeOptions.root.style,{verticalAlign:"bottom",overflow:""}),d("sdk.PluginUtils").resize(b.iframeOptions.root,d("sdk.PluginUtils").parse(a.width),d("sdk.PluginUtils").parse(a.height)),b.updateLift(),window.clearTimeout(b.$IframePluginClass1)});b.subscribe("xd.resize",function(a){e("Object","assign",!1,b.iframeOptions.root.style,{verticalAlign:"bottom",overflow:""}),d("sdk.PluginUtils").resize(b.iframeOptions.root,d("sdk.PluginUtils").parse(a.width),d("sdk.PluginUtils").parse(a.height)),d("sdk.PluginUtils").resize(b.iframe,d("sdk.PluginUtils").parse(a.width),d("sdk.PluginUtils").parse(a.height)),b.$IframePluginClass2=!0,b.updateLift(),window.clearTimeout(b.$IframePluginClass1)});b.subscribe("xd.resize.iframe",function(a){d("sdk.PluginUtils").resize(b.iframe,d("sdk.PluginUtils").parse(a.width),d("sdk.PluginUtils").parse(a.height)),b.$IframePluginClass2=!0,b.updateLift(),window.clearTimeout(b.$IframePluginClass1)});b.subscribe("xd.sdk_event",function(a){var c=e("JSON","parse",!1,a.data);c.pluginID=l;d("sdk.Event").fire(a.event,c,j)});k=i.should_use_new_domain?d("UrlMap").resolve("social_plugin")+"/"+h+".php?":d("UrlMap").resolve("www")+"/plugins/"+h+".php?";var m={};d("sdk.PluginUtils").validate(b.getParams(),j,i,m);d("sdk.PluginUtils").validate(d("sdk.PluginUtils").baseParams,j,i,m);e("Object","assign",!1,m,{app_id:c("sdk.Runtime").getClientID(),locale:c("sdk.Runtime").getLocale(),sdk:"joey",kid_directed_site:c("sdk.Runtime").getKidDirectedSite(),channel:d("sdk.XD").handler(function(a){a!=null&&b.inform("xd."+a.type,a)},"parent.parent",!0)});b.shouldIgnoreWidth()&&(m.width=void 0);m.container_width=j.offsetWidth;d("sdk.DOM").addCss(j,"fb_iframe_widget");var n=c("guid")();b.subscribe("xd.verify",function(a){d("sdk.XD").sendToFacebook(n,{method:"xd/verify",params:e("JSON","stringify",!1,a.token)})});b.subscribe("xd.refreshLoginStatus",function(){d("sdk.AuthUtils").removeLogoutState(),c("sdk.Auth.LoginStatus").getLoginStatus(e(b.inform,"bind",!0,f.assertThisInitialized(b),"login.status"),!0)});i=document.createElement("span");e("Object","assign",!1,i.style,{verticalAlign:"top",width:m.lazy?"1px":"0px",height:m.lazy?"1px":"0px",overflow:"hidden"});b.element=j;b.ns=g;b.tag=h;b.params=m;b.iframeOptions={root:i,url:k+c("QueryString").encode(m),name:n,width:b.config.mobile_fullsize&&c("sdk.UA").mobile()?void 0:m.width||1e3,height:m.height||1e3,style:{border:"none",visibility:"hidden"},title:b.ns+":"+b.tag+" Facebook Social Plugin",testid:b.ns+":"+b.tag+" Facebook Social Plugin",onload:function(){return b.inform("render")},onerror:function(){return d("sdk.PluginUtils").collapseIframe(b.iframe)},lazy:m.lazy};b.config.fluid&&m.width!=="auto"&&(d("sdk.DOM").addCss(b.element,"fbiframe_widget_fluid_desktop"),!m.width&&b.config.full_width&&(b.element.style.width="100%",b.iframeOptions.root.style.width="100%",b.iframeOptions.style.width="100%",b.params.container_width=b.element.offsetWidth,b.iframeOptions.url=k+c("QueryString").encode(b.params)));return b}var g=b.prototype;g.shouldIgnoreWidth=function(){return c("sdk.UA").mobile()&&this.config.mobile_fullsize};g.useInlineHeightForMobile=function(){return!0};g.process=function(){var a=this;if(c("sdk.Runtime").getIsVersioned()){d("sdk.PlatformVersioning").assertVersionIsSet();var b=new(c("sdk.URI"))(this.iframeOptions.url);this.iframeOptions.url=b.setPath("/"+c("sdk.Runtime").getVersion()+b.getPath()).toString()}b=f["extends"]({},this.params);delete b.channel;var i=c("QueryString").encode(b);if(this.element.getAttribute("fb-iframe-plugin-query")==i){d("Log").info("Skipping render: %s:%s %s",this.ns,this.tag,i);this.inform("render");return}this.element.setAttribute("fb-iframe-plugin-query",i);this.subscribe("render",function(){d("sdk.Event").fire("iframeplugin:onload"),a.iframe.style.visibility="visible",a.$IframePluginClass2||d("sdk.PluginUtils").collapseIframe(a.iframe)});while(this.element.firstChild)this.element.removeChild(this.element.firstChild);this.element.appendChild(this.iframeOptions.root);var h=c("sdk.UA").mobile()?120:45;this.$IframePluginClass1=window.setTimeout(function(){d("sdk.PluginUtils").collapseIframe(a.iframe),d("Log").warn("%s:%s failed to resize in %ss",a.ns,a.tag,h)},h*1e3);this.iframe=c("sdk.createIframe")(this.iframeOptions);d("sdk.Event").fire("iframeplugin:create");(c("sdk.UA").mobile()||b.width==="auto")&&(this.useInlineHeightForMobile()&&d("sdk.DOM").addCss(this.element,"fbiframe_widget_fluid"),this.iframeOptions.width||(e("Object","assign",!1,this.element.style,{display:"block",width:"100%",height:"auto"}),e("Object","assign",!1,this.iframeOptions.root.style,{width:"100%",height:"auto"}),i={height:"auto",position:"static",width:"100%"},(c("sdk.UA").iphone()||c("sdk.UA").ipad())&&e("Object","assign",!1,i,{width:"220px",minWidth:"100%"}),e("Object","assign",!1,this.iframe.style,i)))};g.getParams=function(){return this.params};g.updateLift=function(){var a=this.iframe.style.width===this.iframeOptions.root.style.width&&this.iframe.style.height===this.iframeOptions.root.style.height;(a?d("sdk.DOM").removeCss:d("sdk.DOM").addCss)(this.iframe,"fbiframe_widget_lift")};return b}(d("sdk.Observable").Observable),i["default"]=a},98);c.__d("MPNExplicitUserInteractions",[],function(a,b,c,d,e,f){"use strict";var g=36e5;function a(a){return a==null?!1:Date.now()<=a+g}f.hasUserInteraction=a},66);c.__d("MPNLocalState",[],function(a,b,c,d,e,f){"use strict";a={LANDING_BANNER:1,WELCOME_PAGE:2,ITP_CONTINUE:3,THREAD_VIEW:4,BUBBLE:5,REENGAGEMENT_COLLAPSED_VIEW:6,REENGAGEMENT_EXPANDED_VIEW:7},b={CHAT_NOT_STARTED:1,LOGGED_IN_CHAT_STARTED:2,GUEST_CHAT_STARTED:3},c="__fb_chat_plugin",f.MPNLocalStatePath=a,f.MPNChatState=b,f.LOCAL_STATE_KEY=c},66);c.__d("getFacebookOriginForTarget",["Log"],function(a,b,c,d,e,f,g){function a(a,b){b===void 0&&(b=top);var c=0,e=!1,f=200;window.addEventListener("message",function(c){c.source===b&&c.data.xdArbiterAck&&(/\.facebook\.(com|net)$/.test(c.origin)&&/^https:/.test(c.origin)?e===!1&&(e=!0,d("Log").debug("initXdArbiter got xdArbiterAck from "+c.origin),a(c.origin)):d("Log").error("xdAbiterAck was not from Facebook: ",c.origin))},!1);b.postMessage({xdArbiterSyn:!0},"*");c=window.setInterval(function(){!e&&f>0?(f--,d("Log").debug("resending xdArbiterSyn"),b.postMessage({xdArbiterSyn:!0},"*")):window.clearInterval(c)},200)}g["default"]=a},98);c.__d("MPNSingletonProvider",[],function(a,b,c,d,e,f){"use strict";a=function(){function a(a){this.$1=null,this.$2=a}var b=a.prototype;b.get=function(){this.$1==null&&(this.$1=this.$2());return this.$1};b.clear=function(){this.$1=null};return a}(),f["default"]=a},66);c.__d("sdk.DocumentTitle",[],function(b,c,d,e,f,g){var h=document.title,i=null,a=1500,j=null,k=!1;function l(){i!==null?k?n():m(i):(clearInterval(j),j=null,n())}function m(a){document.title=a,k=!0}function n(){o(h),k=!1}function b(){return h}function o(a){document.title=a}function c(b){i=b;j===null&&(j=setInterval(l,a));return{stop:function(){i=null}}}g.get=b;g.set=o;g.blink=c},66);c.__d("sdk.cp.Constants",["MPNLocalState","UrlMap","sdk.Runtime"],function(a,b,c,d,e,f,g){"use strict";a=c("sdk.Runtime").getIsVersioned()?d("UrlMap").resolve("www")+"/"+c("sdk.Runtime").getVersion()+"/plugins/customer_chat/bubble":d("UrlMap").resolve("www")+"/plugins/customer_chat/bubble",b=c("sdk.Runtime").getIsVersioned()?d("UrlMap").resolve("social_plugin")+"/"+c("sdk.Runtime").getVersion()+"/customer_chat/bubble":d("UrlMap").resolve("social_plugin")+"/customer_chat/bubble",e={attribute:{alignment:"alignment",mobilePath:"mobile_path",desktopBottomSpacing:"desktop_bottom_spacing"},path:{landingPage:"/",welcomePage:"/welcome",bubble:"/bubble",itp:"/itpcontinue"},localStateKey:d("MPNLocalState").LOCAL_STATE_KEY,animationEvents:["animationend","mozAnimationEnd","MSAnimationEnd","oAnimationEnd","webkitAnimationEnd"],blankFrameURL:a,blankFrameNewDomainURL:b},g["default"]=e},98);c.__d("sdk.cp.Actions",["DOMEventListener","MPNExplicitUserInteractions","MPNLocalState","MPNSingletonProvider","sdk.DOM","sdk.DocumentTitle","sdk.URI","sdk.WebStorage","sdk.cp.Constants"],function(a,b,c,d,f,g,h){"use strict";var i=function(){function a(){}var b=a.prototype;b.reloadIframe=function(a,b){var e;if(a==null)return;var f=new(c("sdk.URI"))(a.src),i=f.getQueryData();i.local_state=(e=d("sdk.WebStorage").getLocalStorage())==null?void 0:e.getItem(c("sdk.cp.Constants").localStateKey);i.request_time=Date.now();(b==="true"||this.getExplicitUserInteractionFlag())&&(i.has_explicit_interaction="1");i.is_implicit_reload="1";f.setQueryData(i);a.src=f.valueOf()};b.getExplicitUserInteractionFlag=function(){var a=d("sdk.WebStorage").getLocalStorage(),b=null;if(a!=null)try{b=a.getItem(d("MPNLocalState").LOCAL_STATE_KEY)}catch(a){return!1}if(b!=null)try{a=e("JSON","parse",!1,b);return d("MPNExplicitUserInteractions").hasUserInteraction(a==null?void 0:a.euit)}catch(a){return!1}return!1};b.setDialogAppearance=function(a,b){if(a==null)return;var c=b.height,e=b.boxShadow,f=b.margin,i=b.width;b=b.bottom;e!=null&&d("sdk.DOM").setStyle(a,"boxShadow",e);f!=null&&d("sdk.DOM").setStyle(a,"margin",f);c!=null&&d("sdk.DOM").setStyle(a,"height",c);i!=null&&d("sdk.DOM").setStyle(a,"width",i);b!=null&&d("sdk.DOM").setStyle(a,"bottom",b)};b.blinkPageTitle=function(a){var b=this;a!=null?(this.$2(),this.$1=d("sdk.DocumentTitle").blink(a),c("DOMEventListener").add(window,"focus",function(a){b.$2()})):this.$1&&a==null&&this.$2()};b.$2=function(){this.$1!=null&&(this.$1.stop(),this.$1=null)};return a}();a=new(c("MPNSingletonProvider"))(function(){return new i()});b=a.get();h["default"]=b},98);c.__d("sdk.cp.Animation",["sdk.DOM","sdk.UA","sdk.cp.Constants"],function(a,b,c,d,e,f,g){"use strict";function a(a){if(!a)return;var b=d("sdk.DOM").getAttr(a,c("sdk.cp.Constants").attribute.alignment);a=d("sdk.DOM").getAttr(a,c("sdk.cp.Constants").attribute.mobilePath);var e=c("sdk.UA").mobile(),f;if(e)switch(a){case c("sdk.cp.Constants").path.landingPage:return"fb_mpn_mobile_landing_page_slide_up";case c("sdk.cp.Constants").path.welcomePage:case c("sdk.cp.Constants").path.bubble:case c("sdk.cp.Constants").path.itp:return null;default:return"fb_mpn_mobile_bounce_in"}else switch(b){case"left":f="fb_customer_chat_bounce_in_from_left";break;case"right":default:f="fb_customer_chat_bounce_in_v2"}return f}function b(a){if(!a)return;var b=d("sdk.DOM").getAttr(a,c("sdk.cp.Constants").attribute.alignment);a=d("sdk.DOM").getAttr(a,c("sdk.cp.Constants").attribute.mobilePath);var e=c("sdk.UA").mobile(),f;if(e)switch(a){case c("sdk.cp.Constants").path.landingPage:return b==="left"?"fb_mpn_mobile_landing_page_slide_out_from_left":"fb_mpn_mobile_landing_page_slide_out";case c("sdk.cp.Constants").path.bubble:return"fb_mpn_mobile_bounce_out_v2";default:return"fb_mpn_mobile_bounce_out"}else switch(b){case"left":f="fb_customer_chat_bounce_out_from_left";break;case"right":default:f="fb_customer_chat_bounce_out_v2"}return f}g.iframeBounceInAnimation=a;g.iframeBounceOutAnimation=b},98);c.__d("sdk.cp.Storage",["MPNSingletonProvider","sdk.WebStorage","sdk.cp.Constants"],function(a,b,c,d,g,h,i){"use strict";var j=function(){function a(){}var b=a.prototype;b.setState=function(a){var b=d("sdk.WebStorage").getLocalStorageForRead();if(!b)return;try{if(a==null)b.removeItem(c("sdk.cp.Constants").localStateKey);else{var h=b.getItem(c("sdk.cp.Constants").localStateKey);h==null?b.setItem(c("sdk.cp.Constants").localStateKey,e("JSON","stringify",!1,e("JSON","parse",!1,a))):(h=e("JSON","parse",!1,h),a=e("JSON","parse",!1,a),b.setItem(c("sdk.cp.Constants").localStateKey,e("JSON","stringify",!1,f["extends"]({},h,a))))}}catch(a){return}};b.getStateJSON=function(){var a=d("sdk.WebStorage").getLocalStorageForRead();if(!a)return"{}";a=a.getItem(c("sdk.cp.Constants").localStateKey);return a==null?"{}":e("JSON","stringify",!1,e("JSON","parse",!1,a))};return a}();a=new(c("MPNSingletonProvider"))(function(){return new j()});b=a.get();i["default"]=b},98);c.__d("sdk.XFBML.CustomerChatNew",["ChatPluginSDKPreLoggingUtils","DOMEventListener","IframePluginClass","Log","MPNExplicitUserInteractions","MPNLocalState","QueryString","UrlMap","getFacebookOriginForTarget","performanceAbsoluteNow","sdk.Content","sdk.DOM","sdk.DialogUtils","sdk.Event","sdk.UA","sdk.WebStorage","sdk.XD","sdk.XFBML.CustomerChatWrapper","sdk.cp.Actions","sdk.cp.Animation","sdk.cp.Constants","sdk.cp.Storage","sdk.createIframe"],function(a,b,c,d,g,h,i){"use strict";a=function(a){f.inheritsLoose(b,a);function b(b,c,f,h){var i;i=a.call(this,b,c,f,h)||this;i.$CustomerChat1=null;i.$CustomerChat2=null;i.$CustomerChat3=null;i.$CustomerChat4=null;i.$CustomerChat5=null;i.$CustomerChat6=null;i.$CustomerChat7=null;i.$CustomerChat8=null;i.$CustomerChat9=null;i.$CustomerChat10=null;i.$CustomerChat11=null;i.$CustomerChat12=!1;i.$CustomerChat13=null;i.$CustomerChat14=!1;i.$CustomerChat15=!1;i.show=function(a){a===void 0&&(a=!0),i.$CustomerChat15=!1,i.$CustomerChat1!=null&&d("sdk.DOM").setStyle(i.$CustomerChat1,"display","inline"),a&&i.$CustomerChat30(i.$CustomerChat4),d("sdk.Event").fire("customerchat.show"),i.$CustomerChat34("show")};i.hide=function(){i.$CustomerChat15=!0,i.$CustomerChat1!=null&&d("sdk.DOM").setStyle(i.$CustomerChat1,"display","none"),i.$CustomerChat29(i.$CustomerChat4),d("sdk.Event").fire("customerchat.hide"),i.$CustomerChat34("hide")};i.showDialog=function(){i.$CustomerChat1!=null&&d("sdk.DOM").setStyle(i.$CustomerChat1,"display","inline"),i.$CustomerChat30(i.$CustomerChat4),i.$CustomerChat34("showDialog")};i.hideDialog=function(){i.$CustomerChat29(i.$CustomerChat4),i.$CustomerChat34("hideDialog")};i.update=function(a){var b;d("sdk.XD").sendToFacebook((b=i.$CustomerChat5)!=null?b:"",{method:"updateCustomerChat",params:e("JSON","stringify",!1,a||{})});i.$CustomerChat34("update")};d("sdk.DOM").addCss(b,"fb_invisible_flow");d("sdk.DOM").remove(b);d("sdk.Content").append(b);i.$CustomerChat16=Boolean(h.should_use_new_domain);i.$CustomerChat17();d("sdk.Event").fire("customerchat.load");i.$CustomerChat18();d("ChatPluginSDKPreLoggingUtils").preLogging(i.$CustomerChat16,"chat_plugin_sdk_dialog_iframe_create",i.params,!1);return i}var g=b.prototype;g.$CustomerChat17=function(){var a=d("sdk.WebStorage").getLocalStorage(),b=null;if(a!=null)try{b=a.getItem(d("MPNLocalState").LOCAL_STATE_KEY)}catch(a){d("Log").warn("Failed to access localStorage")}b!=null&&e("Object","assign",!1,this.params,{local_state:b});if(b!=null)try{a=e("JSON","parse",!1,b),d("MPNExplicitUserInteractions").hasUserInteraction(a==null?void 0:a.euit)&&e("Object","assign",!1,this.params,{has_explicit_interaction:1})}catch(a){d("Log").warn("Invalid local state")}b=c("performanceAbsoluteNow")();e("Object","assign",!1,this.params,{request_time:b});a=this.$CustomerChat16?d("UrlMap").resolve("social_plugin")+"/"+this.tag+".php?":d("UrlMap").resolve("www")+"/plugins/"+this.tag+".php?";this.iframeOptions.url=a+c("QueryString").encode(this.params);this.iframeOptions.title=""};g.$CustomerChat18=function(){var a=this;this.subscribe("render",function(){d("ChatPluginSDKPreLoggingUtils").preLogging(a.$CustomerChat16,"chat_plugin_sdk_dialog_iframe_load",a.params)});this.subscribe("xd.mpn.storeState",function(a){c("sdk.cp.Storage").setState(a.state)});this.subscribe("xd.mpn.getState",function(b){b=c("sdk.cp.Storage").getStateJSON(),b={name:"mpnDidFetchState",params:b},a.$CustomerChat19(b),a.$CustomerChat20(b)});this.subscribe("xd.mpn.setupIconIframe",function(b){a.$CustomerChat21(b)});this.subscribe("xd.mpn.setupDialogIframe",function(b){a.$CustomerChat22(b)});this.subscribe("xd.mpn.toggleDialogVisibility",function(b){a.$CustomerChat23(b)});this.subscribe("xd.mpn.toggleGreetingDialogVisibility",function(b){a.$CustomerChat24(b)});this.subscribe("xd.mpn.updateGreetingAppearance",function(b){c("sdk.cp.Actions").setDialogAppearance(a.$CustomerChat10,b)});this.subscribe("xd.mpn.updateDialogAppearance",function(b){c("sdk.cp.Actions").setDialogAppearance(a.iframe,b)});this.subscribe("xd.mpn.updateIconAppearance",function(b){c("sdk.cp.Actions").setDialogAppearance(a.$CustomerChat3,b)});this.subscribe("xd.mpn.reload",function(b){c("sdk.cp.Actions").reloadIframe(a.iframe,b.hasExplicitInteraction)});this.subscribe("xd.mpn.updatePageTitle",function(a){c("sdk.cp.Actions").blinkPageTitle(a.title)});this.subscribe("xd.mpn.navigateToWelcomePage",function(b){a.$CustomerChat25(b.isHidden)});d("sdk.XFBML.CustomerChatWrapper").CustomerChatInternalEvent.subscribe(d("sdk.XFBML.CustomerChatWrapper").CustomerChatInternalEventType.SHOW,this.show);d("sdk.XFBML.CustomerChatWrapper").CustomerChatInternalEvent.subscribe(d("sdk.XFBML.CustomerChatWrapper").CustomerChatInternalEventType.HIDE,this.hide);d("sdk.XFBML.CustomerChatWrapper").CustomerChatInternalEvent.subscribe(d("sdk.XFBML.CustomerChatWrapper").CustomerChatInternalEventType.SHOW_DIALOG,this.showDialog);d("sdk.XFBML.CustomerChatWrapper").CustomerChatInternalEvent.subscribe(d("sdk.XFBML.CustomerChatWrapper").CustomerChatInternalEventType.HIDE_DIALOG,this.hideDialog);d("sdk.XFBML.CustomerChatWrapper").CustomerChatInternalEvent.subscribe(d("sdk.XFBML.CustomerChatWrapper").CustomerChatInternalEventType.UPDATE,this.update)};g.$CustomerChat21=function(b){var f=this;this.$CustomerChat1&&d("sdk.DOM").remove(this.$CustomerChat1);var g=b.frameName,h=b.iconSVG,i=d("sdk.DialogUtils").setupNewDialog(),j=e("JSON","parse",!1,b.cssText),a=document.createElement("div");h!=null&&(d("sdk.DOM").html(a,h),e("Object","assign",!1,a.style,j),a.style.boxShadow="none",d("sdk.Content").append(a,i.contentRoot));var k="blank_"+g;this.$CustomerChat1=i.dialogElement;this.$CustomerChat3=c("sdk.createIframe")({url:this.$CustomerChat16?c("sdk.cp.Constants").blankFrameNewDomainURL:c("sdk.cp.Constants").blankFrameURL,name:k,root:i.contentRoot,tabindex:-1,width:60,style:j,"data-testid":"bubble_iframe",onload:function(){d("ChatPluginSDKPreLoggingUtils").preLogging(f.$CustomerChat16,"chat_plugin_sdk_icon_iframe_load",f.params),f.$CustomerChat2=k,f.$CustomerChat26(),f.$CustomerChat27(),window.setTimeout(function(){d("sdk.DOM").remove(a)},100)}});this.$CustomerChat1&&this.$CustomerChat1.setAttribute(c("sdk.cp.Constants").attribute.alignment,b.alignment);this.$CustomerChat1&&d("sdk.Content").append(this.$CustomerChat1);var l="availabilityStatus_"+g;this.$CustomerChat8=c("sdk.createIframe")({url:this.$CustomerChat16?c("sdk.cp.Constants").blankFrameNewDomainURL:c("sdk.cp.Constants").blankFrameURL,name:l,root:i.contentRoot,tabindex:-1,style:e("JSON","parse",!1,b.availabilityStatusCssText),"data-testid":"availabilityStatus_iframe",onload:function(){f.$CustomerChat9=l,f.$CustomerChat26(),f.$CustomerChat27()}});d("sdk.Content").append(this.$CustomerChat8,i.contentRoot);var m="unread_"+g;this.$CustomerChat6=c("sdk.createIframe")({url:this.$CustomerChat16?c("sdk.cp.Constants").blankFrameNewDomainURL:c("sdk.cp.Constants").blankFrameURL,name:m,root:i.contentRoot,tabindex:-1,style:e("JSON","parse",!1,b.unreadCountCssText),"data-testid":"unread_iframe",onload:function(){f.$CustomerChat7=m,f.$CustomerChat26(),f.$CustomerChat27()}});d("sdk.Content").append(this.$CustomerChat6,i.contentRoot);h=c("sdk.UA").mobile();if(!h){var n="greeting_"+g;this.$CustomerChat10=c("sdk.createIframe")({url:this.$CustomerChat16?c("sdk.cp.Constants").blankFrameNewDomainURL:c("sdk.cp.Constants").blankFrameURL,name:n,root:i.contentRoot,tabindex:-1,style:e("JSON","parse",!1,b.greetingCssText),"data-testid":"greeting_iframe",onload:function(){f.$CustomerChat11=n,f.$CustomerChat26(),f.$CustomerChat27()}});d("sdk.Content").append(this.$CustomerChat10,i.contentRoot)}};g.$CustomerChat27=function(){this.$CustomerChat2!==null&&this.$CustomerChat7!==null&&this.$CustomerChat9!==null&&this.$CustomerChat5!==null&&this.$CustomerChat11!==null&&!this.$CustomerChat12&&(this.$CustomerChat12=!0,this.inform("iframes_loaded"))};g.$CustomerChat26=function(){var a;this.$CustomerChat19({name:"bubbleFrameLoaded",frameName:this.$CustomerChat2,unreadCountFrameName:this.$CustomerChat7,availabilityStatusIframeName:this.$CustomerChat9,greetingIframeName:this.$CustomerChat11,iconSrc:(a=this.$CustomerChat3)==null?void 0:a.src,unreadSrc:(a=this.$CustomerChat6)==null?void 0:a.src,request_time:this.params.request_time,log_id:this.params.log_id})};g.$CustomerChat25=function(a){this.$CustomerChat19({name:"navigateToWelcomePage",isHidden:a})};g.$CustomerChat22=function(a){var b=a.cssText,e=a.mobilePath,f=a.isDialogHidden;a=a.desktopBottomSpacing;this.$CustomerChat1&&(e&&this.$CustomerChat1.setAttribute(c("sdk.cp.Constants").attribute.mobilePath,e),a&&this.$CustomerChat1.setAttribute(c("sdk.cp.Constants").attribute.desktopBottomSpacing,a.toString()));this.$CustomerChat14=f==="true";this.iframe&&(this.iframe.setAttribute("data-testid","dialog_iframe"),this.iframe.style.cssText=b);this.$CustomerChat4=this.iframe;this.$CustomerChat5=this.iframe.name;this.$CustomerChat27();a=c("sdk.UA").mobile();a&&!this.$CustomerChat14&&(e==c("sdk.cp.Constants").path.landingPage&&(f=d("sdk.cp.Animation").iframeBounceInAnimation(this.$CustomerChat1),f!=null&&d("sdk.DOM").addCss(this.$CustomerChat4,f)),e!=c("sdk.cp.Constants").path.landingPage&&e!=c("sdk.cp.Constants").path.bubble&&this.$CustomerChat28());this.$CustomerChat15&&this.hide()};g.$CustomerChat23=function(a){a=a.shouldHide,a==="true"?this.$CustomerChat29(this.iframe):this.$CustomerChat30(this.iframe)};g.$CustomerChat24=function(a){a=a.shouldHide,a==="true"?this.$CustomerChat31(this.$CustomerChat10):this.$CustomerChat32(this.$CustomerChat10)};g.getParams=function(){return{allow_guests:"bool",attribution:"string",greeting_dialog_display:"string",greeting_dialog_delay:"string",logged_in_greeting:"string",logged_out_greeting:"string",minimized:"bool",page_id:"string",theme_color:"string",override:"string",attribution_version:"string",is_loaded_by_facade:"bool",current_url:"string",log_id:"string",request_time:"px"}};g.$CustomerChat33=function(a,b){var d=this,e=window.frames[a],i=function(a){e==null?void 0:e.postMessage(f["extends"]({},b),a)};this.$CustomerChat13===null?c("getFacebookOriginForTarget")(function(a){d.$CustomerChat13=a,i(d.$CustomerChat13)},e):i(this.$CustomerChat13)};g.$CustomerChat20=function(a){var b;this.$CustomerChat33((b=this.$CustomerChat2)!=null?b:"",a)};g.$CustomerChat19=function(a){var b;this.$CustomerChat33((b=this.$CustomerChat5)!=null?b:"",a)};g.$CustomerChat34=function(a){this.$CustomerChat19({name:"CustomerChat.SDK.Called",event:a})};g.$CustomerChat28=function(){var a="fb_new_ui_mobile_overlay_active";d("sdk.DOM").addCss(document.body,a)};g.$CustomerChat30=function(a){if(!a)return;if(this.$CustomerChat14){this.$CustomerChat14=!1;var b=d("sdk.cp.Animation").iframeBounceInAnimation(this.$CustomerChat1),e=d("sdk.cp.Animation").iframeBounceOutAnimation(this.$CustomerChat1);e!=null&&d("sdk.DOM").removeCss(a,e);b!=null&&d("sdk.DOM").addCss(a,b);c("sdk.UA").mobile()?(d("sdk.DOM").setStyle(a,"maxHeight","100%"),d("sdk.DOM").setStyle(a,"height","100%"),d("sdk.DOM").setStyle(a,"width","100%")):(e=this.$CustomerChat1&&d("sdk.DOM").getAttr(this.$CustomerChat1,c("sdk.cp.Constants").attribute.desktopBottomSpacing),b=e==null?"80":Number(e)+60,d("sdk.DOM").setStyle(a,"maxHeight","calc(100% - "+b+"px)"),d("sdk.DOM").setStyle(a,"minHeight","300px"));this.$CustomerChat19({name:"CustomerChat.isDialogHidden",params:{is_dialog_hidden:!1}});this.$CustomerChat20({name:"CustomerChat.isDialogHidden",params:{is_dialog_hidden:!1}});d("sdk.Event").fire("customerchat.dialogShow")}};g.$CustomerChat29=function(a){var b=this;if(!a)return;if(!this.$CustomerChat14){this.$CustomerChat14=!0;var e=d("sdk.cp.Animation").iframeBounceInAnimation(this.$CustomerChat1),f=d("sdk.cp.Animation").iframeBounceOutAnimation(this.$CustomerChat1);e!=null&&d("sdk.DOM").removeCss(a,e);f!=null&&d("sdk.DOM").addCss(a,f);var i={};c("sdk.cp.Constants").animationEvents.forEach(function(e){var f=function(e){b.$CustomerChat14&&(d("sdk.DOM").setStyle(a,"maxHeight","0"),d("sdk.DOM").setStyle(a,"minHeight","0"),c("sdk.cp.Constants").animationEvents.forEach(function(b){i[b]&&(d("DOMEventListener").remove(a,b,i[b]),delete i[b])}))};i[e]=f;d("DOMEventListener").add(a,e,f)});this.$CustomerChat19({name:"CustomerChat.isDialogHidden",params:{is_dialog_hidden:!0}});this.$CustomerChat20({name:"CustomerChat.isDialogHidden",params:{is_dialog_hidden:!0}})}d("sdk.Event").fire("customerchat.dialogHide")};g.$CustomerChat31=function(a){if(!a)return;d("sdk.DOM").setStyle(a,"maxHeight","0");d("sdk.DOM").setStyle(a,"minHeight","0")};g.$CustomerChat32=function(a){if(!a)return;d("sdk.DOM").setStyle(a,"maxHeight","calc(100% - 80px)")};return b}(c("IframePluginClass")),i["default"]=a},98);c.__d("sdk.XFBML.CustomerChatWrapper",["$InternalEnum","CORSRequest","UrlMap","sdk.Observable","sdk.XFBML.ChatDOM"],function(a,b,c,d,e,g,h){a=b("$InternalEnum")({SHOW:"SHOW",HIDE:"HIDE",SHOW_DIALOG:"SHOW_DIALOG",HIDE_DIALOG:"HIDE_DIALOG",UPDATE:"UDPATE"});e=new(d("sdk.Observable").Observable)();g=function(a,b,c,d){return new i(a,b,c,d)};var i=function(b){f.inheritsLoose(a,b);function a(a,c,d,e){var f;f=b.call(this)||this;f.$CustomerChatWrapper2=a;f.$CustomerChatWrapper3=c;f.$CustomerChatWrapper4=d;f.$CustomerChatWrapper5=e;return f}var e=a.prototype;e.process=function(){var a=this,b=d("UrlMap").resolve("social_plugin")+"/new_domain_gating/";c("CORSRequest").execute(b,"get",{page_id:this.$CustomerChatWrapper5.page_id,endpoint:this.$CustomerChatWrapper4},function(b){a.$CustomerChatWrapper5.should_use_new_domain=b.should_use_new_domain,a.$CustomerChatWrapper1=new(c("sdk.XFBML.ChatDOM"))(a.$CustomerChatWrapper2,a.$CustomerChatWrapper3,a.$CustomerChatWrapper4,a.$CustomerChatWrapper5),a.$CustomerChatWrapper1.subscribe("render",function(){a.inform("render")}),a.$CustomerChatWrapper1.process()})};return a}(d("sdk.Observable").Observable);h.CustomerChatInternalEventType=a;h.CustomerChatInternalEvent=e;h.CustomerChatWrapperPlugin=g},98);c.__d("sdk.XFBML.LWIAdsCreation",["IframePlugin","sdk.createIframe"],function(a,b,c,d,e,f,g){"use strict";a=c("IframePlugin").extend({constructor:function(a,b,c,d){this.parent(a,b,c,d),this._setUpSubscriptions()},getParams:function(){return{fbe_extras:"string",fbe_redirect_uri:"string",fbe_scopes:"string",fbe_state:"string",hide_manage_button:"bool",hide_explore_more_options:"bool",preferred_ad_options:"string"}},_setUpSubscriptions:function(){var a=this;this.subscribe("xd.lwiadscreation.load",function(b){a._createIframe(b)})},_createIframe:function(a){c("sdk.createIframe")({url:a.iframeURL,name:"LWIAdsCreationRootIframe",root:document.body,height:300,width:950})}}),b=a,g["default"]=b},98);c.__d("sdk.XFBML.LWIAdsInsights",["IframePlugin","sdk.createIframe"],function(a,b,c,d,e,f,g){"use strict";a=c("IframePlugin").extend({constructor:function(a,b,c,d){this.parent(a,b,c,d),this._setUpSubscriptions()},getParams:function(){return{fbe_extras:"string",fbe_redirect_uri:"string",fbe_scopes:"string",fbe_state:"string"}},_setUpSubscriptions:function(){var a=this;this.subscribe("xd.lwiadsinsights.load",function(b){a._createIframe(b)})},_createIframe:function(a){c("sdk.createIframe")({url:a.iframeURL,name:"LWIAdsInsightsRootIframe",root:document.body,height:800,width:1050})}}),b=a,g["default"]=b},98);c.__d("sdk.SVGLogos",["guid"],function(b,c,d,f,g,h,i){"use strict";var j="M90,212v-75h-27v-31h27v-25q0,-40 40,-40q15,0 24,2v26h-14q-16,0 -16,16v21h30l-5,31h-27v75",a="a106 106,0,1,0,-32 0",k="a106 106,1,0,1,-32 0";function l(a,b){Object.getOwnPropertyNames(b).forEach(function(c){return a.setAttribute(c,b[c])});return a}function m(a,b,c){b=l(document.createElementNS("http://www.w3.org/2000/svg",b),c);a==null?void 0:a.appendChild(b);return b}b=function(a){a=m(null,"svg",e("Object","assign",!1,{viewBox:"0 0 100 100",preserveAspectRatio:"xMinYMin"},a));m(a,"line",{x1:"0",y1:"100",x2:"100",y2:"0","stroke-width":"12"});m(a,"line",{x1:"0",y1:"0",x2:"100",y2:"100","stroke-width":"12"});return a};c=function(b){b=m(null,"svg",e("Object","assign",!1,{viewBox:"0 0 213 213",preserveAspectRatio:"xMinYMin"},b));m(b,"path",{d:j+a,"class":"f_logo_circle"});m(b,"path",{d:j+k,"class":"f_logo_f"});return b};f=function(b){b=m(null,"svg",e("Object","assign",!1,{viewBox:"0 0 213 213",preserveAspectRatio:"xMinYMin"},b));m(b,"path",{d:j+a,"class":"f_logo_circle",fill:"white"});m(b,"path",{d:j+k,"class":"f_logo_f",fill:"white"});return b};g=function(a){a=m(null,"svg",e("Object","assign",!1,{viewBox:"-2 -2 104 104",preserveAspectRatio:"xMinYMin"},a));m(a,"rect",{x:"5",y:"5",width:"91",height:"91","stroke-width":"9",rx:"23","class":"ig_logo_body"});m(a,"circle",{cx:"77",cy:"23",r:"6","class":"ig_logo_flash"});m(a,"circle",{cx:"50",cy:"50",r:"21","stroke-width":"9","class":"ig_logo_lens"});return a};h=function(a){var b=d("guid")();a=m(null,"svg",e("Object","assign",!1,{viewBox:"-2 -2 104 104",preserveAspectRatio:"xMinYMin"},a));var c=m(a,"defs",{}),f=m(c,"mask",{id:b});m(f,"circle",{cx:"77",cy:"23",r:"6",fill:"white"});m(f,"circle",{cx:"50",cy:"50",r:"21","stroke-width":"9",stroke:"white"});m(f,"rect",{x:"5",y:"5",width:"91",height:"91","stroke-width":"9",rx:"23",stroke:"white",fill:"none"});f=m(c,"linearGradient",{id:"purplepink",x1:"0",x2:".15",y1:"0",y2:".6"});m(f,"stop",{offset:"12%","stop-color":"rgb(88,85,214)"});m(f,"stop",{offset:"85%","stop-color":"rgb(215,27,122)"});f=m(c,"radialGradient",{id:"yelloworange",cx:".35",cy:"1",r:"2"});m(f,"stop",{offset:"7%","stop-color":"rgb(252,215,114)"});m(f,"stop",{offset:"20%","stop-color":"rgb(244,102,37)"});m(f,"stop",{offset:"38%","stop-color":"rgb(225,37,122)","stop-opacity":"0"});m(a,"rect",{x:"1",y:"1",width:"99",height:"99","stroke-width":"0",rx:"23",fill:"url(#purplepink)",style:"mask: url(#"+b+")"});m(a,"rect",{x:"1",y:"1",width:"99",height:"99","stroke-width":"0",rx:"23",fill:"url(#yelloworange)",style:"mask: url(#"+b+")"});return a};i.close=b;i.facebook=c;i.facebookWhite=f;i.instagram=g;i.instagramColor=h},98);c.__d("sdk.SharedStringConstants",["sdk.fbt"],function(a,b,c,d,e,f,g){"use strict";a={continueWith:c("sdk.fbt")._("Continue with {facebook_app_name} or {instagram_app_name}"),continueWithShort:c("sdk.fbt")._("{facebook_app_name} or {instagram_app_name}"),loginButtonAriaLabel:c("sdk.fbt")._("Continue with Facebook or Instagram"),logout:c("sdk.fbt")._("Logout"),logoutButtonAriaLabel:c("sdk.fbt")._("Logout the current website"),titleText:c("sdk.fbt")._("Choose Account"),promptText:c("sdk.fbt")._("Which account would you like to use to log in?"),facebookText:c("sdk.fbt")._("Log in with Facebook"),facebookTextShort:c("sdk.fbt")._("Log in"),instagramText:c("sdk.fbt")._("Log in with Instagram"),disambiguationDialogAriaLabelText:c("sdk.fbt")._("Log in with Facebook or Instagram"),fbButtonText:c("sdk.fbt")._("Continue with Facebook"),igButtonText:c("sdk.fbt")._("Continue with Instagram")},g.buttonStringsFBT=a},98);c.__d("sdk.XFBML.ShadowDOMLoginButton",["DOMPlugin","UrlMap","sdk.Auth","sdk.Event","sdk.LoggingUtils","sdk.PluginUtils","sdk.Runtime","sdk.SVGLogos","sdk.SharedStringConstants","sdk.createIframe","sdk.ui"],function(b,c,d,g,h,i,j){var k={small:"11px",medium:"13px",large:"16px"},a={small:"20px",medium:"30px",large:"40px"},l="{facebook_app_name}";b=function(b){f.inheritsLoose(c,b);function c(a,c,l,m,n){a=b.call(this,a,c,l,m,n)||this;a.stateObservers=[];a.shadowCss=["css:fb.shadow.css.fb_login_button"];a.container=document.createElement("div");a.container.classList.add("fb_login_button_container");a.container.dir="auto";a.loginButtonText=a.updateLabel();a.fbLoginButton=document.createElement("button");a.borderRadius=a.updateRadius(a.params);a.fbLoginButton=a.createSingleButton(a.loginButtonText);a.createFBButton("fb-button-main-element",g("sdk.SharedStringConstants").buttonStringsFBT.logout,g("sdk.SharedStringConstants").buttonStringsFBT.logoutButtonAriaLabel,t,function(a){this.style.display=a.status==="connected"?"flex":"none"},e(function(a){d("sdk.Auth").logout(),a&&a.detail===0&&this.fbLoginButton&&this.fbLoginButton.style.display!=="none"&&this.fbLoginButton.focus()},"bind",!0,f.assertThisInitialized(a)));return a}var l=c.prototype;l.render=function(a){var b=this;this.updateDisplay({shouldHideDisambiguation:!0,status:d("sdk.Runtime").getLoginStatus()});g("sdk.Event").subscribe("auth.statusChange",function(a){a={shouldHideDisambiguation:!0,status:a.status,fxApp:a.loginSource},b.updateDisplay(a)});return this.container};l.createSingleButton=function(a){return this.createFBButton("fb-button-main-element",a,a,s,function(a){this.style.display=a.status==="connected"?"none":"flex"},e(function(a){a.stopPropagation(),g("sdk.LoggingUtils").logLoginEvent(this.params,g("sdk.LoggingUtils").logEventName.buttonClick+"_single_fb"),this.loginTrigger()},"bind",!0,this))};l.loginTrigger=function(){var a="";d("sdk.ui")({method:"permissions.oauth",display:"popup",scope:a},this.loginCb())};l.createFBButton=function(a,b,c,d,f,k){var l=document.createElement("button");l.classList.add(a);l.setAttribute("aria-label",c);a=document.createElement("span");a.classList.add("fb_button_label_element");a.classList.add("fb_button_label");this.applyStyles(l,this.params);this.use_continue_as===!0?a.append(this.createIframeOverlay(this.container,this.params)):d(this.params,b,a);l.appendChild(a);l.addEventListener("click",function(a){k(a),l.blur()});l.updateDisplay=e(f,"bind",!0,l);this.stateObservers.push(l);this.container.appendChild(l);return l};l.loginCb=function(){var a=this;return function(b){b.authResponse!=null&&b.status==="connected"?g("sdk.LoggingUtils").logLoginEvent(a.params,g("sdk.LoggingUtils").logEventName.loginSuccess+"_single_fb"):g("sdk.LoggingUtils").logLoginEvent(a.params,g("sdk.LoggingUtils").logEventName.loginCancel+"_single_fb")}};l.updateDisplay=function(a){this.stateObservers.forEach(function(b){return b.updateDisplay(a)})};l.updateLabel=function(){var a=this.params["button-type"]==="login_with"?g("sdk.SharedStringConstants").buttonStringsFBT.facebookText:g("sdk.SharedStringConstants").buttonStringsFBT.fbButtonText,b=q(this.params,a);this.params["button-type"]==="login_with"&&(a=b?a:g("sdk.SharedStringConstants").buttonStringsFBT.facebookTextShort);return a};l.updateRadius=function(a){var b;b=(b=g("sdk.PluginUtils").getVal(a,"layout"))!=null?b:"default";a=String(g("sdk.PluginUtils").getVal(a,"size"));a=a!==""?a:"small";a=a==="large"?"4px":"3px";return b==="rounded"?"20px":a};l.applyStyles=function(b,c){var d=String(g("sdk.PluginUtils").getVal(c,"size"));d=d!==""?d:"small";b.style.borderRadius=this.borderRadius;c=(c=g("sdk.PluginUtils").getVal(c,"width"))!=null?c:null;b.style.width=m(d,c).toString();b.style.fontSize=k[d];b.style.height=a[d];b.style.backgroundColor="rgb(26,119,242)";b.style.color="#fff";b.style.border="0";b.style.fontWeight="bold"};l.createIframeOverlay=function(a,b){var c,e=String(g("sdk.PluginUtils").getVal(b,"size"));e=e!==""?e:"small";var f=d("sdk.Runtime").getClientID(),l=String(g("sdk.PluginUtils").getVal(b,"layout"));l=l!==""?l:"default";c=(c=g("sdk.PluginUtils").getVal(b,"width"))!=null?c:null;c=m(e,c).toString();f=g("UrlMap").resolve("www")+("/plugins/login_button_overlay/"+f+"/"+c+"/"+e+"/"+l+"/");l={root:a,url:f,borderRadius:(e=g("sdk.PluginUtils").getVal(b,"layout"))!=null?e:"default",width:c};a=d("sdk.createIframe")(l);a.classList.add("fb-iframe-overlay");return a};return c}(g("DOMPlugin").DOMPlugin);function m(a,b){a=a!=null?a:"small";return b===""||b==null?o(a):n(a,b)}function n(a,b){b=isNaN(b)?0:Number(b);var c=o(a),d=p(a);return b<o(a)?c:b>p(a)?d:b}function o(a){switch(a){case"large":return 240;case"medium":return 200;default:return 200}}function p(a){switch(a){case"large":return 400;case"medium":return 320;default:return 300}}function q(a,b){var c;b=b.replace(/\s?{facebook_app_name}\s?/,"");c=(c=g("sdk.PluginUtils").getVal(a,"width"))!=null?c:null;a=(a=g("sdk.PluginUtils").getVal(a,"size"))!=null?a:"large";a=m(a,c);c=r(b)?r(b):0;return c<a}function r(a){var b=r.canvas||(r.canvas=document.createElement("canvas"));b=b.getContext("2d");b=b==null?void 0:b.measureText(a);return b==null?void 0:b.width}function s(a,b,c){var d=document.createElement("span"),e=u();e.classList.add("single_button_svg_logo");c.append(e);d.textContent=q(a,b)?b:"";c.append(d)}function t(a,b,c){c.textContent="";a=b.search(l);var d=a+l.length,e=Math.min(a),f=Math.min(d);a=Math.max(a);d=Math.max(d);var m=u(),n=document.createElement("span");n.style.whiteSpace="nowrap";n.append(b.substring(0,e));e=document.createElement("span");e.style.whiteSpace="nowrap";e.append(b.substring(f,a));f=document.createElement("span");f.style.whiteSpace="nowrap";f.append(b.substring(d,b.length));c.append(m);c.append(n);c.append(e);c.append(f)}function u(){return g("sdk.SVGLogos").facebookWhite({"class":"fb_button_svg_logo login_fb_logo"})}j["default"]=b},98);c.__d("sdk.XFBML.MessengerCheckbox",["FB","IframePluginClass","Log","PluginAttrTypes","sdk.XD"],function(a,b,c,d,e,g,h){"use strict";function i(a){var b=a.app_id,c=a.page_id;a=a.user_ref;c='[page_id="'+c+'"][messenger_app_id="'+b+'"][user_ref="'+a+'"] iframe';b=document.querySelector(c);return(b==null?void 0:b.getAttribute("name"))||null}c("FB").provide("CheckboxPlugin",{confirm:function(a){var b=a.app_id,c=a.page_id,e=a.user_ref,f=i(a);if(b==null){d("Log").warn("app_id is a required parameter.");return}if(c==null){d("Log").warn("page_id is a required parameter.");return}if(e==null){d("Log").warn("user_ref is a required parameter.");return}if(f==null){d("Log").warn("No matching checkbox for the app_id, page_id, and user_ref given.");return}d("sdk.XD").sendToFacebook(f,{method:"confirmCheckboxSubmission",params:a})}});a=function(a){f.inheritsLoose(b,a);function b(b,c,d,e){return a.call(this,b,c,d,e,{fluid:!0,full_width:!0,mobile_fullsize:!1})||this}var c=b.prototype;c.getParams=function(){return{messenger_app_id:d("PluginAttrTypes").string,page_id:d("PluginAttrTypes").string,pixel_id:d("PluginAttrTypes").string,prechecked:d("PluginAttrTypes").bool,allow_login:d("PluginAttrTypes").bool,size:d("PluginAttrTypes").string,origin:d("PluginAttrTypes").string,user_ref:d("PluginAttrTypes").string,identity_match:d("PluginAttrTypes").string,center_align:d("PluginAttrTypes").bool,opt_in_type:d("PluginAttrTypes").string,promotional_frequency:d("PluginAttrTypes").string,promotional_topic:d("PluginAttrTypes").string}};return b}(c("IframePluginClass"));h["default"]=a},98);c.__d("sdk.XFBML.MessengerCheckboxWrapper",["CORSRequest","UrlMap","sdk.Observable","sdk.XFBML.MessengerCheckbox"],function(a,b,c,d,e,g,h){a=function(d,a,b,c){return new i(d,a,b,c)};var i=function(b){f.inheritsLoose(a,b);function a(a,c,d,e){var f;f=b.call(this)||this;f.$MessengerCheckboxWrapper2=a;f.$MessengerCheckboxWrapper3=c;f.$MessengerCheckboxWrapper4=d;f.$MessengerCheckboxWrapper5=e;return f}var e=a.prototype;e.process=function(){var a=this,b=d("UrlMap").resolve("social_plugin")+"/new_domain_gating/";c("CORSRequest").execute(b,"get",{page_id:this.$MessengerCheckboxWrapper5.page_id,endpoint:this.$MessengerCheckboxWrapper4},function(b){a.$MessengerCheckboxWrapper5.should_use_new_domain=b.should_use_new_domain,a.$MessengerCheckboxWrapper1=new(c("sdk.XFBML.MessengerCheckbox"))(a.$MessengerCheckboxWrapper2,a.$MessengerCheckboxWrapper3,a.$MessengerCheckboxWrapper4,a.$MessengerCheckboxWrapper5),a.$MessengerCheckboxWrapper1.subscribe("render",function(){a.inform("render")}),a.$MessengerCheckboxWrapper1.process()})};return a}(d("sdk.Observable").Observable);b=a;h["default"]=b},98);c.__d("sdk.XFBML.MessengerMessageUs",["IframePluginClass","PluginAttrTypes"],function(a,b,c,d,e,g,h){"use strict";a=function(a){f.inheritsLoose(b,a);function b(b,c,d,e){return a.call(this,b,c,d,e,{fluid:!1,full_width:!1,mobile_fullsize:!0})||this}var c=b.prototype;c.getParams=function(){return{messenger_app_id:d("PluginAttrTypes").string,page_id:d("PluginAttrTypes").string,color:d("PluginAttrTypes").string,size:d("PluginAttrTypes").string}};return b}(c("IframePluginClass")),h["default"]=a},98);c.__d("sdk.XFBML.MessengerMessageUsWrapper",["sdk.Observable","sdk.XFBML.MessengerMessageUs"],function(a,b,c,d,e,g,h){a=function(d,a,b,c){return new i(d,a,b,c)};var i=function(b){f.inheritsLoose(a,b);function a(a,c,d,e){var f;f=b.call(this)||this;f.$MessengerMessageUsWrapper2=a;f.$MessengerMessageUsWrapper3=c;f.$MessengerMessageUsWrapper4=d;f.$MessengerMessageUsWrapper5=e;return f}var d=a.prototype;d.process=function(){var a=this;this.$MessengerMessageUsWrapper5.should_use_new_domain=!0;this.$MessengerMessageUsWrapper1=new(c("sdk.XFBML.MessengerMessageUs"))(this.$MessengerMessageUsWrapper2,this.$MessengerMessageUsWrapper3,this.$MessengerMessageUsWrapper4,this.$MessengerMessageUsWrapper5);this.$MessengerMessageUsWrapper1.subscribe("render",function(){a.inform("render")});this.$MessengerMessageUsWrapper1.process()};return a}(d("sdk.Observable").Observable);b=a;h["default"]=b},98);c.__d("sdk.XFBML.Save",["IframePlugin","UrlMap","sdk.Content","sdk.DOM","sdk.DialogUtils","sdk.Event","sdk.Runtime","sdk.UA","sdk.XD","sdk.createIframe"],function(a,b,c,d,f,g,h){"use strict";var i;a=c("IframePlugin").extend({constructor:function(b,f,j,g){var h=this;this.parent(b,f,j,g);var a=c("sdk.UA").mobile();this.subscribe("xd.savePluginGetBlankIframe",function(b){var c,f=function(a){a&&d("sdk.DOM").removeCss(a,"fb_invisible")},j=function(a){a&&d("sdk.DOM").addCss(a,"fb_invisible")};a&&(c=d("sdk.DialogUtils").setupNewDarkOverlay(),j(c),d("sdk.Content").append(c),d("sdk.DialogUtils").addDoubleClickAction(c,function(){return k.forEach(j)},5e3));var g=h.setupNewIframeDialog(e("JSON","parse",!1,b.data),b.fromIframe);j(g);d("sdk.Content").append(g);var k=[g,c],l=function(){k.forEach(j),d("sdk.DialogUtils").onDialogHideCleanup(a),window.clearInterval(i)},m;h.subscribe("xd.savePluginShowIframe",function(){d("sdk.Event").fire("savePlugin:hideDialog"),k.forEach(f),h.positionOnScreen(g,c),!a&&!m&&(m=d("sdk.DialogUtils").addIdleDesktopAction(g,l,7e3))});h.subscribe("xd.savePluginHideIframe",function(){return l()});d("sdk.Event").subscribe("savePlugin:hideDialog",function(){return l()});var n=window.setInterval(function(){var a=document.getElementsByName(b.fromIframe);a.length===0&&(window.clearInterval(n),l(),k.forEach(function(a){a&&a.parentNode.removeChild(a)}))},500)})},positionOnScreen:function(a,b){var e=c("sdk.UA").mobile();if(e){var f=function(a,b){b!=null&&d("sdk.DialogUtils").setDialogPositionToCenter(b,e),d("sdk.DialogUtils").setDialogPositionToCenter(a,e)};f(a,b);d("sdk.DialogUtils").addMobileOrientationChangeAction(function(c){f(a,b)});i=window.setInterval(function(){return f(a,b)},100)}else d("sdk.DOM").setStyle(a,"position","fixed"),d("sdk.DOM").setStyle(a,"top","20px"),d("sdk.DOM").setStyle(a,"right","20px")},getOverlayIFrameURL:function(){return d("UrlMap").resolve("www")+(c("sdk.Runtime").getIsVersioned()?"/"+c("sdk.Runtime").getVersion():"")+"/plugins/save/overlay?app_id="+c("sdk.Runtime").getClientID()},setupNewIframeDialog:function(a,b){var f=this,i=d("sdk.DialogUtils").setupNewDialog(),h=function(){d("sdk.XD").sendToFacebook(b,{method:"saveOverlayIFrameAck",params:e("JSON","stringify",!1,{name:"overlay_"+f._iframeOptions.name})})};c("sdk.createIframe")({url:this.getOverlayIFrameURL(),name:"overlay_"+this._iframeOptions.name,root:i.contentRoot,tabindex:-1,onload:e(h,"bind",!0,this)});d("sdk.DOM").addCss(i.contentRoot,"fb_dialog_iframe");e("Object","assign",!1,i.dialogElement.style,a.style||{});d("sdk.DOM").setStyle(i.dialogElement,"width",a.width+"px");d("sdk.DOM").setStyle(i.dialogElement,"height",a.height+"px");a.classList.forEach(function(a){return d("sdk.DOM").addCss(i.dialogElement,a)});d("sdk.DOM").removeCss(i.dialogElement,"fb_dialog_advanced");return i.dialogElement},getParams:function(){return{uri:"url",url_category:"string",size:"string"}}});b=a;h["default"]=b},98);c.__d("sdk.XFBML.SendToMessenger",["IframePluginClass","PluginAttrTypes"],function(a,b,c,d,e,g,h){"use strict";a=function(a){f.inheritsLoose(b,a);function b(b,c,d,e){return a.call(this,b,c,d,e,{fluid:!1,full_width:!1,mobile_fullsize:!0})||this}var c=b.prototype;c.getParams=function(){return{messenger_app_id:d("PluginAttrTypes").string,page_id:d("PluginAttrTypes").string,color:d("PluginAttrTypes").string,size:d("PluginAttrTypes").string,enforce_login:d("PluginAttrTypes").bool,identity_match:d("PluginAttrTypes").string,origin:d("PluginAttrTypes").string,cta_text:d("PluginAttrTypes").string,allow_login:d("PluginAttrTypes").bool}};return b}(c("IframePluginClass")),h["default"]=a},98);c.__d("sdk.XFBML.SendToMessengerWrapper",["sdk.Observable","sdk.XFBML.SendToMessenger"],function(a,b,c,d,e,g,h){a=function(d,a,b,c){return new i(d,a,b,c)};var i=function(b){f.inheritsLoose(a,b);function a(a,c,d,e){var f;f=b.call(this)||this;f.$SendToMessengerWrapper2=a;f.$SendToMessengerWrapper3=c;f.$SendToMessengerWrapper4=d;f.$SendToMessengerWrapper5=e;return f}var d=a.prototype;d.process=function(){var a=this;this.$SendToMessengerWrapper5.should_use_new_domain=!0;this.$SendToMessengerWrapper1=new(c("sdk.XFBML.SendToMessenger"))(this.$SendToMessengerWrapper2,this.$SendToMessengerWrapper3,this.$SendToMessengerWrapper4,this.$SendToMessengerWrapper5);this.$SendToMessengerWrapper1.subscribe("render",function(){a.inform("render")});this.$SendToMessengerWrapper1.process()};return a}(d("sdk.Observable").Observable);b=a;h["default"]=b},98);c.__d("sdk.XFBML.ShareButton",["IframePlugin"],function(a,b,c,d,e,f,g){"use strict";a=c("IframePlugin").extend({constructor:function(a,b,c,d){this.parent(a,b,c,d)},getParams:function(){return{href:"url",layout:"string",mobile_iframe:"bool",type:"string",size:"string"}}}),b=a,g["default"]=b},98);c.__d("sdk.XFBML.Video",["Assert","IframePlugin","ObservableMixin","sdk.Event","sdk.XD"],function(b,c,d,f,g,h,i){var j=function(){function a(a){this.$1=a.isMuted,this.$2=a.volume,this.$3=a.timePosition,this.$4=a.duration}var b=a.prototype;b.update=function(a){a.isMuted!==void 0&&(this.$1=a.isMuted),a.volume!==void 0&&(this.$2=a.volume),a.timePosition!==void 0&&(this.$3=a.timePosition),a.duration!==void 0&&(this.$4=a.duration)};b.isMuted=function(){return this.$1};b.getVolume=function(){return this.$1?0:this.$2};b.getCurrentPosition=function(){return this.$3};b.getDuration=function(){return this.$4};return a}(),a=function(){function a(a,b,c){this.$1=a,this.$2=b,this.$3=c}var b=a.prototype;b.play=function(){f("sdk.XD").sendToFacebook(this.$1,{method:"play",params:e("JSON","stringify",!1,{})})};b.pause=function(){f("sdk.XD").sendToFacebook(this.$1,{method:"pause",params:e("JSON","stringify",!1,{})})};b.seek=function(a){d("Assert").isNumber(a,"Invalid argument"),f("sdk.XD").sendToFacebook(this.$1,{method:"seek",params:e("JSON","stringify",!1,{target:a})})};b.mute=function(){f("sdk.XD").sendToFacebook(this.$1,{method:"mute",params:e("JSON","stringify",!1,{})})};b.unmute=function(){f("sdk.XD").sendToFacebook(this.$1,{method:"unmute",params:e("JSON","stringify",!1,{})})};b.setVolume=function(a){d("Assert").isNumber(a,"Invalid argument"),f("sdk.XD").sendToFacebook(this.$1,{method:"setVolume",params:e("JSON","stringify",!1,{volume:a})})};b.isMuted=function(){return this.$3.isMuted()};b.getVolume=function(){return this.$3.getVolume()};b.getCurrentPosition=function(){return this.$3.getCurrentPosition()};b.getDuration=function(){return this.$3.getDuration()};b.subscribe=function(a,b){var c=this;d("Assert").isString(a,"Invalid argument");d("Assert").isFunction(b,"Invalid argument");this.$2.subscribe(a,b);return{release:function(){c.$2.unsubscribe(a,b)}}};return a}();b=d("IframePlugin").extend({constructor:function(b,c,i,h){this.parent(b,c,i,h),this._videoController=null,this._sharedObservable=null,this._sharedVideoCache=null,this.subscribe("xd.onVideoAPIReady",function(b){this._sharedObservable=new(d("ObservableMixin"))(),this._sharedVideoCache=new j(e("JSON","parse",!1,b.data)),this._videoController=new a(this._iframeOptions.name,this._sharedObservable,this._sharedVideoCache),f("sdk.Event").fire("xfbml.ready",{type:"video",id:h.id,instance:this._videoController})}),this.subscribe("xd.stateChange",function(a){this._sharedObservable.inform(a.state)}),this.subscribe("xd.cachedStateUpdateRequest",function(a){this._sharedVideoCache.update(e("JSON","parse",!1,a.data))})},getParams:function(){return{allowfullscreen:"bool",autoplay:"bool",controls:"bool",href:"url",show_captions:"bool",show_text:"bool"}},getConfig:function(){return{fluid:!0,full_width:!0}}});c=b;i["default"]=c},98);c.__d("sdk.api-public",["ApiClient","FB","sdk.Runtime","sdk.Scribe","sdk.api","sdk.feature"],function(b,c,d,f,g,h,i){var j=d("sdk.feature")("should_log_response_error",!1),a;function b(){d("sdk.Runtime").subscribe("ClientID.change",function(a){return d("ApiClient").setClientID(a)}),d("sdk.Runtime").subscribe("AccessToken.change",function(b){a=b,d("ApiClient").setAccessToken(b)}),d("ApiClient").setDefaultParams({sdk:"joey"}),d("ApiClient").subscribe("request.complete",function(b,c,e,f){b=!1,f&&typeof f==="object"&&(f.error?(f.error=="invalid_token"||f.error.type=="OAuthException"&&f.error.code==190)&&(b=!0):f.error_code&&f.error_code=="190"&&(b=!0)),b&&a===d("sdk.Runtime").getAccessToken()&&d("sdk.Runtime").setAccessToken(null)}),d("ApiClient").subscribe("request.complete",function(a,b,c,e){(a=="/me/permissions"&&b==="delete"||a=="/restserver.php"&&c.method=="Auth.revokeAuthorization")&&e===!0&&d("sdk.Runtime").setAccessToken(null)}),d("ApiClient").subscribe("request.error",function(a,b,c,i){j&&i.error.type==="http"&&f("sdk.Scribe").log("jssdk_error",{appId:d("sdk.Runtime").getClientID(),error:"transport",extra:{name:"transport",message:e("JSON","stringify",!1,i.error)+" from "+a+" , "+b}})}),d("FB").provide("",{api:d("sdk.api")})}c={init:b};g=c;i["default"]=g},98);c.__d("sdk.MBasicInitializer",["UrlMap","sdk.DOM","sdk.Runtime","sdk.UA","sdk.URI","sdk.fbt"],function(a,b,c,d,f,g,h){var i=function(){function a(a){if(!a)return;var b=a.parentNode;if(!b)return;var e=d("sdk.DOM").getAttr(a,"href")||window.location.href,f=new(c("sdk.URI"))(d("UrlMap").resolve("m"));f.setPath("/dialog/share");f.addQueryData("href",encodeURI(e));f.addQueryData("app_id",c("sdk.Runtime").getClientID());f.addQueryData("mbasic_link",1);e=document.createElement("a");e.style="display:inline-block; zoom:1;";e.textContent=c("sdk.fbt")._("Share to Facebook");e.setAttribute("href",f.toString());e.setAttribute("target","_blank");b.insertBefore(e,a);b.removeChild(a)}e("Array","from",!1,document.getElementsByTagName("fb:share-button")).forEach(function(b){return a(b)});e("Array","from",!1,document.getElementsByClassName("fb-share-button")).forEach(function(b){return a(b)})};function a(){if(!c("sdk.UA").mBasic())return;i()}h.init=a},98);c.__d("sdk.init",["Log","ManagedError","sdk.Cookie","sdk.Event","sdk.MBasicInitializer","sdk.PlatformVersioning","sdk.Runtime","sdk.UA","sdk.URI"],function(a,b,c,d,e,g,h){function i(a){var b=typeof a==="number"&&a>0||typeof a==="string"&&/^[0-9a-f]{21,}$|^[0-9]{1,21}$/.test(a);if(b)return a.toString();d("Log").warn("Invalid App Id: Must be a number or numeric string representing the application id.");return null}function a(a){c("sdk.Runtime").getInitialized()&&d("Log").warn("FB.init has already been called - this could indicate a problem");if(c("sdk.Runtime").getIsVersioned()){if(Object.prototype.toString.call(a)!=="[object Object]")throw new(c("ManagedError"))("Invalid argument");if(a.authResponse)throw new(c("ManagedError"))("Setting authResponse is not supported");a.version||(a.version=new(c("sdk.URI"))(location.href).getQueryData().sdk_version);d("sdk.PlatformVersioning").assertValidVersion(a.version);c("sdk.Runtime").setVersion(a.version)}else/number|string/.test(typeof a)&&(d("Log").warn("FB.init called with invalid parameters"),a={apiKey:a}),a.status==null&&(a.legacyStatusInit=!0),a=f["extends"]({status:!0},a||{});var b=i(a.appId||a.apiKey);b!==null&&c("sdk.Runtime").setClientID(b);"scope"in a&&c("sdk.Runtime").setScope(a.scope);a.cookie&&(c("sdk.Runtime").setUseCookie(!0),typeof a.cookie==="string"&&d("sdk.Cookie").setDomain(a.cookie));(a.localStorage===!1||a.localStorage==="false")&&c("sdk.Runtime").setUseLocalStorage(!1);a.kidDirectedSite&&c("sdk.Runtime").setKidDirectedSite(!0);a.useFamilyLogin&&c("sdk.Runtime").setShouldLoadFamilyLogin(!0);(a.autoLogAppEvents==="1"||a.autoLogAppEvents==="true")&&(a.autoLogAppEvents=!0);a.ab&&c("sdk.Runtime").setSDKAB(a.ab);c("sdk.Runtime").setInitialized(!0);c("sdk.UA").mBasic()&&d("sdk.MBasicInitializer").init();d("sdk.Event").fire("init:post",a)}h["default"]=a},98);c.__d("sdk.init-public",["FB","QueryString","sdk.AppEvents","sdk.ErrorHandling","sdk.Event","sdk.Frictionless","sdk.XD","sdk.init"],function(a,b,c,d,f,g,h){"use strict";function a(){b("sdk.XD"),b("sdk.AppEvents"),b("sdk.Frictionless"),window.setTimeout(function(){var a=/(connect\.facebook\.net|\.facebook\.com\/assets.php|\.facebook\.net\/assets.php).*?#(.*)/;e("Array","from",!1,window.document.getElementsByTagName("script")).forEach(function(b){if(b.src){b=a.exec(b.src);if(b){var d={};b=c("QueryString").decode(b[2]);for(var e in b)if(Object.prototype.hasOwnProperty.call(b,e)){var f=b[e];f==="0"?d[e]=0:d[e]=f}c("sdk.init")(d)}}});window.fbAsyncInit&&!window.fbAsyncInit.hasRun&&(d("sdk.Event").fire("init:asyncstart"),window.fbAsyncInit.hasRun=!0,c("sdk.ErrorHandling").unguard(window.fbAsyncInit)())},0),c("FB").provide("",{init:c("sdk.init")}),d("sdk.Event").subscribe("init:post",function(){window.__buffer!==void 0&&window.__buffer.replay()}),window.setTimeout(function(){window.__buffer&&window.__buffer.opts&&c("sdk.init")(window.__buffer.opts)},0)}f={initialize:a};g=f;h["default"]=g},98);c.__d("sdk.Time",["Log","sdk.Impressions","sdk.Runtime","sdk.URI","sdk.feature"],function(b,c,d,f,g,h,i){"use strict";var j=window.performance,a=j&&"now"in j&&"getEntriesByName"in j,k,l={};function b(){function b(a,b){var c=!1;try{a=new(d("sdk.URI"))(a.name);var i=a.getDomain();a=a.getPath();c=i===b.getDomain()&&e(a,"includes",!0,"/rsrc.php/")}catch(a){f("Log").error("Malformed URL was passed to the URL constructor: Error %s occured",a.message)}return c}function c(a){var c=j.getEntriesByType("resource").filter(function(c){return b(c,new(d("sdk.URI"))(a))}),f=c.length>=1;f||(c=j.getEntriesByType("resource").filter(function(b){return e(b.name,"startsWith",!0,a)}));return c}if(a){var g=d("sdk.Runtime").getSDKUrl(),h=null;c=c(g);if(c.length>1)if(c>2)c=null;else{var i=e(c,"findIndex",!0,function(a){return e(a.name,"startsWith",!0,g+"?hash=")});i?(h=c.splice(i)[0],c=c[0]):c=null}else c.length===1?(i=document.getElementById("facebook-jssdk-iframe"),i&&i instanceof HTMLIFrameElement&&(h=i.contentWindow.performance.getEntriesByType("resource").find(function(a){return e(a.name,"startsWith",!0,g)})),c=c[0]):c=null;c&&(l.fetchTime=Math.round(c.duration),h&&(l.fetchTime+=Math.round(h.duration)),"transferSize"in c&&(l.transferSize=c.transferSize,h&&(l.transferSize+=h.transferSize)),f("Log").debug("sdkperf: it took %s ms and %s bytes to load %s",l.fetchTime,l.transferSize,g),k=c.startTime,l.ns=d("sdk.Runtime").getSDKNS(),k&&window.setTimeout(function(){var a=d("sdk.feature")("log_perf",!1),b=d("sdk.Runtime").getSDKAB();b&&(l.ab=b,a=!0);a&&f("sdk.Impressions").log(116,l)},1e4))}}function c(b){if(!a||!k)return;l[b]=Math.round(j.now()-k);f("Log").debug("sdkperf: %s logged after %s ms",b,l[b])}i.recordBootload=b;i.log=c},98);c.__d("sdk.time-public",["runOnce","sdk.Event","sdk.Time"],function(a,b,c,d,e,f,g){"use strict";function a(){d("sdk.Time").recordBootload(),d("sdk.Event").subscribe("init:post",function(){d("sdk.Time").log("init")}),d("sdk.Event").subscribe("init:asyncstart",function(){d("sdk.Time").log("asyncstart")}),d("sdk.Event").subscribe("iframeplugin:create",c("runOnce")(function(){return d("sdk.Time").log("pluginframe")})),d("sdk.Event").subscribe("iframeplugin:onload",c("runOnce")(function(){return d("sdk.Time").log("ttfp")}))}b={init:a};e=b;g["default"]=e},98);c.__d("legacy:fb.sdk.index",["FB","sdk.AppEvents-public","sdk.Auth-public","sdk.Canvas-public","sdk.Event-public","sdk.Frictionless-public","sdk.GamingServices-public","sdk.Runtime","sdk.api-public","sdk.init-public","sdk.time-public","sdk.ui"],function(a,b,c,d,e,f,g){c("sdk.api-public").init(),c("sdk.AppEvents-public").init(),c("sdk.Auth-public").init(),c("sdk.Canvas-public").init(),c("sdk.Canvas-public").initCanvasPlugin(),c("sdk.Canvas-public").initCanvasPrefetcher(),c("sdk.Canvas-public").initCanvasPresence(),c("sdk.Event-public").init(),c("sdk.Frictionless-public").init(),c("sdk.GamingServices-public").init(),c("sdk.init-public").initialize(),c("sdk.time-public").init(),c("FB").provide("",{ui:c("sdk.ui")}),c("sdk.Runtime").setIsVersioned(!0)},35);window.FB&&window.FB.__buffer&&(window.__buffer=f["extends"]({},window.FB.__buffer))}).call(b)})()}catch(a){}}f["default"]=a}),66);
__d("PolarisInitWindowsPWAKeyCommand",["ExecutionEnvironment","PolarisUA"],(function(a,b,c,d,e,f,g){"use strict";var h,i=!1;function a(){if(i)return;(h||(h=c("ExecutionEnvironment"))).canUseDOM&&d("PolarisUA").isDesktopPWA()&&(window.addEventListener("keydown",function(a){a.ctrlKey&&a.key==="r"&&window.location.reload()}),i=!0)}g["default"]=a}),98);
__d("PolarisLogPushNotification",["InstagramODS","InstagramWebNotificationFalcoEvent","PolarisConfig","PolarisQueryParams","URI","polarisReferrerFormatter"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(){d("PolarisQueryParams").isPushNotificationParam()&&(c("InstagramWebNotificationFalcoEvent").log(function(){var a;return{landing_page_url:(a=d("polarisReferrerFormatter").sanitizeReferrer(window.location.pathname))!=null?a:"",ndid:(a=d("PolarisQueryParams").getNdidParam())!=null?a:"",target_user_id:(a=d("PolarisConfig").getViewerId())!=null?a:"",web_push_event_name:"web_push_clicked"}}),i())}function i(){try{var a=new(h||(h=c("URI")))(window.location.href);a.removeQueryData("utm_source");a.removeQueryData("ndid");window.history.replaceState({},null,a)}catch(a){c("InstagramODS").incr("web.notifications.remove_utm.failure")}}g["default"]=a}),98);
__d("WebPerfDeviceInfoLogFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1871697");b=d("FalcoLoggerInternal").create("web_perf_device_info_log",a);e=b;g["default"]=e}),98);
__d("XDeviceClassRealtimeControllerRouteBuilder",["jsRouteBuilder"],(function(a,b,c,d,e,f,g){a=c("jsRouteBuilder")("/web_perf/get_perf_level/",Object.freeze({}),void 0);b=a;g["default"]=b}),98);
__d("WebDevicePerfInfoLogging",["AsyncTypedRequest","JSScheduler","Promise","WebDevicePerfInfoData","WebPerfDeviceInfoLogFalcoEvent","XDeviceClassRealtimeControllerRouteBuilder","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(a){var b=document.createElement("canvas");b=b.getContext("webgl")||b.getContext("experimental-webgl");if(!b)return;var c=b.getExtension("WEBGL_debug_renderer_info");if(!c)return;var d=b.getParameter(c.UNMASKED_RENDERER_WEBGL);b=b.getParameter(c.UNMASKED_VENDOR_WEBGL);a.gpu_vendor=b;a.gpu_renderer=d}function k(){var a=window.navigator,b={};a&&a.hardwareConcurrency!==void 0&&(b.cpu_cores=a.hardwareConcurrency);a&&a.deviceMemory!==void 0&&(b.ram=a.deviceMemory);c("WebDevicePerfInfoData").needsFullUpdate&&j(b);return b}function l(){var a=k();c("WebPerfDeviceInfoLogFalcoEvent").log(function(){var b;return{cpu_cores:(b=a.cpu_cores)!=null?b:null,ram:(b=a.ram)!=null?b:null,gpu_renderer:(b=a.gpu_renderer)!=null?b:null,gpu_vendor:(b=a.gpu_vendor)!=null?b:null}})}function m(){return n.apply(this,arguments)}function n(){n=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var a=k();a=(yield new(c("AsyncTypedRequest"))(c("XDeviceClassRealtimeControllerRouteBuilder").buildURL({})).setData(a).promisePayload());return a.devicePerfClassLevel});return n.apply(this,arguments)}function a(){(c("WebDevicePerfInfoData").needsFullUpdate||c("WebDevicePerfInfoData").needsPartialUpdate)&&(i||(i=d("JSScheduler"))).scheduleSpeculativeCallback(l)}function e(){return new(h||(h=b("Promise")))(function(a,b){c("WebDevicePerfInfoData").needsFullUpdate||c("WebDevicePerfInfoData").needsPartialUpdate?(i||(i=d("JSScheduler"))).scheduleSpeculativeCallback(function(){m().then(a)["catch"](b)}):a()})}g.doLog=a;g.doLogPromise=e}),98);
__d("ZenonProductConnectFunnel",["FBLogger","performanceNow","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";var h,i=function(){return Math.trunc((h||(h=c("performanceNow")))())};a=function(){function a(){this.$6=null,this.$7=null,this.$1=null,this.$2=null,this.$3=null,this.$4=null,this.$5=null,this.$8=new Map(),this.$9=new Map()}var b=a.prototype;b.$10=function(a){var b=this.$8.get(a);if(b!=null)return b;b={acceptedTs:this.$3,connectedTs:null,connectingTs:null,contactingTs:null,endCallTs:null,permissionsReceivedTs:this.$5,permissionsRequestTs:this.$4,ringingTs:this.$2,triggeredTs:this.$1};var c={cameraPermission:this.$6,microphonePermission:this.$7};this.clearPreJoinTimings();this.$8.set(a,b);this.$9.set(a,c);return b};b.$11=function(a,b){a=a==null?void 0:(a=a.getLogIdentifiers())==null?void 0:a.clientSessionID;if(a==null){c("FBLogger")("rtc_www").warn("ProductConnectFunnel: could not record UI event due to missing signaling ID");return}a=this.$10(a);if(a[b]!=null)return;a[b]=i()};b.getTimings=function(a){return this.$8.get(a)};b.getPreJoinTimings=function(){return{acceptedTs:this.$3,ringingTs:this.$2,triggeredTs:this.$1}};b.clearPreJoinTimings=function(){this.$3=null,this.$2=null,this.$1=null,this.$4=null,this.$5=null,this.$6=null,this.$7=null};b.setPreJoinTimings=function(a){this.$3=a.acceptedTs,this.$2=a.ringingTs,this.$1=a.triggeredTs};b.getAnnotations=function(a){return this.$9.get(a)};b.remove=function(a){this.clearPreJoinTimings(),this.$8["delete"](a),this.$9["delete"](a)};b.setUITriggered=function(){var a;(a=this.$1)!=null?a:this.$1=i()};b.setUIPermissionsRequest=function(){var a=this;if(this.$4!=null)return;this.$4=i();c("promiseDone")(navigator.permissions.query({name:"microphone"}).then(function(b){a.$7=b.state}));c("promiseDone")(navigator.permissions.query({name:"camera"}).then(function(b){a.$6=b.state}))};b.setUIPermissionsReceived=function(){var a;(a=this.$5)!=null?a:this.$5=i()};b.setUIAccepted=function(){var a;(a=this.$3)!=null?a:this.$3=i()};b.setUIConnected=function(a){this.$11(a,"connectedTs")};b.setUIConnecting=function(a){this.$11(a,"connectingTs")};b.setUIRinging=function(){var a;(a=this.$2)!=null?a:this.$2=i()};b.setUIContacting=function(a){this.$11(a,"contactingTs")};b.setUIEnded=function(a){this.$11(a,"endCallTs")};return a}();b=new a();g["default"]=b}),98);
__d("polarisDebugLogODS",["cr:4890"],(function(a,b,c,d,e,f,g){"use strict";function a(a,c){b("cr:4890")==null?void 0:b("cr:4890")(a,c)}g["default"]=a}),98);