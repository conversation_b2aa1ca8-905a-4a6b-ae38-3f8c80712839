;/*FB_PKG_DELIM*/

__d("LSUpdateCommunityThreadStaleState",[],(function(a,b,c,d,e,f){function a(){var a=arguments,b=a[a.length-1],c=[];return b.sequence([function(c){return b.filter(b.db.table(9).fetch([[[a[0]]]]),function(c){return b.i64.eq(c.threadKey,a[0])&&([b.i64.cast([0,23]),b.i64.cast([0,21]),b.i64.cast([0,18]),b.i64.cast([0,26]),b.i64.cast([0,27])].some(function(a){return b.i64.eq(c.threadType,a)})||b.i64.eq(c.threadType,b.i64.cast([0,152])))}).next().then(function(c,d){d=c.done;c=c.value;return d?0:(c.item,b.db.table(294).fetch([[[a[0]]]]).next().then(function(c,d){d=c.done;c=c.value;return d?b.db.table(294).add({threadKey:a[0],threadQueueSequenceId:b.i64.cast([0,0]),isStale:a[1]}):(c.item,b.forEach(b.db.table(294).fetch([[[a[0]]]]),function(b){var c=b.update;b.item;return c({isStale:a[1]})}))}))})},function(a){return b.resolve(c)}])}a.__sproc_name__="LSMailboxUpdateCommunityThreadStaleStateStoredProcedure";a.__tables__=["threads","community_thread_sync_info"];e.exports=a}),null);
__d("LSUpdateThreadNullState",[],(function(a,b,c,d,e,f){function a(){var a=arguments,b=a[a.length-1],c=[];return b.sequence([function(c){return b.forEach(b.db.table(9).fetch([[[a[0]]]]),function(b){var c=b.update;b.item;return c({threadPictureUrl:a[1],threadPictureUrlFallback:a[2],threadPictureUrlExpirationTimestampMs:a[3],nullstateDescriptionText1:a[5],nullstateDescriptionType1:a[10],nullstateDescriptionText2:a[6],nullstateDescriptionType2:a[11],nullstateDescriptionText3:a[7],nullstateDescriptionType3:a[12],capabilities:a[15],threadName:a[16],threadSubtype:a[4]})})},function(a){return b.resolve(c)}])}a.__sproc_name__="LSMailboxUpdateThreadNullStateStoredProcedure";a.__tables__=["threads"];e.exports=a}),null);