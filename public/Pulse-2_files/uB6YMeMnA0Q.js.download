;/*FB_PKG_DELIM*/

__d("useSubscriptionValue",["react"],(function(a,b,c,d,e,f,g){var h;b=h||d("react");var i=b.useCallback,j=b.useEffect,k=b.useState;function a(a){var b=a.getCurrentValue,c=a.subscribe;a=k(function(){return b()});var d=a[0],e=a[1],f=i(function(){e(b)},[b]);a=k(function(){return b});var g=a[0];a=a[1];g!==b&&(a(function(){return b}),f());j(function(){var a=!1,b=function(){a||f()},d=c(b);f();return function(){a=!0,d()}},[f,c]);return d}g["default"]=a}),98);
__d("useWindowSize",["ExecutionEnvironment","react","throttle","useSubscriptionValue"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=(i||d("react")).useCallback;function k(){return!(h||(h=c("ExecutionEnvironment"))).canUseDOM?{innerHeight:0,innerWidth:0,outerHeight:0,outerWidth:0}:{innerHeight:window.innerHeight,innerWidth:window.innerWidth,outerHeight:window.outerHeight,outerWidth:window.outerWidth}}function a(a){a===void 0&&(a=500);var b=j(function(b){var d=c("throttle")(b,a);window.addEventListener("resize",d);return function(){window.removeEventListener("resize",d)}},[a]);return c("useSubscriptionValue")({getCurrentValue:k,subscribe:b})}g["default"]=a}),98);