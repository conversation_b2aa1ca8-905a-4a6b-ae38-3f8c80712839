;/*FB_PKG_DELIM*/

__d("CentralImpressionRecord",["getStyleProperty","intersectionObserverEntryIsIntersecting"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return{impressedAt:0,invisibleReason:null,status:null,target:a,visible:!1,visiblePercentage:0}}function b(a,b){var d=a.target,e=a.boundingClientRect,f=null,g=!1;!c("intersectionObserverEntryIsIntersecting")(a)?f="NOT_IN_VIEWPORT":e==null?f="UNDEFINED_BOUNDING_RECT":e.height===0||e.width===0?f="TARGET_SIZE_0":c("getStyleProperty")(d,"opacity")==="0"?f="TARGET_TRANSPARENT":c("getStyleProperty")(d,"visibility")==="hidden"?f="TARGET_HIDDEN":g=!0;e=g===!1?0:a.intersectionRatio;var h=b.visible,i=b.impressedAt,j=null;h&&!g?j="EXIT":!h&&g?(j="ENTER",i=Date.now()):b.visiblePercentage!==e&&(j="UPDATE");return{boundingClientRect:a.boundingClientRect,impressedAt:i,intersectionRect:a.intersectionRect,invisibleReason:f,rootBounds:a.rootBounds,status:j,target:d,visible:g,visiblePercentage:e}}function h(a,b){return!a.visible?null:babelHelpers["extends"]({},a,{invisibleReason:b,status:"EXIT",visible:!1,visiblePercentage:0})}function d(a){return h(a,"PUSH_VIEW_HIDDEN")}function e(a){return h(a,"COMPONENT_UNMOUNTED")}function f(a){return h(a,"PAGE_VISIBILITY_HIDDEN")}g.initImpressionRecord=a;g.calcNextObservedRecord=b;g.calcExitRecordOnPushViewHidden=d;g.calcExitRecordOnUnmount=e;g.calcExitRecordOnViewportHidden=f}),98);
__d("calculateImpressionViewablePercentage",[],(function(a,b,c,d,e,f){"use strict";var g={height:0,width:0,x:0,y:0};function h(a,b){var c=Math.max(a.y,b.y),d=Math.min(a.y+a.height,b.y+b.height);if(c>=d)return g;var e=Math.max(a.x,b.x);a=Math.min(a.x+a.width,b.x+b.width);return e>=a?g:{height:d-c,width:a-e,x:e,y:c}}function a(a,b,c){b={height:b.height-c.top-c.bottom,width:b.width-c.left-c.right,x:b.x+c.left,y:b.y+c.top};if(a.height===0||a.width===0)return{boundingClientRect:a,rootBounds:b,visiblePercentage:1};if(b.height<=0||b.width<=0)return{boundingClientRect:a,rootBounds:b,visiblePercentage:0};c=h(b,a);return{boundingClientRect:a,intersectionRect:c,rootBounds:b,visiblePercentage:c.height*c.width/(a.height*a.width)}}f["default"]=a}),66);
__d("isImpressionTargetOccluded",["containsNode","getViewportDimensions"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,d){d===void 0&&(d={bottom:0,left:0,right:0,top:0});try{if(!a||!b||!document.elementFromPoint)return!1;var e=c("getViewportDimensions")();if(e.height===0||e.width===0)return!1;var f=b.x,g=b.y,h=b.width;b=b.height;h=Math.min(f+h,e.width-1);b=Math.min(g+b,e.height-1);e=[{x:f,y:g},{x:f,y:b},{x:h,y:g},{x:h,y:b},{x:(f+h)/2,y:(g+b)/2}];d.top!==0&&d.top>g&&d.top<b&&e.push({x:(f+h)/2,y:(d.top+b)/2});return e.every(function(b){b=document.elementFromPoint(b.x,b.y);b=!!b&&!c("containsNode")(b,a)&&!c("containsNode")(a,b);return b})}catch(a){return!1}}g["default"]=a}),98);
__d("calcNextImpressionSnapshotRecord",["Visibility","calculateImpressionViewablePercentage","getElementPosition","getStyleProperty","getViewportDimensions","gkx","isImpressionTargetOccluded"],(function(a,b,c,d,e,f,g){"use strict";var h={bottom:0,left:0,right:0,top:0};function i(a){if(a==null||document.documentElement==null)return null;a=a.parentElement;while(a!=null){if(a===document.documentElement||a.nodeType!==1)return null;if(c("getStyleProperty")(a,"position")==="absolute"&&c("getStyleProperty")(a,"overflow")!=="visible")return a;a=a.parentElement}return null}function a(a,b,d){d===void 0&&(d={});if(c("gkx")("23489")&&d.isCometHiddenSubtree)return{impressedAt:b.impressedAt,invisibleReason:"PUSH_VIEW_HIDDEN",status:b.visible?"EXIT":null,target:a,visible:!1,visiblePercentage:0};var e=c("getElementPosition")(a),f=babelHelpers["extends"]({x:0,y:0},c("getViewportDimensions")()),g=c("calculateImpressionViewablePercentage")(e,f,h),j=!1;if(g.visiblePercentage>0){var k=i(a);k!=null&&g.intersectionRect!=null&&(j=!0,g=c("calculateImpressionViewablePercentage")(g.intersectionRect,c("getElementPosition")(k),h))}k=d;k=k.viewportMargin;j=k&&!j?c("calculateImpressionViewablePercentage")(e,f,k):g;j.visiblePercentage;f=babelHelpers.objectWithoutPropertiesLoose(j,["visiblePercentage"]);g=g;var l=g.intersectionRect;g=g.visiblePercentage;var m=null,n=!1;g<=0?m="NOT_IN_VIEWPORT":l&&c("isImpressionTargetOccluded")(a,l,k||h)?m=j.visiblePercentage===0?"OCCLUDED_BY_MARGIN":d.isCometHiddenSubtree===!0?"PUSH_VIEW_HIDDEN":"OCCLUSION_DETECTED":c("Visibility").isHidden()?m="PAGE_VISIBILITY_HIDDEN":e.height===0||e.width===0?m="TARGET_SIZE_0":c("getStyleProperty")(a,"opacity")==="0"?m="TARGET_TRANSPARENT":c("getStyleProperty")(a,"visibility")==="hidden"?m="TARGET_HIDDEN":n=!0;g=n===!1?0:j.visiblePercentage;l=b.visible;k=b.impressedAt;d=null;l&&!n?d="EXIT":!l&&n?(d="ENTER",k=Date.now()):b.visiblePercentage!==g&&(d="UPDATE");return babelHelpers["extends"]({impressedAt:k,invisibleReason:m,status:d,target:a,visible:n,visiblePercentage:g},f)}g["default"]=a}),98);
__d("CentralImpressionScrollBasedTracker",["CentralImpressionRecord","CometThrottle","ImpressionLoggingTimerThreshold","Visibility","calcNextImpressionSnapshotRecord","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b,e,f){var g=this;e===void 0&&(e=null);f===void 0&&(f={});this.THROTTLE_TIMEOUT=c("ImpressionLoggingTimerThreshold").THROTTLE_TIMEOUT;this.takeSnapshot=c("CometThrottle")(function(){if(g.$3==null||g.$8===!0)return;var a=c("calcNextImpressionSnapshotRecord")(g.$3,g.$2,g.$7);g.$9(a)},this.THROTTLE_TIMEOUT);this.takeSnapshotWithDoubleRaf=c("CometThrottle")(function(){if(g.$3==null||g.$8===!0)return;var a=c("calcNextImpressionSnapshotRecord")(g.$3,g.$2,g.$7);g.$9(a)},this.THROTTLE_TIMEOUT);this.$5=e;this.$2=d("CentralImpressionRecord").initImpressionRecord(a);this.$1=b;this.$3=a;this.$7=f;this.$8=!1;c("setTimeout")(this.takeSnapshotWithDoubleRaf,0);document.addEventListener("scroll",this.takeSnapshotWithDoubleRaf,{capture:!0,passive:!0});window.addEventListener("resize",this.takeSnapshotWithDoubleRaf,{capture:!0,passive:!0});"MutationObserver"in window&&(this.$6=new MutationObserver(this.takeSnapshotWithDoubleRaf),this.$6.observe(document,{attributes:!0,characterData:!0,childList:!0,subtree:!0}));this.$4=[c("Visibility").addListener(c("Visibility").HIDDEN,this.takeSnapshot),c("Visibility").addListener(c("Visibility").VISIBLE,this.takeSnapshot)]}var b=a.prototype;b.getOptionalUniqueID=function(){return this.$5};b.peek=function(){return this.$2};b.setIsCometHiddenInSubtree=function(a){this.$7.isCometHiddenSubtree=a};b.remove=function(){document.removeEventListener("scroll",this.takeSnapshotWithDoubleRaf,{capture:!0,passive:!0}),window.removeEventListener("resize",this.takeSnapshotWithDoubleRaf,{capture:!0,passive:!0}),this.$6&&this.$6.disconnect(),this.$4.forEach(function(a){return a.remove()}),this.$8=!0};b.unmount=function(){var a=d("CentralImpressionRecord").calcExitRecordOnUnmount(this.$2);a!=null&&this.$9(a);this.remove()};b.update=function(a,b,c){c===void 0&&(c={}),this.$1=b,this.$3=a,this.$7=babelHelpers["extends"]({},this.$7,c)};b.$9=function(a){var b=this.$2;this.$2=a;a.status!=null&&this.$1(a,this,b,this.$7.isAdRefresh||!1)};return a}();g["default"]=a}),98);
__d("IgRenderingValidationAutomaticFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("5007");b=d("FalcoLoggerInternal").create("ig_rendering_validation_automatic",a);e=b;g["default"]=e}),98);
__d("warnUnsupportedProp",["warning"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,d){c("warning")(!1,"%s component does not support prop `%s`.%s",a,b,d?" "+d:"")}g["default"]=a}),98);
__d("ImageCore.react",["CometVisualCompletionAttributes","URI","coerceImageishSprited","coerceImageishURL","getImageSourceURLFromImageish","joinClasses","react","warnUnsupportedProp"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function k(a){return a instanceof(h||(h=c("URI")))?a.toString():a}function l(a){var b=a.forwardedRef;a=babelHelpers.objectWithoutPropertiesLoose(a,["forwardedRef"]);var d=c("joinClasses")(a.className,"img"),e=k(a.src);if(e==null)return j.jsx("img",babelHelpers["extends"]({},a,{className:d,ref:b}));var f=c("coerceImageishSprited")(e),g=!!f&&a.alt!=null&&String(a.alt)!==""?j.jsx("u",{children:a.alt}):null;if(typeof e==="string")return j.jsx("img",babelHelpers["extends"]({},a,{className:d,ref:b,src:e,children:g}));if(f){a.src;var h=a.style,i=babelHelpers.objectWithoutPropertiesLoose(a,["src","style"]);return j.jsx("i",babelHelpers["extends"]({},i,c("CometVisualCompletionAttributes").CSS_IMG,{className:c("joinClasses")(d,f.type==="css"?f.className:void 0),ref:b,style:f.type==="cssless"?babelHelpers["extends"]({},h,f.style):h,children:g}))}i=c("getImageSourceURLFromImageish")(e);f=c("coerceImageishURL")(e);return a.width===void 0&&a.height===void 0&&f?j.jsx("img",babelHelpers["extends"]({},a,{className:d,height:f.height,src:i,ref:b,width:f.width,children:g})):j.jsx("img",babelHelpers["extends"]({},a,{className:d,ref:b,src:i,children:g}))}l.displayName=l.name+" [from "+f.id+"]";function a(a){var b=a.ref;a=babelHelpers.objectWithoutPropertiesLoose(a,["ref"]);Object.prototype.hasOwnProperty.call(a,"source")&&c("warnUnsupportedProp")("ImageCore","source","Did you mean `src`?");return j.jsx(l,babelHelpers["extends"]({},a,{alt:a.alt===void 0?"":a.alt,forwardedRef:b}))}a.displayName=a.name+" [from "+f.id+"]";a.displayName="ImageCore";g["default"]=a}),98);
__d("Image.react",["ImageCore.react"],(function(a,b,c,d,e,f,g){g["default"]=c("ImageCore.react")}),98);
__d("ImageWwwCssDependency",[],(function(a,b,c,d,e,f){"use strict";a=null;f["default"]=a}),66);
__d("InstagramAdBrandProfileFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("4282");b=d("FalcoLoggerInternal").create("instagram_ad_brand_profile",a);e=b;g["default"]=e}),98);
__d("InstagramAdImpressionSecondChannelFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("2964");b=d("FalcoLoggerInternal").create("instagram_ad_impression_second_channel",a);e=b;g["default"]=e}),98);
__d("InstagramAdSubImpressionSecondChannelFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("2965");b=d("FalcoLoggerInternal").create("instagram_ad_sub_impression_second_channel",a);e=b;g["default"]=e}),98);
__d("InstagramAdTimeSpentFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("3553");b=d("FalcoLoggerInternal").create("instagram_ad_time_spent",a);e=b;g["default"]=e}),98);
__d("InstagramAdViewabilityFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("3668");b=d("FalcoLoggerInternal").create("instagram_ad_viewability",a);e=b;g["default"]=e}),98);
__d("PolarisMediaPrefetcher.react",["cx","PolarisResponsiveImage.react","polarisLogAction","react"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=i||d("react"),k="PREFETCHED_IMAGE";b="PREFETCHED_VIDEO";var l=new Set(),m=new Set(),n=new Set(),o=[],p=10*1e3;e=function(a){babelHelpers.inheritsLoose(b,a);function b(){var d,e;for(var f=arguments.length,g=new Array(f),h=0;h<f;h++)g[h]=arguments[h];return(d=e=a.call.apply(a,[this].concat(g))||this,e.state={next:null},e.$6=function(){var a=e.state.next;a!=null&&(c("polarisLogAction")("mediaPrefetchSuccess",{resource:e.state.next}),l.add(a.id),e.setState(b.prefetchNext(e.props)));e.$1()&&e.$2()},d)||babelHelpers.assertThisInitialized(e)}b.getDerivedStateFromProps=function(a){return b.prefetchNext(a)};var d=b.prototype;d.componentDidMount=function(){this.$1()?this.$2():this.$3()};d.componentDidUpdate=function(){this.$3()};d.componentWillUnmount=function(){n.clear();m.clear();for(var a of o)window.clearTimeout(a);o.length=0};d.shouldComponentUpdate=function(a,b){return this.props.enabled!==a.enabled||this.state.next!==b.next};b.prefetchNext=function(a){var b=a.enabled;a=a.resources;if(!b)return null;for(b of a){if(l.has(b.id)||m.has(b.id))continue;return{next:b}}return{next:null}};d.$4=function(a){return l.has(a)||m.has(a)};d.$1=function(){if(this.props.postIds==null)return!1;for(var a of this.props.resources)if(!this.$4(a.id))return!1;return!0};d.$3=function(){var a=this,b=this.state.next;if(b!=null&&!n.has(b.id)){var c=window.setTimeout(function(){l.has(b.id)||a.$5(b.id)},p);n.add(b.id);o.push(c)}};d.$2=function(){this.props.resources.length>0&&c("polarisLogAction")("mediaPrefetchComplete",{eligibleResourceCount:this.props.resources.length,successCount:l.size,timeoutCount:m.size,viewKey:this.props.viewKey}),this.props.onLoadComplete!=null&&this.props.onLoadComplete()};d.$5=function(a){c("polarisLogAction")("mediaPrefetchTimedOut",{resource:this.state.next}),m.add(a),n["delete"](a),this.setState(b.prefetchNext(this.props)),this.$1()&&this.$2()};d.render=function(){var a=this.props,b=a.currentPostId,d=a.enabled;a=a.layoutWrapper;var e=this.state.next;if(e==null||!d)return null;d=!this.$4(e.id)&&(b==null||b!=null&&b!==e.id);b=d&&(e.type===k?j.jsx(c("PolarisResponsiveImage.react"),{className:"_ac9q",onError:this.$6,onLoad:this.$6,src:e.src,srcSet:e.srcSet},e.src):null);return j.jsx(a,{resource:e,children:b})};return b}(j.Component);e.defaultProps={enabled:!0,layoutWrapper:function(a){a=a.children;return a}};function a(){l.clear(),n.clear(),m.clear()}g.PREFETCHED_IMAGE=k;g.PREFETCHED_VIDEO=b;g.TIMEOUT_DURATION=p;g.MediaPrefetcher=e;g._resetState=a}),98);
__d("PolarisMediaPrefetchContainer_DEPRECATED.react",["PolarisMediaPrefetcher.react","PolarisPostUtils","PolarisReactRedux.react","emptyArray","polarisPostSelectors","polarisStorySelectors","react"],(function(a,b,c,d,e,f,g){"use strict";var h;h||d("react");function i(a){if(a.isVideo===!0)return null;return a.src!=null&&a.src!==""&&a.displayResources&&a.dimensions!=null?{id:a.id,intrinsicDimensions:a.dimensions,src:a.src,srcSet:a.displayResources,type:d("PolarisMediaPrefetcher.react").PREFETCHED_IMAGE}:null}function j(a){var b=a.sidecarChildren;if(b!=null&&b.length){var c=d("PolarisPostUtils").getInitialSidecarIndexFromPost(a);return i(b[c])}return i(a)}function a(a,b){var e=b.getResourceFromPost||j,f=d("polarisStorySelectors").getCurrentReel(a),g=a.stories.currentReelItemIndex;f=f!=null&&f.itemIds!=null?f.itemIds[g]:null;return{currentPostId:f,enabled:b.viewKey==null||!!a.mediaPrefetches.isEnabledForView[b.viewKey],resources:(b.postIds||c("emptyArray")).map(function(b){b=d("polarisPostSelectors").getPostOrNullById(a,b);return b?e(b):null}).filter(Boolean)}}b=d("PolarisReactRedux.react").connect(a)(d("PolarisMediaPrefetcher.react").MediaPrefetcher);g["default"]=b}),98);
__d("PolarisPCManager",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(){this.$1={}}var b=a.prototype;b.setComponentData=function(a,b,c){this.$1[b]={data:babelHelpers["extends"]({},c),trackingNode:a}};b.getRenderingData=function(){var a=this,b={};Object.keys(this.$1).forEach(function(c){c=a.$1[c];var d=c.trackingNode;!b[d]?b[d]=[babelHelpers["extends"]({},c.data)]:b[d].push(babelHelpers["extends"]({},c.data))});return b};return a}();f["default"]=a}),66);
__d("useFullViewImpression",["react","useVisibilityObserver"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");var i=b.useCallback,j=b.useRef;function a(a){var b=a.onFullViewStart,d=j({bottomRight:!1,hasBeenFullyViewed:!1,topLeft:!1});a=i(function(a){var c=a.entry;a=a.isElementVisible;if(!a&&d.current.hasBeenFullyViewed)d.current={bottomRight:!1,hasBeenFullyViewed:!1,topLeft:!1};else if(a&&!d.current.hasBeenFullyViewed){c.intersectionRatio===1?(d.current.bottomRight=!0,d.current.topLeft=!0):(d.current.bottomRight||(d.current.bottomRight=c.boundingClientRect.bottom<=c.rootBounds.bottom&&c.boundingClientRect.right<=c.rootBounds.right),d.current.topLeft||(d.current.topLeft=c.boundingClientRect.top>=c.rootBounds.top&&c.boundingClientRect.left>=c.rootBounds.left));if(d.current.bottomRight&&d.current.topLeft){d.current.hasBeenFullyViewed=!0;return b({currentTime:Date.now()})}}},[b]);var e=i(function(a){a.hiddenTime,a.visibleDuration,a.visibleTime,d.current={bottomRight:!1,hasBeenFullyViewed:!1,topLeft:!1}},[]);return c("useVisibilityObserver")({onHidden:e,onIntersection:a,options:{thresholdOverride:"EXPENSIVE"}})}g["default"]=a}),98);
__d("PolarisPCManagerProvider.react",["IgRenderingValidationAutomaticFalcoEvent","PolarisPCManager","PolarisPCManagerContext.react","react","react-compiler-runtime","useFullViewImpression","useMergeRefs","usePartialViewImpression","useStable"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react")),j=h.useState;function k(a){return a.CTA!=null&&a.MEDIA!=null&&a.TITLE!=null&&a.SPONSORED_LABEL!=null&&a.PROFILE_IMAGE!=null&&a.CAPTION!=null}function a(a){var b=d("react-compiler-runtime").c(11),e=a.adLoggingFields;a=a.children;var f=c("useStable")(l),g=j(!1),h=g[0],m=g[1];b[0]===Symbol["for"]("react.memo_cache_sentinel")?(g={onFullViewStart:function(){m(!0)}},b[0]=g):g=b[0];g=c("useFullViewImpression")(g);var n;b[1]!==e||b[2]!==h||b[3]!==f?(n={onImpressionEnd:function(){if(h===!0||k(f.getRenderingData())){if(e.userId!=null&&e.mpk!=null){var a={a_pk:e.userId,ad_id:e.adId,locale:e.locale,m_pk:e.mpk,media_type:e.mediaType,pc_component_dict_new:f.getRenderingData(),source_of_action:e.containerModule,tracking_token:e.trackingToken};c("IgRenderingValidationAutomaticFalcoEvent").log(function(){return a})}m(!1)}}},b[1]=e,b[2]=h,b[3]=f,b[4]=n):n=b[4];n=c("usePartialViewImpression")(n);g=c("useMergeRefs")(g,n);b[5]!==a||b[6]!==g?(n=typeof a==="function"?a(g):i.jsx("div",{ref:g,children:a}),b[5]=a,b[6]=g,b[7]=n):n=b[7];b[8]!==f||b[9]!==n?(a=i.jsx(c("PolarisPCManagerContext.react").Provider,{value:f,children:n}),b[8]=f,b[9]=n,b[10]=a):a=b[10];return a}function l(){return new(c("PolarisPCManager"))()}g["default"]=a}),98);
__d("PolarisSponsoredBlockedElStore",[],(function(a,b,c,d,e,f){"use strict";var g=new Set();function a(a){g.add(a)}function b(a){return g.has(a)}function c(a){return g["delete"](a)}function d(){return g.size>0}f.m=a;f.c=b;f.d=c;f.b=d}),66);
__d("PolarisSponsoredElDisplayMonitor",["clearTimeout","cometGHLContentDisplayCheck","setTimeoutCometLoggingPri"],(function(a,b,c,d,e,f,g){"use strict";var h=new Set(),i=1e3,j=null;a=function(a){h.has(a)||h.add(a);j===null&&k();return{dispose:function(){h["delete"](a),h.size===0&&(c("clearTimeout")(j),j=null)}}};var k=function a(){h.forEach(function(a,b){a=b.onLogBlockCallback;var d=b.startTime;b=b.targetRef;b=b.current;if(b==null)return;b=c("cometGHLContentDisplayCheck")(b);if(!b){b=Date.now()-d;a(b)}}),j=c("setTimeoutCometLoggingPri")(a,i)};g.m=a}),98);
__d("PolarisStoryStrings",["fbt"],(function(a,b,c,d,e,f,g,h){"use strict";function a(a){return h._(/*BTDS*/"Reply to {username}...",[h._param("username",a)])}function b(a){return h._(/*BTDS*/"Story by {username}, not seen",[h._param("username",a)])}function c(a){return h._(/*BTDS*/"Story by {username}, seen",[h._param("username",a)])}g.directReplyPlaceholder=a;g.unseenStoryTrayItemAriaLabel=b;g.seenStoryTrayItemAriaLabel=c}),226);
__d("getPolarisStoriesV3IsReelSeen_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={kind:"InlineDataFragment",name:"getPolarisStoriesV3IsReelSeen_reel"};e.exports=a}),null);
__d("getPolarisStoriesV3IsReelSeen",["CometRelay","getPolarisStoriesV3IsReelSeen_reel.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){var c,e;a=d("CometRelay").readInlineData(h!==void 0?h:h=b("getPolarisStoriesV3IsReelSeen_reel.graphql"),a);c=Number((c=a.latest_reel_media)!=null?c:0);e=Number((e=a.seen)!=null?e:0);return a.muted===!0||e>=c}g["default"]=a}),98);
__d("usePolarisSponsoredElTracker",["IgWebSwankFalcoEvent","PolarisSponsoredBlockedElStore","PolarisSponsoredElDisplayMonitor","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");b.useCallback;var i=b.useEffect,j=b.useRef;function a(a){var b=d("react-compiler-runtime").c(13),e=a.mediaType,f=a.position,g=a.postId,h=a.trackingToken,k=j(null),l=j(!1);b[0]!==e||b[1]!==f||b[2]!==h?(a=function(a){if(l.current)return;l.current=!0;c("IgWebSwankFalcoEvent").log(function(){return{en:String(0),hd:String(a),mt:String(e),sli:String(f),tt:h}});var b=k.current;b!==null&&d("PolarisSponsoredBlockedElStore").m(b)},b[0]=e,b[1]=f,b[2]=h,b[3]=a):a=b[3];var m=a;b[4]===Symbol["for"]("react.memo_cache_sentinel")?(a=function(){var a=k.current;return function(){a&&d("PolarisSponsoredBlockedElStore").d(a)}},b[4]=a):a=b[4];var n;b[5]!==f||b[6]!==g?(n=[k,f,g],b[5]=f,b[6]=g,b[7]=n):n=b[7];i(a,n);b[8]!==m?(a=function(){var a;a=null;var b=Date.now();if(k===null)return;if(l.current===!0)return;a=d("PolarisSponsoredElDisplayMonitor").m({onLogBlockCallback:m,startTime:b,targetRef:k});return function(){a&&a.dispose()}},b[8]=m,b[9]=a):a=b[9];b[10]!==m||b[11]!==g?(n=[k,g,l,m],b[10]=m,b[11]=g,b[12]=n):n=b[12];i(a,n);return k}g["default"]=a}),98);
__d("usePolarisSponsoredPostProfileClickLogger",["InstagramAdBrandProfileFalcoEvent","InstagramODS","InstagramWebAdEventsAuditFalcoEvent","PolarisContainerModuleUtils","PolarisNavChain","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;function a(a){var b=d("react-compiler-runtime").c(9),e=a.adId,f=a.adTrackingToken,g=a.analyticsContext,h=a.followStatus,i=a.mpk,j=a.postMediaType,k=a.postOwnerId,l=a.socialContext;b[0]!==e||b[1]!==f||b[2]!==g||b[3]!==h||b[4]!==i||b[5]!==j||b[6]!==k||b[7]!==(l==null?void 0:l.social_context_type)?(a=function(){var a,b=d("PolarisContainerModuleUtils").getContainerModule(g);if(e==null||f==null)return;var m={a_pk:k,ad_id:e,follow_status:h,m_pk:i,m_t:j,nav_chain:(a=c("PolarisNavChain").getInstance())==null?void 0:a.getNavChainForSend(),pigeon_reserved_keyword_module:b,source_of_action:b,tracking_token:f};c("InstagramWebAdEventsAuditFalcoEvent").log(function(){return{client_token:f,event:"profile_click",social_context_type:l==null?void 0:l.social_context_type}});c("InstagramODS").incr("web.ads.feed.profile.click");c("InstagramAdBrandProfileFalcoEvent").log(function(){return m})},b[0]=e,b[1]=f,b[2]=g,b[3]=h,b[4]=i,b[5]=j,b[6]=k,b[7]=l==null?void 0:l.social_context_type,b[8]=a):a=b[8];l==null?void 0:l.social_context_type;return a}g["default"]=a}),98);
__d("usePolarisStoriesV3IsReelSeen_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3IsReelSeen_reel",selections:[{kind:"InlineDataFragmentSpread",name:"getPolarisStoriesV3IsReelSeen_reel",selections:[{alias:null,args:null,kind:"ScalarField",name:"latest_reel_media",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"muted",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"seen",storageKey:null}],args:null,argumentDefinitions:[]}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3IsReelSeen",["CometRelay","getPolarisStoriesV3IsReelSeen","react-compiler-runtime","usePolarisStoriesV3IsReelSeen_reel.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){var e=d("react-compiler-runtime").c(2);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3IsReelSeen_reel.graphql"),a);var f;e[0]!==a?(f=c("getPolarisStoriesV3IsReelSeen")(a),e[0]=a,e[1]=f):f=e[1];return f}g["default"]=a}),98);
__d("useScrollBasedImpressionTracker",["BaseViewportMarginsContext","CentralImpressionScrollBasedTracker","HiddenSubtreePassiveContext","react"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");var i=b.useCallback,j=b.useContext,k=b.useEffect,l=b.useRef;function a(a,b){var d=l(null),e=l(null),f=j(c("BaseViewportMarginsContext")),g=l(null),h=j(c("HiddenSubtreePassiveContext")),m=i(function(b){if(d.current==null)return;if(e.current==null||b!=null&&e.current.getOptionalUniqueID()!==b){var i=!1;e.current!=null&&(e.current.unmount(),e.current=null,i=!0);e.current=new(c("CentralImpressionScrollBasedTracker"))(d.current,a,b,{isAdRefresh:i,isCometHiddenSubtree:h.getCurrentState().hiddenOrBackgrounded_FIXME,viewportMargin:f});g.current!=null&&(g.current.remove(),g.current=null);g.current=h.subscribeToChanges(function(a){e.current!=null&&e.current.setIsCometHiddenInSubtree(a.hiddenOrBackgrounded_FIXME)})}else e.current.update(d.current,a,{isCometHiddenSubtree:h.getCurrentState().hiddenOrBackgrounded_FIXME,viewportMargin:f})},[h,a,f]),n=i(function(a){d.current!==a&&a!=null&&(d.current=a,m(b))},[m,b]);k(function(){return function(){e.current!=null&&(e.current.unmount(),e.current=null),g.current!=null&&(g.current.remove(),g.current=null)}},[]);k(function(){m(b)},[m,b]);return n}g["default"]=a}),98);