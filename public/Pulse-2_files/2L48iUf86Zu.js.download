;/*FB_PKG_DELIM*/

__d("LSClearLoadingFlagsForThreadRange",[],(function(a,b,c,d,e,f){function a(){var a=arguments,b=a[a.length-1],c=[],d=[];return b.sequence([function(d){return a[1]?b.forEach(b.db.table(10).fetch([[[a[0]]]]),function(a){var b=a.update;a.item;return b({isLoadingAfter:!1})}):b.sequence([function(d){return b.i64.eq(b.i64.cast([0,0]),a[0])?b.sequence([function(c){return b.forEach(b.db.table(198).fetch([[[a[2]]]]),function(a){var b=a.update;a.item;return b({isLoadingBefore:!1})})},function(a){return c[8]=b.i64.cast([0,0]),c[9]=void 0,c[10]=!1,c[11]=!1,b.forEach(b.db.table(198).fetch(),function(a){a=a.item;return c[12]=a.minLastActivityTimestampMs,c[14]=a.minThreadKey,c[13]=b.i64.lt(c[8]==null?c[12]:c[8],c[12]),c[8]=c[13]?c[12]:c[8],c[9]=c[13]?c[14]:c[9],c[10]=c[10]||a.isLoadingBefore,c[11]=c[11]||b.i64.gt(c[12],b.i64.cast([0,1]))&&b.i64.gt(c[14],b.i64.cast([-2147483648,0]))})},function(a){return a=[c[8],c[9],c[10],c[11]],c[0]=a[0],c[1]=a[1],c[2]=a[2],c[3]=a[3],a}]):b.resolve((d=[b.i64.cast([0,0]),void 0,!1,!1],c[0]=d[0],c[1]=d[1],c[2]=d[2],c[3]=d[3],d))},function(c){return b.forEach(b.db.table(220).fetch([[[a[2],a[0]]]]),function(a){var b=a.update;a.item;return b({isLoadingBefore:!1})})},function(d){return c[4]=c[0],c[5]=c[1],c[6]=c[2],c[7]=c[3],b.forEach(b.filter(b.db.table(220).fetch(),function(c){return b.i64.eq(c.parentThreadKey,a[0])}),function(a){a=a.item;return c[8]=a.minLastActivityTimestampMs,c[10]=a.minThreadKey,c[9]=b.i64.lt(c[4]==null?c[8]:c[4],c[8]),c[4]=c[9]?c[8]:c[4],c[5]=c[9]?c[10]:c[5],c[6]=c[6]||a.isLoadingBefore,c[7]=c[7]||b.i64.gt(c[8],b.i64.cast([0,1]))&&b.i64.gt(c[10],b.i64.cast([-2147483648,0]))})},function(d){return b.forEach(b.db.table(10).fetch([[[a[0]]]]),function(a){var b=a.update;a.item;return b({isLoadingBefore:c[6]})})}])},function(a){return b.resolve(d)}])}a.__sproc_name__="LSMailboxClearLoadingFlagsForThreadRangeStoredProcedure";a.__tables__=["threads_ranges_v2__generated","inbox_threads_ranges","sync_group_threads_ranges"];e.exports=a}),null);
__d("LSDeleteThenInsertIgThreadInfo",[],(function(a,b,c,d,e,f){function a(){var a=arguments,b=a[a.length-1],c=[];return b.sequence([function(c){return b.db.table(194).put({threadKey:a[0],igThreadId:a[1],igDmSettingsMode:a[2],igDmSettingsTtlSec:a[3],igDmSettingsLastSetActorId:a[4],igDmSettingsLastSetTimestampMs:a[5],threadFbid:a[6]})},function(a){return b.resolve(c)}])}a.__sproc_name__="LSMailboxDeleteThenInsertIgThreadInfoStoredProcedure";a.__tables__=["ig_thread_info"];e.exports=a}),null);
__d("LSHasMatchingAttachmentCTA",[],(function(a,b,c,d,e,f){function a(){var a=arguments,b=a[a.length-1],c=[],d=[];return b.sequence([function(e){return b.sequence([function(d){return b.count(b.filter(b.db.table(19).fetch([[[a[0]]],"fk_attachments"]),function(c){return b.i64.eq(c.threadKey,a[0])&&c.attachmentFbid===a[1]})).then(function(a){return c[0]=a})},function(a){return d[0]=b.i64.gt(c[0],b.i64.cast([0,0]))}])},function(a){return b.resolve(d)}])}a.__sproc_name__="LSMailboxHasMatchingAttachmentCTAStoredProcedure";a.__tables__=["attachment_ctas"];e.exports=a}),null);
__d("LSInsertXmaAttachment",[],(function(a,b,c,d,e,f){function a(){var a=arguments,b=a[a.length-1],c=[];return b.sequence([function(c){return b.sequence([function(c){return b.forEach(b.filter(b.db.table(16).fetch([[[a[25],a[30],a[32]]]]),function(c){return b.i64.eq(c.threadKey,a[25])&&b.i64.eq(b.i64.cast([0,0]),a[26])&&c.messageId===a[30]&&c.attachmentFbid===a[32]&&b.i64.lt(c.authorityLevel,a[130])&&(b.i64.eq(c.attachmentType,b.i64.cast([0,7]))||b.i64.eq(c.attachmentType,b.i64.cast([0,0])))&&c.hasMedia===!1&&c.hasXma===!0&&b.i64.eq(c.ephemeralMediaState,void 0)}),function(a){return a["delete"]()})},function(c){return b.db.table(16).add({threadKey:a[25],messageId:a[30],attachmentFbid:a[32],filename:a[1],filesize:a[2],hasMedia:!1,isSharable:a[3],playableUrl:a[4],playableUrlFallback:a[5],playableUrlExpirationTimestampMs:a[6],playableUrlMimeType:a[7],previewUrl:a[8],previewUrlFallback:a[9],previewUrlExpirationTimestampMs:a[10],previewUrlMimeType:a[11],previewWidth:a[13],previewHeight:a[14],attributionAppId:a[15],attributionAppName:a[16],attributionAppIcon:a[17],attributionAppIconFallback:a[18],attributionAppIconUrlExpirationTimestampMs:a[19],attachmentIndex:a[20],accessibilitySummaryText:a[21],shouldRespectServerPreviewSize:a[22],subtitleIconUrl:a[23],shouldAutoplayVideo:a[24],attachmentType:a[27],timestampMs:a[29],offlineAttachmentId:a[31],hasXma:!0,xmaLayoutType:a[33],xmasTemplateType:a[34],collapsibleId:a[35],defaultCtaId:a[36],defaultCtaTitle:a[37],defaultCtaType:a[38],attachmentCta1Id:a[40],cta1Title:a[41],cta1IconType:a[42],cta1Type:a[43],attachmentCta2Id:a[45],cta2Title:a[46],cta2IconType:a[47],cta2Type:a[48],attachmentCta3Id:a[50],cta3Title:a[51],cta3IconType:a[52],cta3Type:a[53],imageUrl:a[54],imageUrlFallback:a[55],imageUrlExpirationTimestampMs:a[56],actionUrl:a[57],titleText:a[58],subtitleText:a[59],subtitleDecorationType:a[60],maxTitleNumOfLines:a[61],maxSubtitleNumOfLines:a[62],descriptionText:a[63],sourceText:a[64],faviconUrl:a[65],faviconUrlFallback:a[66],faviconUrlExpirationTimestampMs:a[67],listItemsId:a[69],listItemsDescriptionText:a[70],listItemsDescriptionSubtitleText:a[71],listItemsSecondaryDescriptionText:a[72],listItemId1:a[73],listItemTitleText1:a[74],listItemContactUrlList1:a[75],listItemProgressBarFilledPercentage1:a[76],listItemContactUrlExpirationTimestampList1:a[77],listItemContactUrlFallbackList1:a[78],listItemAccessibilityText1:a[79],listItemTotalCount1:a[80],listItemId2:a[81],listItemTitleText2:a[82],listItemContactUrlList2:a[83],listItemProgressBarFilledPercentage2:a[84],listItemContactUrlExpirationTimestampList2:a[85],listItemContactUrlFallbackList2:a[86],listItemAccessibilityText2:a[87],listItemTotalCount2:a[88],listItemId3:a[89],listItemTitleText3:a[90],listItemContactUrlList3:a[91],listItemProgressBarFilledPercentage3:a[92],listItemContactUrlExpirationTimestampList3:a[93],listItemContactUrlFallbackList3:a[94],listItemAccessibilityText3:a[95],listItemTotalCount3:a[96],isBorderless:a[100],headerImageUrlMimeType:a[101],headerTitle:a[102],headerSubtitleText:a[103],headerImageUrl:a[104],headerImageUrlFallback:a[105],headerImageUrlExpirationTimestampMs:a[106],previewImageDecorationType:a[107],shouldHighlightHeaderTitleInTitle:a[108],targetId:a[109],attachmentLoggingType:a[112],previewUrlLarge:a[114],bodyText:a[115],gatingType:a[116],gatingTitle:a[117],targetExpiryTimestampMs:a[118],countdownTimestampMs:a[119],shouldBlurSubattachments:a[120],verifiedType:a[121],captionBodyText:a[122],isPublicXma:a[123],replyCount:a[124],playableAudioUrl:a[125],xmaDataclass:a[126],previewOverlayCountdownExpiry:a[127],stickerType:a[128],loggingGenericXmaContentType:a[129],authorityLevel:a[130]})}])},function(a){return b.resolve(c)}])}a.__sproc_name__="LSMailboxInsertXmaAttachmentStoredProcedure";a.__tables__=["attachments"];e.exports=a}),null);
__d("LSIssueThreadCapabilitySyncTask",[],(function(a,b,c,d,e,f){function a(){var a=arguments,b=a[a.length-1],c=[];return b.resolve(c)}a.__sproc_name__="LSMailboxIssueThreadCapabilitySyncTaskStoredProcedure";a.__tables__=[];e.exports=a}),null);
__d("LSSetHMPSStatus",[],(function(a,b,c,d,e,f){function a(){var a=arguments,b=a[a.length-1],c=[];return b.resolve(c)}a.__sproc_name__="LSMailboxSetHMPSStatusStoredProcedure";a.__tables__=[];e.exports=a}),null);
__d("LSSetMessageTextHasLinks",[],(function(a,b,c,d,e,f){function a(){var a=arguments,b=a[a.length-1],c=[];return b.sequence([function(c){return b.forEach(b.filter(b.db.table(12).fetch([[[a[0],a[2],a[1]]]]),function(c){return b.i64.eq(c.threadKey,a[0])&&b.i64.eq(b.i64.cast([0,0]),b.i64.cast([0,0]))&&b.i64.eq(c.timestampMs,a[2])&&c.messageId===a[1]}),function(a){var b=a.update;a.item;return b({textHasLinks:!0})})},function(a){return b.resolve(c)}])}a.__sproc_name__="LSMailboxSetMessageTextHasLinksStoredProcedure";a.__tables__=["messages"];e.exports=a}),null);
__d("LSThreadsRangesQuery",["LSClearLoadingFlagsForThreadRange","LSGetCursor","LSIssueNewTaskAndGetTaskID","LSUpsertInboxThreadsRange","LSUpsertSyncGroupThreadsRange"],(function(a,b,c,d,e,f){function a(){var a=arguments,c=a[a.length-1],d=[],e=[];return c.sequence([function(e){return c.sequence([function(e){return a[1]&&c.i64.eq(a[6],c.i64.cast([0,0]))?c.storedProcedure(b("LSClearLoadingFlagsForThreadRange"),a[0],!1,c.i64.cast([0,1])):(a[1]&&c.i64.neq(a[6],void 0)&&c.i64.neq(a[3],void 0)||a[2]&&c.i64.neq(a[5],void 0)&&c.i64.neq(a[4],void 0))&&!(a[1]&&c.i64.neq(a[6],void 0)&&c.i64.neq(a[3],void 0)&&a[2]&&c.i64.neq(a[5],void 0)&&c.i64.neq(a[4],void 0))?c.sequence([function(e){return d[16]=a[3]==null?c.i64.cast([-2147483648,0]):a[3],d[15]=a[6]==null?c.i64.cast([0,1]):a[6],d[17]=c.i64.gt(d[15],c.i64.cast([0,1]))&&c.i64.gt(d[16],c.i64.cast([-2147483648,0])),d[4]=c.i64.eq(c.i64.cast([0,1]),c.i64.cast([0,1]))||c.i64.eq(c.i64.cast([0,1]),c.i64.cast([0,95])),d[4]&&c.i64.eq(a[0],c.i64.cast([0,0]))?c.sequence([function(a){return c.storedProcedure(b("LSUpsertInboxThreadsRange"),c.i64.cast([0,1]),d[15],d[17],!0,d[16])},function(a){return d[18]=d[15],d[19]=d[16],d[20]=!0,d[21]=d[17],c.forEach(c.db.table(198).fetch(),function(a){a=a.item;return d[22]=a.minLastActivityTimestampMs,d[24]=a.minThreadKey,d[23]=c.i64.lt(d[18]==null?d[22]:d[18],d[22]),d[18]=d[23]?d[22]:d[18],d[19]=d[23]?d[24]:d[19],d[20]=d[20]||a.isLoadingBefore,d[21]=d[21]||c.i64.gt(d[22],c.i64.cast([0,1]))&&c.i64.gt(d[24],c.i64.cast([-2147483648,0]))})},function(a){return a=[d[18],d[19],d[20],d[21]],d[0]=a[0],d[1]=a[1],d[2]=a[2],d[3]=a[3],a}]):c.resolve((e=[d[15],d[16],!0,d[17]],d[0]=e[0],d[1]=e[1],d[2]=e[2],d[3]=e[3],e))},function(e){return d[4]?c.sequence([function(e){return c.storedProcedure(b("LSUpsertSyncGroupThreadsRange"),c.i64.cast([0,1]),a[0],d[15],d[17],!0,d[16])},function(b){return d[18]=d[15],d[19]=d[16],d[20]=!0,d[21]=d[17],c.forEach(c.filter(c.db.table(220).fetch(),function(b){return c.i64.eq(b.parentThreadKey,a[0])}),function(a){a=a.item;return d[22]=a.minLastActivityTimestampMs,d[24]=a.minThreadKey,d[23]=c.i64.lt(d[18]==null?d[22]:d[18],d[22]),d[18]=d[23]?d[22]:d[18],d[19]=d[23]?d[24]:d[19],d[20]=d[20]||a.isLoadingBefore,d[21]=d[21]||c.i64.gt(d[22],c.i64.cast([0,1]))&&c.i64.gt(d[24],c.i64.cast([-2147483648,0]))})},function(a){return a=[d[18],d[19],d[20],d[21]],d[5]=a[0],d[6]=a[1],d[7]=a[2],d[8]=a[3],a}]):c.resolve((e=[d[0],d[1],d[2],d[3]],d[5]=e[0],d[6]=e[1],d[7]=e[2],d[8]=e[3],e))},function(a){return c.storedProcedure(b("LSGetCursor"),c.i64.cast([0,1])).then(function(a){return a=a,d[9]=a[0],a})},function(e){return d[10]=new c.Map(),d[10].set("is_after",a[2]),d[10].set("parent_thread_key",a[0]),d[10].set("reference_thread_key",a[2]?a[4]:a[3]),d[10].set("reference_activity_timestamp",a[2]?a[5]:a[6]),d[10].set("additional_pages_to_fetch",a[7]),d[10].set("cursor",d[9]),d[10].set("messaging_tag",void 0),d[10].set("sync_group",c.i64.cast([0,1])),c.i64.gt(c.i64.cast([0,0]),c.i64.cast([0,0]))?(d[18]=c.i64.of_float(Date.now()),d[11]=c.i64.add(d[18],c.i64.cast([0,0]))):d[11]=c.i64.cast([0,0]),d[12]=c.toJSON(d[10]),c.storedProcedure(b("LSIssueNewTaskAndGetTaskID"),"trq",c.i64.cast([0,145]),d[12],void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]),void 0,void 0,d[11],c.i64.cast([0,0])).then(function(a){return a=a,d[13]=a[0],a})},function(b){return c.db.table(10).fetch([[[a[0]]]]).next().then(function(b,e){e=b.done;b=b.value;return e?c.db.table(10).add({parentThreadKey:a[0],minThreadKey:d[6]==null?c.i64.cast([-2147483648,0]):d[6],minLastActivityTimestampMs:d[5]==null?c.i64.cast([0,1]):d[5],maxLastActivityTimestampMs:c.i64.cast([0,1]),maxThreadKey:c.i64.cast([-2147483648,0]),isLoadingBefore:d[7],isLoadingAfter:!1,hasMoreBefore:d[8],hasMoreAfter:!1}):(b.item,c.forEach(c.db.table(10).fetch([[[a[0]]]]),function(a){var b=a.update;a.item;return b({isLoadingBefore:d[7],isLoadingAfter:!1,hasMoreBefore:d[8],hasMoreAfter:!1})}))})}]):c.resolve()},function(e){return a[1]&&c.i64.eq(a[6],c.i64.cast([0,0]))?c.storedProcedure(b("LSClearLoadingFlagsForThreadRange"),a[0],!1,c.i64.cast([0,95])):(a[1]&&c.i64.neq(a[6],void 0)&&c.i64.neq(a[3],void 0)||a[2]&&c.i64.neq(a[5],void 0)&&c.i64.neq(a[4],void 0))&&!(a[1]&&c.i64.neq(a[6],void 0)&&c.i64.neq(a[3],void 0)&&a[2]&&c.i64.neq(a[5],void 0)&&c.i64.neq(a[4],void 0))?c.sequence([function(e){return d[16]=a[3]==null?c.i64.cast([-2147483648,0]):a[3],d[15]=a[6]==null?c.i64.cast([0,1]):a[6],d[17]=c.i64.gt(d[15],c.i64.cast([0,1]))&&c.i64.gt(d[16],c.i64.cast([-2147483648,0])),d[4]=c.i64.eq(c.i64.cast([0,95]),c.i64.cast([0,1]))||c.i64.eq(c.i64.cast([0,95]),c.i64.cast([0,95])),d[4]&&c.i64.eq(a[0],c.i64.cast([0,0]))?c.sequence([function(a){return c.storedProcedure(b("LSUpsertInboxThreadsRange"),c.i64.cast([0,95]),d[15],d[17],!0,d[16])},function(a){return d[18]=d[15],d[19]=d[16],d[20]=!0,d[21]=d[17],c.forEach(c.db.table(198).fetch(),function(a){a=a.item;return d[22]=a.minLastActivityTimestampMs,d[24]=a.minThreadKey,d[23]=c.i64.lt(d[18]==null?d[22]:d[18],d[22]),d[18]=d[23]?d[22]:d[18],d[19]=d[23]?d[24]:d[19],d[20]=d[20]||a.isLoadingBefore,d[21]=d[21]||c.i64.gt(d[22],c.i64.cast([0,1]))&&c.i64.gt(d[24],c.i64.cast([-2147483648,0]))})},function(a){return a=[d[18],d[19],d[20],d[21]],d[0]=a[0],d[1]=a[1],d[2]=a[2],d[3]=a[3],a}]):c.resolve((e=[d[15],d[16],!0,d[17]],d[0]=e[0],d[1]=e[1],d[2]=e[2],d[3]=e[3],e))},function(e){return d[4]?c.sequence([function(e){return c.storedProcedure(b("LSUpsertSyncGroupThreadsRange"),c.i64.cast([0,95]),a[0],d[15],d[17],!0,d[16])},function(b){return d[18]=d[15],d[19]=d[16],d[20]=!0,d[21]=d[17],c.forEach(c.filter(c.db.table(220).fetch(),function(b){return c.i64.eq(b.parentThreadKey,a[0])}),function(a){a=a.item;return d[22]=a.minLastActivityTimestampMs,d[24]=a.minThreadKey,d[23]=c.i64.lt(d[18]==null?d[22]:d[18],d[22]),d[18]=d[23]?d[22]:d[18],d[19]=d[23]?d[24]:d[19],d[20]=d[20]||a.isLoadingBefore,d[21]=d[21]||c.i64.gt(d[22],c.i64.cast([0,1]))&&c.i64.gt(d[24],c.i64.cast([-2147483648,0]))})},function(a){return a=[d[18],d[19],d[20],d[21]],d[5]=a[0],d[6]=a[1],d[7]=a[2],d[8]=a[3],a}]):c.resolve((e=[d[0],d[1],d[2],d[3]],d[5]=e[0],d[6]=e[1],d[7]=e[2],d[8]=e[3],e))},function(a){return c.storedProcedure(b("LSGetCursor"),c.i64.cast([0,95])).then(function(a){return a=a,d[9]=a[0],a})},function(e){return d[10]=new c.Map(),d[10].set("is_after",a[2]),d[10].set("parent_thread_key",a[0]),d[10].set("reference_thread_key",a[2]?a[4]:a[3]),d[10].set("reference_activity_timestamp",a[2]?a[5]:a[6]),d[10].set("additional_pages_to_fetch",a[7]),d[10].set("cursor",d[9]),d[10].set("messaging_tag",void 0),d[10].set("sync_group",c.i64.cast([0,95])),c.i64.gt(c.i64.cast([0,0]),c.i64.cast([0,0]))?(d[18]=c.i64.of_float(Date.now()),d[11]=c.i64.add(d[18],c.i64.cast([0,0]))):d[11]=c.i64.cast([0,0]),d[12]=c.toJSON(d[10]),c.storedProcedure(b("LSIssueNewTaskAndGetTaskID"),"trq",c.i64.cast([0,145]),d[12],void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]),void 0,void 0,d[11],c.i64.cast([0,0])).then(function(a){return a=a,d[13]=a[0],a})},function(b){return c.db.table(10).fetch([[[a[0]]]]).next().then(function(b,e){e=b.done;b=b.value;return e?c.db.table(10).add({parentThreadKey:a[0],minThreadKey:d[6]==null?c.i64.cast([-2147483648,0]):d[6],minLastActivityTimestampMs:d[5]==null?c.i64.cast([0,1]):d[5],maxLastActivityTimestampMs:c.i64.cast([0,1]),maxThreadKey:c.i64.cast([-2147483648,0]),isLoadingBefore:d[7],isLoadingAfter:!1,hasMoreBefore:d[8],hasMoreAfter:!1}):(b.item,c.forEach(c.db.table(10).fetch([[[a[0]]]]),function(a){var b=a.update;a.item;return b({isLoadingBefore:d[7],isLoadingAfter:!1,hasMoreBefore:d[8],hasMoreAfter:!1})}))})}]):c.resolve()}])},function(a){return c.resolve(e)}])}a.__sproc_name__="LSMailboxThreadsRangesQueryStoredProcedure";a.__tables__=["inbox_threads_ranges","sync_group_threads_ranges","threads_ranges_v2__generated"];e.exports=a}),null);
__d("LSUpdateAttachmentCtaAtIndexIgnoringAuthority",[],(function(a,b,c,d,e,f){function a(){var a=arguments,b=a[a.length-1],c=[];return b.sequence([function(c){return b.i64.eq(a[6],b.i64.cast([0,0]))?b.forEach(b.db.table(16).fetch([[[a[0],a[1],a[2]]]]),function(b){var c=b.update;b.item;return c({defaultCtaId:a[3],defaultCtaTitle:a[4],defaultCtaType:a[5]})}):b.i64.eq(a[6],b.i64.cast([0,1]))?b.forEach(b.db.table(16).fetch([[[a[0],a[1],a[2]]]]),function(b){var c=b.update;b.item;return c({attachmentCta1Id:a[3],cta1Title:a[4],cta1Type:a[5],cta1IconType:void 0})}):b.i64.eq(a[6],b.i64.cast([0,2]))?b.forEach(b.db.table(16).fetch([[[a[0],a[1],a[2]]]]),function(b){var c=b.update;b.item;return c({attachmentCta2Id:a[3],cta2Title:a[4],cta2Type:a[5],cta2IconType:void 0})}):b.i64.eq(a[6],b.i64.cast([0,3]))?b.forEach(b.db.table(16).fetch([[[a[0],a[1],a[2]]]]),function(b){var c=b.update;b.item;return c({attachmentCta3Id:a[3],cta3Title:a[4],cta3Type:a[5],cta3IconType:void 0})}):b.resolve(function(a){b.logger(a).warn(a)}("Unexpected CTA index"))},function(a){return b.resolve(c)}])}a.__sproc_name__="LSMailboxUpdateAttachmentCtaAtIndexIgnoringAuthorityStoredProcedure";a.__tables__=["attachments"];e.exports=a}),null);