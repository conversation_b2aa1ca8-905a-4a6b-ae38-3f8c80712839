;/*FB_PKG_DELIM*/

__d("PolarisFollowListContentWrapperQuery_instagramRelayOperation",[],(function(a,b,c,d,e,f){e.exports="9122536117848226"}),null);
__d("PolarisFollowListContentWrapperQuery$Parameters",["PolarisFollowListContentWrapperQuery_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a={kind:"PreloadableConcreteRequest",params:{id:b("PolarisFollowListContentWrapperQuery_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["fetch__XDTUserDict"]},name:"PolarisFollowListContentWrapperQuery",operationKind:"query",text:null}};e.exports=a}),null);
__d("PolarisProfileFeedTabRoot.entrypoint",["JSResourceForInteraction","PolarisProfilePostsQuery$Parameters","buildPolarisProfileRoute.entrypoint"],(function(a,b,c,d,e,f,g){"use strict";a=c("buildPolarisProfileRoute.entrypoint")(c("JSResourceForInteraction")("PolarisProfileFeedTabRoot.react").__setRef("PolarisProfileFeedTabRoot.entrypoint"),function(a){a=a.routeParams.username;return{queries:{contentQuery:{environmentProviderOptions:{preloaderGroup:"ig_web_profile_feed"},options:{},parameters:c("PolarisProfilePostsQuery$Parameters"),variables:{data:{count:12,include_reel_media_seen_timestamp:!0,include_relationship_info:!0,latest_besties_reel_media:!0,latest_reel_media:!0},username:a}}}}});g["default"]=a}),98);
__d("PolarisProfileFollowRoot.entrypoint",["JSResourceForInteraction","PolarisFollowListContentWrapperQuery$Parameters"],(function(a,b,c,d,e,f,g){"use strict";a={getPreloadProps:function(a){a=a.routeProps.id;return{queries:{userQuery:{parameters:c("PolarisFollowListContentWrapperQuery$Parameters"),variables:{id:a}}}}},root:c("JSResourceForInteraction")("PolarisProfileFollowRoot.react").__setRef("PolarisProfileFollowRoot.entrypoint")};g["default"]=a}),98);
__d("PolarisProfileReelsTabContentQuery_instagramRelayOperation",[],(function(a,b,c,d,e,f){e.exports="24127588873492897"}),null);
__d("PolarisProfileReelsTabContentQuery$Parameters",["PolarisProfileReelsTabContentQuery_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a={kind:"PreloadableConcreteRequest",params:{id:b("PolarisProfileReelsTabContentQuery_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__clips__user__connection_v2","xdt_viewer"]},name:"PolarisProfileReelsTabContentQuery",operationKind:"query",text:null}};e.exports=a}),null);
__d("PolarisProfileReelsTabRoot.entrypoint",["JSResourceForInteraction","PolarisProfileReelsTabContentQuery$Parameters","buildPolarisProfileRoute.entrypoint"],(function(a,b,c,d,e,f,g){"use strict";a=c("buildPolarisProfileRoute.entrypoint")(c("JSResourceForInteraction")("PolarisProfileReelsTabRoot.react").__setRef("PolarisProfileReelsTabRoot.entrypoint"),function(a){a=a.routeProps;var b=a.id;a=a.number_of_preloaded_posts;return{queries:{contentQuery:{environmentProviderOptions:{preloaderGroup:"ig_web_profile_feed"},options:{},parameters:c("PolarisProfileReelsTabContentQuery$Parameters"),variables:{data:{include_feed_video:!0,page_size:(a=a)!=null?a:12,target_user_id:b}}}}}});g["default"]=a}),98);
__d("PolarisProfileTaggedTabContentQuery_instagramRelayOperation",[],(function(a,b,c,d,e,f){e.exports="29792479243684029"}),null);
__d("PolarisProfileTaggedTabContentQuery$Parameters",["PolarisProfileTaggedTabContentQuery_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a={kind:"PreloadableConcreteRequest",params:{id:b("PolarisProfileTaggedTabContentQuery_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__usertags__user_id__feed_connection"]},name:"PolarisProfileTaggedTabContentQuery",operationKind:"query",text:null}};e.exports=a}),null);
__d("PolarisProfileTaggedTabRoot.entrypoint",["JSResourceForInteraction","PolarisProfileTaggedTabContentQuery$Parameters","buildPolarisProfileRoute.entrypoint"],(function(a,b,c,d,e,f,g){"use strict";a=c("buildPolarisProfileRoute.entrypoint")(c("JSResourceForInteraction")("PolarisProfileTaggedTabRoot.react").__setRef("PolarisProfileTaggedTabRoot.entrypoint"),function(a){a=a.routeProps;return{queries:{contentQuery:{environmentProviderOptions:{preloaderGroup:"ig_web_profile_feed"},options:{},parameters:c("PolarisProfileTaggedTabContentQuery$Parameters"),variables:{count:12,user_id:a.id}}}}});g["default"]=a}),98);