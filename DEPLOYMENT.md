# Pulse.co.za Deployment Guide

## 🚀 Vercel Deployment Checklist

### Prerequisites
- [ ] Vercel account created
- [ ] GitHub repository connected to Vercel
- [ ] Supabase project set up and configured
- [ ] Domain name ready (optional)

### Environment Variables Setup

#### Required Variables
Set these in your Vercel project settings:

```bash
# App Configuration
NEXT_PUBLIC_APP_NAME="Pulse.co.za"
NEXT_PUBLIC_APP_URL="https://your-domain.vercel.app"
NODE_ENV="production"

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your_supabase_anon_key"
SUPABASE_SERVICE_ROLE_KEY="your_supabase_service_role_key"
```

#### Optional Variables (for full functionality)
```bash
# Stripe (for payments)
STRIPE_SECRET_KEY="sk_live_your_stripe_secret_key"
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_live_your_stripe_publishable_key"
STRIPE_WEBHOOK_SECRET="whsec_your_stripe_webhook_secret"

# Email (for contact forms)
RESEND_API_KEY="re_your_resend_api_key"

# Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID="G-your_analytics_id"

# AI Features
NEXT_PUBLIC_GOOGLE_API_KEY="your_google_api_key"
```

### Deployment Steps

1. **Connect Repository to Vercel**
   ```bash
   # Install Vercel CLI (optional)
   npm i -g vercel
   
   # Login to Vercel
   vercel login
   
   # Deploy from project root
   vercel
   ```

2. **Configure Environment Variables**
   - Go to Vercel Dashboard → Your Project → Settings → Environment Variables
   - Add all required environment variables
   - Make sure to set them for Production, Preview, and Development environments

3. **Set Up Custom Domain (Optional)**
   - Go to Vercel Dashboard → Your Project → Settings → Domains
   - Add your custom domain
   - Configure DNS records as instructed

4. **Configure Supabase for Production**
   - Update Supabase project settings
   - Add your Vercel domain to allowed origins
   - Configure RLS policies for production

### Build Verification

Before deploying, verify your build works locally:

```bash
# Install dependencies
npm install

# Type check
npm run type-check

# Build the project
npm run build

# Test production build locally
npm run start
```

### Post-Deployment Checklist

- [ ] All pages load correctly
- [ ] Authentication flows work
- [ ] Database connections are successful
- [ ] Contact forms submit properly
- [ ] Images load correctly
- [ ] Theme switching works
- [ ] Mobile responsiveness verified
- [ ] Performance metrics are acceptable

### Monitoring & Analytics

1. **Vercel Analytics**
   - Enable in Vercel Dashboard → Your Project → Analytics
   - Monitor Core Web Vitals and performance

2. **Error Monitoring**
   - Check Vercel Functions logs
   - Monitor Supabase logs
   - Set up error alerts

3. **Performance Monitoring**
   - Use Lighthouse for performance audits
   - Monitor bundle size
   - Check loading times

### Troubleshooting

#### Common Issues

**Build Failures**
- Check TypeScript errors: `npm run type-check`
- Verify all environment variables are set
- Check for missing dependencies

**Runtime Errors**
- Check Vercel function logs
- Verify Supabase connection
- Check environment variable values

**Authentication Issues**
- Verify Supabase URL and keys
- Check allowed origins in Supabase
- Verify middleware configuration

**Image Loading Issues**
- Check Next.js image configuration
- Verify image domains in next.config.mjs
- Ensure images exist in public folder

### Security Considerations

- [ ] Environment variables are properly secured
- [ ] No sensitive data in client-side code
- [ ] CORS is properly configured
- [ ] Security headers are set
- [ ] Rate limiting is configured (if needed)

### Performance Optimization

- [ ] Images are optimized
- [ ] Bundle size is minimized
- [ ] Caching strategies are implemented
- [ ] CDN is properly configured
- [ ] Database queries are optimized

### Backup & Recovery

- [ ] Database backups are configured
- [ ] Environment variables are documented
- [ ] Deployment process is documented
- [ ] Recovery procedures are tested

## 📞 Support

If you encounter issues during deployment:

1. Check the [Vercel Documentation](https://vercel.com/docs)
2. Review [Supabase Documentation](https://supabase.com/docs)
3. Check project logs in Vercel Dashboard
4. Verify environment variables are correctly set

## 🔄 Continuous Deployment

The project is configured for automatic deployments:
- **Production**: Deploys from `main` branch
- **Preview**: Deploys from pull requests
- **Development**: Local development environment

Any push to the main branch will trigger a new production deployment.
