-- Drop all tables, RLS policies, and custom enum types in the public schema
-- WARNING: This is destructive and will delete all data and structure in the public schema!

-- Disable triggers to avoid dependency issues
SET session_replication_role = replica;

-- Drop all RLS policies in the public schema
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT schemaname, tablename, policyname FROM pg_policies WHERE schemaname = 'public') LOOP
        EXECUTE format('DROP POLICY IF EXISTS "%s" ON %I.%I;', r.policyname, r.schemaname, r.tablename);
    END LOOP;
END $$;

-- Drop all tables in the public schema (<PERSON><PERSON><PERSON><PERSON> drops all relations/foreign keys)
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
        EXECUTE format('DROP TABLE IF EXISTS %I CASCADE;', r.tablename);
    END LOOP;
END $$;

-- Drop all custom enum types in the public schema
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (
        SELECT t.typname
        FROM pg_type t
        LEFT JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace
        WHERE (t.typrelid = 0 OR (SELECT c.relkind = 'c' FROM pg_catalog.pg_class c WHERE c.oid = t.typrelid))
          AND NOT EXISTS (
              SELECT 1 FROM pg_catalog.pg_type el WHERE el.oid = t.typelem AND el.typarray = t.oid
          )
          AND n.nspname = 'public'
          AND t.typtype = 'e' -- only enums
    ) LOOP
        EXECUTE format('DROP TYPE IF EXISTS %I CASCADE;', r.typname);
    END LOOP;
END $$;

-- Re-enable triggers
SET session_replication_role = DEFAULT; 