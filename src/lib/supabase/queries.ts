import { createClient } from '@/lib/supabase/client'
import { Database } from './database.types'

type Tables = Database['public']['Tables']
type Profile = Tables['profiles']['Row']
type CheckIn = Tables['check_ins']['Row']
type Feedback = Tables['feedback']['Row']
type Payment = Tables['payments']['Row']
type BlogPost = Tables['blog_posts']['Row']
type HelpfulMaterial = Tables['helpful_materials']['Row']
type Achievement = Tables['achievements']['Row']

// Profile queries
export async function getProfile(userId: string): Promise<Profile | null> {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single()

  if (error) {
    console.error('Error fetching profile:', error)
    return null
  }

  return data
}

export async function updateProfile(userId: string, updates: Partial<Profile>) {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('id', userId)
    .select()
    .single()

  if (error) {
    console.error('Error updating profile:', error)
    throw error
  }

  return data
}

export async function getAllProfiles(): Promise<Profile[]> {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching profiles:', error)
    return []
  }

  return data || []
}

// Check-in queries
export async function createCheckIn(userId: string) {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('check_ins')
    .insert({ user_id: userId })
    .select()
    .single()

  if (error) {
    console.error('Error creating check-in:', error)
    throw error
  }

  // Update user's last visit and total visits
  await supabase
    .from('profiles')
    .update({ 
      last_visit: new Date().toISOString(),
      total_visits: supabase.rpc('increment_visits', { user_id: userId })
    })
    .eq('id', userId)

  return data
}

export async function updateCheckOut(checkInId: string) {
  const supabase = createClient()
  const checkOutTime = new Date().toISOString()
  
  // Get the check-in record to calculate duration
  const { data: checkIn } = await supabase
    .from('check_ins')
    .select('check_in_time')
    .eq('id', checkInId)
    .single()

  if (!checkIn) throw new Error('Check-in not found')

  const duration = Math.round(
    (new Date(checkOutTime).getTime() - new Date(checkIn.check_in_time).getTime()) / (1000 * 60)
  )

  const { data, error } = await supabase
    .from('check_ins')
    .update({ 
      check_out_time: checkOutTime,
      duration_minutes: duration
    })
    .eq('id', checkInId)
    .select()
    .single()

  if (error) {
    console.error('Error updating check-out:', error)
    throw error
  }

  return data
}

export async function getUserCheckIns(userId: string, limit = 10): Promise<CheckIn[]> {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('check_ins')
    .select('*')
    .eq('user_id', userId)
    .order('check_in_time', { ascending: false })
    .limit(limit)

  if (error) {
    console.error('Error fetching check-ins:', error)
    return []
  }

  return data || []
}

// Feedback queries
export async function createFeedback(feedback: Omit<Feedback, 'id' | 'created_at' | 'updated_at'>) {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('feedback')
    .insert(feedback)
    .select()
    .single()

  if (error) {
    console.error('Error creating feedback:', error)
    throw error
  }

  return data
}

export async function getAllFeedback(): Promise<Feedback[]> {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('feedback')
    .select(`
      *,
      profiles!feedback_user_id_fkey(first_name, last_name, email)
    `)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching feedback:', error)
    return []
  }

  return data || []
}

export async function updateFeedbackResponse(feedbackId: string, response: string, respondedBy: string) {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('feedback')
    .update({
      response,
      responded_by: respondedBy,
      responded_at: new Date().toISOString(),
      status: 'responded'
    })
    .eq('id', feedbackId)
    .select()
    .single()

  if (error) {
    console.error('Error updating feedback response:', error)
    throw error
  }

  return data
}

// Payment queries
export async function getUserPayments(userId: string): Promise<Payment[]> {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('payments')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching payments:', error)
    return []
  }

  return data || []
}

export async function getAllPayments(): Promise<Payment[]> {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('payments')
    .select(`
      *,
      profiles!payments_user_id_fkey(first_name, last_name, email)
    `)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching all payments:', error)
    return []
  }

  return data || []
}

// Blog queries
export async function getPublishedBlogPosts(): Promise<BlogPost[]> {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('blog_posts')
    .select(`
      *,
      profiles!blog_posts_author_id_fkey(first_name, last_name)
    `)
    .eq('published', true)
    .order('published_at', { ascending: false })

  if (error) {
    console.error('Error fetching blog posts:', error)
    return []
  }

  return data || []
}

export async function getBlogPost(slug: string): Promise<BlogPost | null> {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('blog_posts')
    .select(`
      *,
      profiles!blog_posts_author_id_fkey(first_name, last_name)
    `)
    .eq('slug', slug)
    .eq('published', true)
    .single()

  if (error) {
    console.error('Error fetching blog post:', error)
    return null
  }

  // Increment view count
  await supabase
    .from('blog_posts')
    .update({ view_count: data.view_count + 1 })
    .eq('id', data.id)

  return data
}

// Helpful materials queries
export async function getHelpfulMaterials(): Promise<HelpfulMaterial[]> {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('helpful_materials')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching helpful materials:', error)
    return []
  }

  return data || []
}

export async function incrementDownloadCount(materialId: string) {
  const supabase = createClient()
  const { error } = await supabase
    .rpc('increment_download_count', { material_id: materialId })

  if (error) {
    console.error('Error incrementing download count:', error)
  }
}

// Achievement queries
export async function getUserAchievements(userId: string): Promise<Achievement[]> {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('achievements')
    .select('*')
    .eq('user_id', userId)
    .order('earned_at', { ascending: false })

  if (error) {
    console.error('Error fetching achievements:', error)
    return []
  }

  return data || []
}

export async function awardAchievement(userId: string, achievementData: Omit<Achievement, 'id' | 'user_id' | 'created_at'>) {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('achievements')
    .insert({
      ...achievementData,
      user_id: userId,
      earned: true,
      earned_at: new Date().toISOString()
    })
    .select()
    .single()

  if (error) {
    console.error('Error awarding achievement:', error)
    throw error
  }

  return data
}

// Analytics queries
export async function getAnalyticsData() {
  const supabase = createClient()
  
  // Get member statistics
  const { data: memberStats } = await supabase
    .from('profiles')
    .select('membership_status, membership_type, created_at')

  // Get check-in statistics for the last 30 days
  const thirtyDaysAgo = new Date()
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
  
  const { data: checkInStats } = await supabase
    .from('check_ins')
    .select('check_in_time, duration_minutes')
    .gte('check_in_time', thirtyDaysAgo.toISOString())

  // Get feedback statistics
  const { data: feedbackStats } = await supabase
    .from('feedback')
    .select('rating, category, status')

  // Get payment statistics
  const { data: paymentStats } = await supabase
    .from('payments')
    .select('amount, status, created_at')
    .eq('status', 'completed')

  return {
    memberStats: memberStats || [],
    checkInStats: checkInStats || [],
    feedbackStats: feedbackStats || [],
    paymentStats: paymentStats || []
  }
}
