import { createClient } from '@/lib/supabase/client'
import { Database } from './database.types'

type Tables = Database['public']['Tables']

// Migration utility to populate database with initial data
export async function runMigration() {
  const supabase = createClient()
  
  try {
    console.log('Starting database migration...')
    
    // Check if we already have data
    const { data: existingProfiles } = await supabase
      .from('profiles')
      .select('id')
      .limit(1)
    
    if (existingProfiles && existingProfiles.length > 0) {
      console.log('Database already has data, skipping migration')
      return { success: true, message: 'Database already populated' }
    }
    
    // Database schema is ready - no sample data needed
    // Content will be created through the admin interface
    console.log('✅ Database migration completed successfully - ready for real data')

    return {
      success: true,
      message: 'Database migration completed successfully - ready for real data'
    }
    
  } catch (error) {
    console.error('Migration failed:', error)
    return { 
      success: false, 
      message: 'Migration failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// Utility to check database connection
export async function checkDatabaseConnection() {
  const supabase = createClient()
  
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1)
    
    if (error) {
      return { connected: false, error: error.message }
    }
    
    return { connected: true, message: 'Database connection successful' }
  } catch (error) {
    return { 
      connected: false, 
      error: error instanceof Error ? error.message : 'Unknown connection error'
    }
  }
}

// Utility to get database statistics
export async function getDatabaseStats() {
  const supabase = createClient()
  
  try {
    const [
      { count: profilesCount },
      { count: checkInsCount },
      { count: feedbackCount },
      { count: paymentsCount },
      { count: blogPostsCount },
      { count: materialsCount },
      { count: achievementsCount }
    ] = await Promise.all([
      supabase.from('profiles').select('*', { count: 'exact', head: true }),
      supabase.from('check_ins').select('*', { count: 'exact', head: true }),
      supabase.from('feedback').select('*', { count: 'exact', head: true }),
      supabase.from('payments').select('*', { count: 'exact', head: true }),
      supabase.from('blog_posts').select('*', { count: 'exact', head: true }),
      supabase.from('helpful_materials').select('*', { count: 'exact', head: true }),
      supabase.from('achievements').select('*', { count: 'exact', head: true })
    ])
    
    return {
      profiles: profilesCount || 0,
      checkIns: checkInsCount || 0,
      feedback: feedbackCount || 0,
      payments: paymentsCount || 0,
      blogPosts: blogPostsCount || 0,
      helpfulMaterials: materialsCount || 0,
      achievements: achievementsCount || 0
    }
  } catch (error) {
    console.error('Error getting database stats:', error)
    return null
  }
}

// Utility to reset database (for development only)
export async function resetDatabase() {
  const supabase = createClient()
  
  if (process.env.NODE_ENV === 'production') {
    throw new Error('Database reset is not allowed in production')
  }
  
  try {
    // Delete all data in reverse dependency order
    await supabase.from('achievements').delete().neq('id', '00000000-0000-0000-0000-000000000000')
    await supabase.from('check_ins').delete().neq('id', '00000000-0000-0000-0000-000000000000')
    await supabase.from('feedback').delete().neq('id', '00000000-0000-0000-0000-000000000000')
    await supabase.from('payments').delete().neq('id', '00000000-0000-0000-0000-000000000000')
    await supabase.from('blog_posts').delete().neq('id', '00000000-0000-0000-0000-000000000000')
    await supabase.from('helpful_materials').delete().neq('id', '00000000-0000-0000-0000-000000000000')
    await supabase.from('profiles').delete().neq('id', '00000000-0000-0000-0000-000000000000')
    
    console.log('✅ Database reset completed')
    return { success: true, message: 'Database reset completed' }
  } catch (error) {
    console.error('Database reset failed:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}
