export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          first_name: string | null
          last_name: string | null
          avatar_url: string | null
          phone: string | null
          date_of_birth: string | null
          membership_type: 'basic' | 'premium' | 'vip'
          membership_status: 'active' | 'inactive' | 'suspended'
          join_date: string
          last_visit: string | null
          total_visits: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          first_name?: string | null
          last_name?: string | null
          avatar_url?: string | null
          phone?: string | null
          date_of_birth?: string | null
          membership_type?: 'basic' | 'premium' | 'vip'
          membership_status?: 'active' | 'inactive' | 'suspended'
          join_date?: string
          last_visit?: string | null
          total_visits?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          first_name?: string | null
          last_name?: string | null
          avatar_url?: string | null
          phone?: string | null
          date_of_birth?: string | null
          membership_type?: 'basic' | 'premium' | 'vip'
          membership_status?: 'active' | 'inactive' | 'suspended'
          join_date?: string
          last_visit?: string | null
          total_visits?: number
          created_at?: string
          updated_at?: string
        }
      }
      check_ins: {
        Row: {
          id: string
          user_id: string
          check_in_time: string
          check_out_time: string | null
          duration_minutes: number | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          check_in_time?: string
          check_out_time?: string | null
          duration_minutes?: number | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          check_in_time?: string
          check_out_time?: string | null
          duration_minutes?: number | null
          created_at?: string
        }
      }
      feedback: {
        Row: {
          id: string
          user_id: string
          category: 'workout' | 'facility' | 'trainer' | 'general'
          rating: number
          comment: string | null
          status: 'pending' | 'responded' | 'resolved'
          response: string | null
          responded_by: string | null
          responded_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          category: 'workout' | 'facility' | 'trainer' | 'general'
          rating: number
          comment?: string | null
          status?: 'pending' | 'responded' | 'resolved'
          response?: string | null
          responded_by?: string | null
          responded_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          category?: 'workout' | 'facility' | 'trainer' | 'general'
          rating?: number
          comment?: string | null
          status?: 'pending' | 'responded' | 'resolved'
          response?: string | null
          responded_by?: string | null
          responded_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      payments: {
        Row: {
          id: string
          user_id: string
          amount: number
          currency: string
          description: string
          status: 'pending' | 'completed' | 'failed' | 'refunded'
          stripe_payment_id: string | null
          due_date: string | null
          paid_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          amount: number
          currency?: string
          description: string
          status?: 'pending' | 'completed' | 'failed' | 'refunded'
          stripe_payment_id?: string | null
          due_date?: string | null
          paid_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          amount?: number
          currency?: string
          description?: string
          status?: 'pending' | 'completed' | 'failed' | 'refunded'
          stripe_payment_id?: string | null
          due_date?: string | null
          paid_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      blog_posts: {
        Row: {
          id: string
          title: string
          slug: string
          excerpt: string
          content: string
          category: string
          author_id: string
          featured: boolean
          published: boolean
          published_at: string | null
          read_time: number
          view_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          slug: string
          excerpt: string
          content: string
          category: string
          author_id: string
          featured?: boolean
          published?: boolean
          published_at?: string | null
          read_time?: number
          view_count?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          slug?: string
          excerpt?: string
          content?: string
          category?: string
          author_id?: string
          featured?: boolean
          published?: boolean
          published_at?: string | null
          read_time?: number
          view_count?: number
          created_at?: string
          updated_at?: string
        }
      }
      helpful_materials: {
        Row: {
          id: string
          title: string
          description: string
          type: 'pdf' | 'video' | 'audio' | 'guide'
          category: string
          file_url: string | null
          file_size: string | null
          duration: string | null
          download_count: number
          featured: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description: string
          type: 'pdf' | 'video' | 'audio' | 'guide'
          category: string
          file_url?: string | null
          file_size?: string | null
          duration?: string | null
          download_count?: number
          featured?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string
          type?: 'pdf' | 'video' | 'audio' | 'guide'
          category?: string
          file_url?: string | null
          file_size?: string | null
          duration?: string | null
          download_count?: number
          featured?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      achievements: {
        Row: {
          id: string
          user_id: string
          type: 'streak' | 'milestone' | 'goal' | 'special'
          level: 'bronze' | 'silver' | 'gold' | 'platinum'
          title: string
          description: string
          earned: boolean
          earned_at: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          type: 'streak' | 'milestone' | 'goal' | 'special'
          level: 'bronze' | 'silver' | 'gold' | 'platinum'
          title: string
          description: string
          earned?: boolean
          earned_at?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          type?: 'streak' | 'milestone' | 'goal' | 'special'
          level?: 'bronze' | 'silver' | 'gold' | 'platinum'
          title?: string
          description?: string
          earned?: boolean
          earned_at?: string | null
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      membership_type: 'basic' | 'premium' | 'vip'
      membership_status: 'active' | 'inactive' | 'suspended'
      feedback_category: 'workout' | 'facility' | 'trainer' | 'general'
      feedback_status: 'pending' | 'responded' | 'resolved'
      payment_status: 'pending' | 'completed' | 'failed' | 'refunded'
      material_type: 'pdf' | 'video' | 'audio' | 'guide'
      achievement_type: 'streak' | 'milestone' | 'goal' | 'special'
      achievement_level: 'bronze' | 'silver' | 'gold' | 'platinum'
    }
  }
}
