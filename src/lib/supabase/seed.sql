-- Seed data for Pulse.co.za gym management system

-- Admin profiles will be created through the authentication system
-- No sample data needed

-- Blog posts and helpful materials will be created through the admin interface
-- No sample data needed

-- Create SQL functions for common operations
CREATE OR REPLACE FUNCTION increment_visits(user_id UUID)
RETURNS INTEGER AS $$
DECLARE
    current_visits INTEGER;
BEGIN
    SELECT total_visits INTO current_visits FROM profiles WHERE id = user_id;
    UPDATE profiles SET total_visits = current_visits + 1 WHERE id = user_id;
    RETURN current_visits + 1;
END;
$$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION increment_download_count(material_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE helpful_materials
    SET download_count = download_count + 1
    WHERE id = material_id;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Create function to calculate member retention rate
CREATE OR REPLACE FUNCTION calculate_retention_rate()
R<PERSON>URNS DECIMAL AS $$
DECLARE
    total_members INTEGER;
    active_members INTEGER;
    retention_rate DECIMAL;
BEGIN
    SELECT COUNT(*) INTO total_members FROM profiles;
    SELECT COUNT(*) INTO active_members FROM profiles WHERE membership_status = 'active';

    IF total_members > 0 THEN
        retention_rate := (active_members::DECIMAL / total_members::DECIMAL) * 100;
    ELSE
        retention_rate := 0;
    END IF;

    RETURN retention_rate;
END;
$$ LANGUAGE plpgsql STABLE;

-- Create function to get peak hours data
CREATE OR REPLACE FUNCTION get_peak_hours()
RETURNS TABLE(hour_of_day INTEGER, check_in_count BIGINT) AS $$
BEGIN
    RETURN QUERY
    SELECT
        EXTRACT(HOUR FROM check_in_time)::INTEGER as hour_of_day,
        COUNT(*) as check_in_count
    FROM check_ins
    WHERE check_in_time >= CURRENT_TIMESTAMP - INTERVAL '30 days'
    GROUP BY EXTRACT(HOUR FROM check_in_time)
    ORDER BY hour_of_day;
END;
$$ LANGUAGE plpgsql STABLE;

-- Create function to get member growth data
CREATE OR REPLACE FUNCTION get_member_growth()
RETURNS TABLE(month_year TEXT, new_members BIGINT) AS $$
BEGIN
    RETURN QUERY
    SELECT
        TO_CHAR(created_at, 'YYYY-MM') as month_year,
        COUNT(*) as new_members
    FROM profiles
    WHERE created_at >= CURRENT_TIMESTAMP - INTERVAL '12 months'
    GROUP BY TO_CHAR(created_at, 'YYYY-MM')
    ORDER BY month_year;
END;
$$ LANGUAGE plpgsql STABLE;

-- Create function to get feedback summary
CREATE OR REPLACE FUNCTION get_feedback_summary()
RETURNS TABLE(
    total_feedback BIGINT,
    average_rating DECIMAL,
    pending_count BIGINT,
    responded_count BIGINT,
    resolved_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*) as total_feedback,
        ROUND(AVG(rating), 2) as average_rating,
        COUNT(*) FILTER (WHERE status = 'pending') as pending_count,
        COUNT(*) FILTER (WHERE status = 'responded') as responded_count,
        COUNT(*) FILTER (WHERE status = 'resolved') as resolved_count
    FROM feedback;
END;
$$ LANGUAGE plpgsql STABLE;

-- Create function to award automatic achievements
CREATE OR REPLACE FUNCTION check_and_award_achievements(user_id UUID)
RETURNS VOID AS $$
DECLARE
    visit_count INTEGER;
    consecutive_days INTEGER;
BEGIN
    -- Get user's total visits
    SELECT total_visits INTO visit_count FROM profiles WHERE id = user_id;
    
    -- Award visit milestone achievements
    IF visit_count = 10 AND NOT EXISTS (
        SELECT 1 FROM achievements 
        WHERE user_id = check_and_award_achievements.user_id 
        AND title = 'First 10 Visits'
    ) THEN
        INSERT INTO achievements (user_id, type, level, title, description, earned, earned_at)
        VALUES (user_id, 'milestone', 'bronze', 'First 10 Visits', 'Completed your first 10 gym visits!', true, NOW());
    END IF;
    
    IF visit_count = 50 AND NOT EXISTS (
        SELECT 1 FROM achievements 
        WHERE user_id = check_and_award_achievements.user_id 
        AND title = 'Halfway Hero'
    ) THEN
        INSERT INTO achievements (user_id, type, level, title, description, earned, earned_at)
        VALUES (user_id, 'milestone', 'silver', 'Halfway Hero', 'Reached 50 gym visits - you''re building a great habit!', true, NOW());
    END IF;
    
    IF visit_count = 100 AND NOT EXISTS (
        SELECT 1 FROM achievements 
        WHERE user_id = check_and_award_achievements.user_id 
        AND title = 'Century Club'
    ) THEN
        INSERT INTO achievements (user_id, type, level, title, description, earned, earned_at)
        VALUES (user_id, 'milestone', 'gold', 'Century Club', 'Amazing! You''ve completed 100 gym visits!', true, NOW());
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically award achievements on check-in
CREATE OR REPLACE FUNCTION trigger_achievement_check()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM check_and_award_achievements(NEW.user_id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER check_achievements_on_checkin
    AFTER INSERT ON check_ins
    FOR EACH ROW
    EXECUTE FUNCTION trigger_achievement_check();

-- Achievements will be awarded automatically through triggers
-- No sample data needed
