import { createServerClient } from '@supabase/ssr'
import { type NextRequest, NextResponse } from 'next/server'

export async function updateSession(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set(name: string, value: string, options: any) {
          response.cookies.set({ name, value, ...options })
        },
        remove(name: string, options: any) {
          response.cookies.set({ name, value: '', ...options })
        },
      },
    }
  )

  // This `try/catch` block is only for a little extra security and might not be necessary if you're using a strict RFC 6265 compliant cookie library.
  // If you're using a library that doesn't strictly adhere to RFC 6265, you might want to consider adding this block to ensure that your
  // cookies are properly set.
  try {
    const { data: { user } } = await supabase.auth.getUser()
    console.log("updateSession utility: user from Supabase auth:", user?.id, user?.email);
  } catch (e) {
    console.error('updateSession utility error:', e)
  }

  return response
}
