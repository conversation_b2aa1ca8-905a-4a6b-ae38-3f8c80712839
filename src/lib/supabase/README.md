# Pulse.co.za Database Schema

This directory contains the Supabase database schema for the Pulse.co.za gym management platform.

## Schema Files

- `final-schema.sql` - The finalized database schema with all tables, types, and RLS policies
- `schema.sql` - The original schema blueprint
- `current-schema.sql` - The current implementation schema
- `new-schema.sql` - An alternative schema implementation

## Database Structure

### Core Tables

1. **profiles** - User profiles extending Supabase auth
   - Membership types: basic, premium, vip (admin)
   - VIP membership type grants admin access

2. **check_ins** - Gym visit tracking
   - Records user check-ins and check-outs
   - Calculates visit duration

3. **feedback** - Member feedback and ratings
   - Categories: workout, facility, trainer, general
   - Admin response tracking

4. **payments** - Membership payments and billing
   - Payment status tracking
   - Currency support (default: ZAR)

5. **blog_posts** - Content management for blog
   - Publishing workflow
   - View count tracking

6. **helpful_materials** - Resource library
   - Multiple file types: PDF, video, audio, guide
   - Download tracking

7. **achievements** - Gamification system
   - Achievement types and levels
   - Progress tracking

8. **contact_submissions** - Contact form management
   - Status tracking: unread, read, responded
   - Admin notes and response tracking

## Row Level Security (RLS)

The schema implements comprehensive RLS policies:

- **Users** can only access their own data
- **Admins** (VIP members) can access management data
- **Public** content (blog, materials) is readable by all
- **Contact submissions** can be created by anyone but only viewed by admins

## Authentication System

- Admin-only user creation
- VIP membership type grants admin access
- User profile management with self-service updates

## Implementation Notes

1. The `handle_new_user()` function automatically creates a profile when a new user is registered
2. All tables have proper indexes for performance optimization
3. `updated_at` columns are automatically maintained via triggers
4. The schema supports the EMS (Electrical Muscle Stimulation) training focus of the gym

## Usage

To apply this schema to your Supabase project:

1. Go to the SQL Editor in your Supabase dashboard
2. Copy the contents of `final-schema.sql`
3. Paste and execute the SQL

## Security Considerations

- All sensitive operations require admin (VIP) access
- Public operations are limited to reading published content and submitting contact forms
- User data is isolated through RLS policies