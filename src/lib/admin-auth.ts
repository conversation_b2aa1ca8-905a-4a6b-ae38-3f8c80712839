import { supabase } from "@/lib/supabase/client";

/**
 * Check if the current user is an admin
 * @returns Promise<boolean> - true if user is admin, false otherwise
 */
export async function isCurrentUserAdmin(): Promise<boolean> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return false;
    }

    // Check if user has admin role in their profile
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (error) {
      console.error('Error checking admin status:', error);
      return false;
    }

    return profile?.role === 'admin';
  } catch (error) {
    console.error('Error in isCurrentUserAdmin:', error);
    return false;
  }
}

/**
 * Get the current user's profile
 * @returns Promise<Profile | null>
 */
export async function getCurrentUserProfile() {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return null;
    }

    const { data: profile, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (error) {
      console.error('Error fetching user profile:', error);
      return null;
    }

    return profile;
  } catch (error) {
    console.error('Error in getCurrentUserProfile:', error);
    return null;
  }
}

/**
 * Redirect to login if user is not authenticated or not admin
 */
export async function requireAdmin() {
  const isAdmin = await isCurrentUserAdmin();
  
  if (!isAdmin) {
    // Redirect to login or show unauthorized message
    window.location.href = '/auth/signin';
    return false;
  }
  
  return true;
}

/**
 * Check environment configuration
 */
export function checkEnvironmentConfig() {
  const requiredEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY'
  ];

  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missing.length > 0) {
    console.error('Missing required environment variables:', missing);
    return false;
  }
  
  return true;
}

/**
 * Check if server-side environment is properly configured
 */
export function checkServerEnvironmentConfig() {
  const requiredServerEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];

  const missing = requiredServerEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missing.length > 0) {
    console.error('Missing required server environment variables:', missing);
    return { isValid: false, missing };
  }
  
  return { isValid: true, missing: [] };
}
