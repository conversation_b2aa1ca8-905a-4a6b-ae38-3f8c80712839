import { createClient } from "@supabase/supabase-js";
import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    const { email, password, firstName, lastName, phone, dateOfBirth, avatarUrl, membershipType, membershipStatus } = await request.json();

    // Validate required fields
    if (!email || !password) {
      return NextResponse.json({ error: "Email and password are required" }, { status: 400 });
    }

    if (!firstName || !lastName) {
      return NextResponse.json({ error: "First name and last name are required" }, { status: 400 });
    }

    // Check environment variables
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl) {
      console.error("NEXT_PUBLIC_SUPABASE_URL is not set");
      return NextResponse.json({ error: "Server configuration error: Missing Supabase URL" }, { status: 500 });
    }

    if (!supabaseServiceKey) {
      console.error("SUPABASE_SERVICE_ROLE_KEY is not set");
      return NextResponse.json({ error: "Server configuration error: Missing service role key" }, { status: 500 });
    }

    // Create admin client with service role key
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

    // Create user with admin privileges
    const { data, error } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Auto-confirm email for admin-created users
      user_metadata: {
        first_name: firstName,
        last_name: lastName,
        phone: phone || null,
        date_of_birth: dateOfBirth || null,
        avatar_url: avatarUrl || null,
        membership_type: membershipType || 'basic',
        membership_status: membershipStatus || 'active',
      },
    });

    if (error) {
      console.error("Error creating user:", error);
      return NextResponse.json({
        error: `Failed to create user: ${error.message}`,
        details: error
      }, { status: 500 });
    }

    if (!data.user) {
      return NextResponse.json({ error: "User creation failed: No user data returned" }, { status: 500 });
    }

    // The profile should be automatically created by the trigger function
    // Let's verify it was created and update it if needed
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', data.user.id)
      .single();

    if (profileError) {
      console.error("Error fetching created profile:", profileError);
      // Profile might not exist yet due to timing, let's try to create it manually
      const { error: insertError } = await supabaseAdmin
        .from('profiles')
        .insert({
          id: data.user.id,
          email: email,
          first_name: firstName,
          last_name: lastName,
          phone: phone || null,
          date_of_birth: dateOfBirth ? new Date(dateOfBirth) : null,
          avatar_url: avatarUrl || null,
          membership_type: membershipType || 'basic',
          membership_status: membershipStatus || 'active',
        });

      if (insertError) {
        console.error("Error creating profile manually:", insertError);
        return NextResponse.json({
          error: `User created but profile creation failed: ${insertError.message}`,
          user: data.user
        }, { status: 500 });
      }
    }

    return NextResponse.json({
      message: "Member created successfully",
      user: data.user,
      profile: profile
    });

  } catch (error: any) {
    console.error("Unexpected error creating member:", error);
    return NextResponse.json({
      error: error.message || "An unexpected error occurred",
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    }, { status: 500 });
  }
} 