'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { supabase } from '@/lib/supabase/client'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Calendar, User, Tag, Loader2 } from 'lucide-react'
import { MaxWidthWrapper } from "@/components"
import Image from "next/image";

interface BlogPost {
  id: string
  title: string
  slug: string
  content: string
  author: string
  published_at: string
  image_url?: string
  tags: string[]
  is_published: boolean
}

const BlogPostPage = () => {
  const { slug } = useParams()
  const [blogPost, setBlogPost] = useState<BlogPost | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (slug) {
      console.log("Attempting to fetch blog post for slug:", slug)
      fetchBlogPostBySlug(slug as string)
    }
  }, [slug])

  const fetchBlogPostBySlug = async (postSlug: string) => {
    setLoading(true)
    setError(null)
    try {
      const { data, error } = await supabase
        .from('blogs')
        .select('*')
        .eq('slug', postSlug)
        .single()

      if (error) {
        console.error("Supabase fetch error:", error)
        if (error.code === 'PGRST116') { // No rows found
          setError("Blog post not found.")
        } else {
          throw error
        }
      }

      console.log("Supabase data received:", data)
      setBlogPost(data || null)
    } catch (err: any) {
      console.error("Error fetching blog post:", err)
      setError(err.message || "Failed to fetch blog post")
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader2 className="w-10 h-10 animate-spin text-pink-500" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto py-12 text-center text-red-500">
        <h1 className="text-4xl font-bold mb-4">Error</h1>
        <p className="text-xl">{error}</p>
        <p className="text-md text-muted-foreground mt-2">Please try again later or check the URL.</p>
      </div>
    )
  }

  if (!blogPost) {
    return (
      <div className="container mx-auto py-12 text-center">
        <h1 className="text-4xl font-bold mb-4">Blog Post Not Found</h1>
        <p className="text-xl text-muted-foreground">The blog post you are looking for does not exist or has been removed.</p>
      </div>
    )
  }

  return (
    <MaxWidthWrapper className="py-12">
      <Card>
        <CardHeader>
          {blogPost.image_url && (
            <div className="relative w-full h-64 rounded-md mb-6">
              <Image src={blogPost.image_url} alt={blogPost.title} fill className="object-cover rounded-md" />
            </div>
          )}
          <CardTitle className="text-4xl font-extrabold mb-4 leading-tight">{blogPost.title}</CardTitle>
          <div className="flex items-center space-x-4 text-muted-foreground text-sm">
            <div className="flex items-center gap-1">
              <User className="w-4 h-4" />
              <span>{blogPost.author}</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="w-4 h-4" />
              <span>{new Date(blogPost.published_at).toLocaleDateString()}</span>
            </div>
          </div>
          <div className="flex flex-wrap gap-2 mt-4">
            {blogPost.tags?.map(tag => (
              <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                <Tag className="w-3 h-3" />
                {tag}
              </Badge>
            ))}
          </div>
        </CardHeader>
        <CardContent className="prose dark:prose-invert max-w-none">
          <div dangerouslySetInnerHTML={{ __html: blogPost.content }} />
        </CardContent>
      </Card>
    </MaxWidthWrapper>
  )
}

export default BlogPostPage 