"use client"

import { useState } from "react"

// Prevent static generation for pages that use Supabase
export const dynamic = 'force-dynamic'
import { useRouter } from "next/navigation"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { supabase } from "@/lib/supabase/client"
import { <PERSON>mbbell, Eye, EyeOff, Mail, Lock, ArrowRight, AlertCircle } from "lucide-react"

export default function SignInPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [formData, setFormData] = useState({
    email: "",
    password: ""
  })
  const router = useRouter()

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      console.log("Attempting sign-in with:", formData.email);
      const { data, error } = await supabase.auth.signInWithPassword({
        email: formData.email,
        password: formData.password,
      })

      console.log("Supabase sign-in response - data:", data);
      console.log("Supabase sign-in response - error:", error);

      if (error) {
        console.error("Supabase sign-in error:", error.message);
        throw error;
      }

      // Check if user is admin (VIP member)
      console.log("Fetching profile for user ID:", data.user.id);
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('membership_type')
        .eq('id', data.user.id)
        .single()

      console.log("Profile data:", profile);
      console.log("Profile fetch error:", profileError);

      if (profileError) {
        console.error("Profile fetch error:", profileError.message);
        throw profileError;
      }

      if (profile?.membership_type === 'vip') {
        console.log("Redirecting to /admin");
        router.refresh();
        router.push('/admin')
      } else {
        console.log("Redirecting to /dashboard");
        router.refresh();
        router.push('/dashboard')
      }
    } catch (error: any) {
      console.error("An unexpected error occurred during sign-in:", error);
      setError(error.message || 'An error occurred during sign in')
    } finally {
      console.log("Sign-in process finished. Setting isLoading to false.");
      setIsLoading(false)
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <Card className="bg-gray-900/50 backdrop-blur-sm border-pink-500/20 shadow-2xl shadow-pink-500/10">
        <CardHeader className="text-center space-y-4">
          {/* Logo */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring" }}
            className="flex justify-center"
          >
            <div className="w-16 h-16 rounded-full bg-pink-500 shadow-lg shadow-pink-500/50 flex items-center justify-center">
              <Dumbbell className="w-8 h-8 text-white" />
            </div>
          </motion.div>
          
          <div>
            <CardTitle className="text-2xl font-bold text-white">Welcome Back</CardTitle>
            <CardDescription className="text-gray-400">
              Sign in to your Pulse20.co.za account
            </CardDescription>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {error && (
            <div className="p-3 bg-red-500/20 border border-red-500/30 rounded-lg flex items-center gap-2 text-red-400">
              <AlertCircle className="w-4 h-4" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Email Field */}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-white">Email</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="Enter your email"
                  className="pl-10 bg-gray-800 border-gray-700 text-white placeholder:text-gray-500 focus:border-pink-400 focus:ring-pink-400"
                  required
                />
              </div>
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <Label htmlFor="password" className="text-white">Password</Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="Enter your password"
                  className="pl-10 pr-10 bg-gray-800 border-gray-700 text-white placeholder:text-gray-500 focus:border-pink-400 focus:ring-pink-400"
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1 h-8 w-8 p-0 text-gray-400 hover:text-pink-400"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </Button>
              </div>
            </div>

            {/* Forgot Password */}
            <div className="text-right">
              <Link href="/auth/forgot-password" className="text-sm text-pink-400 hover:text-pink-300 transition-colors">
                Forgot password?
              </Link>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              disabled={isLoading}
              className="w-full bg-pink-500 hover:bg-pink-400 text-white shadow-lg shadow-pink-500/30 group"
            >
              {isLoading ? (
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              ) : (
                <>
                  Sign In
                  <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </>
              )}
            </Button>
          </form>

          {/* Info Message */}
          <div className="text-center">
            <p className="text-sm text-gray-400">
              Need an account? Contact your administrator.
            </p>
            <Link href="/contact" className="text-sm text-pink-400 hover:text-pink-300 transition-colors">
              Contact Us
            </Link>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
