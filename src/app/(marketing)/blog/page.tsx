'use client'

import { useState, useEffect } from "react"

// Prevent static generation for pages that use Supabase
export const dynamic = 'force-dynamic'
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Calendar, Clock, User, Search, Loader2 } from "lucide-react"
import Link from "next/link"
import { supabase } from "@/lib/supabase/client"
import Image from "next/image"

interface BlogPost {
  id: string
  title: string
  slug: string
  content: string
  category: string
  author: string
  published_at: string
  image_url?: string
  tags: string[]
  is_published: boolean
}

const BlogPage = () => {
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [filterCategory, setFilterCategory] = useState<string>("All")

  useEffect(() => {
    const fetchBlogPosts = async () => {
      setLoading(true)
      setError(null)
      try {
        let query = supabase
          .from('blogs')
          .select('id, title, slug, content, author, published_at, image_url, tags, is_published')
          .eq('is_published', true)
          .order('published_at', { ascending: false })

        if (filterCategory !== "All") {
          query = query.contains('tags', [filterCategory.toLowerCase()])
        }

        if (searchQuery) {
          query = query.or(`title.ilike.%${searchQuery}%,content.ilike.%${searchQuery}%,author.ilike.%${searchQuery}%`)
        }

        const { data, error } = await query

        if (error) throw error

        const fetchedPosts: BlogPost[] = (data || []).map(post => ({
          id: post.id,
          title: post.title,
          slug: post.slug,
          content: post.content,
          category: post.tags && post.tags.length > 0 ? post.tags[0] : "General", // Use first tag as category for display
          author: post.author,
          published_at: post.published_at,
          image_url: post.image_url,
          tags: post.tags,
          is_published: post.is_published
        }))

        setBlogPosts(fetchedPosts)
      } catch (err: any) {
        console.error("Error fetching blog posts:", err)
        setError(err.message || "Failed to fetch blog posts")
      } finally {
        setLoading(false)
      }
    }

    fetchBlogPosts()
  }, [filterCategory, searchQuery])

  const categories = ["All", "Fitness", "Nutrition", "Recovery", "Motivation", "Wellness", "Health", "Training"]

  const calculateReadTime = (content: string) => {
    const wordsPerMinute = 200
    const wordCount = content.split(/\s+/).length
    return Math.ceil(wordCount / wordsPerMinute)
  }

  return (
    <>
      {/* Enhanced Hero Section */}
      <section className="relative overflow-hidden bg-black min-h-[50vh]">
        {/* Enhanced Background with Gradient */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-black via-black/90 to-black z-10" />
          <div className="absolute inset-0 bg-gradient-to-r from-pink-500/10 via-transparent to-pink-400/10 z-20" />
          {/* Fallback gradient background */}
          <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black" />
          {/* Optional background image overlay */}
          <div className="absolute inset-0 opacity-20">
            <div className="w-full h-full bg-gradient-to-br from-pink-500/20 via-transparent to-pink-400/20"></div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-pink-500/5 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-pink-400/5 rounded-full blur-3xl" />

        {/* Neon glow effects */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl z-20" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-pink-400/8 rounded-full blur-3xl z-20" />

        <div className="relative z-30 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="min-h-[50vh] flex items-center justify-center">
            <div className="text-center">
              <Badge className="mb-6 bg-pink-500/20 text-pink-400 border-pink-500/30">
                Latest Insights
              </Badge>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white">
                Wellness{" "}
                <span className="text-pink-400 text-glow-pink">
                  Blog
                </span>
              </h1>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                Expert insights, workout tips, and wellness advice to support your fitness journey and personal growth.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Search and Filter */}
      <section className="py-12 lg:py-16 bg-background border-b border-border">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row gap-6 items-center justify-between">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search articles..."
                className="pl-10 bg-input border-border text-foreground placeholder:text-muted-foreground focus:border-pink-400 focus:ring-pink-400"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex items-center gap-3 flex-wrap">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={filterCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFilterCategory(category)}
                  className={filterCategory === category ? "bg-pink-500 hover:bg-pink-400 text-white" : "border-pink-500/30 text-pink-400 hover:bg-pink-500/10"}
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Blog Posts */}
      <section className="py-20 lg:py-32 bg-background">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="w-10 h-10 animate-spin text-pink-500" />
            </div>
          ) : error ? (
            <div className="text-center text-red-500">{error}</div>
          ) : blogPosts.length === 0 ? (
            <div className="text-center text-muted-foreground">
              No blog posts found.
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {blogPosts.map((post) => (
                <Card key={post.id} className="hover:shadow-lg hover:shadow-pink-500/20 transition-all duration-300 group bg-card backdrop-blur-sm border-border hover:border-pink-500/50">
                  {post.image_url ? (
                    <div className="aspect-video relative overflow-hidden rounded-t-lg">
                      <Image src={post.image_url} alt={post.title} fill className="object-cover group-hover:scale-105 transition-transform duration-300" />
                      {post.is_published && (
                        <Badge className="absolute top-4 left-4 bg-pink-500 text-white">
                          Featured
                        </Badge>
                      )}
                    </div>
                  ) : (
                    <div className="aspect-video bg-gradient-to-br from-pink-500/20 to-pink-400/10 relative">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-center">
                          <div className="w-12 h-12 mx-auto rounded-full bg-pink-500/30 flex items-center justify-center mb-2">
                            <User className="w-6 h-6 text-pink-400" />
                          </div>
                          <p className="text-pink-400 font-medium text-sm">{post.category}</p>
                        </div>
                      </div>
                      {post.is_published && (
                        <Badge className="absolute top-4 left-4 bg-pink-500 text-white">
                          Featured
                        </Badge>
                      )}
                    </div>
                  )}
                  
                  <CardContent className="p-6">
                    <div className="flex items-center gap-4 text-sm text-gray-400 mb-3">
                      <Badge variant="outline" className="text-pink-400 border-pink-500/30">
                        {post.category}
                      </Badge>
                      <span className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        {new Date(post.published_at).toLocaleDateString()}
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {calculateReadTime(post.content)} min
                      </span>
                    </div>
                    
                    <h3 className="text-xl font-bold mb-3 group-hover:text-pink-400 transition-colors line-clamp-2 text-white">
                      {post.title}
                    </h3>
                    
                    <p className="text-gray-400 mb-4 leading-relaxed line-clamp-3">
                      {post.content.substring(0, 150)}...
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-400">
                        By {post.author}
                      </span>
                      <Button variant="ghost" size="sm" className="text-pink-400 hover:text-pink-300 hover:bg-pink-500/10" asChild>
                        <Link href={`/blog/${post.slug}`}>
                          Read More →
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* "Load More Articles" button (can be implemented with pagination if needed) */}
          {blogPosts.length > 0 && (
            <div className="text-center mt-16">
              <Button variant="outline" size="lg" className="border-pink-500/30 text-pink-400 hover:bg-pink-500/10">
                Load More Articles
              </Button>
            </div>
          )}
        </div>
      </section>

      {/* Newsletter */}
      <section className="py-20 lg:py-32 bg-gray-900/30">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <Card className="max-w-2xl mx-auto bg-gradient-to-r from-pink-500/10 to-pink-400/10 border-pink-500/20 bg-gray-900/50 backdrop-blur-sm">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold mb-4 text-white">Stay Updated</h3>
              <p className="text-gray-400 mb-6">
                Get the latest wellness tips delivered to your inbox weekly.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <Input placeholder="Enter your email" className="flex-1 bg-gray-800 border-gray-700 text-white placeholder:text-gray-500 focus:border-pink-400 focus:ring-pink-400" />
                <Button className="bg-pink-500 hover:bg-pink-400 text-white shadow-lg shadow-pink-500/30">
                  Subscribe
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </>
  )
}

export default BlogPage
