"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { motion } from "framer-motion"
import { Shield, Lock, Eye, Database, User<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>riangle } from "lucide-react"

export default function PrivacyPolicyPage() {
  return (
    <div className="min-h-screen bg-black text-white">
      <div className="w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <Badge className="mb-6 bg-pink-500/20 text-pink-400 border-pink-500/30">
            Privacy & Data Protection
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Privacy <span className="text-pink-400">Policy</span>
          </h1>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Your privacy is important to us. This policy explains how Pulse20 (Pty) Ltd collects, 
            uses, and protects your personal information.
          </p>
        </motion.div>

        {/* POPIA Compliance */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-8"
        >
          <Card className="border-2 border-green-500/30 bg-green-500/5 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-400">
                <Shield className="w-5 h-5" />
                POPIA Compliance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300">
                Pulse20 (Pty) Ltd is fully compliant with the Protection of Personal Information Act (POPIA) 
                of South Africa. We are committed to safeguarding your personal information and ensuring 
                it is stored securely within our data-secure database infrastructure.
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Privacy Sections */}
        <div className="space-y-8">
          {/* Information We Collect */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="border-2 border-gray-800 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-pink-400">
                  <Database className="w-5 h-5" />
                  Information We Collect
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Personal Information</h3>
                  <ul className="text-gray-300 space-y-2 list-disc list-inside">
                    <li>Full name and surname</li>
                    <li>ID number</li>
                    <li>Contact details (email, telephone, address)</li>
                    <li>Next of kin information</li>
                    <li>Medical history and PAR-Q form data</li>
                  </ul>
                </div>
                
                <Separator className="bg-gray-700" />
                
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Membership Information</h3>
                  <ul className="text-gray-300 space-y-2 list-disc list-inside">
                    <li>Membership type and duration</li>
                    <li>Payment information and transaction history</li>
                    <li>Session attendance records</li>
                    <li>Progress tracking data</li>
                    <li>Feedback and communication records</li>
                  </ul>
                </div>

                <Separator className="bg-gray-700" />

                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Health & Fitness Data</h3>
                  <ul className="text-gray-300 space-y-2 list-disc list-inside">
                    <li>Medical history and health conditions</li>
                    <li>Fitness assessments and progress measurements</li>
                    <li>EMS training session data</li>
                    <li>Therapeutic treatment records</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* How We Use Your Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card className="border-2 border-gray-800 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-pink-400">
                  <UserCheck className="w-5 h-5" />
                  How We Use Your Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Service Delivery</h3>
                  <ul className="text-gray-300 space-y-2 list-disc list-inside">
                    <li>Provide personalized EMS training and therapeutic services</li>
                    <li>Ensure safe and effective training programs</li>
                    <li>Track your progress and fitness goals</li>
                    <li>Schedule and manage appointments</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Communication</h3>
                  <ul className="text-gray-300 space-y-2 list-disc list-inside">
                    <li>Send appointment reminders and confirmations</li>
                    <li>Provide important updates about our services</li>
                    <li>Respond to your inquiries and feedback</li>
                    <li>Emergency contact purposes</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Business Operations</h3>
                  <ul className="text-gray-300 space-y-2 list-disc list-inside">
                    <li>Process payments and manage billing</li>
                    <li>Maintain membership records</li>
                    <li>Improve our services and facilities</li>
                    <li>Comply with legal and regulatory requirements</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Data Protection & Security */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Card className="border-2 border-gray-800 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-pink-400">
                  <Lock className="w-5 h-5" />
                  Data Protection & Security
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <Shield className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-blue-400 mb-2">Secure Storage</h4>
                      <p className="text-gray-300 text-sm">
                        All personal information is stored on our data-secure database within 
                        Pulse20's ICT infrastructure, in line with POPIA requirements.
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Security Measures</h3>
                  <ul className="text-gray-300 space-y-2 list-disc list-inside">
                    <li>Encrypted data storage and transmission</li>
                    <li>Access controls and user authentication</li>
                    <li>Regular security audits and updates</li>
                    <li>Staff training on data protection protocols</li>
                    <li>Secure backup and recovery procedures</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Access Limitations</h3>
                  <ul className="text-gray-300 space-y-2 list-disc list-inside">
                    <li>Only authorized personnel have access to personal data</li>
                    <li>Access is limited to what is necessary for service delivery</li>
                    <li>All access is logged and monitored</li>
                    <li>Third-party access requires explicit consent or legal authorization</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Your Rights */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <Card className="border-2 border-gray-800 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-pink-400">
                  <Eye className="w-5 h-5" />
                  Your Privacy Rights
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Under POPIA, You Have the Right To:</h3>
                  <ul className="text-gray-300 space-y-2 list-disc list-inside">
                    <li>Access your personal information we hold</li>
                    <li>Request correction of inaccurate information</li>
                    <li>Request deletion of your personal information (subject to legal requirements)</li>
                    <li>Object to processing of your personal information</li>
                    <li>Request restriction of processing</li>
                    <li>Withdraw consent where processing is based on consent</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Media Consent</h3>
                  <p className="text-gray-300">
                    You have the right to opt out of media release consent by informing us in writing. 
                    This includes photographs and videos taken during training sessions for promotional purposes.
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Data Sharing */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Card className="border-2 border-gray-800 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-pink-400">Data Sharing & Disclosure</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-red-400 mb-2">No Unauthorized Disclosure</h4>
                      <p className="text-gray-300 text-sm">
                        Your personal information will NOT be disclosed to any external organization 
                        or unauthorized persons without your express authorization.
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Limited Exceptions</h3>
                  <p className="text-gray-300 mb-2">
                    We may only share your information when:
                  </p>
                  <ul className="text-gray-300 space-y-2 list-disc list-inside">
                    <li>Required by law or legal process</li>
                    <li>Necessary for emergency medical situations</li>
                    <li>You have provided explicit written consent</li>
                    <li>Required for payment processing (with secure payment providers)</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Contact & Complaints */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.7 }}
          >
            <Card className="border-2 border-pink-500/30 bg-pink-500/5 backdrop-blur-sm">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-white mb-4 text-center">
                  Privacy Questions or Concerns?
                </h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-pink-400 mb-2">Contact Our Privacy Officer:</h4>
                    <p className="text-gray-300">
                      Email: <EMAIL><br />
                      Phone: +27 83 408 3665<br />
                      Address: Lenasia: Signet Terrace Office Park | Mulbarton: 34 The Broads St
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-pink-400 mb-2">File a Complaint:</h4>
                    <p className="text-gray-300">
                      If you believe your privacy rights have been violated, you may file a complaint 
                      with the Information Regulator of South Africa.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Last Updated */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-12 text-gray-500"
        >
          <p>Last updated: December 2024</p>
          <p className="mt-2">This policy may be updated periodically. We will notify you of any significant changes.</p>
        </motion.div>
      </div>
    </div>
  )
}
