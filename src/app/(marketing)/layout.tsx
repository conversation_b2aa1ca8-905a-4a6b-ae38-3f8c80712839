import React from "react";
import { Navigation } from "@/components/public/navigation";
import { Footer } from "@/components/public/footer";
// import { currentUser } from "@clerk/nextjs/server";

interface Props {
    children: React.ReactNode
}

const MarketingLayout = async ({ children }: Props) => {
    // TODO: Re-enable user authentication in Phase 5
    // const user = await currentUser();

    return (
        <div className="min-h-screen flex flex-col">
            <div className="absolute inset-0 bg-[linear-gradient(to_right,#f1f5f9_1px,transparent_1px),linear-gradient(to_bottom,#f1f5f9_1px,transparent_1px)] bg-[size:3rem_3rem] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_110%)] h-[150%] dark:bg-[linear-gradient(to_right,#1e293b_1px,transparent_1px),linear-gradient(to_bottom,#1e293b_1px,transparent_1px)]" />
            <Navigation />
            <main className="flex-1 w-full z-40 relative">
                {children}
            </main>
            <Footer />
        </div>
    );
};

export default MarketingLayout