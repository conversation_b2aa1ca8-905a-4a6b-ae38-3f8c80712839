'use client'

// Prevent static generation for pages that use Supabase
export const dynamic = 'force-dynamic'

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { BookOpen, Video, LinkIcon, Download, Search, Loader2 } from "lucide-react"
import Link from "next/link"
import { supabase } from "@/lib/supabase/client"

interface Resource {
  id: string
  title: string
  slug: string
  description: string
  file_url: string
  resource_type: string
  uploaded_at: string
  tags: string[]
  is_active: boolean
}

const ResourcesPage = () => {
  const [resources, setResources] = useState<Resource[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [filterType, setFilterType] = useState<string>("All")

  useEffect(() => {
    const fetchResources = async () => {
      setLoading(true)
      setError(null)
      try {
        let query = supabase
          .from('resources')
          .select('id, title, slug, description, file_url, resource_type, uploaded_at, tags, is_active')
          .eq('is_active', true)
          .order('uploaded_at', { ascending: false })

        if (filterType !== "All") {
          query = query.eq('resource_type', filterType.toLowerCase())
        }

        if (searchQuery) {
          query = query.or(`title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%,tags.cs.{${searchQuery}}`)
        }

        const { data, error } = await query

        if (error) throw error

        setResources(data || [])
      } catch (err: any) {
        console.error("Error fetching resources:", err)
        setError(err.message || "Failed to fetch resources")
      } finally {
        setLoading(false)
      }
    }

    fetchResources()
  }, [filterType, searchQuery])

  const resourceTypes = ["All", "Pdf", "Video", "Link"]

  const getIconForResourceType = (type: string) => {
    switch (type) {
      case "pdf": return <BookOpen className="w-4 h-4 mr-2" />
      case "video": return <Video className="w-4 h-4 mr-2" />
      case "link": return <LinkIcon className="w-4 h-4 mr-2" />
      default: return <BookOpen className="w-4 h-4 mr-2" />
    }
  }

  return (
    <>
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-black min-h-[50vh]">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-black via-black/90 to-black z-10" />
          <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 via-transparent to-green-400/10 z-20" />
          <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black" />
          <div className="absolute inset-0 opacity-20">
            <div className="w-full h-full bg-gradient-to-br from-green-500/20 via-transparent to-green-400/20"></div>
          </div>
        </div>

        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-green-500/5 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-green-400/5 rounded-full blur-3xl" />

        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-green-500/10 rounded-full blur-3xl z-20" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-green-400/8 rounded-full blur-3xl z-20" />

        <div className="relative z-30 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="min-h-[50vh] flex items-center justify-center">
            <div className="text-center">
              <Badge className="mb-6 bg-green-500/20 text-green-400 border-green-500/30">
                Learning Hub
              </Badge>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white">
                Fitness{" "}
                <span className="text-green-400 text-glow-green">
                  Resources
                </span>
              </h1>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                Unlock your potential with our curated collection of guides, videos, and tools.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Search and Filter */}
      <section className="py-12 lg:py-16 bg-background border-b border-border">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row gap-6 items-center justify-between">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search resources..."
                className="pl-10 bg-input border-border text-foreground placeholder:text-muted-foreground focus:border-green-400 focus:ring-green-400"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex items-center gap-3 flex-wrap">
              {resourceTypes.map((type) => (
                <Button
                  key={type}
                  variant={filterType === type ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFilterType(type)}
                  className={filterType === type ? "bg-green-500 hover:bg-green-400 text-white" : "border-green-500/30 text-green-400 hover:bg-green-500/10"}
                >
                  {type}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Resources List */}
      <section className="py-20 lg:py-32 bg-background">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="w-10 h-10 animate-spin text-green-500" />
            </div>
          ) : error ? (
            <div className="text-center text-red-500">{error}</div>
          ) : resources.length === 0 ? (
            <div className="text-center text-muted-foreground">
              No resources found for the selected criteria.
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {resources.map((resource) => (
                <Card key={resource.id} className="hover:shadow-lg hover:shadow-green-500/20 transition-all duration-300 group bg-card backdrop-blur-sm border-border hover:border-green-500/50">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4 text-sm text-gray-400 mb-3">
                      <Badge variant="outline" className="text-green-400 border-green-500/30 flex items-center">
                        {getIconForResourceType(resource.resource_type)}
                        {resource.resource_type.toUpperCase()}
                      </Badge>
                      <span className="flex items-center gap-1">
                        <LinkIcon className="w-3 h-3" />
                        {new Date(resource.uploaded_at).toLocaleDateString()}
                      </span>
                    </div>
                    
                    <h3 className="text-xl font-bold mb-3 group-hover:text-green-400 transition-colors line-clamp-2 text-white">
                      {resource.title}
                    </h3>
                    
                    <p className="text-gray-400 mb-4 leading-relaxed line-clamp-3">
                      {resource.description}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex flex-wrap gap-2">
                        {resource.tags.map(tag => (
                          <Badge key={tag} variant="secondary" className="bg-gray-700 text-gray-300">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      <Button variant="ghost" size="sm" className="text-green-400 hover:text-green-300 hover:bg-green-500/10" asChild>
                        <a href={resource.file_url} target="_blank" rel="noopener noreferrer" download>
                          <Download className="w-4 h-4 mr-2" />
                          View/Download
                        </a>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* "Load More Resources" button (can be implemented with pagination if needed) */}
          {resources.length > 0 && (
            <div className="text-center mt-16">
              <Button variant="outline" size="lg" className="border-green-500/30 text-green-400 hover:bg-green-500/10">
                Load More Resources
              </Button>
            </div>
          )}
        </div>
      </section>

      {/* Newsletter (Optional - can be shared with blog page) */}
      <section className="py-20 lg:py-32 bg-gray-900/30">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <Card className="max-w-2xl mx-auto bg-gradient-to-r from-green-500/10 to-green-400/10 border-green-500/20 bg-gray-900/50 backdrop-blur-sm">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold mb-4 text-white">Stay Updated</h3>
              <p className="text-gray-400 mb-6">
                Get the latest fitness resources and updates delivered to your inbox.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <Input placeholder="Enter your email" className="flex-1 bg-gray-800 border-gray-700 text-white placeholder:text-gray-500 focus:border-green-400 focus:ring-green-400" />
                <Button className="bg-green-500 hover:bg-green-400 text-white shadow-lg shadow-green-500/30">
                  Subscribe
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </>
  )
}

export default ResourcesPage
