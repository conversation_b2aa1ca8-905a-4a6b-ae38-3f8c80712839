"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { motion } from "framer-motion"
import { <PERSON>, Alert<PERSON>riangle, Heart, FileText, UserChe<PERSON> } from "lucide-react"

export default function LiabilityWaiverPage() {
  return (
    <div className="min-h-screen bg-black text-white">
      <div className="w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <Badge className="mb-6 bg-pink-500/20 text-pink-400 border-pink-500/30">
            Safety & Liability
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Liability <span className="text-pink-400">Waiver</span>
          </h1>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Important information about risks, responsibilities, and liability limitations 
            for Pulse20 EMS training and therapeutic services.
          </p>
        </motion.div>

        {/* Important Notice */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-8"
        >
          <Card className="border-2 border-red-500/30 bg-red-500/5 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-400">
                <AlertTriangle className="w-5 h-5" />
                Important Safety Notice
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 text-lg">
                <strong>READ CAREFULLY:</strong> By participating in Pulse20 EMS training and therapeutic services, 
                you acknowledge and accept certain risks and responsibilities. This waiver is a legal document 
                that affects your rights.
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Waiver Sections */}
        <div className="space-y-8">
          {/* Health & Fitness Confirmation */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="border-2 border-gray-800 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-pink-400">
                  <Heart className="w-5 h-5" />
                  Health & Fitness Confirmation
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Member Confirmation</h3>
                  <p className="text-gray-300 mb-4">
                    By participating in Pulse20 services, you confirm that:
                  </p>
                  <ul className="text-gray-300 space-y-2 list-disc list-inside">
                    <li>You are physically and medically fit to undertake Pulse20 EMS training routines</li>
                    <li>You suffer from no known illness that prevents you from undertaking EMS training</li>
                    <li>All information provided on the Medical History/PAR-Q Form is true and correct</li>
                    <li>You have disclosed all relevant medical conditions and medications</li>
                  </ul>
                </div>
                
                <Separator className="bg-gray-700" />
                
                <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <AlertTriangle className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-yellow-400 mb-2">Medical Clearance Required</h4>
                      <p className="text-gray-300 text-sm">
                        If you have any medical conditions, injuries, or concerns, you must obtain 
                        medical clearance from your healthcare provider before participating in EMS training.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Assumption of Risk */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card className="border-2 border-gray-800 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-pink-400">
                  <Shield className="w-5 h-5" />
                  Assumption of Risk
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Inherent Risks</h3>
                  <p className="text-gray-300 mb-4">
                    You acknowledge and understand that participation in EMS training and therapeutic services involves inherent risks, including but not limited to:
                  </p>
                  <ul className="text-gray-300 space-y-2 list-disc list-inside">
                    <li>Muscle soreness, fatigue, or temporary discomfort</li>
                    <li>Risk of injury from electrical muscle stimulation</li>
                    <li>Potential allergic reactions to equipment or products</li>
                    <li>Exacerbation of pre-existing medical conditions</li>
                    <li>Slips, falls, or other accidents on the premises</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Voluntary Participation</h3>
                  <p className="text-gray-300">
                    You acknowledge that your participation in Pulse20 training is entirely voluntary and 
                    that you undertake all activities at your own risk. You understand that no activity 
                    is without risk and that injuries can occur despite proper instruction and safety measures.
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Indemnity & Release */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Card className="border-2 border-gray-800 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-pink-400">
                  <FileText className="w-5 h-5" />
                  Indemnity & Release of Liability
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Release of Claims</h3>
                  <p className="text-gray-300 mb-4">
                    You hereby release, waive, discharge, and covenant not to sue Pulse20 (Pty) Ltd, 
                    its owners, employees, trainers, and agents from any and all liability, claims, 
                    demands, actions, and causes of action whatsoever arising out of or related to:
                  </p>
                  <ul className="text-gray-300 space-y-2 list-disc list-inside">
                    <li>Any loss, damage, or injury that may be sustained while participating in EMS training</li>
                    <li>Any loss, damage, or injury while on Pulse20 premises</li>
                    <li>Any loss, damage, or injury resulting from therapeutic treatments</li>
                    <li>Any equipment malfunction or failure</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Indemnification</h3>
                  <p className="text-gray-300">
                    You agree to indemnify and hold harmless Pulse20 (Pty) Ltd from any loss, liability, 
                    damage, or costs that may arise from your participation in training activities or 
                    your presence on the premises.
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Weight Loss Disclaimer */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <Card className="border-2 border-gray-800 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-pink-400">Results Disclaimer</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">No Guarantee of Results</h3>
                  <p className="text-gray-300">
                    While weight loss, muscle gain, and fitness improvements may be attained through 
                    adherence to Pulse20 training programs, there is no guarantee that specific goals 
                    will be obtained and/or maintained.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Individual Factors</h3>
                  <p className="text-gray-300">
                    You recognize that fitness and weight control are subject to various factors outside 
                    the control of Pulse20, including but not limited to genetics, lifestyle, diet, 
                    medical conditions, and individual effort and commitment.
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Emergency Procedures */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Card className="border-2 border-gray-800 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-pink-400">
                  <UserCheck className="w-5 h-5" />
                  Emergency Procedures & Medical Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Emergency Contact Authorization</h3>
                  <p className="text-gray-300">
                    In case of emergency, you authorize Pulse20 staff to contact your designated emergency 
                    contact and/or emergency medical services. You consent to receive emergency medical 
                    treatment if required.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Medical Information Sharing</h3>
                  <p className="text-gray-300">
                    You authorize Pulse20 to share relevant medical information with emergency responders 
                    or medical professionals in case of emergency situations.
                  </p>
                </div>

                <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <Heart className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-blue-400 mb-2">Your Safety is Our Priority</h4>
                      <p className="text-gray-300 text-sm">
                        Our trained staff are equipped to handle emergencies and will take all reasonable 
                        measures to ensure your safety during training sessions.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Legal Acknowledgment */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.7 }}
          >
            <Card className="border-2 border-pink-500/30 bg-pink-500/5 backdrop-blur-sm">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-white mb-4 text-center">
                  Legal Acknowledgment
                </h3>
                <div className="space-y-4 text-gray-300">
                  <p>
                    <strong>I acknowledge that:</strong>
                  </p>
                  <ul className="space-y-2 list-disc list-inside">
                    <li>I have read and understood this liability waiver in its entirety</li>
                    <li>I understand the risks involved in EMS training and therapeutic services</li>
                    <li>I am voluntarily participating in these activities</li>
                    <li>This waiver is binding on my heirs, executors, and assigns</li>
                    <li>If any provision is found invalid, the remainder remains in effect</li>
                  </ul>
                  
                  <div className="mt-6 p-4 bg-gray-800/50 rounded-lg">
                    <p className="text-center text-pink-400 font-semibold">
                      By participating in Pulse20 services, you agree to be bound by this waiver.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Contact Information */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-12"
        >
          <Card className="border-2 border-gray-800 bg-gray-900/50 backdrop-blur-sm">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4">
                Questions About This Waiver?
              </h3>
              <p className="text-gray-300 mb-4">
                If you have any questions about this liability waiver or our safety procedures, 
                please contact us before participating in any activities.
              </p>
              <p className="text-pink-400">
                Email: <EMAIL> | Phone: [Contact Number]
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Last Updated */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.9 }}
          className="text-center mt-8 text-gray-500"
        >
          <p>Last updated: {new Date().toLocaleDateString()}</p>
        </motion.div>
      </div>
    </div>
  )
}
