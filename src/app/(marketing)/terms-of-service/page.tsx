"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { motion } from "framer-motion"
import { Scale, Shield, FileText, AlertTriangle } from "lucide-react"

export default function TermsOfServicePage() {
  return (
    <div className="min-h-screen bg-black text-white">
      <div className="w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <Badge className="mb-6 bg-pink-500/20 text-pink-400 border-pink-500/30">
            Legal Documents
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Terms of <span className="text-pink-400">Service</span>
          </h1>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Please read these terms and conditions carefully before using Pulse20 (Pty) Ltd services.
          </p>
        </motion.div>

        {/* Company Information */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-8"
        >
          <Card className="border-2 border-gray-800 bg-gray-900/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-pink-400">
                <FileText className="w-5 h-5" />
                Company Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300">
                <strong>Company Name:</strong> Pulse20 (Pty) Ltd<br />
                <strong>Registration Number:</strong> 2020/122007/07<br />
                <strong>Services:</strong> EMS Training, Sports Massage, Therapeutic Treatments
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Terms Sections */}
        <div className="space-y-8">
          {/* Membership Terms */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="border-2 border-gray-800 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-pink-400">
                  <Scale className="w-5 h-5" />
                  Membership Terms & Conditions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Payment Terms</h3>
                  <ul className="text-gray-300 space-y-2 list-disc list-inside">
                    <li>All payments/fees are payable prior to the start of the first session or by the 3rd of each month</li>
                    <li>Prepaid and single training sessions must be paid for in advance</li>
                    <li>Diamond, Express, and Starter membership contracts require monthly payments</li>
                    <li>Rejected debit orders will incur bank charges as levied against Pulse20's account</li>
                  </ul>
                </div>
                
                <Separator className="bg-gray-700" />
                
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Contract Validity Periods</h3>
                  <ul className="text-gray-300 space-y-2 list-disc list-inside">
                    <li>10 prepaid package: Valid for 1 year from date of payment</li>
                    <li>3-month package: Valid for 3 months from date of payment</li>
                    <li>6-month package: Valid for 6 months from date of payment</li>
                    <li>12-month package: Valid for 12 months from date of payment</li>
                  </ul>
                </div>

                <Separator className="bg-gray-700" />

                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Session Cancellation Policy</h3>
                  <ul className="text-gray-300 space-y-2 list-disc list-inside">
                    <li>24-hour cancellation notice required via WhatsApp/SMS/phone/email</li>
                    <li>Cancellations without 24-hour notice will be charged for the session</li>
                    <li>Make-up sessions available with 24-hour notice, subject to availability</li>
                    <li>Make-up sessions must be completed within 1 month of original cancelled session</li>
                    <li>Maximum make-up sessions: 2 for 3-month, 4 for 6-month, 6 for 12-month contracts</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Liability & Indemnity */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card className="border-2 border-gray-800 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-pink-400">
                  <Shield className="w-5 h-5" />
                  Liability & Indemnity
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <AlertTriangle className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-yellow-400 mb-2">Important Notice</h4>
                      <p className="text-gray-300 text-sm">
                        By using Pulse20 services, you confirm that you are physically and medically fit 
                        to undertake EMS training and therapeutic treatments.
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Member Responsibilities</h3>
                  <ul className="text-gray-300 space-y-2 list-disc list-inside">
                    <li>Confirm physical and medical fitness for Pulse20 training routines</li>
                    <li>Provide accurate medical information on the Medical History/PAR-Q Form</li>
                    <li>Undertake training entirely at your own risk</li>
                    <li>Indemnify and hold Pulse20 (Pty) Ltd harmless against any claims or liability</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Weight Loss Disclaimer</h3>
                  <p className="text-gray-300">
                    While weight loss may be attained through adherence to Pulse20 training, there is no guarantee 
                    that weight loss goals will be obtained and/or maintained. Weight control is subject to various 
                    factors outside the control of Pulse20.
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Contract Modifications */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Card className="border-2 border-gray-800 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-pink-400">Contract Modifications & Cancellation</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Amendment Rights</h3>
                  <ul className="text-gray-300 space-y-2 list-disc list-inside">
                    <li>Contracts may be amended within 30 days of signature by mutual consent without penalty</li>
                    <li>Amendments after 30 days require one month's written notice and may incur penalties</li>
                    <li>Decrease in sessions: 25% penalty of monthly fee × remaining months</li>
                    <li>Shorter contract period: 50% penalty of monthly fee × remaining months</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Cancellation Policy</h3>
                  <ul className="text-gray-300 space-y-2 list-disc list-inside">
                    <li>7-day cooling-off period: Cancel within 7 days without penalty</li>
                    <li>After 7 days: One month's written notice required</li>
                    <li>Early termination: Liable for remainder of contract period</li>
                    <li>No refunds or pro-rata adjustments for early termination</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Membership Freeze</h3>
                  <ul className="text-gray-300 space-y-2 list-disc list-inside">
                    <li>Available for 6-month and 12-month contracts only</li>
                    <li>Maximum 1 freeze per contract period (1 month duration)</li>
                    <li>One month's written notice required (emergency exceptions considered)</li>
                    <li>Contract period extended by freeze duration</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Privacy & Media */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <Card className="border-2 border-gray-800 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-pink-400">Privacy & Media Consent</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Personal Information Protection (POPIA)</h3>
                  <p className="text-gray-300">
                    Pulse20 (Pty) Ltd safeguards all personal information in compliance with the Protection of 
                    Personal Information Act (POPIA). Your data is stored securely and will not be disclosed 
                    to external organizations without your express authorization, unless required by law.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Media Release Consent</h3>
                  <p className="text-gray-300">
                    By using our services, you grant Pulse20 (Pty) Ltd the right to take photographs and videos 
                    during training sessions for promotional purposes. You may opt out by providing written notice.
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Card className="border-2 border-pink-500/30 bg-pink-500/5 backdrop-blur-sm">
              <CardContent className="p-6 text-center">
                <h3 className="text-xl font-semibold text-white mb-4">
                  Questions About Our Terms?
                </h3>
                <p className="text-gray-300 mb-4">
                  Contact us for clarification on any terms and conditions.
                </p>
                <p className="text-pink-400">
                  Email: <EMAIL> | Phone: [Contact Number]
                </p>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Last Updated */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.7 }}
          className="text-center mt-12 text-gray-500"
        >
          <p>Last updated: {new Date().toLocaleDateString()}</p>
        </motion.div>
      </div>
    </div>
  )
}
