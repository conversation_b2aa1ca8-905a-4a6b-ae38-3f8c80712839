// Prevent static generation for pages that use Supabase
export const dynamic = 'force-dynamic'

import { HeroSection } from "@/components/public/hero-section";
import { FeaturesSection } from "@/components/public/features-section";
import { WhyChooseSection } from "@/components/public/why-choose-section";
import { FacilitiesSection } from "@/components/public/facilities-section";
import { TestimonialsSection } from "@/components/public/testimonials-section";
import { Pulse20EMSSection } from "@/components/public/pulse20-ems-section";
import { AnimationContainer, Icons, MaxWidthWrapper } from "@/components";
import { Button, buttonVariants } from "@/components/ui/button";
import { FEATURES, PLANS } from "@/constants";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib";
import { ArrowRightIcon, CheckIcon } from "lucide-react";
import Link from "next/link";
import React from "react"
import { <PERSON><PERSON><PERSON>, TooltipContent, Toolt<PERSON><PERSON><PERSON>ider, TooltipTrigger } from "@/components/ui/tooltip"
import Image from "next/image";
import LocationsSection from "@/components/public/locations-section";
import PricingSpecialsSection from "@/components/public/pricing-specials-section";

const HomePage = () => {

    const baseDelay = 0.2;

    return (
        <>
            {/* Enhanced Hero Section */}
            <HeroSection />

            {/* Enhanced Features Section */}
            <FeaturesSection />

            {/* Dedicated Pulse20 EMS Training Section */}
            <Pulse20EMSSection />

            {/* Why Choose Pulse Section */}
            <WhyChooseSection />

            {/* Facilities Showcase Section */}
            <FacilitiesSection />

            {/* Locations Section */}
            <LocationsSection />

            {/* Pricing & Specials Section */}
            <PricingSpecialsSection />

            {/* Testimonials Carousel Section (Community) */}
            <TestimonialsSection />

            {/* Blog CTA Section */}
            <div className="relative py-24 flex items-center justify-center bg-black overflow-hidden">
              <video
                autoPlay
                loop
                muted
                playsInline
                className="absolute inset-0 w-full h-full object-cover opacity-40 z-0"
                onError={(e) => {
                  // Hide video on error and show gradient background
                  e.currentTarget.style.display = 'none'
                }}
              >
                <source src="/cta-video.webm" type="video/webm" />
                <source src="/cta-video.mp4" type="video/mp4" />
              </video>
              {/* Fallback gradient background */}
              <div className="absolute inset-0 bg-gradient-to-br from-pink-500/10 via-black to-pink-400/5" />

              <div className="relative z-10 flex flex-col items-center justify-center text-center px-4">
                <h2 className="text-3xl md:text-4xl font-extrabold text-white mb-4 drop-shadow-lg">
                  Want more tips, guides, and wellness material?
                </h2>
                <p className="text-lg text-pink-300 mb-6 drop-shadow-lg">
                  Check out our blogs and helpful materials for your fitness journey.
                </p>
                <Link
                  href="/helpful-materials"
                  className="px-8 py-4 bg-pink-500 hover:bg-pink-400 text-white rounded-lg font-semibold shadow-lg transition-colors text-lg"
                >
                  Explore Blogs & Materials
                </Link>
              </div>
            </div>

        </>
    )
};

export default HomePage
