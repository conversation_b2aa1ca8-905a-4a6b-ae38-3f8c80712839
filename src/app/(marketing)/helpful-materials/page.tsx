'use client'

// Prevent static generation for pages that use Supabase
export const dynamic = 'force-dynamic'

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  Download, 
  FileText, 
  Video, 
  Headphones, 
  Search,
  BookOpen
} from "lucide-react"
import Link from "next/link"
import { useEffect, useState } from "react"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"

interface HelpfulMaterial {
  id: string
  title: string
  description: string
  type: "pdf" | "video" | "audio" | "guide"
  category: string
  downloadCount?: number
  size?: string
  duration?: string
  featured?: boolean
  file_url: string // Changed to file_url to match Supabase convention
}

const HelpfulMaterialsPage = () => {
  const [materials, setMaterials] = useState<HelpfulMaterial[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClientComponentClient()

  useEffect(() => {
    const fetchMaterials = async () => {
      try {
        const { data, error } = await supabase
          .from('helpful_materials') // Assuming your table name is 'helpful_materials'
          .select('*') // Select all columns

        if (error) {
          throw error
        }

        setMaterials(data as HelpfulMaterial[])
      } catch (err: any) {
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    fetchMaterials()
  }, [supabase])

  const categories = ["All", "Beginner", "Technique", "Nutrition", "Mental Health", "Recovery"]

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "pdf": return FileText
      case "video": return Video
      case "audio": return Headphones
      case "guide": return BookOpen
      default: return FileText
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[50vh] text-white">
        Loading helpful materials...
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-[50vh] text-red-500">
        Error: {error}
      </div>
    )
  }

  return (
    <>
      {/* Enhanced Hero Section */}
      <section className="relative overflow-hidden bg-black min-h-[50vh]">
        <div className="absolute inset-0 bg-black" />
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-pink-400/8 rounded-full blur-3xl" />

        <div className="relative w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="min-h-[50vh] flex items-center justify-center">
            <div className="text-center">
              <Badge className="mb-6 bg-pink-500/20 text-pink-400 border-pink-500/30">
                Free Resources
              </Badge>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white">
                Helpful{" "}
                <span className="text-pink-400">
                  Materials
                </span>
              </h1>
              <p className="text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
                Free resources to support your fitness journey. Download guides, watch tutorials,
                and access expert-curated content to accelerate your progress.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Search and Filter */}
      <section className="py-20 lg:py-32 bg-black border-b border-gray-800">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row gap-6 items-center justify-between">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search materials..."
                className="pl-10 bg-gray-800 border-gray-700 text-white placeholder:text-gray-500 focus:border-pink-400 focus:ring-pink-400"
              />
            </div>
            <div className="flex items-center gap-3 flex-wrap">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={category === "All" ? "default" : "outline"}
                  size="sm"
                  className={category === "All" ? "bg-pink-500 hover:bg-pink-400 text-white" : "border-pink-500/30 text-pink-400 hover:bg-pink-500/10"}
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Featured Materials */}
      <section className="py-20 lg:py-32 bg-black">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold mb-12 text-white">Featured Resources</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
            {materials.filter(material => material.featured).map((material) => {
              const TypeIcon = getTypeIcon(material.type)

              return (
                <Card key={material.id} className="hover:shadow-xl hover:shadow-pink-500/20 transition-all duration-300 group bg-gray-900/50 backdrop-blur-sm border-gray-800 hover:border-pink-500/50">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-4">
                        <div className="w-16 h-16 rounded-lg bg-pink-500/20 flex items-center justify-center">
                          <TypeIcon className="w-8 h-8 text-pink-400" />
                        </div>
                        <div>
                          <Badge className="bg-pink-500/20 text-pink-400 mb-2">
                            {material.category}
                          </Badge>
                          <Badge className="ml-2 bg-pink-500 text-white">
                            Featured
                          </Badge>
                        </div>
                      </div>
                      <Button className="bg-pink-500 hover:bg-pink-400 text-white shadow-lg shadow-pink-500/30" asChild>
                        <Link href={material.file_url} target="_blank" rel="noopener noreferrer" download>
                          <Download className="w-4 h-4 mr-2" />
                          Download
                        </Link>
                      </Button>
                    </div>
                    <CardTitle className="group-hover:text-pink-400 transition-colors text-white text-2xl">
                      {material.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-400 mb-6 leading-relaxed">
                      {material.description}
                    </p>
                    <div className="flex items-center justify-between text-sm text-gray-400">
                      <span>{(material.downloadCount ?? 0).toLocaleString()} downloads</span>
                      <span>
                        {material.size && `${material.size} • `}
                        {material.duration && `${material.duration} • `}
                        {material.type.toUpperCase()}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* All Materials */}
          <h2 className="text-3xl font-bold mb-12 text-white">All Resources</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {materials.map((material) => {
              const TypeIcon = getTypeIcon(material.type)

              return (
                <Card key={material.id} className="hover:shadow-lg hover:shadow-pink-500/20 transition-all duration-300 group bg-gray-900/50 backdrop-blur-sm border-gray-800 hover:border-pink-500/50">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="w-12 h-12 rounded-lg bg-pink-500/20 flex items-center justify-center">
                        <TypeIcon className="w-6 h-6 text-pink-400" />
                      </div>
                      <Badge className="bg-pink-500/20 text-pink-400">
                        {material.category}
                      </Badge>
                    </div>

                    <h3 className="font-bold mb-3 group-hover:text-pink-400 transition-colors line-clamp-2 text-white text-lg">
                      {material.title}
                    </h3>

                    <p className="text-sm text-gray-400 mb-4 line-clamp-3 leading-relaxed">
                      {material.description}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="text-xs text-gray-400">
                        <div>{(material.downloadCount ?? 0).toLocaleString()} downloads</div>
                        <div>
                          {material.size && material.size}
                          {material.duration && material.duration}
                        </div>
                      </div>
                      <Button size="sm" variant="outline" className="border-pink-500/30 text-pink-400 hover:bg-pink-500/10" asChild>
                        <Link href={material.file_url} target="_blank" rel="noopener noreferrer" download>
                          <Download className="w-3 h-3 mr-1" />
                          Get
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 lg:py-32 bg-gray-900/30">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <Card className="max-w-2xl mx-auto bg-gradient-to-r from-pink-500/10 to-pink-400/10 border-pink-500/20 bg-gray-900/50 backdrop-blur-sm">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold mb-4 text-white">Need More Support?</h3>
              <p className="text-gray-400 mb-6">
                Join our community for personalized guidance, expert advice, and access to exclusive content.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-pink-500 hover:bg-pink-400 text-white shadow-lg shadow-pink-500/30" asChild>
                  <Link href="/auth/signup">
                    Join Community
                  </Link>
                </Button>
                <Button variant="outline" className="border-pink-500/30 text-pink-400 hover:bg-pink-500/10" asChild>
                  <Link href="/contact">
                    Contact Us
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </>
  )
}

export default HelpfulMaterialsPage
