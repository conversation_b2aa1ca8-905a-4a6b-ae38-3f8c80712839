"use client"

// Prevent static generation for pages that use Supabase
export const dynamic = 'force-dynamic'

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { useState, useEffect } from "react"
import { supabase } from "@/lib/supabase/client"
import { useSearchParams } from "next/navigation"
import {
  Mail,
  Phone,
  MapPin,
  Clock,
  MessageSquare,
  Send,
  CheckCircle,
  Users,
  Headphones,
  Loader2,
  Star,
  Zap,
  Target
} from "lucide-react"
import Image from "next/image"

const packages = [
  { id: 'intro', name: 'Intro Consultation', price: 'R500', description: 'Perfect for first-time clients' },
  { id: 'starter', name: 'Starter Package', price: 'R350/session', description: '3-month commitment' },
  { id: 'express', name: 'Express Package', price: 'R300/session', description: '6-month commitment - Most Popular' },
  { id: 'diamond', name: 'Diamond Package', price: 'R250/session', description: '12-month commitment - Best Value' },
  { id: 'other', name: 'Other Inquiry', price: '', description: 'General questions or custom needs' }
];

const ContactPage = () => {
  const searchParams = useSearchParams();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    selectedPackage: 'intro'
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')

  useEffect(() => {
    const packageParam = searchParams.get('package');
    if (packageParam && packages.find(p => p.id === packageParam)) {
      setFormData(prev => ({
        ...prev,
        selectedPackage: packageParam,
        subject: packages.find(p => p.id === packageParam)?.name || ''
      }));
    }
  }, [searchParams]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setSubmitStatus('idle')

    try {
      const selectedPkg = packages.find(p => p.id === formData.selectedPackage);
      const messageWithPackage = `Package Interest: ${selectedPkg?.name} ${selectedPkg?.price ? `(${selectedPkg?.price})` : ''}\n${formData.phone ? `Phone: ${formData.phone}\n` : ''}\n${formData.message}`;

      const { error } = await supabase
        .from('contact_submissions')
        .insert([
          {
            first_name: formData.firstName,
            last_name: formData.lastName,
            email: formData.email,
            subject: formData.subject,
            message: messageWithPackage
          }
        ])

      if (error) throw error

      setSubmitStatus('success')
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        subject: '',
        message: '',
        selectedPackage: 'intro'
      })
    } catch (error) {
      console.error('Error submitting contact form:', error)
      setSubmitStatus('error')
    } finally {
      setIsSubmitting(false)
    }
  }
  return (
    <>
      <section className="h-screen flex flex-col lg:flex-row">
        {/* Left: Contact Form (compact) */}
        <div className="flex-1 flex flex-col justify-center bg-black px-4 lg:px-8 order-2 lg:order-1">
          <div className="max-w-ms w-full mx-auto">
            <div className="flex flex-col items-center mb-6">
              <div className="flex items-center justify-center">
                <svg className="w-8 h-8 text-pink-400 mr-3" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" stroke="#ec4899" strokeWidth="2"/><path d="M12 6v6l4 2" stroke="#ec4899" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-center">
                  <span className="text-white">Your Journey,</span>
                  <span className="text-pink-400">Our Priority</span>
                 
                </h1>
              </div>
            </div>
           <Card className="bg-transparent backdrop-blur-sm border-black">
              <CardHeader className="pb-3">
                <CardTitle className="text-xl font-bold text-white">Send us a Message</CardTitle>
                <p className="text-gray-400 text-sm">
                  Fill out the form below and we'll get back to you within 24 hours.
                </p>
              </CardHeader>
              <CardContent>
                {submitStatus === 'success' && (
                  <div className="mb-6 p-4 bg-green-500/20 border border-green-500/30 rounded-lg">
                    <div className="flex items-center gap-2 text-green-400">
                      <CheckCircle className="w-5 h-5" />
                      <span className="font-medium">Message sent successfully!</span>
                    </div>
                    <p className="text-green-300 text-sm mt-1">
                      We'll get back to you within 24 hours.
                    </p>
                  </div>
                )}

                {submitStatus === 'error' && (
                  <div className="mb-6 p-4 bg-red-500/20 border border-red-500/30 rounded-lg">
                    <div className="flex items-center gap-2 text-red-400">
                      <MessageSquare className="w-5 h-5" />
                      <span className="font-medium">Failed to send message</span>
                    </div>
                    <p className="text-red-300 text-sm mt-1">
                      Please try again or contact us directly.
                    </p>
                  </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        First Name *
                      </label>
                      <Input
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        placeholder="John"
                        required
                        className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-500 focus:border-pink-400 focus:ring-pink-400 text-sm py-2"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Last Name *
                      </label>
                      <Input
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        placeholder="Doe"
                        required
                        className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-500 focus:border-pink-400 focus:ring-pink-400 text-sm py-2"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Email Address *
                      </label>
                      <Input
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        placeholder="<EMAIL>"
                        required
                        className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-500 focus:border-pink-400 focus:ring-pink-400 text-sm py-2"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Phone Number
                      </label>
                      <Input
                        name="phone"
                        type="tel"
                        value={formData.phone}
                        onChange={handleInputChange}
                        placeholder="+27 83 408 3665"
                        className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-500 focus:border-pink-400 focus:ring-pink-400 text-sm py-2"
                      />
                    </div>
                  </div>

                  {/* Package Selection - Compact */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                      I'm interested in *
                    </label>
                    <select
                      name="selectedPackage"
                      value={formData.selectedPackage}
                      onChange={(e) => {
                        const selectedPkg = packages.find(p => p.id === e.target.value);
                        setFormData(prev => ({
                          ...prev,
                          selectedPackage: e.target.value,
                          subject: selectedPkg?.name || ''
                        }));
                      }}
                      className="w-full bg-gray-800 border border-gray-700 text-white placeholder:text-gray-500 focus:border-pink-400 focus:ring-pink-400 text-sm py-2 px-3 rounded-md"
                      required
                    >
                      {packages.map((pkg) => (
                        <option key={pkg.id} value={pkg.id}>
                          {pkg.name} {pkg.price ? `- ${pkg.price}` : ''}
                          {pkg.id === 'intro' ? ' ⭐' : ''}
                          {pkg.id === 'express' ? ' (Popular)' : ''}
                          {pkg.id === 'diamond' ? ' (Best Value)' : ''}
                        </option>
                      ))}
                    </select>
                    <p className="text-gray-500 text-xs mt-1">
                      {packages.find(p => p.id === formData.selectedPackage)?.description}
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                      Subject *
                    </label>
                    <Input
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      placeholder="How can we help you?"
                      required
                      className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-500 focus:border-pink-400 focus:ring-pink-400 text-sm py-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                      Message *
                    </label>
                    <Textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      placeholder="Tell us more about your inquiry..."
                      rows={4}
                      required
                      className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-500 focus:border-pink-400 focus:ring-pink-400 text-sm"
                    />
                  </div>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-pink-500 hover:bg-pink-400 text-white shadow-lg shadow-pink-500/30 disabled:opacity-50 text-sm py-2.5"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4 mr-2" />
                        Send Message
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
        {/* Right: Hero/Info with Background Image */}
        <div className="flex-1 relative h-full flex items-center justify-center order-1 lg:order-2">
          <div className="absolute inset-0">
            <Image src="/lenasia1.jpg" alt="Contact Hero" fill className="object-cover" />
            <div className="absolute inset-0 bg-black/60" />
          </div>
          <div className="relative z-10 flex flex-col items-center justify-center text-center px-8">
            <Badge className="mb-6 bg-pink-500/20 text-pink-400 border-pink-500/30">Get In Touch</Badge>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white">
              Contact <span className="text-pink-400">Our Team</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-2xl mb-8">
              Experience the future of fitness with private, personalized EMS sessions and dedicated customer care. At Pulse, your journey is our priority—every session, every goal, every step.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <div className="flex flex-col items-center">
                <span className="text-3xl font-bold text-white">24/7</span>
                <span className="text-pink-400 text-sm font-medium">Support</span>
              </div>
              <div className="flex flex-col items-center">
                <span className="text-3xl font-bold text-white">2hr</span>
                <span className="text-pink-400 text-sm font-medium">Avg. Response</span>
              </div>
              <div className="flex flex-col items-center">
                <span className="text-3xl font-bold text-white">1000+</span>
                <span className="text-pink-400 text-sm font-medium">Happy Members</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}

export default ContactPage
