// Force dynamic rendering for all pages to prevent Supabase prerender errors
export const dynamic = 'force-dynamic'

import Providers from "@/components/global/providers";
import { Toaster } from "@/components/ui/sonner";
import { WhatsAppFloat } from "@/components/ui/whatsapp-float";
import { ClientOnly } from "@/components/ui/client-only";
import { dmSans, inter } from "@/constants";
import { cn } from "@/lib/utils";
import "@/styles/globals.css";
import { generateMetadata } from "@/utils";
import { DM_Sans } from "next/font/google";

const font = DM_Sans({ subsets: ["latin"] });

export const metadata = {
  title: "Pulse20.co.za - Revolutionary EMS Training & Therapeutic Services",
  description: "Experience Pulse20 Wireless EMS Training - 36,000 muscle contractions in 20 minutes. Combined with sports massage, cupping, and needling therapy. Locations in Lenasia & Mulbarton.",
  keywords: ["EMS training", "wireless EMS", "sports massage", "cupping therapy", "needling", "acupuncture", "fitness", "Lenasia", "<PERSON><PERSON>barton", "Pulse20"],
  authors: [{ name: "Pulse20 EMS Training" }],
  creator: "Pulse20.co.za",
  publisher: "Pulse20.co.za",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="en">
            <body
                className={cn(
                    "min-h-screen bg-background text-foreground !font-heading antialiased",
                    inter.variable,
                    dmSans.variable,
                )}
            >
                <Providers>
                    <Toaster richColors position="top-center" />
                    {children}
                    <ClientOnly>
                        <WhatsAppFloat
                            phoneNumber="+27 83 408 3665"
                            message="Hi! I'm interested in Pulse20 EMS Training. Can you help me get started with my FREE session?"
                        />
                    </ClientOnly>
                </Providers>
            </body>
        </html>
    );
};
