// Force dynamic rendering for all pages to prevent Supabase prerender errors
export const dynamic = 'force-dynamic'

import Providers from "@/components/global/providers";
import { Toaster } from "@/components/ui/sonner";
import { dmSans, inter } from "@/constants";
import { cn } from "@/lib/utils";
import "@/styles/globals.css";
import { generateMetadata } from "@/utils";
import { DM_Sans } from "next/font/google";

const font = DM_Sans({ subsets: ["latin"] });

export const metadata = {
  title: "Pulse20.co.za - Your Gym Management Platform",
  description: "Comprehensive web-based member management platform for gyms focused on self-growth and personal development.",
  keywords: ["gym management", "fitness tracking", "member portal", "attendance tracking", "gym software"],
  authors: [{ name: "Pulse20.co.za Team" }],
  creator: "Pulse20.co.za",
  publisher: "Pulse20.co.za",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="en">
            <body
                className={cn(
                    "min-h-screen bg-background text-foreground !font-heading antialiased",
                    inter.variable,
                    dmSans.variable,
                )}
            >
                <Providers>
                    <Toaster richColors position="top-center" />
                    {children}
                </Providers>
            </body>
        </html>
    );
};
