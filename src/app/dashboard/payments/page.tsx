"use client"

// Prevent static generation for pages that use Supabase
export const dynamic = 'force-dynamic'

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { NeonButton } from "@/components/ui/neon-button"
import { Badge } from "@/components/ui/badge"
import { supabase } from "@/lib/supabase/client"
import {
  CreditCard,
  Calendar,
  CheckCircle,
  AlertCircle,
  Download,
  Clock,
  Loader2
} from "lucide-react"

interface Payment {
  id: string
  date: Date
  amount: number
  description: string
  status: string
  method: string
}

interface Profile {
  membership_type: string
  membership_status: string
  join_date: string
}

const PaymentsPage = () => {
  const [profile, setProfile] = useState<Profile | null>(null)
  const [paymentHistory, setPaymentHistory] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchPaymentData()
  }, [])

  const fetchPaymentData = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      // Fetch user profile
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileError) throw profileError
      setProfile(profileData)

      // Fetch payment history
      const { data: payments, error: paymentsError } = await supabase
        .from('payments')
        .select('*')
        .eq('user_id', user.id)
        .order('payment_date', { ascending: false })

      if (paymentsError) throw paymentsError

      // Transform payments data
      const transformedPayments: Payment[] = (payments || []).map(payment => ({
        id: payment.id,
        date: new Date(payment.payment_date),
        amount: payment.amount,
        description: payment.description || 'Monthly Membership Fee',
        status: payment.status,
        method: payment.payment_method || 'Credit Card'
      }))

      setPaymentHistory(transformedPayments)
    } catch (error) {
      console.error('Error fetching payment data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 animate-spin text-pink-400" />
      </div>
    )
  }

  // Calculate payment info
  const currentBalance = 0 // This would be calculated from unpaid invoices
  const nextPaymentDue = new Date()
  nextPaymentDue.setMonth(nextPaymentDue.getMonth() + 1)

  const getMembershipFee = (type: string) => {
    switch (type) {
      case 'vip': return 150
      case 'premium': return 100
      case 'basic': return 50
      default: return 50
    }
  }

  const monthlyFee = profile ? getMembershipFee(profile.membership_type) : 50

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-member mb-2">
          Payment Management 💳
        </h1>
        <p className="text-muted-foreground">
          Manage your membership payments and billing information
        </p>
      </div>

      {/* Payment Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <CreditCard className="w-4 h-4 text-member" />
              Current Balance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-member">
              R{currentBalance.toFixed(2)}
            </div>
            <p className="text-xs text-muted-foreground">
              {currentBalance === 0 ? "All caught up!" : "Outstanding balance"}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Calendar className="w-4 h-4 text-marketing" />
              Next Payment
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-marketing">
              {nextPaymentDue.toLocaleDateString()}
            </div>
            <p className="text-xs text-muted-foreground">
              R{monthlyFee} due
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-blog" />
              Payment Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blog">Active</div>
            <p className="text-xs text-muted-foreground">
              Membership current
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <NeonButton variant="member" className="w-full">
              <CreditCard className="w-4 h-4 mr-2" />
              Make Payment
            </NeonButton>
            <Button variant="outline" className="w-full">
              <Calendar className="w-4 h-4 mr-2" />
              Update Payment Method
            </Button>
            <Button variant="outline" className="w-full">
              <Download className="w-4 h-4 mr-2" />
              Download Receipt
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Payment History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5 text-admin" />
            Payment History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {paymentHistory.map((payment) => (
              <div
                key={payment.id}
                className="flex items-center justify-between p-4 border rounded-lg"
              >
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 rounded-full bg-blog/20 flex items-center justify-center">
                    <CheckCircle className="w-5 h-5 text-blog" />
                  </div>
                  <div>
                    <p className="font-medium">{payment.description}</p>
                    <p className="text-sm text-muted-foreground">
                      {payment.date.toLocaleDateString()} • {payment.method}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium">R{payment.amount}</p>
                  <Badge variant="secondary" className="text-xs">
                    {payment.status === "paid" ? "Paid" : "Pending"}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Membership Details */}
      <Card>
        <CardHeader>
          <CardTitle>Membership Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">Current Plan</h4>
              <p className="text-2xl font-bold text-member mb-1 capitalize">
                {profile?.membership_type || 'Basic'} Membership
              </p>
              <p className="text-sm text-muted-foreground">
                {profile?.membership_type === 'vip' ? 'Full access + personal training' :
                 profile?.membership_type === 'premium' ? 'Full access to all gym facilities and classes' :
                 'Basic gym access'}
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">Billing Information</h4>
              <div className="space-y-1 text-sm">
                <p>Monthly Fee: <span className="font-medium">R{monthlyFee}</span></p>
                <p>Billing Cycle: <span className="font-medium">Monthly</span></p>
                <p>Next Billing: <span className="font-medium">{nextPaymentDue.toLocaleDateString()}</span></p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default PaymentsPage
