"use client"

import { FeedbackForm } from "@/components/member/feedback-form"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { MessageSquare, TrendingUp, Users, Star } from "lucide-react"

const FeedbackPage = () => {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-communication mb-2">
          Your Feedback Matters 💬
        </h1>
        <p className="text-muted-foreground">
          Help us improve your gym experience by sharing your thoughts
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <MessageSquare className="w-4 h-4 text-communication" />
              Your Feedback
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-communication">12</div>
            <p className="text-xs text-muted-foreground">Total submissions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Star className="w-4 h-4 text-member" />
              Average Rating
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-member">4.8</div>
            <p className="text-xs text-muted-foreground">Out of 5 stars</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <TrendingUp className="w-4 h-4 text-blog" />
              Response Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blog">95%</div>
            <p className="text-xs text-muted-foreground">Staff responses</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Feedback Form */}
      <FeedbackForm />

      {/* Recent Feedback */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5 text-admin" />
            Your Recent Feedback
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Mock feedback items */}
            <div className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Workout Experience</span>
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className="w-4 h-4 fill-member text-member"
                      />
                    ))}
                  </div>
                </div>
                <span className="text-xs text-muted-foreground">2 days ago</span>
              </div>
              <p className="text-sm text-muted-foreground">
                "Great workout session today! The new equipment is fantastic and the gym was clean."
              </p>
              <div className="mt-2 p-2 bg-blog/10 rounded border-l-2 border-blog">
                <p className="text-xs text-blog font-medium">Staff Response:</p>
                <p className="text-xs text-muted-foreground">
                  Thank you for the positive feedback! We're glad you're enjoying the new equipment.
                </p>
              </div>
            </div>

            <div className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Facility & Equipment</span>
                  <div className="flex">
                    {[1, 2, 3, 4].map((star) => (
                      <Star
                        key={star}
                        className="w-4 h-4 fill-member text-member"
                      />
                    ))}
                    <Star className="w-4 h-4 text-muted-foreground" />
                  </div>
                </div>
                <span className="text-xs text-muted-foreground">1 week ago</span>
              </div>
              <p className="text-sm text-muted-foreground">
                "The locker room could use better ventilation, but overall facilities are good."
              </p>
              <div className="mt-2 p-2 bg-blog/10 rounded border-l-2 border-blog">
                <p className="text-xs text-blog font-medium">Staff Response:</p>
                <p className="text-xs text-muted-foreground">
                  Thanks for bringing this to our attention. We're working on improving the ventilation system.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default FeedbackPage
