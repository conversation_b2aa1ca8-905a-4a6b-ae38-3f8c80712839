"use client"

// Prevent static generation for pages that use Supabase
export const dynamic = 'force-dynamic'

import { useState, useEffect } from "react";
import { supabase } from "@/lib/supabase/client";
import { Loader2, CalendarDays, User, Clock, ClipboardList, PlusCircle } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { format, parseISO } from "date-fns";
import { cn } from "@/lib/utils";

interface Appointment {
    id: string;
    user_id: string;
    trainer_name: string; // Assuming a trainer name field
    service_type: string; // e.g., "Personal Training", "Massage"
    appointment_time: string;
    duration_minutes: number;
    status: "confirmed" | "cancelled" | "pending";
    created_at: string;
}

export default function AppointmentsPage() {
    const [appointments, setAppointments] = useState<Appointment[]>([]);
    const [loading, setLoading] = useState(true);
    const [user, setUser] = useState<any>(null);

    useEffect(() => {
        const getUser = async () => {
            const { data: { user } } = await supabase.auth.getUser();
            setUser(user);
        };
        getUser();
    }, []);

    useEffect(() => {
        const fetchAppointments = async () => {
            if (!user) return;

            setLoading(true);
            // Assuming an 'appointments' table in Supabase
            const { data, error } = await supabase
                .from('appointments')
                .select('*')
                .eq('user_id', user.id)
                .order('appointment_time', { ascending: true });

            if (error) {
                console.error("Error fetching appointments:", error);
            } else {
                setAppointments(data || []);
            }
            setLoading(false);
        };

        if (user) {
            fetchAppointments();
        }
    }, [user]);

    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <Loader2 className="w-8 h-8 animate-spin text-member" />
            </div>
        );
    }

    return (
        <div className="w-full space-y-8">
            <div className="text-center">
                <h1 className="text-3xl font-bold text-member mb-2">
                    Your Appointments 🗓️
                </h1>
                <p className="text-muted-foreground">
                    View your upcoming and past scheduled sessions.
                </p>
            </div>

            <div className="flex justify-center mb-6">
                <Button variant="default" className="gap-2">
                    <PlusCircle className="w-5 h-5" />
                    Book New Appointment
                </Button>
            </div>

            {appointments.length === 0 ? (
                <Card className="p-6 text-center">
                    <CardContent className="text-muted-foreground">
                        No appointments scheduled yet. Tap the button above to book one!
                    </CardContent>
                </Card>
            ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {appointments.map((appointment) => (
                        <Card key={appointment.id} className="relative overflow-hidden group">
                            <CardHeader className="pb-2">
                                <CardTitle className="text-lg font-semibold text-member flex items-center gap-2">
                                    <CalendarDays className="w-5 h-5" />
                                    {format(parseISO(appointment.appointment_time), 'PPP')}
                                </CardTitle>
                                <span className="text-sm text-muted-foreground">
                                    {format(parseISO(appointment.appointment_time), 'p')}
                                </span>
                            </CardHeader>
                            <CardContent className="space-y-2">
                                <div className="flex items-center gap-2 text-foreground">
                                    <User className="w-4 h-4 text-muted-foreground" />
                                    <span className="font-medium">Trainer:</span> {appointment.trainer_name}
                                </div>
                                <div className="flex items-center gap-2 text-foreground">
                                    <ClipboardList className="w-4 h-4 text-muted-foreground" />
                                    <span className="font-medium">Service:</span> {appointment.service_type}
                                </div>
                                <div className="flex items-center gap-2 text-foreground">
                                    <Clock className="w-4 h-4 text-muted-foreground" />
                                    <span className="font-medium">Duration:</span> {appointment.duration_minutes} mins
                                </div>
                                <div className={cn(
                                    "text-sm font-semibold mt-2",
                                    appointment.status === "confirmed" && "text-green-500",
                                    appointment.status === "cancelled" && "text-red-500",
                                    appointment.status === "pending" && "text-yellow-500"
                                )}>
                                    Status: {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                                </div>
                            </CardContent>
                            <div className="absolute inset-0 bg-gradient-to-br from-member/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                            <CalendarDays className="absolute bottom-4 right-4 w-10 h-10 text-member/20 group-hover:text-member/50 transition-colors duration-300" />
                        </Card>
                    ))}
                </div>
            )}
        </div>
    );
} 