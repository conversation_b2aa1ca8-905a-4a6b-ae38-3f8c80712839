"use client"

// Prevent static generation for pages that use Supabase
export const dynamic = 'force-dynamic'

import { useState, useEffect } from "react";
import { supabase } from "@/lib/supabase/client";
import { Loader2, Send } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

export default function ContactPage() {
    const [subject, setSubject] = useState("");
    const [message, setMessage] = useState("");
    const [loading, setLoading] = useState(false);
    const [user, setUser] = useState<any>(null);

    useEffect(() => {
        const getUser = async () => {
            const { data: { user } } = await supabase.auth.getUser();
            setUser(user);
        };
        getUser();
    }, []);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!user) {
            toast.error("You must be logged in to send a message.");
            return;
        }
        if (!subject.trim() || !message.trim()) {
            toast.error("Subject and message cannot be empty.");
            return;
        }

        setLoading(true);
        try {
            // Assuming a 'messages' table in Supabase
            const { error } = await supabase
                .from('messages')
                .insert({
                    user_id: user.id,
                    subject: subject.trim(),
                    message: message.trim(),
                    status: "pending" // Default status
                });

            if (error) {
                throw error;
            }

            toast.success("Your message has been sent to staff!");
            setSubject("");
            setMessage("");
        } catch (error: any) {
            console.error("Error sending message:", error);
            toast.error(`Failed to send message: ${error.message || "Unknown error"}`);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="w-full space-y-8">
            <div className="text-center">
                <h1 className="text-3xl font-bold text-communication mb-2">
                    Contact Staff 📞
                </h1>
                <p className="text-muted-foreground">
                    Have a question or need assistance? Send us a message.
                </p>
            </div>

            <Card className="max-w-2xl mx-auto p-6">
                <CardHeader className="text-center pb-4">
                    <CardTitle className="text-2xl font-semibold">Send a Message</CardTitle>
                </CardHeader>
                <CardContent>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div>
                            <label htmlFor="subject" className="block text-sm font-medium text-foreground mb-1">Subject</label>
                            <Input
                                id="subject"
                                type="text"
                                placeholder="e.g., Membership inquiry, Facility question"
                                value={subject}
                                onChange={(e) => setSubject(e.target.value)}
                                disabled={loading}
                            />
                        </div>
                        <div>
                            <label htmlFor="message" className="block text-sm font-medium text-foreground mb-1">Message</label>
                            <Textarea
                                id="message"
                                placeholder="Type your message here..."
                                rows={6}
                                value={message}
                                onChange={(e) => setMessage(e.target.value)}
                                disabled={loading}
                            />
                        </div>
                        <Button type="submit" className="w-full gap-2" disabled={loading}>
                            {loading ? (
                                <Loader2 className="w-4 h-4 animate-spin" />
                            ) : (
                                <Send className="w-4 h-4" />
                            )}
                            {loading ? "Sending..." : "Send Message"}
                        </Button>
                    </form>
                </CardContent>
            </Card>
        </div>
    );
} 