"use client"

import { useState, useEffect, useCallback } from "react"
import { CheckInButton } from "@/components/member/check-in-button"
import { ProgressDashboard } from "@/components/member/progress-dashboard"
import { AttendanceCalendar } from "@/components/member/attendance-calendar"
import { QuickActions } from "@/components/member/quick-actions"
import { MemberLayout } from "@/components/layouts/member-layout"
import { supabase } from "@/lib/supabase/client"
import { Loader2, Zap, Target, Flame, Trophy, Calendar, Clock, TrendingUp, Activity, Star } from "lucide-react"
import { toast } from "sonner"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import Link from "next/link"

// Prevent static generation for pages that use Supabase
export const dynamic = 'force-dynamic'

interface CheckIn {
  id: string
  user_id: string
  check_in_time: string
  check_out_time?: string
}

interface ProgressData {
  monthlyAttendance: number
  currentStreak: number
  monthlyGoal: number
  totalWorkouts: number
}

interface AttendanceData {
  date: Date
  attended: boolean
  duration: number
}

const DashboardPage = () => {
  const [progressData, setProgressData] = useState<ProgressData | null>(null)
  const [attendanceData, setAttendanceData] = useState<AttendanceData[]>([])
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<any>(null)
  const [isCheckedIn, setIsCheckedIn] = useState(false)
  const [checkInTime, setCheckInTime] = useState<string | null>(null)

  const fetchDashboardData = useCallback(async () => {
    try {
      // Fetch check-ins for the current user
      const { data: checkIns, error } = await supabase
        .from('check_ins')
        .select('*')
        .eq('user_id', user.id)
        .order('check_in_time', { ascending: false })

      if (error) throw error

      // Check if user is currently checked in
      const activeCheckIn = checkIns?.find(checkIn => !checkIn.check_out_time)
      if (activeCheckIn) {
        setIsCheckedIn(true)
        setCheckInTime(activeCheckIn.check_in_time)
      } else {
        setIsCheckedIn(false)
        setCheckInTime(null)
      }

      // Calculate progress data
      const now = new Date()
      const currentMonth = now.getMonth()
      const currentYear = now.getFullYear()
      
      const monthlyCheckIns = checkIns?.filter(checkIn => {
        const checkInDate = new Date(checkIn.check_in_time)
        return checkInDate.getMonth() === currentMonth && 
               checkInDate.getFullYear() === currentYear &&
               checkIn.check_out_time // Only count completed sessions
      }) || []

      // Calculate streak
      let currentStreak = 0
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      for (let i = 0; i < 30; i++) {
        const checkDate = new Date(today)
        checkDate.setDate(today.getDate() - i)
        
        const hasWorkout = checkIns?.some(checkIn => {
          const checkInDate = new Date(checkIn.check_in_time)
          checkInDate.setHours(0, 0, 0, 0)
          return checkInDate.getTime() === checkDate.getTime() && checkIn.check_out_time
        })
        
        if (hasWorkout) {
          currentStreak++
        } else if (i > 0) { // Allow today to not have a workout yet
          break
        }
      }

      const progressData: ProgressData = {
        monthlyAttendance: monthlyCheckIns.length,
        currentStreak,
        monthlyGoal: 12, // Default goal
        totalWorkouts: checkIns?.filter(c => c.check_out_time).length || 0
      }

      setProgressData(progressData)

      // Generate attendance data for calendar
      const attendanceData: AttendanceData[] = []
      for (let i = 0; i < 30; i++) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        
        const checkIn = checkIns?.find(c => {
          const checkInDate = new Date(c.check_in_time)
          return checkInDate.toDateString() === date.toDateString() && c.check_out_time
        })
        
        if (checkIn) {
          const duration = checkIn.check_out_time ? 
            Math.round((new Date(checkIn.check_out_time).getTime() - new Date(checkIn.check_in_time).getTime()) / (1000 * 60)) : 
            60
          
          attendanceData.push({
            date,
            attended: true,
            duration
          })
        }
      }

      setAttendanceData(attendanceData)
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      toast.error('Failed to load dashboard data')
    } finally {
      setLoading(false)
    }
  }, [user])

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      if (!user) {
        setLoading(false)
      }
    }
    getUser()
  }, [])

  useEffect(() => {
    if (loading === false && !user) {
      window.location.href = '/auth/signin'
    }
  }, [loading, user])

  useEffect(() => {
    if (user) fetchDashboardData()
  }, [user, fetchDashboardData])

  const handleCheckIn = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('check_ins')
        .insert({
          user_id: user.id,
        })
        .select()
        .single()

      if (error) throw error

      setIsCheckedIn(true)
      setCheckInTime(data.check_in_time)
      fetchDashboardData()
      toast.success("Checked in successfully!")
    } catch (error: any) {
      console.error("Error during check-in:", error)
      toast.error(`Failed to check in: ${error.message || "Unknown error"}`)
    }
  }, [user, fetchDashboardData])

  const handleCheckOut = useCallback(async () => {
    if (!checkInTime) {
      toast.info("No active check-in to check out from.")
      return
    }

    try {
      const checkInTimeDate = new Date(checkInTime)
      const durationMinutes = Math.round((new Date().getTime() - checkInTimeDate.getTime()) / (1000 * 60))

      const { error } = await supabase
        .from('check_ins')
        .update({ check_out_time: new Date().toISOString() })
        .eq('user_id', user.id)
        .eq('check_in_time', checkInTime)
        .is('check_out_time', null)

      if (error) throw error

      setIsCheckedIn(false)
      setCheckInTime(null)
      fetchDashboardData()
      toast.success(`Checked out successfully! Duration: ${durationMinutes} minutes.`)
    } catch (error: any) {
      console.error("Error during check-out:", error)
      toast.error(`Failed to check out: ${error.message || "Unknown error"}`)
    }
  }, [checkInTime, fetchDashboardData, user])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 flex items-center justify-center">
        <div className="text-center space-y-6">
          <div className="relative">
            <div className="w-16 h-16 border-4 border-slate-200 rounded-full animate-spin mx-auto">
              <div className="w-4 h-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full absolute top-0 left-1/2 transform -translate-x-1/2"></div>
            </div>
          </div>
          <div className="space-y-2">
            <h3 className="text-xl font-semibold text-slate-900">Loading Dashboard</h3>
            <p className="text-slate-600 text-sm">Preparing your workout data...</p>
          </div>
        </div>
      </div>
    )
  }

  const getMotivationalMessage = () => {
    if (!progressData) return "Ready to start your fitness journey?"
    
    const { currentStreak, monthlyAttendance, monthlyGoal } = progressData
    
    if (currentStreak >= 7) return `🔥 ${currentStreak}-day streak! You're on fire!`
    if (currentStreak >= 3) return `⚡ ${currentStreak}-day streak! Keep it up!`
    if (monthlyAttendance >= monthlyGoal) return "🏆 Monthly goal achieved!"
    if (monthlyAttendance >= monthlyGoal * 0.8) return "🎯 Almost there! You've got this!"
    return "💪 Time to crush your goals!"
  }

  const progressPercentage = progressData ? (progressData.monthlyAttendance / progressData.monthlyGoal) * 100 : 0

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 relative overflow-hidden">
      {/* Modern Background Pattern */}
      <div className="fixed inset-0 opacity-[0.02] z-0">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgb(15 23 42) 1px, transparent 0)`,
          backgroundSize: '24px 24px'
        }}></div>
      </div>

      {/* Subtle Gradient Overlays */}
      <div className="fixed inset-0 pointer-events-none z-0">
        <div className="absolute top-0 left-0 w-full h-96 bg-gradient-to-b from-blue-50/30 to-transparent"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tl from-violet-50/30 to-transparent"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 py-6 sm:py-8">
        {/* Modern Header */}
        <div className="mb-8 sm:mb-12">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 sm:gap-6">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-violet-600 rounded-xl flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl sm:text-3xl font-bold text-slate-900">Welcome back</h1>
                  <p className="text-slate-600 text-sm sm:text-base">Ready for your next workout?</p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="text-right">
                <p className="text-sm text-slate-500">Today</p>
                <p className="text-lg font-semibold text-slate-900">
                  {new Date().toLocaleDateString('en-US', { 
                    weekday: 'short', 
                    month: 'short', 
                    day: 'numeric' 
                  })}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Modern Stats Cards */}
        {progressData && (
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mb-8">
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Current Streak</p>
                  <p className="text-3xl font-bold text-slate-900 mt-1">{progressData.currentStreak}</p>
                  <p className="text-xs text-slate-500 mt-1">days in a row</p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                  <Flame className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">This Month</p>
                  <p className="text-3xl font-bold text-slate-900 mt-1">{progressData.monthlyAttendance}<span className="text-lg text-slate-500">/{progressData.monthlyGoal}</span></p>
                  <p className="text-xs text-slate-500 mt-1">workouts completed</p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                  <Target className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Total Sessions</p>
                  <p className="text-3xl font-bold text-slate-900 mt-1">{progressData.totalWorkouts}</p>
                  <p className="text-xs text-slate-500 mt-1">all time</p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl flex items-center justify-center">
                  <Trophy className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Modern Check-in Section */}
        <div className="mb-8">
          <div className="bg-white rounded-2xl p-6 sm:p-8 shadow-sm border border-slate-200">
            <div className="text-center space-y-6">
              {isCheckedIn && checkInTime && (
                <div className="inline-flex items-center gap-3 bg-emerald-50 text-emerald-700 px-4 py-2 rounded-full border border-emerald-200">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                  <Clock className="w-4 h-4" />
                  <span className="text-sm font-medium">
                    Active since {new Date(checkInTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </span>
                </div>
              )}

              <div className="space-y-4">
                <CheckInButton
                  isCheckedIn={isCheckedIn}
                  onCheckIn={handleCheckIn}
                  onCheckOut={handleCheckOut}
                />

                {!isCheckedIn && (
                  <p className="text-slate-600 text-sm">
                    {getMotivationalMessage()}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Modern Progress Dashboard */}
        {progressData && (
          <div className="mb-8">
            <ProgressDashboard data={progressData} />
          </div>
        )}

        {/* Modern Main Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Attendance Calendar */}
          <div className="lg:col-span-2 order-2 lg:order-1">
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 h-full">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl flex items-center justify-center">
                  <Calendar className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-slate-900">Training Calendar</h2>
                  <p className="text-sm text-slate-600">Track your workout history</p>
                </div>
              </div>
              <AttendanceCalendar attendanceData={attendanceData} />
            </div>
          </div>

          {/* Quick Actions */}
          <div className="lg:col-span-1 order-1 lg:order-2">
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 h-full">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-r from-violet-500 to-purple-500 rounded-xl flex items-center justify-center">
                  <Activity className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-slate-900">Quick Actions</h2>
                  <p className="text-sm text-slate-600">Access your tools</p>
                </div>
              </div>
              {progressData && <QuickActions quickStats={progressData} />}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardPage
