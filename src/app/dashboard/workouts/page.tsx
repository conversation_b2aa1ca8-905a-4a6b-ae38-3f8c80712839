"use client"

// Prevent static generation for pages that use Supabase
export const dynamic = 'force-dynamic'

import { useState, useEffect } from "react";
import { supabase } from "@/lib/supabase/client";
import { Loader2, Calendar, Clock, BarChart2 } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { format, parseISO } from "date-fns";
import { cn } from "@/lib/utils";

interface CheckIn {
    id: string;
    user_id: string;
    check_in_time: string;
    check_out_time: string | null;
    duration_minutes: number | null;
    created_at: string;
}

export default function WorkoutsPage() {
    const [workouts, setWorkouts] = useState<CheckIn[]>([]);
    const [loading, setLoading] = useState(true);
    const [user, setUser] = useState<any>(null);

    useEffect(() => {
        const getUser = async () => {
            const { data: { user } } = await supabase.auth.getUser();
            setUser(user);
        };
        getUser();
    }, []);

    useEffect(() => {
        const fetchWorkouts = async () => {
            if (!user) return;

            setLoading(true);
            const { data, error } = await supabase
                .from('check_ins')
                .select('*')
                .eq('user_id', user.id)
                .not('check_out_time', 'is', null) // Only show completed workouts
                .order('check_in_time', { ascending: false });

            if (error) {
                console.error("Error fetching workouts:", error);
            } else {
                setWorkouts(data || []);
            }
            setLoading(false);
        };

        if (user) {
            fetchWorkouts();
        }
    }, [user]);

    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <Loader2 className="w-8 h-8 animate-spin text-member" />
            </div>
        );
    }

    return (
        <div className="w-full space-y-8">
            <div className="text-center">
                <h1 className="text-3xl font-bold text-member mb-2">
                    Your Workout History 🏋️‍♀️
                </h1>
                <p className="text-muted-foreground">
                    Review your past gym sessions and track your progress.
                </p>
            </div>

            {workouts.length === 0 ? (
                <Card className="p-6 text-center">
                    <CardContent className="text-muted-foreground">
                        No completed workouts yet. Check in to start tracking!
                    </CardContent>
                </Card>
            ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {workouts.map((workout) => (
                        <Card key={workout.id} className="relative overflow-hidden group">
                            <CardHeader className="flex flex-row items-center justify-between pb-2">
                                <CardTitle className="text-sm font-medium text-member flex items-center gap-2">
                                    <Calendar className="w-4 h-4" />
                                    {format(parseISO(workout.check_in_time), 'PPP')}
                                </CardTitle>
                                <span className="text-xs text-muted-foreground">
                                    {format(parseISO(workout.check_in_time), 'p')}
                                </span>
                            </CardHeader>
                            <CardContent>
                                <div className="text-xl font-bold text-foreground flex items-center gap-2">
                                    <Clock className="w-5 h-5 text-muted-foreground" />
                                    {workout.duration_minutes || 0} minutes
                                </div>
                                <p className="text-xs text-muted-foreground mt-1">
                                    {workout.check_out_time ? `Checked out at ${format(parseISO(workout.check_out_time), 'p')}` : 'Ongoing'}
                                </p>
                            </CardContent>
                            <div className="absolute inset-0 bg-gradient-to-br from-member/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                            <BarChart2 className="absolute bottom-4 right-4 w-10 h-10 text-member/20 group-hover:text-member/50 transition-colors duration-300" />
                        </Card>
                    ))}
                </div>
            )}
        </div>
    );
} 