import React from "react";
import { MemberLayout } from "@/components/layouts/member-layout";
import { Breadcrumbs } from "@/components/ui/breadcrumbs";

interface Props {
    children: React.ReactNode
}

const DashboardLayout = async ({ children }: Props) => {
    // TODO: Add Supabase authentication check in Phase 5
    // For now, we'll use the new member layout without auth checks

    const breadcrumbItems = [
        { label: "Home", href: "/" },
        { label: "Dashboard", href: "/dashboard" },
    ];

    return (
        <MemberLayout>
            <div className="w-full h-full px-4 py-6">

                {children}
            </div>
        </MemberLayout>
    );
};

export default DashboardLayout
