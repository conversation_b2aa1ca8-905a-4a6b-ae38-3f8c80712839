'use client'

// Prevent static generation for pages that use Supabase
export const dynamic = 'force-dynamic'

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Search, PlusCircle, Edit, Trash2, Loader2, Calendar, User, Tag } from "lucide-react"
import Link from "next/link"
import { supabase } from "@/lib/supabase/client"

interface BlogPost {
  id: string
  title: string
  slug: string
  content: string
  author: string
  published_at: string
  image_url?: string
  tags: string[]
  is_published: boolean
}

const AdminBlogPage = () => {
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [currentPost, setCurrentPost] = useState<Partial<BlogPost> | null>(null)
  const [formLoading, setFormLoading] = useState(false)

  useEffect(() => {
    fetchBlogPosts()
  }, [])

  const fetchBlogPosts = async () => {
    setLoading(true)
    setError(null)
    try {
      const { data, error } = await supabase
        .from('blogs')
        .select('*')
        .order('published_at', { ascending: false })

      if (error) throw error
      setBlogPosts(data || [])
    } catch (err: any) {
      console.error("Error fetching blog posts:", err)
      setError(err.message || "Failed to fetch blog posts")
    } finally {
      setLoading(false)
    }
  }

  const handleSavePost = async () => {
    if (!currentPost?.title || !currentPost?.content || !currentPost?.author || !currentPost?.slug) {
      alert("Please fill in all required fields: Title, Content, Author, and Slug.")
      return
    }

    setFormLoading(true)
    try {
      if (currentPost.id) {
        // Update existing post
        const { error } = await supabase
          .from('blogs')
          .update({
            title: currentPost.title,
            slug: currentPost.slug,
            content: currentPost.content,
            author: currentPost.author,
            image_url: currentPost.image_url,
            tags: currentPost.tags,
            is_published: currentPost.is_published,
          })
          .eq('id', currentPost.id)

        if (error) throw error
      } else {
        // Create new post
        const { error } = await supabase
          .from('blogs')
          .insert({
            title: currentPost.title,
            slug: currentPost.slug,
            content: currentPost.content,
            author: currentPost.author,
            image_url: currentPost.image_url,
            tags: currentPost.tags,
            is_published: currentPost.is_published,
          })

        if (error) throw error
      }
      await fetchBlogPosts()
      setIsDialogOpen(false)
      setCurrentPost(null)
    } catch (err: any) {
      console.error("Error saving blog post:", err)
      alert(`Failed to save blog post: ${err.message || "Unknown error"}`)
    } finally {
      setFormLoading(false)
    }
  }

  const handleDeletePost = async (id: string) => {
    if (!confirm("Are you sure you want to delete this blog post?")) return

    try {
      const { error } = await supabase
        .from('blogs')
        .delete()
        .eq('id', id)

      if (error) throw error
      await fetchBlogPosts()
    } catch (err: any) {
      console.error("Error deleting blog post:", err)
      alert(`Failed to delete blog post: ${err.message || "Unknown error"}`)
    }
  }

  const filteredPosts = blogPosts.filter(post =>
    post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    post.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
    post.author.toLowerCase().includes(searchQuery.toLowerCase()) ||
    post.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  const handleTagChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const tagsArray = e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag !== '')
    setCurrentPost(prev => ({ ...prev, tags: tagsArray }))
  }

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Blog Management ✍️</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setCurrentPost({ is_published: true, tags: [] })}>
              <PlusCircle className="w-4 h-4 mr-2" />
              Add New Post
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[800px]">
            <DialogHeader>
              <DialogTitle>{currentPost?.id ? "Edit Blog Post" : "Add New Blog Post"}</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <Input
                placeholder="Title"
                value={currentPost?.title || ""}
                onChange={(e) => setCurrentPost({ ...currentPost, title: e.target.value })}
                className="col-span-4"
                disabled={formLoading}
              />
              <Input
                placeholder="Slug (e.g., my-new-blog-post)"
                value={currentPost?.slug || ""}
                onChange={(e) => setCurrentPost({ ...currentPost, slug: e.target.value })}
                className="col-span-4"
                disabled={formLoading}
              />
              <Input
                placeholder="Author"
                value={currentPost?.author || ""}
                onChange={(e) => setCurrentPost({ ...currentPost, author: e.target.value })}
                className="col-span-4"
                disabled={formLoading}
              />
              <Input
                placeholder="Image URL (optional)"
                value={currentPost?.image_url || ""}
                onChange={(e) => setCurrentPost({ ...currentPost, image_url: e.target.value })}
                className="col-span-4"
                disabled={formLoading}
              />
              <Input
                placeholder="Tags (comma-separated, e.g., fitness, health)"
                value={currentPost?.tags?.join(',') || ""}
                onChange={handleTagChange}
                className="col-span-4"
                disabled={formLoading}
              />
              <Textarea
                placeholder="Content"
                value={currentPost?.content || ""}
                onChange={(e) => setCurrentPost({ ...currentPost, content: e.target.value })}
                className="col-span-4 min-h-[200px]"
                disabled={formLoading}
              />
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="is_published"
                  checked={currentPost?.is_published || false}
                  onCheckedChange={(checked) => setCurrentPost({ ...currentPost, is_published: Boolean(checked) })}
                  disabled={formLoading}
                />
                <label
                  htmlFor="is_published"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Published
                </label>
              </div>
            </div>
            <DialogFooter>
              <Button onClick={handleSavePost} disabled={formLoading}>
                {formLoading ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <PlusCircle className="w-4 h-4 mr-2" />
                )}
                {currentPost?.id ? "Save Changes" : "Add Post"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="w-5 h-5" />
            Search Blog Posts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Input
            placeholder="Search by title, content, or author..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="mb-4"
          />
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="w-10 h-10 animate-spin text-pink-500" />
            </div>
          ) : error ? (
            <div className="text-center text-red-500">{error}</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Author</TableHead>
                  <TableHead>Published At</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPosts.map((post) => (
                  <TableRow key={post.id}>
                    <TableCell className="font-medium">
                      <Link href={`/blog/${post.slug}`} target="_blank" rel="noopener noreferrer" className="hover:underline">
                        {post.title}
                      </Link>
                    </TableCell>
                    <TableCell>{post.author}</TableCell>
                    <TableCell>{new Date(post.published_at).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <Badge variant={post.is_published ? "default" : "secondary"}>
                        {post.is_published ? "Published" : "Draft"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mr-2"
                        onClick={() => {
                          setCurrentPost({ ...post, published_at: new Date(post.published_at).toISOString().split('T')[0] });
                          setIsDialogOpen(true);
                        }}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeletePost(post.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
                {filteredPosts.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center text-muted-foreground py-8">
                      No blog posts found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default AdminBlogPage