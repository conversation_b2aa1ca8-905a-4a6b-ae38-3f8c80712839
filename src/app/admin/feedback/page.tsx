'use client'

// Prevent static generation for pages that use Supabase
export const dynamic = 'force-dynamic'

import { useState, useEffect } from "react"
import { FeedbackManagement } from "@/components/admin/feedback-management"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { MessageSquare, Clock, CheckCircle, Star } from "lucide-react"
import { supabase } from "@/lib/supabase/client"

interface FeedbackItem {
  id: string
  status: string
  category: string
  rating?: number | null
}

interface FeedbackStats {
  totalFeedback: number
  pending: number
  resolved: number
  avgRating: number
}

interface FeedbackCategoryBreakdown {
  workoutExperience: { count: number; avgRating: number }
  facilityEquipment: { count: number; avgRating: number }
  trainerService: { count: number; avgRating: number }
  generalFeedback: { count: number; avgRating: number }
}

const AdminFeedbackPage = () => {
  const [feedbackStats, setFeedbackStats] = useState<FeedbackStats>({
    totalFeedback: 0,
    pending: 0,
    resolved: 0,
    avgRating: 0,
  })
  const [categoryBreakdown, setCategoryBreakdown] = useState<FeedbackCategoryBreakdown>({
    workoutExperience: { count: 0, avgRating: 0 },
    facilityEquipment: { count: 0, avgRating: 0 },
    trainerService: { count: 0, avgRating: 0 },
    generalFeedback: { count: 0, avgRating: 0 },
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchFeedbackData = async () => {
      setLoading(true)
      try {
        // Fetch all feedback to calculate stats
        const { data: allFeedback, error: allFeedbackError } = await supabase
          .from('feedback')
          .select('id, status, category, rating')

        if (allFeedbackError) throw allFeedbackError

        const totalFeedback = allFeedback?.length || 0
        const pending = allFeedback?.filter(f => f.status === 'pending').length || 0
        const resolved = allFeedback?.filter(f => f.status === 'resolved').length || 0
        const totalRating = allFeedback?.reduce((sum, f) => sum + (f.rating || 0), 0) || 0
        const avgRating = totalFeedback > 0 ? parseFloat((totalRating / totalFeedback).toFixed(1)) : 0

        setFeedbackStats({
          totalFeedback,
          pending,
          resolved,
          avgRating,
        })

        // Category Breakdown
        const workoutExperience = allFeedback?.filter(f => f.category === 'Workout Experience') || []
        const facilityEquipment = allFeedback?.filter(f => f.category === 'Facility & Equipment') || []
        const trainerService = allFeedback?.filter(f => f.category === 'Trainer Service') || []
        const generalFeedback = allFeedback?.filter(f => f.category === 'General Feedback') || []

        const calculateCategoryStats = (categoryFeedback: FeedbackItem[]) => {
          const count = categoryFeedback.length
          const totalCatRating = categoryFeedback.reduce((sum, f) => sum + (f.rating || 0), 0)
          const avgRating = count > 0 ? parseFloat((totalCatRating / count).toFixed(1)) : 0
          return { count, avgRating }
        }

        setCategoryBreakdown({
          workoutExperience: calculateCategoryStats(workoutExperience),
          facilityEquipment: calculateCategoryStats(facilityEquipment),
          trainerService: calculateCategoryStats(trainerService),
          generalFeedback: calculateCategoryStats(generalFeedback),
        })

      } catch (error) {
        console.error("Error fetching feedback data:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchFeedbackData()
  }, [])

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-communication mb-2">
          Feedback Management 💬
        </h1>
        <p className="text-muted-foreground">
          Review member feedback and respond to improve gym experience
        </p>
      </div>

      {/* Feedback Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <MessageSquare className="w-4 h-4 text-communication" />
              Total Feedback
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="w-6 h-6 rounded-full bg-gray-700 animate-pulse" />
            ) : (
              <div className="text-2xl font-bold text-communication">{feedbackStats.totalFeedback}</div>
            )}
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Clock className="w-4 h-4 text-marketing" />
              Pending
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="w-6 h-6 rounded-full bg-gray-700 animate-pulse" />
            ) : (
              <div className="text-2xl font-bold text-marketing">{feedbackStats.pending}</div>
            )}
            <p className="text-xs text-muted-foreground">Awaiting response</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-blog" />
              Resolved
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="w-6 h-6 rounded-full bg-gray-700 animate-pulse" />
            ) : (
              <div className="text-2xl font-bold text-blog">{feedbackStats.resolved}</div>
            )}
            <p className="text-xs text-muted-foreground">92% response rate</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Star className="w-4 h-4 text-member" />
              Avg Rating
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="w-6 h-6 rounded-full bg-gray-700 animate-pulse" />
            ) : (
              <div className="text-2xl font-bold text-member">{feedbackStats.avgRating}</div>
            )}
            <p className="text-xs text-muted-foreground">Out of 5 stars</p>
          </CardContent>
        </Card>
      </div>

      {/* Category Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Feedback by Category</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-20">
              <div className="w-6 h-6 border-2 border-pink-500 border-t-transparent rounded-full animate-spin" />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-member mb-1">{categoryBreakdown.workoutExperience.count}</div>
                <div className="text-sm text-muted-foreground">Workout Experience</div>
                <div className="text-xs text-member">{categoryBreakdown.workoutExperience.avgRating} avg rating</div>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-admin mb-1">{categoryBreakdown.facilityEquipment.count}</div>
                <div className="text-sm text-muted-foreground">Facility & Equipment</div>
                <div className="text-xs text-admin">{categoryBreakdown.facilityEquipment.avgRating} avg rating</div>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-blog mb-1">{categoryBreakdown.trainerService.count}</div>
                <div className="text-sm text-muted-foreground">Trainer Service</div>
                <div className="text-xs text-blog">{categoryBreakdown.trainerService.avgRating} avg rating</div>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-communication mb-1">{categoryBreakdown.generalFeedback.count}</div>
                <div className="text-sm text-muted-foreground">General Feedback</div>
                <div className="text-xs text-communication">{categoryBreakdown.generalFeedback.avgRating} avg rating</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Feedback Management Component */}
      <FeedbackManagement />
    </div>
  )
}

export default AdminFeedbackPage
