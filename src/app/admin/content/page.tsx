'use client'

// Prevent static generation for pages that use Supabase
export const dynamic = 'force-dynamic'

import { useState, useEffect } from "react"
import { ContentManagement } from "@/components/admin/content-management"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { FileText, Eye, Edit, TrendingUp } from "lucide-react"
import { supabase } from "@/lib/supabase/client"

interface ContentStats {
  totalContent: number
  totalViews: number // Placeholder for now, requires view tracking logic
  drafts: number
  engagement: number // Placeholder for now, requires more complex logic
}

interface ContentTypeBreakdown {
  blogPosts: { count: number; views: number }
  helpfulMaterials: { count: number; views: number }
  announcements: { count: number; views: number }
}

const AdminContentPage = () => {
  const [contentStats, setContentStats] = useState<ContentStats>({
    totalContent: 0,
    totalViews: 0,
    drafts: 0,
    engagement: 0,
  })
  const [contentTypeBreakdown, setContentTypeBreakdown] = useState<ContentTypeBreakdown>({
    blogPosts: { count: 0, views: 0 },
    helpfulMaterials: { count: 0, views: 0 },
    announcements: { count: 0, views: 0 },
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchContentData = async () => {
      try {
        const [totalContentResponse, draftsResponse, blogPostsResponse, helpfulMaterialsResponse] = await Promise.all([
          supabase.from('blogs').select('id', { count: 'exact' }),
          supabase.from('blogs').select('id', { count: 'exact' }).eq('published', false),
          supabase.from('blogs').select('id, view_count'),
          supabase.from('helpful_materials').select('id, download_count'),
        ]);

        console.log("totalContentResponse:", totalContentResponse);
        console.log("draftsResponse:", draftsResponse);
        console.log("blogPostsResponse:", blogPostsResponse);
        console.log("helpfulMaterialsResponse:", helpfulMaterialsResponse);

        const totalBlogs = totalContentResponse.count || 0;
        const drafts = draftsResponse.count || 0;

        const blogPostsCount = blogPostsResponse.data?.length || 0;
        const blogPostsViews = blogPostsResponse.data?.reduce((sum, post) => sum + (post.view_count || 0), 0) || 0;

        const helpfulMaterialsCount = helpfulMaterialsResponse.data?.length || 0;
        const helpfulMaterialsViews = helpfulMaterialsResponse.data?.reduce((sum, material) => sum + (material.download_count || 0), 0) || 0; // Using download_count as views for helpful materials

        console.log("totalBlogs:", totalBlogs);
        console.log("drafts:", drafts);
        console.log("blogPostsCount:", blogPostsCount);
        console.log("blogPostsViews:", blogPostsViews);
        console.log("helpfulMaterialsCount:", helpfulMaterialsCount);
        console.log("helpfulMaterialsViews:", helpfulMaterialsViews);

        const totalContent = totalBlogs + helpfulMaterialsCount;
        const totalViews = blogPostsViews + helpfulMaterialsViews;

        setContentStats({
          totalContent,
          totalViews,
          drafts,
          engagement: 85, // Placeholder - consider a more dynamic calculation later
        });

        setContentTypeBreakdown({
          blogPosts: { count: blogPostsCount, views: blogPostsViews },
          helpfulMaterials: { count: helpfulMaterialsCount, views: helpfulMaterialsViews },
          announcements: { count: 0, views: 0 }, // Assuming announcements are not yet implemented or have a separate table
        });
      } catch (error) {
        console.error("Error fetching content data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchContentData();
  }, []);

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-blog mb-2">
          Content Management 📝
        </h1>
        <p className="text-muted-foreground">
          Create and manage blog posts, helpful materials, and announcements
        </p>
      </div>

      {/* Content Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <FileText className="w-4 h-4 text-blog" />
              Total Content
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="w-6 h-6 rounded-full bg-gray-700 animate-pulse" />
            ) : (
              <div className="text-2xl font-bold text-blog">{contentStats.totalContent}</div>
            )}
            <p className="text-xs text-muted-foreground">Published items</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Eye className="w-4 h-4 text-member" />
              Total Views
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="w-6 h-6 rounded-full bg-gray-700 animate-pulse" />
            ) : (
              <div className="text-2xl font-bold text-member">{contentStats.totalViews.toLocaleString()}</div>
            )}
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Edit className="w-4 h-4 text-marketing" />
              Drafts
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="w-6 h-6 rounded-full bg-gray-700 animate-pulse" />
            ) : (
              <div className="text-2xl font-bold text-marketing">{contentStats.drafts}</div>
            )}
            <p className="text-xs text-muted-foreground">Pending review</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <TrendingUp className="w-4 h-4 text-communication" />
              Engagement
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="w-6 h-6 rounded-full bg-gray-700 animate-pulse" />
            ) : (
              <div className="text-2xl font-bold text-communication">{contentStats.engagement}%</div>
            )}
            <p className="text-xs text-muted-foreground">Read rate</p>
          </CardContent>
        </Card>
      </div>

      {/* Content Type Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Content by Type</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-20">
              <div className="w-6 h-6 border-2 border-pink-500 border-t-transparent rounded-full animate-spin" />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-blog mb-1">{contentTypeBreakdown.blogPosts.count}</div>
                <div className="text-sm text-muted-foreground">Blog Posts</div>
                <div className="text-xs text-blog">{contentTypeBreakdown.blogPosts.views.toLocaleString()} views</div>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-marketing mb-1">{contentTypeBreakdown.helpfulMaterials.count}</div>
                <div className="text-sm text-muted-foreground">Helpful Materials</div>
                <div className="text-xs text-marketing">{contentTypeBreakdown.helpfulMaterials.views.toLocaleString()} views</div>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-communication mb-1">{contentTypeBreakdown.announcements.count}</div>
                <div className="text-sm text-muted-foreground">Announcements</div>
                <div className="text-xs text-communication">{contentTypeBreakdown.announcements.views.toLocaleString()} views</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Content Management Component */}
      <ContentManagement />
    </div>
  )
}

export default AdminContentPage
