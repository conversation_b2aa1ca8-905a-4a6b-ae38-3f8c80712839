"use client"

import { TwitterDashboard } from "@/components/admin/twitter-dashboard"

// Prevent static generation for pages that use Supabase
export const dynamic = 'force-dynamic'

export default function AdminDashboard() {
  return (
    <div className="space-y-4 lg:space-y-6 w-full">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl lg:text-3xl font-bold text-foreground">Admin Dashboard</h1>
          <p className="text-muted-foreground">Welcome to the Pulse.co.za admin portal</p>
        </div>
      </div>

      <TwitterDashboard />
    </div>
  )
}


