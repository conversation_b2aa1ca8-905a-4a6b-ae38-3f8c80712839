import React from "react";
import { AdminLayout } from "@/components/layouts/admin-layout";

interface Props {
    children: React.ReactNode
}

const AdminDashboardLayout = async ({ children }: Props) => {
    // TODO: Add admin authentication check in Phase 5
    // For now, we'll use the new admin layout without auth checks
    
    return (
        <AdminLayout>
            {children}
        </AdminLayout>
    );
};

export default AdminDashboardLayout;
