'use client'

// Prevent static generation for pages that use Supabase
export const dynamic = 'force-dynamic'

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Search, PlusCircle, Edit, Trash2, Loader2, BookOpen, Video, LinkIcon, Download } from "lucide-react"
import Link from "next/link"
import { supabase } from "@/lib/supabase/client"

interface Resource {
  id: string
  title: string
  slug: string
  description: string
  file_url: string
  resource_type: string
  uploaded_at: string
  tags: string[]
  is_active: boolean
}

const AdminResourcesPage = () => {
  const [resources, setResources] = useState<Resource[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [currentResource, setCurrentResource] = useState<Partial<Resource> | null>(null)
  const [formLoading, setFormLoading] = useState(false)

  useEffect(() => {
    fetchResources()
  }, [])

  const fetchResources = async () => {
    setLoading(true)
    setError(null)
    try {
      const { data, error } = await supabase
        .from('resources')
        .select('*')
        .order('uploaded_at', { ascending: false })

      if (error) throw error
      setResources(data || [])
    } catch (err: any) {
      console.error("Error fetching resources:", err)
      setError(err.message || "Failed to fetch resources")
    } finally {
      setLoading(false)
    }
  }

  const handleSaveResource = async () => {
    if (!currentResource?.title || !currentResource?.file_url || !currentResource?.resource_type || !currentResource?.slug) {
      alert("Please fill in all required fields: Title, File URL, Resource Type, and Slug.")
      return
    }

    setFormLoading(true)
    try {
      if (currentResource.id) {
        // Update existing resource
        const { error } = await supabase
          .from('resources')
          .update({
            title: currentResource.title,
            slug: currentResource.slug,
            description: currentResource.description,
            file_url: currentResource.file_url,
            resource_type: currentResource.resource_type,
            tags: currentResource.tags,
            is_active: currentResource.is_active,
          })
          .eq('id', currentResource.id)

        if (error) throw error
      } else {
        // Create new resource
        const { error } = await supabase
          .from('resources')
          .insert({
            title: currentResource.title,
            slug: currentResource.slug,
            description: currentResource.description,
            file_url: currentResource.file_url,
            resource_type: currentResource.resource_type,
            tags: currentResource.tags,
            is_active: currentResource.is_active,
          })

        if (error) throw error
      }
      await fetchResources()
      setIsDialogOpen(false)
      setCurrentResource(null)
    } catch (err: any) {
      console.error("Error saving resource:", err)
      alert(`Failed to save resource: ${err.message || "Unknown error"}`)
    } finally {
      setFormLoading(false)
    }
  }

  const handleDeleteResource = async (id: string) => {
    if (!confirm("Are you sure you want to delete this resource?")) return

    try {
      const { error } = await supabase
        .from('resources')
        .delete()
        .eq('id', id)

      if (error) throw error
      await fetchResources()
    } catch (err: any) {
      console.error("Error deleting resource:", err)
      alert(`Failed to delete resource: ${err.message || "Unknown error"}`)
    }
  }

  const filteredResources = resources.filter(resource =>
    resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    resource.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    resource.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())) ||
    resource.resource_type.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleTagChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const tagsArray = e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag !== '')
    setCurrentResource(prev => ({ ...prev, tags: tagsArray }))
  }

  const resourceTypeOptions = ["pdf", "video", "link", "other"]

  const getIconForResourceType = (type: string) => {
    switch (type) {
      case "pdf": return <BookOpen className="w-4 h-4 mr-2" />
      case "video": return <Video className="w-4 h-4 mr-2" />
      case "link": return <LinkIcon className="w-4 h-4 mr-2" />
      default: return <BookOpen className="w-4 h-4 mr-2" />
    }
  }

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Resource Management 📚</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setCurrentResource({ is_active: true, tags: [], resource_type: "pdf" })}>
              <PlusCircle className="w-4 h-4 mr-2" />
              Add New Resource
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[800px]">
            <DialogHeader>
              <DialogTitle>{currentResource?.id ? "Edit Resource" : "Add New Resource"}</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <Input
                placeholder="Title"
                value={currentResource?.title || ""}
                onChange={(e) => setCurrentResource({ ...currentResource, title: e.target.value })}
                className="col-span-4"
                disabled={formLoading}
              />
              <Input
                placeholder="Slug (e.g., my-new-resource)"
                value={currentResource?.slug || ""}
                onChange={(e) => setCurrentResource({ ...currentResource, slug: e.target.value })}
                className="col-span-4"
                disabled={formLoading}
              />
              <Textarea
                placeholder="Description"
                value={currentResource?.description || ""}
                onChange={(e) => setCurrentResource({ ...currentResource, description: e.target.value })}
                className="col-span-4 min-h-[100px]"
                disabled={formLoading}
              />
              <Input
                placeholder="File URL"
                value={currentResource?.file_url || ""}
                onChange={(e) => setCurrentResource({ ...currentResource, file_url: e.target.value })}
                className="col-span-4"
                disabled={formLoading}
              />
              <div className="col-span-4">
                <label className="text-sm font-medium leading-none mb-2 block">Resource Type</label>
                <select
                  value={currentResource?.resource_type || ""}
                  onChange={(e) => setCurrentResource({ ...currentResource, resource_type: e.target.value })}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  disabled={formLoading}
                >
                  {resourceTypeOptions.map(type => (
                    <option key={type} value={type}>{type.charAt(0).toUpperCase() + type.slice(1)}</option>
                  ))}
                </select>
              </div>
              <Input
                placeholder="Tags (comma-separated, e.g., fitness, yoga)"
                value={currentResource?.tags?.join(',') || ""}
                onChange={handleTagChange}
                className="col-span-4"
                disabled={formLoading}
              />
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="is_active"
                  checked={currentResource?.is_active || false}
                  onCheckedChange={(checked) => setCurrentResource({ ...currentResource, is_active: Boolean(checked) })}
                  disabled={formLoading}
                />
                <label
                  htmlFor="is_active"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Active
                </label>
              </div>
            </div>
            <DialogFooter>
              <Button onClick={handleSaveResource} disabled={formLoading}>
                {formLoading ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <PlusCircle className="w-4 h-4 mr-2" />
                )}
                {currentResource?.id ? "Save Changes" : "Add Resource"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="w-5 h-5" />
            Search Resources
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Input
            placeholder="Search by title, description, or tags..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="mb-4"
          />
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="w-10 h-10 animate-spin text-green-500" />
            </div>
          ) : error ? (
            <div className="text-center text-red-500">{error}</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Uploaded At</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredResources.map((resource) => (
                  <TableRow key={resource.id}>
                    <TableCell className="font-medium">
                      <Link href={resource.file_url} target="_blank" rel="noopener noreferrer" className="hover:underline flex items-center">
                        {getIconForResourceType(resource.resource_type)}
                        {resource.title}
                      </Link>
                    </TableCell>
                    <TableCell>{resource.resource_type.toUpperCase()}</TableCell>
                    <TableCell>{new Date(resource.uploaded_at).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <Badge variant={resource.is_active ? "default" : "secondary"}>
                        {resource.is_active ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mr-2"
                        onClick={() => {
                          setCurrentResource({ ...resource });
                          setIsDialogOpen(true);
                        }}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeleteResource(resource.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
                {filteredResources.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center text-muted-foreground py-8">
                      No resources found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default AdminResourcesPage 