'use client'

// Prevent static generation for pages that use Supabase
export const dynamic = 'force-dynamic'

import { useState, useEffect } from "react"
import { MemberManagement } from "@/components/admin/member-management"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Users, UserPlus, Download, Filter } from "lucide-react"
import { supabase } from "@/lib/supabase/client"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Loader2 } from "lucide-react"

interface MemberStats {
  totalMembers: number
  activeToday: number
  newThisWeek: number
  retentionRate: number
}

const AdminMembersPage = () => {
  const [stats, setStats] = useState<MemberStats>({
    totalMembers: 0,
    activeToday: 0,
    newThisWeek: 0,
    retentionRate: 0,
  })
  const [loading, setLoading] = useState(true)
  const [isAddMemberDialogOpen, setIsAddMemberDialogOpen] = useState(false)
  const [newMember, setNewMember] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    avatarUrl: '',
    password: '',
    membershipType: 'basic',
    membershipStatus: 'active',
  })
  const [formLoading, setFormLoading] = useState(false)

  useEffect(() => {
    fetchMemberStats()
  }, [])

  const fetchMemberStats = async () => {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay()); // Assuming Sunday is the start of the week

      const [totalMembersResponse, activeTodayResponse, newThisWeekResponse] = await Promise.all([
        supabase.from('profiles').select('id', { count: 'exact' }),
        supabase.from('check_ins').select('id', { count: 'exact' }).gte('check_in_time', today.toISOString()),
        supabase.from('profiles').select('id', { count: 'exact' }).gte('created_at', startOfWeek.toISOString()),
      ]);

      const totalMembers = totalMembersResponse.count || 0;
      const activeToday = activeTodayResponse.count || 0;
      const newThisWeek = newThisWeekResponse.count || 0;

      // Retention rate: (Active Today / Total Members) * 100 or a more robust calculation
      const retentionRate = totalMembers > 0 ? (activeToday / totalMembers) * 100 : 0; 

      setStats({
        totalMembers,
        activeToday,
        newThisWeek,
        retentionRate,
      });
    } catch (error) {
      console.error("Error fetching member stats:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateMember = async () => {
    setFormLoading(true);
    try {
      console.log("Attempting to create new member with:", {
        email: newMember.email,
        password: newMember.password,
        firstName: newMember.firstName,
        lastName: newMember.lastName,
        phone: newMember.phone,
        dateOfBirth: newMember.dateOfBirth,
        avatarUrl: newMember.avatarUrl,
        membershipType: newMember.membershipType,
        membershipStatus: newMember.membershipStatus,
      });

      const response = await fetch('/api/admin/invite-member', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: newMember.email,
          password: newMember.password,
          firstName: newMember.firstName,
          lastName: newMember.lastName,
          phone: newMember.phone,
          dateOfBirth: newMember.dateOfBirth,
          avatarUrl: newMember.avatarUrl,
          membershipType: newMember.membershipType,
          membershipStatus: newMember.membershipStatus,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to create member");
      }

      alert("Member added successfully! An invitation email has been sent to the member.");
      setIsAddMemberDialogOpen(false);
      setNewMember({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        dateOfBirth: '',
        avatarUrl: '',
        password: '',
        membershipType: 'basic',
        membershipStatus: 'active',
      });
      fetchMemberStats(); // Refresh stats after adding member
    } catch (error: any) {
      console.error("Error adding member:", error);
      alert(`Failed to add member: ${error.message || "Unknown error"}`);
    } finally {
      setFormLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-admin mb-2">
            Member Management 👥
          </h1>
          <p className="text-muted-foreground">
            Manage gym members, track attendance, and handle memberships
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export Data
          </Button>
          <Button onClick={() => setIsAddMemberDialogOpen(true)}>
            <UserPlus className="w-4 h-4 mr-2" />
            Add Member
          </Button>
        </div>
      </div>

      {/* Add New Member Dialog */}
      <Dialog open={isAddMemberDialogOpen} onOpenChange={setIsAddMemberDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Member</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <Input
              placeholder="First Name"
              value={newMember.firstName}
              onChange={(e) => setNewMember({ ...newMember, firstName: e.target.value })}
              disabled={formLoading}
            />
            <Input
              placeholder="Last Name"
              value={newMember.lastName}
              onChange={(e) => setNewMember({ ...newMember, lastName: e.target.value })}
              disabled={formLoading}
            />
            <Input
              placeholder="Email"
              type="email"
              value={newMember.email}
              onChange={(e) => setNewMember({ ...newMember, email: e.target.value })}
              disabled={formLoading}
            />
            <Input
              placeholder="Phone (optional)"
              value={newMember.phone}
              onChange={(e) => setNewMember({ ...newMember, phone: e.target.value })}
              disabled={formLoading}
            />
            <Input
              placeholder="Date of Birth (YYYY-MM-DD) (optional)"
              type="date"
              value={newMember.dateOfBirth}
              onChange={(e) => setNewMember({ ...newMember, dateOfBirth: e.target.value })}
              disabled={formLoading}
            />
            <Input
              placeholder="Avatar URL (optional)"
              value={newMember.avatarUrl}
              onChange={(e) => setNewMember({ ...newMember, avatarUrl: e.target.value })}
              disabled={formLoading}
            />
            <Input
              placeholder="Password"
              type="password"
              value={newMember.password}
              onChange={(e) => setNewMember({ ...newMember, password: e.target.value })}
              disabled={formLoading}
            />
            <Select
              value={newMember.membershipType}
              onValueChange={(value) => setNewMember({ ...newMember, membershipType: value as "basic" | "premium" | "vip" })}
              disabled={formLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Membership Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="basic">Basic</SelectItem>
                <SelectItem value="premium">Premium</SelectItem>
                <SelectItem value="vip">VIP</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={newMember.membershipStatus}
              onValueChange={(value) => setNewMember({ ...newMember, membershipStatus: value as "active" | "inactive" | "suspended" })}
              disabled={formLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Membership Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="suspended">Suspended</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <DialogFooter>
            <Button onClick={handleCreateMember} disabled={formLoading}>
              {formLoading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <UserPlus className="w-4 h-4 mr-2" />
              )}
              Add Member
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Users className="w-4 h-4 text-admin" />
              Total Members
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="w-6 h-6 rounded-full bg-gray-700 animate-pulse" />
            ) : (
              <div className="text-2xl font-bold text-admin">{stats.totalMembers}</div>
            )}
            <p className="text-xs text-muted-foreground">Total members registered</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Today</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="w-6 h-6 rounded-full bg-gray-700 animate-pulse" />
            ) : (
              <div className="text-2xl font-bold text-blog">{stats.activeToday}</div>
            )}
            <p className="text-xs text-muted-foreground">Members logged in today</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">New This Week</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="w-6 h-6 rounded-full bg-gray-700 animate-pulse" />
            ) : (
              <div className="text-2xl font-bold text-member">{stats.newThisWeek}</div>
            )}
            <p className="text-xs text-muted-foreground">New sign-ups this week</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Retention Rate</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="w-6 h-6 rounded-full bg-gray-700 animate-pulse" />
            ) : (
              <div className="text-2xl font-bold text-communication">{stats.retentionRate.toFixed(1)}%</div>
            )}
            <p className="text-xs text-muted-foreground">Based on today's active members</p>
          </CardContent>
        </Card>
      </div>

      {/* Member Management Table */}
      <MemberManagement onAddMemberClick={() => setIsAddMemberDialogOpen(true)} />
    </div>
  )
}

export default AdminMembersPage
