"use client"

// Prevent static generation for pages that use Supabase
export const dynamic = 'force-dynamic'

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { supabase } from "@/lib/supabase/client"
import { 
  Mail, 
  MessageSquare, 
  Calendar,
  User,
  Eye,
  CheckCircle,
  Clock,
  Reply,
  Loader2
} from "lucide-react"

interface ContactSubmission {
  id: string
  first_name: string
  last_name: string
  email: string
  subject: string
  message: string
  status: 'unread' | 'read' | 'responded'
  admin_notes: string | null
  responded_by: string | null
  responded_at: string | null
  created_at: string
  updated_at: string
}

const AdminContactPage = () => {
  const [submissions, setSubmissions] = useState<ContactSubmission[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedSubmission, setSelectedSubmission] = useState<ContactSubmission | null>(null)
  const [adminNotes, setAdminNotes] = useState("")
  const [updating, setUpdating] = useState(false)

  useEffect(() => {
    fetchSubmissions()
  }, [])

  const fetchSubmissions = async () => {
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('contact_submissions')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      setSubmissions(data || [])
    } catch (error) {
      console.error('Error fetching contact submissions:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateSubmissionStatus = async (id: string, status: 'read' | 'responded', notes?: string) => {
    setUpdating(true)
    try {
      const { data: { user } } = await supabase.auth.getUser()
      
      const updateData: any = { status }
      if (notes) updateData.admin_notes = notes
      if (status === 'responded') {
        updateData.responded_by = user?.id
        updateData.responded_at = new Date().toISOString()
      }

      const { error } = await supabase
        .from('contact_submissions')
        .update(updateData)
        .eq('id', id)

      if (error) throw error
      await fetchSubmissions()
      setSelectedSubmission(null)
      setAdminNotes("")
    } catch (error) {
      console.error('Error updating submission:', error)
    } finally {
      setUpdating(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'unread': return 'bg-red-500/20 text-red-400'
      case 'read': return 'bg-yellow-500/20 text-yellow-400'
      case 'responded': return 'bg-green-500/20 text-green-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'unread': return <Mail className="w-4 h-4" />
      case 'read': return <Eye className="w-4 h-4" />
      case 'responded': return <CheckCircle className="w-4 h-4" />
      default: return <Clock className="w-4 h-4" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 animate-spin text-pink-400" />
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-white mb-2">
          Contact Submissions 📧
        </h1>
        <p className="text-gray-400">
          Manage and respond to contact form submissions
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-gray-900/50 border-gray-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2 text-gray-400">
              <MessageSquare className="w-4 h-4 text-pink-400" />
              Total Submissions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{submissions.length}</div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900/50 border-gray-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2 text-gray-400">
              <Mail className="w-4 h-4 text-red-400" />
              Unread
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {submissions.filter(s => s.status === 'unread').length}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900/50 border-gray-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2 text-gray-400">
              <Eye className="w-4 h-4 text-yellow-400" />
              Read
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {submissions.filter(s => s.status === 'read').length}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900/50 border-gray-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2 text-gray-400">
              <CheckCircle className="w-4 h-4 text-green-400" />
              Responded
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {submissions.filter(s => s.status === 'responded').length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Submissions List */}
      <Card className="bg-gray-900/50 border-gray-800">
        <CardHeader>
          <CardTitle className="text-white">Contact Submissions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {submissions.map((submission) => (
              <div key={submission.id} className="p-4 border border-gray-800 rounded-lg hover:border-pink-500/50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-medium text-white">
                        {submission.first_name} {submission.last_name}
                      </h3>
                      <Badge className={getStatusColor(submission.status)}>
                        <span className="flex items-center gap-1">
                          {getStatusIcon(submission.status)}
                          {submission.status}
                        </span>
                      </Badge>
                    </div>
                    
                    <div className="text-sm text-gray-400 mb-2">
                      <div className="flex items-center gap-4">
                        <span className="flex items-center gap-1">
                          <Mail className="w-3 h-3" />
                          {submission.email}
                        </span>
                        <span className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          {new Date(submission.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    
                    <div className="mb-2">
                      <p className="font-medium text-white text-sm">Subject: {submission.subject}</p>
                    </div>
                    
                    <p className="text-gray-400 text-sm line-clamp-2">
                      {submission.message}
                    </p>
                    
                    {submission.admin_notes && (
                      <div className="mt-2 p-2 bg-pink-500/10 border border-pink-500/20 rounded text-sm">
                        <p className="text-pink-400 font-medium">Admin Notes:</p>
                        <p className="text-gray-300">{submission.admin_notes}</p>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedSubmission(submission)
                        setAdminNotes(submission.admin_notes || "")
                        if (submission.status === 'unread') {
                          updateSubmissionStatus(submission.id, 'read')
                        }
                      }}
                      className="text-pink-400 hover:text-pink-300"
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                    {submission.status !== 'responded' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedSubmission(submission)
                          setAdminNotes(submission.admin_notes || "")
                        }}
                        className="text-green-400 hover:text-green-300"
                      >
                        <Reply className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
            
            {submissions.length === 0 && (
              <div className="text-center py-8 text-gray-400">
                <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No contact submissions yet.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Submission Detail Modal */}
      {selectedSubmission && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-full max-w-2xl mx-4 bg-gray-900 border-gray-800 max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <CardTitle className="text-white">
                Contact from {selectedSubmission.first_name} {selectedSubmission.last_name}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Email:</span>
                  <p className="text-white">{selectedSubmission.email}</p>
                </div>
                <div>
                  <span className="text-gray-400">Date:</span>
                  <p className="text-white">{new Date(selectedSubmission.created_at).toLocaleString()}</p>
                </div>
              </div>
              
              <div>
                <span className="text-gray-400">Subject:</span>
                <p className="text-white font-medium">{selectedSubmission.subject}</p>
              </div>
              
              <div>
                <span className="text-gray-400">Message:</span>
                <div className="mt-2 p-3 bg-gray-800 rounded border border-gray-700">
                  <p className="text-white whitespace-pre-wrap">{selectedSubmission.message}</p>
                </div>
              </div>
              
              <div>
                <label className="block text-gray-400 mb-2">Admin Notes:</label>
                <Textarea
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                  placeholder="Add notes about this submission..."
                  className="bg-gray-800 border-gray-700 text-white"
                  rows={3}
                />
              </div>
              
              <div className="flex gap-4 pt-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSelectedSubmission(null)
                    setAdminNotes("")
                  }}
                  className="flex-1 border-gray-700 text-gray-300"
                >
                  Close
                </Button>
                <Button
                  onClick={() => updateSubmissionStatus(selectedSubmission.id, 'read', adminNotes)}
                  disabled={updating}
                  className="flex-1 bg-yellow-500 hover:bg-yellow-400 text-white"
                >
                  {updating ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <Eye className="w-4 h-4 mr-2" />}
                  Mark as Read
                </Button>
                <Button
                  onClick={() => updateSubmissionStatus(selectedSubmission.id, 'responded', adminNotes)}
                  disabled={updating}
                  className="flex-1 bg-green-500 hover:bg-green-400 text-white"
                >
                  {updating ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <CheckCircle className="w-4 h-4 mr-2" />}
                  Mark as Responded
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}

export default AdminContactPage
