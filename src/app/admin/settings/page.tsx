import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { NeonButton } from "@/components/ui/neon-button"
import { 
  Settings, 
  Bell, 
  Shield, 
  Database,
  Mail,
  Clock,
  Users,
  CreditCard
} from "lucide-react"

const AdminSettingsPage = () => {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-admin mb-2">
          Admin Settings ⚙️
        </h1>
        <p className="text-muted-foreground">
          Configure gym settings, notifications, and system preferences
        </p>
      </div>

      {/* Settings Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* General Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5 text-admin" />
              General Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="gym-name">Gym Name</Label>
              <Input id="gym-name" defaultValue="Pulse20 EMS Training" />
            </div>
            <div>
              <Label htmlFor="gym-address">Address</Label>
              <Textarea id="gym-address" defaultValue="Lenasia: Signet Terrace Office Park, Lenasia | Mulbarton: 34 The Broads St, Mulbarton" />
            </div>
            <div>
              <Label htmlFor="contact-email">Contact Email</Label>
              <Input id="contact-email" type="email" defaultValue="<EMAIL>" />
            </div>
            <div>
              <Label htmlFor="contact-phone">Contact Phone</Label>
              <Input id="contact-phone" defaultValue="+27 83 408 3665" />
            </div>
          </CardContent>
        </Card>

        {/* Operating Hours */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-marketing" />
              Operating Hours
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-2 text-sm">
              <div className="font-medium">Day</div>
              <div className="font-medium">Open</div>
              <div className="font-medium">Close</div>
            </div>
            {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (
              <div key={day} className="grid grid-cols-3 gap-2">
                <Label className="self-center">{day}</Label>
                <Input defaultValue="05:00" type="time" />
                <Input defaultValue="22:00" type="time" />
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="w-5 h-5 text-communication" />
              Notifications
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>Email Notifications</Label>
                <p className="text-sm text-muted-foreground">Receive email alerts for important events</p>
              </div>
              <Switch defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label>New Member Alerts</Label>
                <p className="text-sm text-muted-foreground">Get notified when new members join</p>
              </div>
              <Switch defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label>Feedback Notifications</Label>
                <p className="text-sm text-muted-foreground">Alert when new feedback is submitted</p>
              </div>
              <Switch defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label>Payment Alerts</Label>
                <p className="text-sm text-muted-foreground">Notify about payment issues</p>
              </div>
              <Switch defaultChecked />
            </div>
          </CardContent>
        </Card>

        {/* Member Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5 text-member" />
              Member Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="max-members">Maximum Members</Label>
              <Input id="max-members" type="number" defaultValue="500" />
            </div>
            <div>
              <Label htmlFor="trial-period">Trial Period (days)</Label>
              <Input id="trial-period" type="number" defaultValue="7" />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label>Auto-suspend overdue accounts</Label>
                <p className="text-sm text-muted-foreground">Automatically suspend members with overdue payments</p>
              </div>
              <Switch defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label>Allow self-registration</Label>
                <p className="text-sm text-muted-foreground">Let users register online</p>
              </div>
              <Switch defaultChecked />
            </div>
          </CardContent>
        </Card>

        {/* Payment Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="w-5 h-5 text-blog" />
              Payment Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="currency">Currency</Label>
              <Input id="currency" defaultValue="ZAR (South African Rand)" />
            </div>
            <div>
              <Label htmlFor="late-fee">Late Payment Fee</Label>
              <Input id="late-fee" type="number" defaultValue="50" />
            </div>
            <div>
              <Label htmlFor="grace-period">Grace Period (days)</Label>
              <Input id="grace-period" type="number" defaultValue="5" />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label>Auto-charge renewals</Label>
                <p className="text-sm text-muted-foreground">Automatically charge recurring memberships</p>
              </div>
              <Switch defaultChecked />
            </div>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5 text-admin" />
              Security
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>Two-factor authentication</Label>
                <p className="text-sm text-muted-foreground">Require 2FA for admin accounts</p>
              </div>
              <Switch />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label>Session timeout</Label>
                <p className="text-sm text-muted-foreground">Auto-logout after inactivity</p>
              </div>
              <Switch defaultChecked />
            </div>
            <div>
              <Label htmlFor="session-duration">Session Duration (minutes)</Label>
              <Input id="session-duration" type="number" defaultValue="60" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <NeonButton variant="admin" size="lg">
          Save Settings
        </NeonButton>
      </div>
    </div>
  )
}

export default AdminSettingsPage
