'use client'

import { useState, useEffect } from "react"

// Prevent static generation for pages that use Supabase
export const dynamic = 'force-dynamic'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Activity,
  Calendar,
  Download,
  Filter,
  RefreshCw
} from "lucide-react"
import { supabase } from "@/lib/supabase/client"

interface AnalyticsData {
  memberGrowth: {
    thisMonth: number
    lastMonth: number
    percentageChange: number
  }
  attendanceStats: {
    totalCheckIns: number
    averageDaily: number
    peakHours: string[]
  }
  revenueMetrics: {
    monthlyRevenue: number
    averagePerMember: number
    growthRate: number
  }
  membershipBreakdown: {
    basic: number
    premium: number
    vip: number
  }
}

const AdminAnalyticsPage = () => {
  const [analytics, setAnalytics] = useState<AnalyticsData>({
    memberGrowth: { thisMonth: 0, lastMonth: 0, percentageChange: 0 },
    attendanceStats: { totalCheckIns: 0, averageDaily: 0, peakHours: [] },
    revenueMetrics: { monthlyRevenue: 0, averagePerMember: 0, growthRate: 0 },
    membershipBreakdown: { basic: 0, premium: 0, vip: 0 }
  })
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('30d')

  useEffect(() => {
    fetchAnalyticsData()
  }, [timeRange])

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true)
      
      // Get current date ranges
      const now = new Date()
      const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1)
      const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1)
      const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0)

      // Fetch member growth data
      const [thisMonthMembers, lastMonthMembers] = await Promise.all([
        supabase.from('profiles').select('*').gte('created_at', thisMonthStart.toISOString()),
        supabase.from('profiles').select('*').gte('created_at', lastMonthStart.toISOString()).lt('created_at', lastMonthEnd.toISOString())
      ])

      // Fetch attendance data
      const { data: checkIns } = await supabase
        .from('check_ins')
        .select('*')
        .gte('check_in_time', thisMonthStart.toISOString())

      // Fetch revenue data
      const { data: payments } = await supabase
        .from('payments')
        .select('amount')
        .gte('created_at', thisMonthStart.toISOString())

      // Fetch membership breakdown
      const { data: allMembers } = await supabase
        .from('profiles')
        .select('membership_type')

      // Calculate analytics
      const thisMonthCount = thisMonthMembers.data?.length || 0
      const lastMonthCount = lastMonthMembers.data?.length || 0
      const percentageChange = lastMonthCount > 0 ? ((thisMonthCount - lastMonthCount) / lastMonthCount) * 100 : 0

      const totalCheckIns = checkIns?.length || 0
      const daysInMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate()
      const averageDaily = totalCheckIns / daysInMonth

      const monthlyRevenue = payments?.reduce((sum, payment) => sum + payment.amount, 0) || 0
      const totalMembers = allMembers?.length || 1
      const averagePerMember = monthlyRevenue / totalMembers

      const membershipBreakdown = allMembers?.reduce((acc, member) => {
        acc[member.membership_type as keyof typeof acc] = (acc[member.membership_type as keyof typeof acc] || 0) + 1
        return acc
      }, { basic: 0, premium: 0, vip: 0 }) || { basic: 0, premium: 0, vip: 0 }

      setAnalytics({
        memberGrowth: {
          thisMonth: thisMonthCount,
          lastMonth: lastMonthCount,
          percentageChange
        },
        attendanceStats: {
          totalCheckIns,
          averageDaily: Math.round(averageDaily * 10) / 10,
          peakHours: ['18:00-19:00', '07:00-08:00', '12:00-13:00'] // Simulated peak hours
        },
        revenueMetrics: {
          monthlyRevenue,
          averagePerMember: Math.round(averagePerMember * 100) / 100,
          growthRate: 12.5 // Simulated growth rate
        },
        membershipBreakdown
      })
    } catch (error) {
      console.error('Error fetching analytics data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-2 border-pink-500 border-t-transparent rounded-full animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl lg:text-3xl font-bold text-white">Analytics Dashboard</h1>
          <p className="text-muted-foreground">Comprehensive insights into gym performance</p>
        </div>
        <div className="flex items-center gap-3">
          <Button variant="outline" size="sm" onClick={fetchAnalyticsData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 lg:gap-6">
        <Card className="border border-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Users className="w-4 h-4 text-pink-400" />
              Member Growth
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-pink-400">{analytics.memberGrowth.thisMonth}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.memberGrowth.percentageChange >= 0 ? '+' : ''}{analytics.memberGrowth.percentageChange.toFixed(1)}% from last month
            </p>
          </CardContent>
        </Card>

        <Card className="border border-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Activity className="w-4 h-4 text-blue-400" />
              Total Check-ins
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-400">{analytics.attendanceStats.totalCheckIns}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.attendanceStats.averageDaily} avg per day
            </p>
          </CardContent>
        </Card>

        <Card className="border border-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <BarChart3 className="w-4 h-4 text-green-400" />
              Monthly Revenue
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-400">R{analytics.revenueMetrics.monthlyRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              R{analytics.revenueMetrics.averagePerMember} per member
            </p>
          </CardContent>
        </Card>

        <Card className="border border-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <TrendingUp className="w-4 h-4 text-orange-400" />
              Growth Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-400">{analytics.revenueMetrics.growthRate}%</div>
            <p className="text-xs text-muted-foreground">
              Monthly growth
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Membership Breakdown */}
        <Card className="border border-border">
          <CardHeader>
            <CardTitle>Membership Breakdown</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm">Basic Members</span>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">{analytics.membershipBreakdown.basic}</Badge>
                <div className="w-20 h-2 bg-muted rounded-full">
                  <div 
                    className="h-full bg-blue-400 rounded-full" 
                    style={{ width: `${(analytics.membershipBreakdown.basic / (analytics.membershipBreakdown.basic + analytics.membershipBreakdown.premium + analytics.membershipBreakdown.vip)) * 100}%` }}
                  />
                </div>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Premium Members</span>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">{analytics.membershipBreakdown.premium}</Badge>
                <div className="w-20 h-2 bg-muted rounded-full">
                  <div 
                    className="h-full bg-green-400 rounded-full" 
                    style={{ width: `${(analytics.membershipBreakdown.premium / (analytics.membershipBreakdown.basic + analytics.membershipBreakdown.premium + analytics.membershipBreakdown.vip)) * 100}%` }}
                  />
                </div>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">VIP Members</span>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">{analytics.membershipBreakdown.vip}</Badge>
                <div className="w-20 h-2 bg-muted rounded-full">
                  <div 
                    className="h-full bg-pink-400 rounded-full" 
                    style={{ width: `${(analytics.membershipBreakdown.vip / (analytics.membershipBreakdown.basic + analytics.membershipBreakdown.premium + analytics.membershipBreakdown.vip)) * 100}%` }}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Peak Hours */}
        <Card className="border border-border">
          <CardHeader>
            <CardTitle>Peak Hours</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.attendanceStats.peakHours.map((hour, index) => (
                <div key={hour} className="flex items-center justify-between">
                  <span className="text-sm">{hour}</span>
                  <Badge variant={index === 0 ? "default" : "secondary"}>
                    {index === 0 ? "Peak" : "High"}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default AdminAnalyticsPage
