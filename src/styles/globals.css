@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    --radius: 0.5rem;
  } */
  :root {
    /* Light theme - Clean backgrounds with neon pink accents */
    --background: 0 0% 98%; /* Off-white */
    --foreground: 0 0% 5%; /* Near black text */
    --card: 0 0% 100%; /* Pure white for cards */
    --card-foreground: 0 0% 5%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 5%;
    --primary: 302 100% 53%; /* Neon pink primary */
    --primary-foreground: 0 0% 100%;
    --secondary: 0 0% 96%; /* Light gray */
    --secondary-foreground: 0 0% 10%;
    --muted: 0 0% 96%; /* Light gray */
    --muted-foreground: 0 0% 45%;
    --accent: 302 100% 53%; /* Neon pink accent */
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 90%; /* Light border */
    --input: 0 0% 96%; /* Light input */
    --ring: 302 100% 53%; /* Neon pink ring */
    --radius: 0.5rem;
  }

  .dark {
    /* Dark theme - Pure black backgrounds with neon pink accents */
    --background: 0 0% 0%; /* Pure black */
    --foreground: 0 0% 95%; /* Near white text */
    --card: 0 0% 3%; /* Very dark gray for cards */
    --card-foreground: 0 0% 95%;
    --popover: 0 0% 3%;
    --popover-foreground: 0 0% 95%;
    --primary: 302 100% 53%; /* Neon pink primary */
    --primary-foreground: 0 0% 100%;
    --secondary: 0 0% 8%; /* Dark gray */
    --secondary-foreground: 0 0% 90%;
    --muted: 0 0% 8%; /* Dark gray */
    --muted-foreground: 0 0% 65%;
    --accent: 302 100% 53%; /* Neon pink accent */
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 15%; /* Dark border */
    --input: 0 0% 8%; /* Dark input */
    --ring: 302 100% 53%; /* Neon pink ring */
  }

  .light {
    /* Light theme - Clean backgrounds with neon pink accents */
    --background: 0 0% 98%; /* Off-white */
    --foreground: 0 0% 5%; /* Near black text */
    --card: 0 0% 100%; /* Pure white for cards */
    --card-foreground: 0 0% 5%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 5%;
    --primary: 302 100% 53%; /* Neon pink primary */
    --primary-foreground: 0 0% 100%;
    --secondary: 0 0% 96%; /* Light gray */
    --secondary-foreground: 0 0% 10%;
    --muted: 0 0% 96%; /* Light gray */
    --muted-foreground: 0 0% 45%;
    --accent: 302 100% 53%; /* Neon pink accent */
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 90%; /* Light border */
    --input: 0 0% 96%; /* Light input */
    --ring: 302 100% 53%; /* Neon pink ring */
  }
}



::selection {
  background: hsl(var(--primary)/0.1);
  color: hsl(var(--foreground));
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--accent-foreground)/0.3);
  border-radius: 9999px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--background));
  border-radius: 9999px;
}

::-webkit-scrollbar-corner {
  background: hsl(var(--foreground) / 0.1);
}

image {
  user-select: none;
  -webkit-user-drag: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.container {
  --uib-size: 45px;
  --uib-color: #FF10F0;
  --uib-speed: 1.75s;
  --uib-bg-opacity: .1;
  height: 31.25px;
  width: 50px;
  transform-origin: center;
  overflow: visible;
}

.car {
  stroke: var(--uib-color);
  stroke-dasharray: 100;
  stroke-dashoffset: 0;
  stroke-linecap: round;
  stroke-linejoin: round;
  animation: travel var(--uib-speed) ease-in-out infinite,
    fade var(--uib-speed) ease-out infinite;
  will-change: stroke-dasharray, stroke-dashoffset;
  transition: stroke 0.5s ease;
}

.track {
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke: var(--uib-color);
  opacity: var(--uib-bg-opacity);
}

.skeleton {
  background: linear-gradient(90deg, #d4d4d4 25%, #f5f5f5 50%, #d4d4d4 75%);
  background-size: 200% 100%;
  animation: loading 2s infinite;
}

@keyframes travel {
  0% {
    stroke-dashoffset: 100;
  }

  75% {
    stroke-dashoffset: 0;
  }
}

@keyframes fade {
  0% {
    opacity: 0;
  }

  20%,
  55% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Pulse.co.za Neon Glow Effects */
@layer utilities {
  .neon-glow-pink {
    box-shadow: 0 0 5px #FF10F0, 0 0 10px #FF10F0, 0 0 15px #FF10F0;
  }

  .neon-glow-pink-subtle {
    box-shadow: 0 0 3px #FF10F0, 0 0 6px #FF10F0;
  }

  .neon-glow-cyan {
    box-shadow: 0 0 5px #00FFFF, 0 0 10px #00FFFF, 0 0 15px #00FFFF;
  }

  .neon-glow-green {
    box-shadow: 0 0 5px #39FF14, 0 0 10px #39FF14, 0 0 15px #39FF14;
  }

  .neon-glow-orange {
    box-shadow: 0 0 5px #FF6B35, 0 0 10px #FF6B35, 0 0 15px #FF6B35;
  }

  .text-glow-pink {
    text-shadow: 0 0 10px #FF10F0, 0 0 20px #FF10F0, 0 0 30px #FF10F0;
  }

  /* Section-specific backgrounds */
  .section-member {
    background: linear-gradient(135deg, rgba(255, 16, 240, 0.1) 0%, rgba(255, 16, 240, 0.05) 100%);
  }

  .section-admin {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.1) 0%, rgba(0, 255, 255, 0.05) 100%);
  }

  .section-blog {
    background: linear-gradient(135deg, rgba(57, 255, 20, 0.1) 0%, rgba(57, 255, 20, 0.05) 100%);
  }

  .section-marketing {
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 107, 53, 0.05) 100%);
  }

  /* Achievement celebration effects */
  .achievement-celebration {
    position: relative;
    overflow: hidden;
  }

  .achievement-celebration::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 16, 240, 0.3) 0%, transparent 70%);
    animation: achievement-bounce 1s ease-in-out;
    pointer-events: none;
  }

  /* Full width layout utilities */
  .full-width-section {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
  }

  /* Ensure proper spacing and full width */
  section {
    width: 100%;
  }

  /* Responsive container max-widths */
  .container-responsive {
    width: 100%;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  @media (min-width: 640px) {
    .container-responsive {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .container-responsive {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }
}
