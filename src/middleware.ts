import { type NextRequest } from 'next/server'
import { updateSession } from '@/lib/supabase/middleware'

export async function middleware(request: NextRequest) {
  return await updateSession(request)
}

export const config = {
  matcher: [
    /*
     * Match dashboard and admin routes for protection
     * Exclude static files, images, and other assets
     */
    '/dashboard/:path*',
    '/admin/:path*',
  ],
}
