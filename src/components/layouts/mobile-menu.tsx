"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, SheetTrigger } from "@/components/ui/sheet"
import { Button } from "@/components/ui/button"
import { Menu, X } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"

interface MobileMenuProps {
  items: {
    href: string
    label: string
    icon: React.ElementType
  }[]
  logo: React.ReactNode
  variant: "member" | "admin"
}

export function MobileMenu({ items, logo, variant }: MobileMenuProps) {
  const [isOpen, setIsOpen] = useState(false)
  const pathname = usePathname()
  
  // Close the menu when the path changes (user navigates)
  useEffect(() => {
    setIsOpen(false)
  }, [pathname])

  return (
    <div className="md:hidden">
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button variant="ghost" size="icon" className="md:hidden">
            <Menu className="h-6 w-6" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="p-0 w-[85%] max-w-[320px]">
          <div className="flex flex-col h-full">
            {/* Header with logo and close button */}
            <div className="p-4 border-b border-border flex items-center justify-between">
              <div className="flex-1 min-w-0">
                {logo}
              </div>
              <Button variant="ghost" size="icon" onClick={() => setIsOpen(false)} className="shrink-0">
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Navigation items */}
            <nav className="flex-1 overflow-y-auto p-4">
              <div className="space-y-1">
                {items.map((item) => {
                  const Icon = item.icon
                  const isActive = pathname === item.href

                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      className={cn(
                        "flex items-center gap-3 px-3 py-3 rounded-md text-sm font-medium transition-colors w-full",
                        isActive
                          ? variant === "member"
                            ? "bg-cyan-500/10 text-cyan-400 border border-cyan-500/20"
                            : "bg-pink-500/10 text-pink-400 border border-pink-500/20"
                          : "text-muted-foreground hover:bg-muted hover:text-foreground"
                      )}
                      onClick={() => setIsOpen(false)}
                    >
                      <Icon className="h-5 w-5 shrink-0" />
                      <span className="truncate">{item.label}</span>
                    </Link>
                  )
                })}
              </div>
            </nav>
            
            {/* Footer */}
            <div className="p-4 border-t border-border">
              <div className="text-xs text-muted-foreground text-center">
                <p>Pulse20.co.za © {new Date().getFullYear()}</p>
                <p>Version 1.0.0</p>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  )
}