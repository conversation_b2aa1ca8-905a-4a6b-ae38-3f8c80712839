"use client"

import { <PERSON>actN<PERSON>, useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { SectionContainer } from "@/components/ui/section-container"
import { ThemeToggle } from "@/components/ui/theme-toggle"
import UserAccount from "@/components/user-account"
import { cn } from "@/lib/utils"
import { MobileMenu } from "./mobile-menu"
import {
  Users,
  BarChart3,
  MessageSquare,
  Settings,
  FileText,
  Home,
  Bell,
  Search,
  TrendingUp,
  Activity,
  CreditCard,
  Plus,
  Calendar,
  Layers,
  ShieldCheck
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { supabase } from "@/lib/supabase/client"

interface AdminLayoutProps {
  children: ReactNode
  className?: string
}

interface DashboardStats {
  totalMembers: number
  activeToday: number
  monthlyRevenue: number
  pendingFeedback: number
}

const sidebarItems = [
  { href: "/admin", label: "Dashboard", icon: Home },
  { href: "/admin/members", label: "Members", icon: Users },
  { href: "/admin/users", label: "User Management", icon: Users },
  { href: "/admin/analytics", label: "Analytics", icon: BarChart3 },
  { href: "/admin/payments", label: "Payments", icon: CreditCard },
  { href: "/admin/blog", label: "Blog", icon: FileText },
  { href: "/admin/resources", label: "Resources", icon: Layers },
  { href: "/admin/contact", label: "Contact", icon: MessageSquare },
  { href: "/admin/feedback", label: "Feedback", icon: MessageSquare },
  { href: "/admin/settings", label: "Settings", icon: Settings },
]

export function AdminLayout({ children, className }: AdminLayoutProps) {
  const pathname = usePathname()
  const [stats, setStats] = useState<DashboardStats>({
    totalMembers: 0,
    activeToday: 0,
    monthlyRevenue: 0,
    pendingFeedback: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const [profilesResponse, checkInsResponse, paymentsResponse, feedbackResponse] = await Promise.all([
          supabase.from('profiles').select('*'),
          supabase.from('check_ins').select('*').gte('check_in_time', new Date().toISOString().split('T')[0]),
          supabase.from('payments').select('amount').gte('created_at', new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString()),
          supabase.from('feedback').select('*').eq('status', 'pending')
        ])
  
        const totalMembers = profilesResponse.data?.length || 0
        const activeToday = checkInsResponse.data?.length || 0
        const monthlyRevenue = paymentsResponse.data?.reduce((sum, payment) => sum + payment.amount, 0) || 0
        const pendingFeedback = feedbackResponse.data?.length || 0
  
        setStats({
          totalMembers,
          activeToday,
          monthlyRevenue,
          pendingFeedback
        })
      } catch (error) {
        console.error('Error fetching dashboard stats in AdminLayout:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  // Logo component for reuse in mobile menu
  const Logo = (
    <Link href="/admin" className="flex items-center gap-2">
      <div className="w-10 h-10 rounded-full bg-gradient-to-br from-pink-500 to-pink-400 flex items-center justify-center neon-glow-pink">
        <ShieldCheck className="w-5 h-5 text-white" />
      </div>
      <span className="text-lg font-bold text-pink-400">Pulse Admin</span>
    </Link>
  )

  return (
    <div className={cn("min-h-screen bg-background text-foreground flex flex-col lg:flex-row", className)}>
      {/* Mobile Menu */}
      <div className="lg:hidden">
        <MobileMenu
          items={sidebarItems}
          logo={Logo}
          variant="admin"
        />
      </div>

      {/* Left Sidebar - Enhanced Admin Style */}
      <aside className="hidden lg:block lg:w-64 xl:w-72 border-r border-border bg-card/95 backdrop-blur-sm shrink-0">
        <div className="flex flex-col h-screen sticky top-0">
          {/* Logo & Header */}
          <div className="p-4 lg:p-6 border-b border-border">
            {Logo}
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-3 lg:p-4 overflow-y-auto">
            <div className="space-y-1">
              {sidebarItems.map((item) => {
                const Icon = item.icon
                const isActive = pathname === item.href

                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors w-full",
                      isActive
                        ? "bg-pink-500/10 text-pink-400 border border-pink-500/20"
                        : "text-muted-foreground hover:bg-muted hover:text-foreground"
                    )}
                  >
                    <Icon className="h-4 w-4 lg:h-5 lg:w-5 shrink-0" />
                    <span className="truncate">{item.label}</span>
                  </Link>
                )
              })}
            </div>
          </nav>

          {/* Admin Profile */}
          <div className="p-3 lg:p-4 border-t border-border">
            <div className="mb-3">
              <UserAccount />
            </div>
            <ThemeToggle />
          </div>
        </div>
      </aside>

      {/* Main Content */}
      <main className="flex-1 flex flex-col min-h-screen overflow-hidden">
        {/* Header */}
        <header className="h-14 lg:h-16 border-b border-border bg-background/50 backdrop-blur-sm flex items-center justify-between px-3 lg:px-6 shrink-0">
          <div className="flex items-center gap-2 lg:gap-4 flex-1 min-w-0">
            <div className="relative w-full max-w-sm lg:max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <input
                type="text"
                placeholder="Search members..."
                className="w-full h-8 lg:h-9 rounded-md border border-border bg-background px-10 text-sm focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              />
            </div>
          </div>

          <div className="flex items-center gap-2 lg:gap-3 shrink-0">
            <Button variant="outline" size="icon" className="relative h-8 w-8 lg:h-9 lg:w-9">
              <Bell className="h-4 w-4 lg:h-5 lg:w-5" />
              <span className="absolute -top-1 -right-1 w-3 h-3 lg:w-4 lg:h-4 bg-pink-500 text-white text-xs rounded-full flex items-center justify-center">5</span>
            </Button>

            <Button size="sm" className="gap-2 bg-pink-500 hover:bg-pink-600 text-white">
              <Plus className="h-4 w-4" />
              <span className="hidden sm:inline">Add Member</span>
            </Button>
          </div>
        </header>

        {/* Content Area with Overflow Handling */}
        <div className="flex-1 flex flex-col xl:flex-row overflow-hidden">
          {/* Center Content - Scrollable */}
          <div className="flex-1 overflow-y-auto p-3 lg:p-6">
            <div className="max-w-none">
              {children}
            </div>
          </div>

          {/* Right Sidebar - Stats & Quick Actions */}
          <aside className="hidden xl:block w-80 2xl:w-96 border-l border-border bg-card/50 backdrop-blur-sm p-4 overflow-y-auto shrink-0">
            <div className="space-y-6">
              {/* Stats Widget */}
              <div className="rounded-xl border border-border p-4">
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-pink-400" />
                  Admin Stats
                </h3>
                {loading ? (
                  <div className="flex justify-center items-center h-20">
                    <div className="w-6 h-6 border-2 border-pink-500 border-t-transparent rounded-full animate-spin" />
                  </div>
                ) : (
                  <div className="grid grid-cols-2 gap-3">
                    <div className="rounded-lg bg-muted/50 p-3 text-center">
                      <div className="text-xl font-bold text-pink-400">{stats.totalMembers}</div>
                      <div className="text-xs text-muted-foreground">Total Members</div>
                    </div>
                    <div className="rounded-lg bg-muted/50 p-3 text-center">
                      <div className="text-xl font-bold text-pink-400">{stats.activeToday}</div>
                      <div className="text-xs text-muted-foreground">Active Today</div>
                    </div>
                    <div className="rounded-lg bg-muted/50 p-3 text-center">
                      <div className="text-xl font-bold text-pink-400">R{stats.monthlyRevenue.toLocaleString()}</div>
                      <div className="text-xs text-muted-foreground">Monthly Revenue</div>
                    </div>
                    <div className="rounded-lg bg-muted/50 p-3 text-center">
                      <div className="text-xl font-bold text-pink-400">{stats.pendingFeedback}</div>
                      <div className="text-xs text-muted-foreground">Pending Feedback</div>
                    </div>
                  </div>
                )}
              </div>

              {/* Quick Actions */}
              <div className="rounded-xl border border-border p-4">
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <Activity className="h-5 w-5 text-pink-400" />
                  Quick Actions
                </h3>
                <div className="space-y-2">
                  <Button className="w-full justify-start gap-2 bg-pink-500 hover:bg-pink-600 text-white">
                    <Plus className="h-4 w-4" />
                    Add New Member
                  </Button>
                  <Button variant="outline" className="w-full justify-start gap-2">
                    <Calendar className="h-4 w-4" />
                    Schedule Event
                  </Button>
                  <Button variant="outline" className="w-full justify-start gap-2">
                    <FileText className="h-4 w-4" />
                    Generate Report
                  </Button>
                </div>
              </div>
            </div>
          </aside>
        </div>
      </main>
    </div>
  )
}
