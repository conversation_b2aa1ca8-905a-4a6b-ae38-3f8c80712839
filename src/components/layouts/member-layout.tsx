"use client"

import { ReactNode } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { SectionContainer } from "@/components/ui/section-container"
import { ThemeToggle } from "@/components/ui/theme-toggle"
import UserAccount from "@/components/user-account"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { NeonButton } from "@/components/ui/neon-button"
import { cn } from "@/lib/utils"
import { MobileMenu } from "./mobile-menu"
import {
  Home,
  Calendar,
  MessageSquare,
  CreditCard,
  Trophy,
  Settings,
  Activity,
  TrendingUp,
  Dumbbell,
  Bell,
  Search,
  Plus,
  Menu,
  Users,
  BarChart3,
  FileText
} from "lucide-react"

interface MemberLayoutProps {
  children: ReactNode
  className?: string
  showThemeToggle?: boolean
}

const sidebarItems = [
  { href: "/dashboard", label: "Home", icon: Home },
  { href: "/dashboard/workouts", label: "Workouts", icon: Activity },
  { href: "/dashboard/progress", label: "Progress", icon: TrendingUp },
  { href: "/dashboard/appointments", label: "Appointments", icon: Calendar },
  { href: "/dashboard/messages", label: "Messages", icon: MessageSquare },
  { href: "/dashboard/payments", label: "Payments", icon: CreditCard },
  { href: "/dashboard/achievements", label: "Achievements", icon: Trophy },
  { href: "/dashboard/settings", label: "Settings", icon: Settings },
]

export function MemberLayout({ children, className, showThemeToggle = true }: MemberLayoutProps) {
  const pathname = usePathname()

  // Logo component for reuse in mobile menu
  const Logo = (
    <Link href="/dashboard" className="flex items-center gap-3">
      <div className="w-10 h-10 rounded-full bg-gradient-to-br from-pink-500 to-pink-400 flex items-center justify-center neon-glow-pink">
        <Dumbbell className="w-5 h-5 text-white" />
      </div>
      <span className="text-lg font-bold text-pink-400">Pulse20.co.za</span>
    </Link>
  )

  return (
    <div className={cn("min-h-screen bg-background text-foreground flex flex-col md:flex-row", className)}>
      {/* Mobile Menu */}
      <MobileMenu 
        items={sidebarItems} 
        logo={Logo} 
        variant="member" 
      />
      
      {/* Left Sidebar - Enhanced Social Media Style */}
      <aside className="hidden md:block md:w-72 border-r border-border bg-card/95 backdrop-blur-sm">
        <div className="flex flex-col h-full">
          {/* Logo & Profile Section */}
          <div className="p-6 border-b border-border">
            <div className="flex items-center justify-between mb-6">
              {Logo}
            </div>
            <UserAccount />
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 overflow-y-auto">
            <div className="space-y-1">
              {sidebarItems.map((item) => {
                const Icon = item.icon
                const isActive = pathname === item.href
                
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors",
                      isActive 
                        ? "bg-member/10 text-member" 
                        : "text-muted-foreground hover:bg-muted"
                    )}
                  >
                    <Icon className="h-5 w-5" />
                    <span>{item.label}</span>
                  </Link>
                )
              })}
            </div>
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-border">
            {showThemeToggle && <ThemeToggle />}
          </div>
        </div>
      </aside>

      {/* Main Content */}
      <main className="flex-1 flex flex-col min-h-screen max-h-screen overflow-hidden">
        {/* Header */}
        <header className="h-16 border-b border-border bg-background/50 backdrop-blur-sm flex items-center justify-between px-4 md:px-6">
          <div className="flex items-center gap-4">
            <div className="relative w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <input 
                type="text" 
                placeholder="Search..." 
                className="w-full h-9 rounded-md border border-border bg-background px-10 text-sm focus:outline-none focus:ring-2 focus:ring-member focus:border-transparent" 
              />
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Button variant="outline" size="icon" className="relative">
              <Bell className="h-5 w-5" />
              <span className="absolute -top-1 -right-1 w-4 h-4 bg-member text-white text-xs rounded-full flex items-center justify-center">3</span>
            </Button>
            
            <NeonButton variant="member" size="sm" className="gap-2">
              <Plus className="h-4 w-4" />
              <span className="hidden sm:inline">New Workout</span>
            </NeonButton>
          </div>
        </header>

        {/* Content Area with Overflow Handling */}
        <div className="flex-1 flex flex-col md:flex-row overflow-hidden">
          {/* Center Content - Scrollable */}
          <div className="flex-1 overflow-y-auto p-4 md:p-6">
   
              {children}
 
          </div>

          {/* Right Sidebar - Goals & Stats */}
          <aside className="hidden lg:block w-80 border-l border-border bg-card/50 backdrop-blur-sm p-4 overflow-y-auto">
            <div className="space-y-6">
              {/* Goals Widget */}
              <div className="rounded-xl border border-border p-4">
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <Trophy className="h-5 w-5 text-member" />
                  Monthly Goals
                </h3>
                <div className="space-y-3">
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-sm">
                      <span>Workouts</span>
                      <span className="font-medium">15/20</span>
                    </div>
                    <div className="h-2 bg-muted rounded-full overflow-hidden">
                      <div className="h-full bg-member rounded-full" style={{ width: '75%' }} />
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-sm">
                      <span>Cardio Minutes</span>
                      <span className="font-medium">120/200</span>
                    </div>
                    <div className="h-2 bg-muted rounded-full overflow-hidden">
                      <div className="h-full bg-member rounded-full" style={{ width: '60%' }} />
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-sm">
                      <span>Weight Training</span>
                      <span className="font-medium">8/12</span>
                    </div>
                    <div className="h-2 bg-muted rounded-full overflow-hidden">
                      <div className="h-full bg-member rounded-full" style={{ width: '66%' }} />
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Stats Widget */}
              <div className="rounded-xl border border-border p-4">
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-member" />
                  Quick Stats
                </h3>
                <div className="grid grid-cols-2 gap-3">
                  <div className="rounded-lg bg-muted/50 p-3 text-center">
                    <div className="text-2xl font-bold text-member">15</div>
                    <div className="text-xs text-muted-foreground">This Month</div>
                  </div>
                  <div className="rounded-lg bg-muted/50 p-3 text-center">
                    <div className="text-2xl font-bold text-member">7</div>
                    <div className="text-xs text-muted-foreground">Current Streak</div>
                  </div>
                  <div className="rounded-lg bg-muted/50 p-3 text-center">
                    <div className="text-2xl font-bold text-member">142</div>
                    <div className="text-xs text-muted-foreground">Total Workouts</div>
                  </div>
                  <div className="rounded-lg bg-muted/50 p-3 text-center">
                    <div className="text-2xl font-bold text-member">4.2</div>
                    <div className="text-xs text-muted-foreground">Avg per Week</div>
                  </div>
                </div>
              </div>
            </div>
          </aside>
        </div>
      </main>
    </div>
  )
}

// Line 160: Replace ' with &apos;
<p>Welcome to your member&apos;s dashboard</p>
