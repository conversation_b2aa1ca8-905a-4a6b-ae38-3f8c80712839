"use client"

import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import Image from "next/image"
import { 
  MapPin, 
  Clock, 
  Wifi, 
  Car, 
  Coffee, 
  Shield,
  Thermometer,
  Dumbbell
} from "lucide-react"
import Link from "next/link"
import { useState } from "react"

interface FacilitiesSectionProps {
  className?: string
}

const facilities = [
  {
    image: "/wellness.jpg",
    title: "EMS Training Studio",
    description: "Experience state-of-the-art EMS equipment in a private, climate-controlled studio. Enjoy self-service digital check-in and a focus on your personal progress."
  },
  {
    image: "/needles.jpg", 
    title: "Private Treatment Rooms",
    description: "Relax in private, serene rooms for acupuncture, needling, and cupping. Your comfort and privacy are our top priority."
  },
  {
    image: "/massage.jpg",
    title: "Sports Massage Suites",
    description: "Recover in luxury with deep tissue and sports massage, all in a private, member-focused environment."
  }
]

const amenities = [
  {
    icon: Thermometer,
    title: "Climate Control",
    description: "Perfect temperature and humidity control for optimal training conditions"
  },
  {
    icon: Shield,
    title: "Sanitized Equipment",
    description: "Hospital-grade sanitization protocols for all equipment and surfaces"
  },
  {
    icon: Wifi,
    title: "High-Speed WiFi",
    description: "Complimentary high-speed internet throughout the facility"
  },
  {
    icon: Car,
    title: "Free Parking",
    description: "Convenient on-site parking available for all members"
  },
  {
    icon: Coffee,
    title: "Refreshment Area",
    description: "Complimentary beverages and healthy snacks available"
  },
  {
    icon: Clock,
    title: "Flexible Hours",
    description: "Extended hours to accommodate your busy schedule"
  }
]

export function FacilitiesSection({ className }: FacilitiesSectionProps) {
  const [showTour, setShowTour] = useState(false)

  return (
    <section className={cn("py-20 lg:py-32 bg-black w-full", className)}>
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <Badge className="mb-6 bg-pink-500/20 text-pink-400 border-pink-500/30">
            Our Facilities
          </Badge>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-white">
            Premium{" "}
            <span className="text-pink-400">
              Wellness
            </span>{" "}
            Environment
          </h2>
          <p className="text-lg text-gray-400 max-w-3xl mx-auto leading-relaxed">
            Step into our world-class facilities designed to provide the ultimate wellness experience. 
            Every detail has been carefully crafted for your comfort, privacy, and results.
          </p>
        </motion.div>

        {/* Main Facilities Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
          {facilities.map((facility, index) => (
            <motion.div
              key={facility.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
            >
              <Card className="overflow-hidden border-2 border-gray-800 hover:border-pink-500/50 transition-all duration-300 group bg-gray-900/50 backdrop-blur-sm h-full">
                <div className="relative aspect-[4/3]">
                  <Image
                    src={facility.image}
                    alt={facility.title}
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
                  
                  {/* Content Overlay */}
                  <div className="absolute bottom-0 left-0 right-0 p-6">
                    <h3 className="text-xl font-bold text-white mb-2 group-hover:text-pink-400 transition-colors">
                      {facility.title}
                    </h3>
                    <p className="text-gray-300 text-sm leading-relaxed">
                      {facility.description}
                    </p>
                  </div>

                  {/* Hover Overlay */}
                  <div className="absolute inset-0 bg-pink-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Virtual Tour CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mb-16"
        >
          <Card className="bg-gradient-to-r from-pink-500/10 via-pink-400/10 to-pink-500/10 border-pink-500/20 bg-gray-900/50 backdrop-blur-sm max-w-2xl mx-auto">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold mb-4 text-white">
                Experience Our Facilities
              </h3>
              <p className="text-gray-400 mb-6">
                Take a virtual tour or schedule an in-person visit to see our private, member-focused facilities firsthand.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-pink-500 hover:bg-pink-400 text-white shadow-lg shadow-pink-500/30" onClick={() => setShowTour(true)}>
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                  Virtual Tour
                </Button>
                <Link href="/contact" passHref legacyBehavior>
                  <Button variant="outline" className="border-pink-500/30 text-pink-400 hover:bg-pink-500/10">
                    <MapPin className="w-4 h-4 mr-2" />
                    Book Appointment
                  </Button>
                </Link>
              </div>
              {/* Modal for Virtual Tour */}
              {showTour && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80">
                  <div className="relative bg-gray-900 rounded-lg shadow-lg max-w-2xl w-full p-4">
                    <button
                      className="absolute top-2 right-2 text-white text-2xl font-bold hover:text-pink-400"
                      onClick={() => setShowTour(false)}
                      aria-label="Close"
                    >
                      &times;
                    </button>
                    <div className="aspect-video w-full">
                      <iframe
                        width="100%"
                        height="100%"
                        src="https://www.youtube.com/embed/dQw4w9WgXcQ"
                        title="Virtual Tour"
                        frameBorder="0"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                      ></iframe>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Amenities Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <h3 className="text-2xl font-bold text-white text-center mb-8">
            Premium Amenities
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {amenities.map((amenity, index) => {
              const Icon = amenity.icon;
              return (
                <motion.div
                  key={amenity.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="flex flex-col items-center justify-center text-center"
                >
                  <Icon className="w-12 h-12 text-pink-400 mb-2" />
                  <h4 className="text-lg font-bold text-pink-400 mb-1">
                    {amenity.title}
                  </h4>
                  <p className="text-gray-300">
                    {amenity.description}
                  </p>
                </motion.div>
              );
            })}
          </div>
        </motion.div>
      </div>
    </section>
  )
}
