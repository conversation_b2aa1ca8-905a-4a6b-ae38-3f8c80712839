"use client"

import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import Image from "next/image"
import Link from "next/link"
import { useState } from "react"
import {
  Zap,
  Clock,
  Target,
  Heart,
  CheckCircle,
  ArrowRight,
  Play,
  ChevronDown,
  ChevronUp
} from "lucide-react"

interface Pulse20EMSSectionProps {
  className?: string
}

const emsFeatures = [
  {
    icon: Zap,
    title: "36,000 Muscle Contractions",
    description: "Every 4 seconds, all major muscle groups activate simultaneously",
    stat: "150x More Effective",
    bgImage: "/workout.jpg",
    color: "pink",
    details: "Revolutionary EMS technology delivers 36,000 muscle contractions per session"
  },
  {
    icon: Clock,
    title: "20 Minutes = 90 Minutes HIIT",
    description: "Maximum efficiency with minimal time investment",
    stat: "Time Saved",
    bgImage: "/lenasia1.jpg",
    color: "blue",
    details: "Achieve the same results as 90 minutes of traditional HIIT training"
  },
  {
    icon: Target,
    title: "90% Muscle Activation",
    description: "Reach muscle fibers impossible with traditional training",
    stat: "Deep Activation",
    bgImage: "/melbuton1.jpg",
    color: "green",
    details: "Activate 90% of muscle fibers compared to 30% in conventional training"
  },
  {
    icon: Heart,
    title: "Joint-Friendly Training",
    description: "Low impact exercise that prevents strain and injury",
    stat: "Safe & Effective",
    bgImage: "/wellness.jpg",
    color: "purple",
    details: "Zero impact on joints while delivering maximum muscle engagement"
  }
]

const benefits = [
  {
    title: "Decreasing waist circumference, abdominal obesity & body fat percentage",
    details: "EMS training specifically targets deep abdominal muscles and subcutaneous fat mass, leading to significant reduction in waist circumference and overall body fat percentage. Studies show measurable results after consistent training.",
    icon: Target
  },
  {
    title: "Improvement of posture and reduction in cellulite",
    details: "The electrical stimulation improves blood flow and lymphatic drainage, reducing cellulite appearance while strengthening postural muscles. Enhanced circulation promotes better skin tone and body alignment.",
    icon: Heart
  },
  {
    title: "Relief for back pains - ideal for back problem sufferers",
    details: "EMS strengthens deep stabilizing muscles around the spine, providing better support and reducing chronic back pain. Perfect for people with back problems who struggle to exercise traditionally.",
    icon: CheckCircle
  },
  {
    title: "Improved blood circulation & reduction of superfluous fat cells",
    details: "Electrical impulses stimulate blood vessels, improving circulation throughout the body. This enhanced blood flow accelerates fat metabolism and helps eliminate excess fat cells naturally.",
    icon: Zap
  },
  {
    title: "Strengthening of core and abdominal muscles",
    details: "EMS activates all layers of abdominal muscles simultaneously, including deep transverse abdominis. Results in stronger, more defined core and improved overall strength and endurance.",
    icon: Target
  },
  {
    title: "Correction of muscular imbalances",
    details: "EMS can target specific muscle groups independently, helping to correct imbalances caused by poor posture, injury, or repetitive movements. Creates more balanced muscle development and prevents future injuries.",
    icon: CheckCircle
  },
  {
    title: "Stimulates internal organs & optimizes digestion",
    details: "EMS helps stimulate internal organs and peristalsis (muscle contractions that promote digestion), optimizing digestion and nutrition absorption. Stronger core supports better digestive function.",
    icon: Heart
  },
  {
    title: "Training easy on joints - prevents strain and injury",
    details: "Unlike traditional high-impact training, EMS is beneficial for joints, preventing strain and injury. The result is a firmer, tighter body with enhanced muscle definition and improved overall fitness.",
    icon: Zap
  }
]

export function Pulse20EMSSection({ className }: Pulse20EMSSectionProps) {
  const [expandedBenefit, setExpandedBenefit] = useState<number | null>(null);

  const toggleBenefit = (index: number) => {
    setExpandedBenefit(expandedBenefit === index ? null : index);
  };

  return (
    <section className={cn("relative py-20 lg:py-32 bg-black overflow-hidden", className)}>
      {/* Background with image and animated elements */}
      <div className="absolute inset-0 z-0">
        {/* Background image */}
        <div
          className="absolute inset-0 bg-cover bg-center opacity-30"
          style={{ backgroundImage: 'url(/workout.jpg)' }}
        />
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-pink-500/10 via-black to-pink-400/5" />
        <div className="absolute inset-0 bg-black/60" />

        {/* Animated background elements */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-pink-400/8 rounded-full blur-3xl animate-pulse delay-1000" />
      </div>

      <div className="relative z-10 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <Badge className="mb-6 bg-pink-500/20 text-pink-400 border-pink-500/30 text-lg px-6 py-2">
            Pulse20 Wireless EMS Training
          </Badge>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white">
            The Future of{" "}
            <span className="text-pink-400 text-glow-pink">
              Fitness
            </span>{" "}
            is Here
          </h2>
          <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed mb-8">
            Experience revolutionary wireless EMS technology that delivers the equivalent of a 90-minute HIIT session 
            in just 20 minutes. Every major muscle group activated simultaneously for unprecedented results.
          </p>
          
          {/* Video Play Button */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="flex justify-center mb-12"
          >
            <Button
              size="lg"
              className="group bg-pink-500/20 hover:bg-pink-500/30 border-2 border-pink-500 text-pink-400 hover:text-white backdrop-blur-sm px-8 py-4 text-lg"
            >
              <Play className="w-6 h-6 mr-3 group-hover:scale-110 transition-transform" />
              Watch EMS Training Demo
            </Button>
          </motion.div>
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Left Side - EMS Features */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              {emsFeatures.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="group"
                >
                  <div className="relative overflow-hidden rounded-2xl h-80 transition-all duration-500 hover:scale-105">
                    {/* Background Image */}
                    <div className="absolute inset-0">
                      <div
                        className="absolute inset-0 bg-cover bg-center"
                        style={{ backgroundImage: `url(${feature.bgImage})` }}
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-black/40 group-hover:from-pink-900/90 group-hover:via-black/60 transition-all duration-500" />
                    </div>

                    {/* Content */}
                    <div className="relative z-10 p-6 h-full flex flex-col justify-between">
                      {/* Top - Icon and Stat */}
                      <div className="flex justify-between items-start">
                        <div className="w-14 h-14 rounded-full bg-pink-500/20 backdrop-blur-sm border border-pink-500/30 flex items-center justify-center group-hover:bg-pink-500/40 transition-all duration-300">
                          <feature.icon className="w-7 h-7 text-pink-400" />
                        </div>
                        <Badge className="bg-pink-500/20 text-pink-400 border-pink-500/30 backdrop-blur-sm">
                          {feature.stat}
                        </Badge>
                      </div>

                      {/* Bottom - Content */}
                      <div className="space-y-3">
                        <h3 className="text-xl font-bold text-white leading-tight">{feature.title}</h3>
                        <p className="text-gray-300 text-sm leading-relaxed">{feature.description}</p>
                        <div className="pt-2">
                          <p className="text-pink-300 text-xs font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            {feature.details}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Hover Glow Effect */}
                    <div className="absolute inset-0 bg-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl blur-xl" />
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Right Side - Benefits List */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="space-y-6"
          >
            <div className="mb-8">
              <h3 className="text-3xl font-bold text-white mb-4">
                Proven <span className="text-pink-400">Benefits</span>
              </h3>
              <p className="text-gray-400 text-lg">
                Pulse20 EMS training delivers measurable results that transform your body and enhance your well-being.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-4">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={benefit.title}
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="rounded-lg bg-gray-900/30 backdrop-blur-sm border border-gray-800 hover:border-pink-500/30 transition-all duration-300 overflow-hidden"
                >
                  <div
                    className="flex items-center gap-4 p-4 cursor-pointer"
                    onClick={() => toggleBenefit(index)}
                  >
                    <benefit.icon className="w-6 h-6 text-pink-400 flex-shrink-0" />
                    <span className="text-gray-300 flex-grow">{benefit.title}</span>
                    {expandedBenefit === index ? (
                      <ChevronUp className="w-5 h-5 text-pink-400" />
                    ) : (
                      <ChevronDown className="w-5 h-5 text-pink-400" />
                    )}
                  </div>

                  {/* Expandable Content */}
                  <motion.div
                    initial={false}
                    animate={{
                      height: expandedBenefit === index ? "auto" : 0,
                      opacity: expandedBenefit === index ? 1 : 0
                    }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    className="overflow-hidden"
                  >
                    <div className="px-4 pb-4 pl-14">
                      <p className="text-gray-400 text-sm leading-relaxed">
                        {benefit.details}
                      </p>
                    </div>
                  </motion.div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center"
        >
          <Card className="bg-gradient-to-r from-pink-500/10 via-pink-400/10 to-pink-500/10 border-pink-500/20 bg-gray-900/50 backdrop-blur-sm">
            <CardContent className="p-12">
              <h3 className="text-3xl font-bold mb-4 text-white">
                Ready to Experience the <span className="text-pink-400">Revolution</span>?
              </h3>
              <p className="text-gray-400 mb-8 max-w-3xl mx-auto text-lg">
                Start with our R500 Intro Consultation including full body assessment, BMI check, and complete EMS training session.
                Experience firsthand why 20 minutes of Pulse20 EMS equals hours of conventional training.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/contact?package=intro" passHref legacyBehavior>
                  <motion.a
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="inline-flex items-center px-8 py-4 bg-pink-500 text-white rounded-lg font-semibold shadow-lg shadow-pink-500/30 hover:bg-pink-400 transition-colors cursor-pointer text-lg"
                  >
                    Book Intro Consultation - R500
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </motion.a>
                </Link>
                <Link href="/contact?package=other" passHref legacyBehavior>
                  <Button
                    variant="outline"
                    size="lg"
                    className="border-pink-500/50 text-pink-400 hover:bg-pink-500/10 px-8 py-4 text-lg"
                  >
                    Learn More About EMS
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
