"use client"

import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import Image from "next/image"
import Link from "next/link"
import { 
  Zap, 
  Clock, 
  Target, 
  Heart, 
  TrendingUp, 
  Shield,
  CheckCircle,
  ArrowRight,
  Play
} from "lucide-react"

interface Pulse20EMSSectionProps {
  className?: string
}

const emsFeatures = [
  {
    icon: Zap,
    title: "36,000 Muscle Contractions",
    description: "Every 4 seconds, all major muscle groups activate simultaneously",
    stat: "150x More Effective"
  },
  {
    icon: Clock,
    title: "20 Minutes = 90 Minutes HIIT",
    description: "Maximum efficiency with minimal time investment",
    stat: "Time Saved"
  },
  {
    icon: Target,
    title: "90% Muscle Activation",
    description: "Reach muscle fibers impossible with traditional training",
    stat: "Deep Activation"
  },
  {
    icon: Heart,
    title: "Joint-Friendly Training",
    description: "Low impact exercise that prevents strain and injury",
    stat: "Safe & Effective"
  }
]

const benefits = [
  "Decreasing waist circumference and abdominal obesity",
  "Reduction in cellulite and improved skin tone",
  "Relief for back pains and improved posture",
  "Enhanced blood circulation and fat cell reduction",
  "Strengthening of core and abdominal muscles",
  "Correction of muscular imbalances",
  "Improved digestion and nutrition absorption",
  "Firmer and tighter body results"
]

export function Pulse20EMSSection({ className }: Pulse20EMSSectionProps) {
  return (
    <section className={cn("relative py-20 lg:py-32 bg-black overflow-hidden", className)}>
      {/* Background Video */}
      <div className="absolute inset-0 z-0">
        <video
          autoPlay
          loop
          muted
          playsInline
          className="absolute inset-0 w-full h-full object-cover opacity-30"
        >
          <source src="/ems-training-video.mp4" type="video/mp4" />
          {/* Fallback background image */}
        </video>
        <div className="absolute inset-0 bg-black/60" />
        
        {/* Animated background elements */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-pink-400/8 rounded-full blur-3xl animate-pulse delay-1000" />
      </div>

      <div className="relative z-10 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <Badge className="mb-6 bg-pink-500/20 text-pink-400 border-pink-500/30 text-lg px-6 py-2">
            Pulse20 Wireless EMS Training
          </Badge>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white">
            The Future of{" "}
            <span className="text-pink-400 text-glow-pink">
              Fitness
            </span>{" "}
            is Here
          </h2>
          <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed mb-8">
            Experience revolutionary wireless EMS technology that delivers the equivalent of a 90-minute HIIT session 
            in just 20 minutes. Every major muscle group activated simultaneously for unprecedented results.
          </p>
          
          {/* Video Play Button */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="flex justify-center mb-12"
          >
            <Button
              size="lg"
              className="group bg-pink-500/20 hover:bg-pink-500/30 border-2 border-pink-500 text-pink-400 hover:text-white backdrop-blur-sm px-8 py-4 text-lg"
            >
              <Play className="w-6 h-6 mr-3 group-hover:scale-110 transition-transform" />
              Watch EMS Training Demo
            </Button>
          </motion.div>
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Left Side - EMS Features */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              {emsFeatures.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <Card className="p-6 border-2 border-gray-800 hover:border-pink-500/50 transition-all duration-300 bg-gray-900/50 backdrop-blur-sm group">
                    <div className="flex flex-col items-center text-center space-y-4">
                      <div className="w-16 h-16 rounded-full bg-pink-500/20 flex items-center justify-center group-hover:bg-pink-500/30 transition-colors">
                        <feature.icon className="w-8 h-8 text-pink-400" />
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-white mb-2">{feature.title}</h3>
                        <p className="text-gray-400 text-sm mb-3">{feature.description}</p>
                        <Badge className="bg-pink-500/20 text-pink-400 border-pink-500/30">
                          {feature.stat}
                        </Badge>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Right Side - Benefits List */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="space-y-6"
          >
            <div className="mb-8">
              <h3 className="text-3xl font-bold text-white mb-4">
                Proven <span className="text-pink-400">Benefits</span>
              </h3>
              <p className="text-gray-400 text-lg">
                Pulse20 EMS training delivers measurable results that transform your body and enhance your well-being.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-4">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={benefit}
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="flex items-center gap-4 p-4 rounded-lg bg-gray-900/30 backdrop-blur-sm border border-gray-800 hover:border-pink-500/30 transition-colors"
                >
                  <CheckCircle className="w-6 h-6 text-pink-400 flex-shrink-0" />
                  <span className="text-gray-300">{benefit}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center"
        >
          <Card className="bg-gradient-to-r from-pink-500/10 via-pink-400/10 to-pink-500/10 border-pink-500/20 bg-gray-900/50 backdrop-blur-sm">
            <CardContent className="p-12">
              <h3 className="text-3xl font-bold mb-4 text-white">
                Ready to Experience the <span className="text-pink-400">Revolution</span>?
              </h3>
              <p className="text-gray-400 mb-8 max-w-3xl mx-auto text-lg">
                Start with a FREE EMS Training Session including full assessment. 
                Experience firsthand why 20 minutes of Pulse20 EMS equals hours of conventional training.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/contact" passHref legacyBehavior>
                  <motion.a
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="inline-flex items-center px-8 py-4 bg-pink-500 text-white rounded-lg font-semibold shadow-lg shadow-pink-500/30 hover:bg-pink-400 transition-colors cursor-pointer text-lg"
                  >
                    Book FREE EMS Session
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </motion.a>
                </Link>
                <Button
                  variant="outline"
                  size="lg"
                  className="border-pink-500/50 text-pink-400 hover:bg-pink-500/10 px-8 py-4 text-lg"
                >
                  Learn More About EMS
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
