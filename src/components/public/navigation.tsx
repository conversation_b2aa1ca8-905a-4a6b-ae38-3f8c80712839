"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { NeonButton } from "@/components/ui/neon-button"
import { ThemeToggle } from "@/components/ui/theme-toggle"
import UserAccount from "@/components/user-account"
import {
  Menu,
  X,
  <PERSON><PERSON>bell,
  FileText,
  BookOpen,
  MessageSquare,
  Users
} from "lucide-react"
import { cn } from "@/lib/utils"
import { supabase } from "@/lib/supabase/client"

interface NavigationProps {
  className?: string
}

const navigationItems = [
  { href: "/", label: "Home" },
  { href: "/blog", label: "Blog", icon: FileText },
  { href: "/helpful-materials", label: "Resources", icon: BookOpen },
  { href: "/contact", label: "Contact", icon: MessageSquare },
]

export function Navigation({ className }: NavigationProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Check current user
    const getUser = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        setUser(user)
      } catch (error) {
        console.error('Error fetching user:', error)
      } finally {
        setLoading(false)
      }
    }

    getUser()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null)
      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  // useEffect(() => {
  //   if (!loading && !user) {
  //     if (typeof window !== 'undefined' && window.location.pathname !== '/auth/signin') {
  //       window.location.href = '/auth/signin';
  //     }
  //   }
  // }, [loading, user]);

  return (
    <nav className={cn("sticky top-0 z-50 bg-background/90 backdrop-blur-md border-b border-pink-500/20 w-full", className)}>
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center gap-2 font-bold text-xl">
            
            <span className="text-pink-400 font-bold">
              Pulse20.co.za
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center gap-8">
            {navigationItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className="text-muted-foreground hover:text-pink-400 transition-colors font-medium relative group"
              >
                {item.label}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-pink-400 transition-all duration-300 group-hover:w-full"></span>
              </Link>
            ))}
          </div>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center gap-4">
            {/* <ThemeToggle /> */}
            {loading ? (
              <div className="w-8 h-8 rounded-full bg-muted animate-pulse" />
            ) : user ? (
              <>
                <Button variant="outline" className="border-pink-500/30 text-pink-400 hover:bg-pink-500/10" asChild>
                  <Link href="/dashboard">Dashboard</Link>
                </Button>
                <UserAccount />
              </>
            ) : (
              <>
                {/* <Button variant="outline" className="border-pink-500/30 text-pink-400 hover:bg-pink-500/10" asChild>
                  <Link href="/auth/signin">Sign In</Link>
                </Button>
                <NeonButton variant="member" className="bg-pink-500 hover:bg-pink-400 text-white shadow-lg shadow-pink-500/30" asChild>
                  <Link href="/auth/signup">Join Pulse</Link>
                </NeonButton> */}
                <Button variant="outline" className="border-pink-500/30 text-pink-400 hover:bg-pink-500/10" asChild>
                  <Link href="/contact">Contact Us</Link>
                </Button>
              </>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden flex items-center gap-2">
            {/* <ThemeToggle /> */}
            <Button
              variant="ghost"
              size="sm"
              className="text-pink-400 hover:bg-pink-500/10"
              onClick={() => setIsOpen(!isOpen)}
            >
              {isOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden py-4 border-t border-pink-500/20 bg-background/95">
            <div className="flex flex-col gap-4">
              {navigationItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="flex items-center gap-2 text-muted-foreground hover:text-pink-400 transition-colors font-medium py-2"
                  onClick={() => setIsOpen(false)}
                >
                  {item.icon && <item.icon className="w-4 h-4" />}
                  {item.label}
                </Link>
              ))}
              <div className="flex flex-col gap-2 pt-4 border-t border-pink-500/20">
                {loading ? (
                  <div className="w-full h-10 bg-muted animate-pulse rounded" />
                ) : user ? (
                  <>
                    <Button variant="outline" className="w-full border-pink-500/30 text-pink-400 hover:bg-pink-500/10" asChild>
                      <Link href="/dashboard">Dashboard</Link>
                    </Button>
                    <div className="flex items-center justify-center py-2">
                      <UserAccount />
                    </div>
                  </>
                ) : (
                  <>
                    {/* <Button variant="outline" className="w-full border-pink-500/30 text-pink-400 hover:bg-pink-500/10" asChild>
                      <Link href="/auth/signin">Sign In</Link>
                    </Button>
                    <NeonButton variant="member" className="w-full bg-pink-500 hover:bg-pink-400 text-white" asChild>
                      <Link href="/auth/signup">Join Pulse</Link>
                    </NeonButton> */}
                    <Button variant="outline" className="w-full border-pink-500/30 text-pink-400 hover:bg-pink-500/10" asChild>
                      <Link href="/contact">Contact Us</Link>
                    </Button>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
