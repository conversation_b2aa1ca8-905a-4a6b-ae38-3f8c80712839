'use client'
import React, { useEffect, useState } from "react";

const locations = [
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    subheading: "Pulse20 Mulbarton Studio",
    address: "34 The Broads St, Mulbarton",
    contact: "Phone: +27 83 408 3665 | Email: <EMAIL>",
    images: [
      "/melbuton1.jpg",
      "/melbuton2.jpg",
      "/melbuton3.jpg",
    ],
  },
  {
    name: "Lenasia",
    subheading: "Pulse20 Lenasia Studio",
    address: "Signet Terrace Office Park, Lenasia",
    contact: "Phone: +27 83 408 3665 | Email: <EMAIL>",
    images: [
      "/lenasia1.jpg",
      "/lenasia2.jpg",
      "/lenasia3.jpg",
    ],
  },
];

const LocationsSection = () => {
  // Track current image index for each location
  const [imgIndexes, setImgIndexes] = useState([0, 0]);

  useEffect(() => {
    const interval = setInterval(() => {
      setImgIndexes((prev) => prev.map((idx, i) => (idx + 1) % locations[i].images.length));
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  return (
    <section className="py-20 bg-black w-full">
      <div className="w-full max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-4xl md:text-5xl font-extrabold mb-4 text-pink-400 text-center">Our Locations</h2>
        <p className="text-lg text-gray-300 text-center mb-10">Visit us at either of our state-of-the-art Pulse20 EMS training studios.</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {locations.map((loc, i) => (
            <div key={loc.name} className="relative rounded-2xl shadow-lg overflow-hidden h-80 flex items-end">
              <img
                src={loc.images[imgIndexes[i]]}
                alt={loc.name + " location"}
                className="object-cover w-full h-full absolute inset-0 z-0 transition-all duration-700"
              />
              {/* Gradient overlay for readability */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent z-10" />
              <div className="relative z-20 p-6 w-full flex flex-col items-center">
                <h3 className="text-2xl md:text-3xl font-extrabold text-pink-400 mb-1 text-center drop-shadow-lg">{loc.name}</h3>
                <h4 className="text-lg font-semibold text-pink-300 mb-2 text-center drop-shadow-lg">{loc.subheading}</h4>
                <p className="text-gray-200 mb-2 text-center drop-shadow-lg">{loc.address}</p>
                <p className="text-gray-300 text-center drop-shadow-lg">{loc.contact}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default LocationsSection; 