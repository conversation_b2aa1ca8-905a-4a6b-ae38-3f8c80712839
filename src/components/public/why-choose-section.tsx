"use client"

import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import Image from "next/image"
import { useState, useEffect, useRef } from "react"
import {
  Zap,
  Clock,
  Award,
  Users,
  Heart,
  Target,
  CheckCircle,
  Star
} from "lucide-react"

interface WhyChooseSectionProps {
  className?: string
}

const uniqueFeatures = [
  {
    icon: Zap,
    title: "Revolutionary EMS Technology",
    description: "Experience Pulse20 Wireless EMS training - 20 minutes equals 90 minutes of HIIT with 36,000 muscle contractions. 150x more effective than conventional training.",
    highlight: "Innovation",
    image: "/self-tracking.jpg",
    stats: { label1: "Efficiency", value1: "150x More", label2: "Duration", value2: "20 Minutes" }
  },
  {
    icon: Heart,
    title: "Comprehensive Therapeutic Services",
    description: "Sports massages for enhanced recovery, needling & acupuncture for pain relief, and various cupping therapies including Hijama for detoxification.",
    highlight: "Wellness",
    image: "/one-on-one.jpg",
    stats: { label1: "Recovery", value1: "Enhanced", label2: "Pain Relief", value2: "Natural" }
  },
  {
    icon: Target,
    title: "Functional Movement Science",
    description: "Functional Release with Movement for enhanced mobility and Functional Fascia Movement Science for optimal alignment and posture correction.",
    highlight: "Mobility",
    image: "/booking.jpg",
    stats: { label1: "Posture", value1: "Improved", label2: "Mobility", value2: "Enhanced" }
  },
  {
    icon: Award,
    title: "Complete Body Transformation",
    description: "Decrease waist circumference, reduce cellulite, relieve back pain, improve circulation, and strengthen core muscles - all while being easy on joints.",
    highlight: "Results",
    image: "/community.jpg",
    stats: { label1: "Fat Loss", value1: "Targeted", label2: "Muscle Tone", value2: "Firmer Body" }
  }
]

export function WhyChooseSection({ className }: WhyChooseSectionProps) {
  const [activeFeature, setActiveFeature] = useState(0)
  const [paused, setPaused] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  // Auto-change feature every 5 seconds (unless paused)
  useEffect(() => {
    if (paused) return;
    intervalRef.current = setInterval(() => {
      setActiveFeature((prev) => (prev + 1) % uniqueFeatures.length)
    }, 5000)
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current)
    }
  }, [paused])

  // Mobile tap/hold handlers
  const handleTap = () => {
    setActiveFeature((prev) => (prev + 1) % uniqueFeatures.length)
  }
  const handleTouchStart = () => setPaused(true)
  const handleTouchEnd = () => setPaused(false)

  return (
    <section className={cn("py-20 lg:py-32 bg-gray-900/30 w-full", className)}>
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <Badge className="mb-6 bg-pink-500/20 text-pink-400 border-pink-500/30">
            Why Choose Pulse
          </Badge>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-white">
            Why Choose{" "}
            <span className="text-pink-400">
              Pulse20
            </span>{" "}
            EMS Training?
          </h2>
          <p className="text-lg text-gray-400 max-w-3xl mx-auto leading-relaxed">
            Revolutionary wireless EMS technology combined with comprehensive therapeutic treatments.
            Experience maximum results in minimal time with our science-backed approach to fitness and wellness.
          </p>
        </motion.div>

        {/* Main Content Grid */}
        <div className="flex flex-col lg:grid lg:grid-cols-2 gap-12 items-stretch mb-16">
          {/* Mobile View: Feature Images as Background with Overlayed Text */}
          <div className="block lg:hidden w-full">
            <div
              className="relative w-full aspect-[3/4] min-h-[420px] rounded-2xl overflow-hidden mb-8"
              onClick={handleTap}
              onTouchStart={handleTouchStart}
              onTouchEnd={handleTouchEnd}
              onTouchCancel={handleTouchEnd}
            >
              <Image
                src={uniqueFeatures[activeFeature].image}
                alt={uniqueFeatures[activeFeature].title}
                fill
                className="object-cover"
                priority
              />
              <div className="absolute inset-0 bg-black/60" />
              <div className="absolute inset-0 flex flex-col justify-center items-center text-center px-6 py-10 z-10">
                <Badge className="mb-4 bg-pink-500/20 text-pink-400 border-pink-500/30">
                  {uniqueFeatures[activeFeature].highlight}
                </Badge>
                <h3 className="text-2xl font-bold text-white mb-2 drop-shadow-lg">{uniqueFeatures[activeFeature].title}</h3>
                <p className="text-md text-gray-200 mb-4 drop-shadow-lg">{uniqueFeatures[activeFeature].description}</p>
                <div className="flex flex-col gap-2 w-full max-w-xs mx-auto">
                  <div className="bg-black/70 rounded-lg p-2 text-pink-400 text-sm font-medium">
                    {uniqueFeatures[activeFeature].stats.label1}: <span className="text-white font-bold">{uniqueFeatures[activeFeature].stats.value1}</span>
                  </div>
                  <div className="bg-black/70 rounded-lg p-2 text-pink-400 text-sm font-medium">
                    {uniqueFeatures[activeFeature].stats.label2}: <span className="text-white font-bold">{uniqueFeatures[activeFeature].stats.value2}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Desktop View: Existing Layout (untouched) */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="relative flex h-full order-1 lg:order-none mb-8 lg:mb-0 hidden lg:flex"
          >
            <Card className="overflow-hidden border-2 border-gray-800 hover:border-pink-500/50 transition-all duration-300 bg-gray-900/50 backdrop-blur-sm flex-1 h-full">
              <div className="relative w-full h-[320px] sm:h-[400px] md:h-[500px] lg:h-full min-h-[240px]">
                <motion.div
                  key={activeFeature}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <Image
                    src={uniqueFeatures[activeFeature].image}
                    alt={uniqueFeatures[activeFeature].title}
                    fill
                    className="object-cover"
                  />
                </motion.div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                {/* Dynamic Stats Overlay */}
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="flex justify-between items-end gap-2 flex-wrap">
                    <motion.div
                      key={`stat1-${activeFeature}`}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.1 }}
                      className="bg-black/80 backdrop-blur-sm rounded-lg p-3 border border-pink-500/30 min-w-[100px] mb-2"
                    >
                      <div className="text-pink-400 text-sm font-medium">{uniqueFeatures[activeFeature].stats.label1}</div>
                      <div className="text-white text-xl font-bold">{uniqueFeatures[activeFeature].stats.value1}</div>
                    </motion.div>
                    <motion.div
                      key={`stat2-${activeFeature}`}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                      className="bg-black/80 backdrop-blur-sm rounded-lg p-3 border border-pink-500/30 min-w-[100px] mb-2"
                    >
                      <div className="text-pink-400 text-sm font-medium">{uniqueFeatures[activeFeature].stats.label2}</div>
                      <div className="text-white text-xl font-bold">{uniqueFeatures[activeFeature].stats.value2}</div>
                    </motion.div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>

          {/* Right Side - Features */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="space-y-6 order-2 lg:order-none hidden lg:block"
          >
            {uniqueFeatures.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card
                  className={cn(
                    "p-6 border-2 transition-all duration-300 group bg-gray-900/50 backdrop-blur-sm cursor-pointer",
                    activeFeature === index
                      ? "border-pink-500 bg-pink-500/10"
                      : "border-gray-800 hover:border-pink-500/50",
                    "mb-2 sm:mb-0"
                  )}
                  onClick={() => setActiveFeature(index)}
                >
                  <div className="flex items-start gap-4">
                    <div className={cn(
                      "w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0 transition-colors",
                      activeFeature === index
                        ? "bg-pink-500/30"
                        : "bg-pink-500/20 group-hover:bg-pink-500/30"
                    )}>
                      <feature.icon className="w-6 h-6 text-pink-400" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className={cn(
                          "text-lg font-bold transition-colors",
                          activeFeature === index
                            ? "text-pink-400"
                            : "text-white group-hover:text-pink-400"
                        )}>
                          {feature.title}
                        </h3>
                        <Badge className={cn(
                          "text-xs",
                          activeFeature === index
                            ? "bg-pink-500/30 text-pink-300"
                            : "bg-pink-500/20 text-pink-400"
                        )}>
                          {feature.highlight}
                        </Badge>
                      </div>
                      <p className="text-gray-400 leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Bottom Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-6"
        >
          {[
            { number: "36,000", label: "Muscle Contractions" },
            { number: "20min", label: "Session Duration" },
            { number: "150x", label: "More Effective" },
            { number: "FREE", label: "First EMS Session" }
          ].map((stat, index) => (
            <Card key={stat.label} className="p-6 text-center border-2 border-gray-800 bg-gray-900/50 backdrop-blur-sm">
              <div className="text-2xl md:text-3xl font-bold text-pink-400 mb-2">
                {stat.number}
              </div>
              <div className="text-gray-400 text-sm">
                {stat.label}
              </div>
            </Card>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
