"use client"

import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import Image from "next/image"
import { useState, useEffect, useRef } from "react"
import {
  Zap,
  Clock,
  Award,
  Users,
  Heart,
  Target,
  CheckCircle,
  Star
} from "lucide-react"

interface WhyChooseSectionProps {
  className?: string
}

const uniqueFeatures = [
  {
    icon: Zap,
    title: "Revolutionary EMS Technology",
    description: "Experience Pulse20 Wireless EMS training - 20 minutes equals 90 minutes of HIIT with 36,000 muscle contractions. 150x more effective than conventional training.",
    highlight: "Innovation",
    image: "/self-tracking.jpg",
    stats: { label1: "Efficiency", value1: "150x More", label2: "Duration", value2: "20 Minutes" }
  },
  {
    icon: Heart,
    title: "Comprehensive Therapeutic Services",
    description: "Sports massages for enhanced recovery, needling & acupuncture for pain relief, and various cupping therapies including Hijama for detoxification.",
    highlight: "Wellness",
    image: "/one-on-one.jpg",
    stats: { label1: "Recovery", value1: "Enhanced", label2: "Pain Relief", value2: "Natural" }
  },
  {
    icon: Target,
    title: "Functional Movement Science",
    description: "Functional Release with Movement for enhanced mobility and Functional Fascia Movement Science for optimal alignment and posture correction.",
    highlight: "Mobility",
    image: "/booking.jpg",
    stats: { label1: "Posture", value1: "Improved", label2: "Mobility", value2: "Enhanced" }
  },
  {
    icon: Award,
    title: "Complete Body Transformation",
    description: "Decrease waist circumference, reduce cellulite, relieve back pain, improve circulation, and strengthen core muscles - all while being easy on joints.",
    highlight: "Results",
    image: "/community.jpg",
    stats: { label1: "Fat Loss", value1: "Targeted", label2: "Muscle Tone", value2: "Firmer Body" }
  }
]

export function WhyChooseSection({ className }: WhyChooseSectionProps) {
  const [activeFeature, setActiveFeature] = useState(0)
  const [paused, setPaused] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  // Auto-change feature every 5 seconds (unless paused)
  useEffect(() => {
    if (paused) return;
    intervalRef.current = setInterval(() => {
      setActiveFeature((prev) => (prev + 1) % uniqueFeatures.length)
    }, 5000)
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current)
    }
  }, [paused])

  // Mobile tap/hold handlers
  const handleTap = () => {
    setActiveFeature((prev) => (prev + 1) % uniqueFeatures.length)
  }
  const handleTouchStart = () => setPaused(true)
  const handleTouchEnd = () => setPaused(false)

  return (
    <section className={cn("py-20 lg:py-32 bg-black w-full", className)}>
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <Badge className="mb-6 bg-pink-500/20 text-pink-400 border-pink-500/30">
            Why Choose Pulse
          </Badge>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-white">
            Why Choose{" "}
            <span className="text-pink-400">
              Pulse20
            </span>{" "}
            EMS Training?
          </h2>
          <p className="text-lg text-gray-400 max-w-3xl mx-auto leading-relaxed">
            Revolutionary wireless EMS technology combined with comprehensive therapeutic treatments.
            Experience maximum results in minimal time with our science-backed approach to fitness and wellness.
          </p>
        </motion.div>

        {/* Main Content Grid */}
        <div className="flex flex-col lg:grid lg:grid-cols-2 gap-12 items-stretch mb-16">
          {/* Mobile View: Feature Images as Background with Overlayed Text */}
          <div className="block lg:hidden w-full">
            <div
              className="relative w-full aspect-[3/4] min-h-[420px] rounded-2xl overflow-hidden mb-8"
              onClick={handleTap}
              onTouchStart={handleTouchStart}
              onTouchEnd={handleTouchEnd}
              onTouchCancel={handleTouchEnd}
            >
              <Image
                src={uniqueFeatures[activeFeature].image}
                alt={uniqueFeatures[activeFeature].title}
                fill
                className="object-cover"
                priority
              />
              <div className="absolute inset-0 bg-black/60" />
              <div className="absolute inset-0 flex flex-col justify-center items-center text-center px-6 py-10 z-10">
                <Badge className="mb-4 bg-pink-500/20 text-pink-400 border-pink-500/30">
                  {uniqueFeatures[activeFeature].highlight}
                </Badge>
                <h3 className="text-2xl font-bold text-white mb-2 drop-shadow-lg">{uniqueFeatures[activeFeature].title}</h3>
                <p className="text-md text-gray-200 mb-4 drop-shadow-lg">{uniqueFeatures[activeFeature].description}</p>
                <div className="flex flex-col gap-2 w-full max-w-xs mx-auto">
                  <div className="bg-black/70 rounded-lg p-2 text-pink-400 text-sm font-medium">
                    {uniqueFeatures[activeFeature].stats.label1}: <span className="text-white font-bold">{uniqueFeatures[activeFeature].stats.value1}</span>
                  </div>
                  <div className="bg-black/70 rounded-lg p-2 text-pink-400 text-sm font-medium">
                    {uniqueFeatures[activeFeature].stats.label2}: <span className="text-white font-bold">{uniqueFeatures[activeFeature].stats.value2}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Desktop View: Existing Layout (untouched) */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="relative flex h-full order-1 lg:order-none mb-8 lg:mb-0 hidden lg:flex"
          >
            <Card className="overflow-hidden border-2 border-gray-800 hover:border-pink-500/50 transition-all duration-300 bg-gray-900/50 backdrop-blur-sm flex-1 h-full">
              <div className="relative w-full h-[320px] sm:h-[400px] md:h-[500px] lg:h-full min-h-[240px]">
                <motion.div
                  key={activeFeature}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <Image
                    src={uniqueFeatures[activeFeature].image}
                    alt={uniqueFeatures[activeFeature].title}
                    fill
                    className="object-cover"
                  />
                </motion.div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                {/* Dynamic Stats Overlay */}
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="flex justify-between items-end gap-2 flex-wrap">
                    <motion.div
                      key={`stat1-${activeFeature}`}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.1 }}
                      className="bg-black/80 backdrop-blur-sm rounded-lg p-3 border border-pink-500/30 min-w-[100px] mb-2"
                    >
                      <div className="text-pink-400 text-sm font-medium">{uniqueFeatures[activeFeature].stats.label1}</div>
                      <div className="text-white text-xl font-bold">{uniqueFeatures[activeFeature].stats.value1}</div>
                    </motion.div>
                    <motion.div
                      key={`stat2-${activeFeature}`}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                      className="bg-black/80 backdrop-blur-sm rounded-lg p-3 border border-pink-500/30 min-w-[100px] mb-2"
                    >
                      <div className="text-pink-400 text-sm font-medium">{uniqueFeatures[activeFeature].stats.label2}</div>
                      <div className="text-white text-xl font-bold">{uniqueFeatures[activeFeature].stats.value2}</div>
                    </motion.div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>

          {/* Right Side - Features */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="space-y-6 order-2 lg:order-none hidden lg:block"
          >
            {uniqueFeatures.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card
                  className={cn(
                    "relative overflow-hidden border-2 transition-all duration-500 group cursor-pointer",
                    activeFeature === index
                      ? "border-pink-500 bg-pink-500/10 scale-105 shadow-lg shadow-pink-500/20"
                      : "border-gray-800 hover:border-pink-500/50 bg-gray-900/30 hover:bg-gray-900/50",
                    "mb-2 sm:mb-0"
                  )}
                  onClick={() => setActiveFeature(index)}
                >
                  {/* Background Image with Overlay */}
                  <div className="absolute inset-0 opacity-20">
                    <div
                      className="absolute inset-0 bg-cover bg-center"
                      style={{ backgroundImage: `url(${feature.image})` }}
                    />
                    <div className={cn(
                      "absolute inset-0 transition-all duration-500",
                      activeFeature === index
                        ? "bg-gradient-to-r from-pink-900/80 to-pink-800/60"
                        : "bg-gradient-to-r from-black/90 to-gray-900/80"
                    )} />
                  </div>

                  <div className="relative z-10 p-6">
                    <div className="flex items-start gap-4">
                      <div className={cn(
                        "w-14 h-14 rounded-full flex items-center justify-center transition-all duration-300 backdrop-blur-sm border",
                        activeFeature === index
                          ? "bg-pink-500/80 text-white border-pink-400/50 shadow-lg"
                          : "bg-pink-500/20 text-pink-400 group-hover:bg-pink-500/40 border-pink-500/30"
                      )}>
                        {/* SVG Icons based on feature type */}
                        {feature.icon === Zap && (
                          <svg className="w-7 h-7" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M13 10V3L4 14h7v7l9-11h-7z"/>
                          </svg>
                        )}
                        {feature.icon === Heart && (
                          <svg className="w-7 h-7" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                          </svg>
                        )}
                        {feature.icon === Target && (
                          <svg className="w-7 h-7" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm0-14c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm0 10c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                          </svg>
                        )}
                        {feature.icon === Award && (
                          <svg className="w-7 h-7" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                          </svg>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-3">
                          <h3 className="text-lg font-bold text-white">{feature.title}</h3>
                          <Badge className={cn(
                            "text-xs backdrop-blur-sm",
                            activeFeature === index
                              ? "bg-pink-400/20 text-pink-200 border-pink-300/40"
                              : "bg-gray-700/50 text-gray-300 border-gray-500/30"
                          )}>
                            {feature.highlight}
                          </Badge>
                        </div>
                        <p className="text-gray-300 text-sm leading-relaxed mb-4">
                          {feature.description}
                        </p>
                        <div className="grid grid-cols-2 gap-2">
                          <div className="bg-black/40 backdrop-blur-sm rounded-lg p-2 border border-pink-500/20">
                            <div className="text-pink-400 text-xs font-medium">{feature.stats.label1}</div>
                            <div className="text-white text-sm font-bold">{feature.stats.value1}</div>
                          </div>
                          <div className="bg-black/40 backdrop-blur-sm rounded-lg p-2 border border-pink-500/20">
                            <div className="text-pink-400 text-xs font-medium">{feature.stats.label2}</div>
                            <div className="text-white text-sm font-bold">{feature.stats.value2}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Active Indicator */}
                  {activeFeature === index && (
                    <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-pink-500 to-pink-400" />
                  )}
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Bottom Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-6"
        >
          {[
            {
              number: "36,000",
              label: "Muscle Contractions",
              icon: <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24"><path d="M13 10V3L4 14h7v7l9-11h-7z"/></svg>
            },
            {
              number: "20min",
              label: "Session Duration",
              icon: <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24"><path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"/></svg>
            },
            {
              number: "150x",
              label: "More Effective",
              icon: <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>
            },
            {
              number: "R500",
              label: "Intro Consultation",
              icon: <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24"><path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" /></svg>
            }
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card className="relative overflow-hidden p-6 text-center border-2 border-gray-800 bg-gray-900/50 backdrop-blur-sm hover:border-pink-500/50 transition-all duration-300 group">
                {/* Background Gradient */}
                <div className="absolute inset-0 bg-gradient-to-br from-pink-500/5 to-pink-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                <div className="relative z-10">
                  <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-pink-500/20 flex items-center justify-center text-pink-400 group-hover:bg-pink-500/30 transition-colors">
                    {stat.icon}
                  </div>
                  <div className="text-2xl md:text-3xl font-bold text-pink-400 mb-2 group-hover:text-pink-300 transition-colors">
                    {stat.number}
                  </div>
                  <div className="text-gray-400 text-sm group-hover:text-gray-300 transition-colors">
                    {stat.label}
                  </div>
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
