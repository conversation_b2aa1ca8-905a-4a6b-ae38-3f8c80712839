"use client"

import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { NeonButton } from "@/components/ui/neon-button"
import { ArrowRight, Play, Star, Users, Trophy, Zap, Heart } from "lucide-react"
import Link from "next/link"
import { cn } from "@/lib/utils"
import Image from "next/image"

interface HeroSectionProps {
  className?: string
}

export function HeroSection({ className }: HeroSectionProps) {
  const serviceProfiles = [
    {
      name: "Pulse20 Wireless EMS",
      role: "20 Minutes = 90 Minutes HIIT",
      image: "/workout.jpg",
      quote: "⚡ 36,000 muscle contractions in 20 minutes - 150x more effective than traditional training"
    },
    {
      name: "Sports Massage",
      role: "Enhanced Recovery",
      image: "/massage.jpg",
      quote: "💆‍♂️ Professional sports massage for enhanced recovery and performance optimization"
    },
    {
      name: "Needling & Acupuncture",
      role: "Pain Relief Therapy",
      image: "/needles.jpg",
      quote: "🎯 Targeted needling and acupuncture for natural pain relief and healing"
    },
    {
      name: "Dynamic Cupping",
      role: "Circulation & Detox",
      image: "/lenasia1.jpg",
      quote: "🔥 Fire/Ventosa/Moxa and Hijama cupping for improved circulation and detoxification"
    },
    {
      name: "Functional Movement",
      role: "Mobility & Alignment",
      image: "/wellness.jpg",
      quote: "🎯 Functional Release with Movement and Fascia Science for optimal body alignment"
    }
  ]

  return (
    <section className={cn("relative overflow-hidden bg-black min-h-[calc(100vh-56px)]", className)}>
      {/* Pure Black Background with Subtle Pink Glow */}
      <div className="absolute inset-0 bg-black" />
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-pink-400/8 rounded-full blur-3xl" />

      <div className="relative w-full max-w-7xl mx-auto px-2 sm:px-6 lg:px-8 py-0">
        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 min-h-screen items-center">

          {/* Left Content - Typography */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="lg:col-span-5 space-y-8 z-10"
          >
            <div className="space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-pink-500/10 border border-pink-500/20 backdrop-blur-sm"
              >
                <Zap className="w-4 h-4 text-pink-400" />
                <span className="text-sm font-medium text-pink-400">Pulse20 Wireless EMS Training</span>
              </motion.div>

              <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold leading-tight text-white">
                20 Minutes.
                <br />
                <span className="text-pink-400">
                  Maximum Results.
                </span>
              </h1>

              <p className="text-xl text-gray-300 max-w-lg leading-relaxed">
                Revolutionary EMS training delivering 36,000 muscle contractions in just 20 minutes.
                Combined with therapeutic services including sports massage, cupping, and needling for complete wellness.
              </p>
            </div>

            {/* CTA Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <Link href="/contact">
                <NeonButton
                  variant="member"
                  size="lg"
                  className="group bg-pink-500 hover:bg-pink-400 text-white px-8 py-4 text-lg font-semibold"
                >
                  Create Your Pulse
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </NeonButton>
              </Link>
            </motion.div>
          </motion.div>

          {/* Right Content - Member Profile Cards */}
          <div className="lg:col-span-7 relative">
            <div className="grid grid-cols-2 gap-4 h-[600px]">

              {/* Large Featured Card */}
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="relative overflow-hidden rounded-2xl group cursor-pointer"
              >
                <div className="relative h-full">
                  <Image
                    src={serviceProfiles[0].image}
                    alt={serviceProfiles[0].name}
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-110"
                    onError={(e) => {
                      e.currentTarget.src = '/placeholder-image.jpg'
                    }}
                  />

                  {/* Content Overlay */}
                  <div className="absolute bottom-6 left-6 right-6 text-white">
                    <h3 className="text-2xl font-bold mb-1">{serviceProfiles[0].name}</h3>
                    <p className="text-pink-200 text-sm mb-3">{serviceProfiles[0].role}</p>
                    <p className="text-sm opacity-90 leading-relaxed">{serviceProfiles[0].quote}</p>
                  </div>
                </div>
              </motion.div>

              {/* Smaller Cards Column */}
              <div className="space-y-4">
                {serviceProfiles.slice(1, 4).map((profile, index) => (
                  <motion.div
                    key={profile.name}
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.8, delay: 0.5 + index * 0.2 }}
                    className="relative overflow-hidden rounded-xl h-[140px] group cursor-pointer"
                  >
                    <div className="relative h-full">
                      <Image
                        src={profile.image}
                        alt={profile.name}
                        fill
                        className="object-cover transition-transform duration-700 group-hover:scale-110"
                        onError={(e) => {
                          e.currentTarget.src = '/placeholder-image.jpg'
                        }}
                      />

                      <div className="absolute bottom-3 left-3 right-3 text-white">
                        <h4 className="font-bold text-lg">{profile.name}</h4>
                        <p className="text-pink-200 text-xs">{profile.role}</p>
                      </div>

                      {/* Floating Stats */}
                      <div className="absolute top-3 right-3 bg-pink-500/90 backdrop-blur-sm rounded-lg px-2 py-1">
                        <div className="flex items-center gap-1">
                          <Heart className="w-3 h-3 text-white" />
                          <span className="text-white text-xs font-bold">4.9</span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}

                {/* Bottom Wide Card */}
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.9 }}
                  className="relative overflow-hidden rounded-xl h-[140px] group cursor-pointer"
                >
                  <div className="relative h-full">
                    <Image
                      src={serviceProfiles[4].image}
                      alt={serviceProfiles[4].name}
                      fill
                      className="object-cover transition-transform duration-700 group-hover:scale-110"
                    />

                    <div className="absolute bottom-3 left-3 right-3 text-white">
                      <h4 className="font-bold text-lg">{serviceProfiles[4].name}</h4>
                      <p className="text-pink-200 text-xs">{serviceProfiles[4].role}</p>
                    </div>

                    {/* Live Indicator */}
                    <div className="absolute top-3 left-3 flex items-center gap-2 bg-black/80 backdrop-blur-sm rounded-full px-3 py-1">
                      <div className="w-2 h-2 bg-pink-400 rounded-full animate-pulse" />
                      <span className="text-white text-xs font-medium">Available Now</span>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
