'use client'
import React, { useEffect, useState } from "react";
import Link from "next/link";

// Membership Plans based on contract pricing
const membershipPlans = {
  starter: {
    label: "Starter",
    duration: "3 Months",
    price: "R350",
    priceNote: "per session",
    features: ["3-month commitment", "Up to 2 make-up sessions", "Professional EMS training", "Progress tracking"],
    bgImage: "/melbuton1.jpg"
  },
  express: {
    label: "Express", 
    duration: "6 Months",
    price: "R300",
    priceNote: "per session",
    features: ["6-month commitment", "Up to 4 make-up sessions", "All Starter features", "Better value per session"],
    bgImage: "/lenasia1.jpg"
  },
  diamond: {
    label: "Diamond",
    duration: "12 Months", 
    price: "R250",
    priceNote: "per session",
    features: ["12-month commitment", "Up to 6 make-up sessions", "Best value per session", "Priority booking"],
    bgImage: "/wellness.jpg"
  }
};

const introOffer = {
  label: "Intro Consultation",
  originalPrice: "R1000",
  specialPrice: "R500",
  features: ["Full body assessment", "BMI check & fitness evaluation", "Complete EMS training session", "Personalized consultation", "No commitment required"],
};

const LiveClock = () => {
  const [time, setTime] = useState(new Date());
  useEffect(() => {
    const interval = setInterval(() => setTime(new Date()), 1000);
    return () => clearInterval(interval);
  }, []);
  const formatted = time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
  return (
    <div className="text-3xl md:text-4xl font-mono font-extrabold text-pink-400 mb-2 text-center">
      {formatted}
    </div>
  );
};

// Modern Card-Based Design Option 3
const PricingSpecialsSectionModern = () => (
  <section className="relative py-20 bg-black w-full">
    {/* Subtle background pattern */}
    <div className="absolute inset-0 opacity-5">
      <div className="absolute inset-0" style={{
        backgroundImage: `radial-gradient(circle at 25% 25%, #FF10F0 0%, transparent 50%),
                         radial-gradient(circle at 75% 75%, #FF10F0 0%, transparent 50%)`
      }} />
    </div>

    <div className="relative z-10 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      
      {/* Header */}
      <div className="text-center mb-16">
        <div className="inline-flex items-center gap-2 bg-pink-500/10 border border-pink-500/20 rounded-full px-6 py-3 mb-8">
          <span className="text-pink-400 font-semibold">💪 PULSE20 EMS TRAINING</span>
        </div>
        <h2 className="text-4xl md:text-5xl lg:text-6xl font-extrabold mb-6">
          <span className="text-white">Choose Your </span>
          <span className="text-transparent bg-clip-text bg-gradient-to-r from-pink-400 via-pink-500 to-pink-600">
            Transformation
          </span>
        </h2>
        <p className="text-xl text-gray-400 max-w-3xl mx-auto">
          Revolutionary EMS training packages designed for every fitness goal and commitment level
        </p>
      </div>

      {/* Hero Intro Offer */}
      <div className="mb-20">
        <div className="relative max-w-4xl mx-auto">
          <div className="absolute inset-0 bg-gradient-to-r from-pink-500/20 to-pink-400/20 rounded-3xl blur-xl"></div>
          <div className="relative bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-sm rounded-3xl border border-pink-500/30 overflow-hidden">
            
            {/* Background Image */}
            <div className="absolute inset-0">
              <div 
                className="absolute inset-0 bg-cover bg-center opacity-20"
                style={{ backgroundImage: 'url(/massage.jpg)' }}
              />
              <div className="absolute inset-0 bg-gradient-to-r from-black/80 to-pink-900/40" />
            </div>
            
            <div className="relative z-10 p-8 lg:p-12">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                
                {/* Left - Offer Details */}
                <div className="space-y-6">
                  <div>
                    <span className="inline-block bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-2 rounded-full text-sm font-bold mb-4 animate-pulse">
                      🔥 LIMITED TIME
                    </span>
                    <h3 className="text-3xl lg:text-4xl font-bold text-white mb-4">
                      {introOffer.label}
                    </h3>
                    <p className="text-gray-300 text-lg">
                      Perfect introduction to Pulse20 EMS training with complete assessment and full session
                    </p>
                  </div>
                  
                  <div className="flex items-center gap-6">
                    <div className="text-center">
                      <div className="text-2xl text-gray-400 line-through">{introOffer.originalPrice}</div>
                      <div className="text-sm text-gray-500">Normal Price</div>
                    </div>
                    <div className="text-6xl text-pink-400 font-black">→</div>
                    <div className="text-center">
                      <div className="text-4xl lg:text-5xl font-extrabold text-pink-400">{introOffer.specialPrice}</div>
                      <div className="text-sm text-green-400 font-semibold">Save R500!</div>
                    </div>
                  </div>
                  
                  <Link href="/contact" passHref legacyBehavior>
                    <button className="w-full lg:w-auto px-8 py-4 bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-400 hover:to-pink-500 text-white rounded-xl font-bold shadow-lg transition-all transform hover:scale-105 text-lg">
                      🚀 Book Now - R500
                    </button>
                  </Link>
                </div>
                
                {/* Right - Features Grid */}
                <div className="grid grid-cols-2 gap-4">
                  {introOffer.features.map((feature, index) => (
                    <div key={index} className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10 text-center">
                      <div className="text-2xl mb-2">
                        {index === 0 ? "📊" : index === 1 ? "⚖️" : index === 2 ? "⚡" : index === 3 ? "👨‍⚕️" : "✨"}
                      </div>
                      <div className="text-gray-200 text-sm font-medium">{feature}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Membership Cards */}
      <div className="mb-16">
        <div className="text-center mb-12">
          <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Membership <span className="text-pink-400">Packages</span>
          </h3>
          <p className="text-gray-400 text-lg">Long-term training solutions for serious fitness goals</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {Object.entries(membershipPlans).map(([key, plan], index) => (
            <div key={key} className={`
              group relative overflow-hidden rounded-2xl transition-all duration-500 hover:scale-105
              ${key === 'express' ? 'ring-2 ring-pink-500 ring-opacity-50 scale-105' : ''}
            `}>
              
              {/* Background Image */}
              <div className="absolute inset-0">
                <div 
                  className="absolute inset-0 bg-cover bg-center"
                  style={{ backgroundImage: `url(${plan.bgImage})` }}
                />
                <div className={`
                  absolute inset-0 transition-all duration-500
                  ${key === 'express' 
                    ? 'bg-gradient-to-t from-pink-900/90 via-pink-800/70 to-pink-900/50' 
                    : 'bg-gradient-to-t from-black/90 via-gray-900/70 to-gray-800/50'
                  }
                  group-hover:from-pink-900/95 group-hover:via-pink-800/75
                `} />
              </div>
              
              {/* Content */}
              <div className="relative z-10 p-8 h-full flex flex-col">
                
                {/* Header */}
                <div className="text-center mb-6">
                  {key === 'express' && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-pink-500 text-white px-4 py-1 rounded-full text-sm font-bold">
                        ⭐ MOST POPULAR
                      </span>
                    </div>
                  )}
                  
                  <div className="text-4xl mb-4">
                    {key === 'starter' ? '🚀' : key === 'express' ? '⚡' : '💎'}
                  </div>
                  <h4 className="text-2xl font-bold text-white mb-2">{plan.label}</h4>
                  <p className="text-pink-300 font-medium">{plan.duration}</p>
                </div>
                
                {/* Pricing */}
                <div className="text-center mb-6">
                  <div className="text-4xl font-extrabold text-white mb-1">{plan.price}</div>
                  <div className="text-gray-300">{plan.priceNote}</div>
                  {key === 'diamond' && (
                    <div className="mt-2 inline-block bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-sm font-semibold">
                      Best Value
                    </div>
                  )}
                </div>
                
                {/* Features */}
                <div className="flex-grow space-y-3 mb-6">
                  {plan.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center gap-3">
                      <div className="w-5 h-5 rounded-full bg-pink-500 flex items-center justify-center flex-shrink-0">
                        <span className="text-white text-xs">✓</span>
                      </div>
                      <span className="text-gray-200 text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
                
                {/* CTA Button */}
                <Link href="/contact" passHref legacyBehavior>
                  <button className={`
                    w-full px-6 py-3 rounded-xl font-semibold transition-all
                    ${key === 'express' 
                      ? 'bg-pink-500 hover:bg-pink-400 text-white shadow-lg' 
                      : 'border-2 border-pink-500 text-pink-400 hover:bg-pink-500/10'
                    }
                  `}>
                    Choose {plan.label}
                  </button>
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Bottom CTA */}
      <div className="text-center bg-gray-900/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-800">
        <LiveClock />
        <p className="text-gray-400 italic mb-6">Time is your most valuable asset - invest it wisely</p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/contact" passHref legacyBehavior>
            <button className="px-8 py-3 bg-pink-500 hover:bg-pink-400 text-white rounded-xl font-semibold transition-all">
              Get Started Today
            </button>
          </Link>
          <Link href="/terms-of-service" passHref legacyBehavior>
            <button className="px-8 py-3 border border-gray-600 text-gray-400 hover:text-white hover:border-gray-500 rounded-xl font-semibold transition-all">
              View Terms
            </button>
          </Link>
        </div>
      </div>
    </div>
  </section>
);

export default PricingSpecialsSectionModern;
