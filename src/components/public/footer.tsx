import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  <PERSON><PERSON><PERSON>,
  MapPin,
  Phone,
  Mail,
  Clock,
  Facebook,
  Instagram,
  Twitter,
  Youtube
} from "lucide-react"

export function Footer() {
  // Use static year to prevent hydration mismatch
  const currentYear = 2024

  return (
    <footer className="bg-black border-t border-pink-500/20 w-full">
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand Section */}
          <div className="space-y-4">
            <Link href="/" className="flex items-center gap-2 font-bold text-xl">
              <div className="w-8 h-8 rounded-full bg-pink-500 shadow-lg shadow-pink-500/50 flex items-center justify-center">
                <Dumbbell className="w-4 h-4 text-white" />
              </div>
              <span className="text-pink-400 font-bold">
                Pulse20.co.za
              </span>
            </Link>
            <p className="text-gray-400 text-sm leading-relaxed">
              Revolutionary Pulse20 Wireless EMS Training delivering 36,000 muscle contractions
              in 20 minutes. Combined with therapeutic services for complete wellness.
            </p>
            <div className="flex items-center gap-3">
              <Button variant="ghost" size="sm" className="w-8 h-8 p-0 text-gray-400 hover:text-pink-400 hover:bg-pink-500/10">
                <Facebook className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" className="w-8 h-8 p-0 text-gray-400 hover:text-pink-400 hover:bg-pink-500/10">
                <Instagram className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" className="w-8 h-8 p-0 text-gray-400 hover:text-pink-400 hover:bg-pink-500/10">
                <Twitter className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" className="w-8 h-8 p-0 text-gray-400 hover:text-pink-400 hover:bg-pink-500/10">
                <Youtube className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="font-semibold text-white">Quick Links</h3>
            <div className="space-y-2">
              <Link href="/blog" className="block text-sm text-gray-400 hover:text-pink-400 transition-colors">
                Wellness Blog
              </Link>
              <Link href="/helpful-materials" className="block text-sm text-gray-400 hover:text-pink-400 transition-colors">
                Helpful Materials
              </Link>
              <Link href="/contact" className="block text-sm text-gray-400 hover:text-pink-400 transition-colors">
                Contact Us
              </Link>
              <Link href="/auth/signin" className="block text-sm text-gray-400 hover:text-pink-400 transition-colors">
                Member Portal
              </Link>
              <Link href="/admin" className="block text-sm text-gray-400 hover:text-pink-400 transition-colors">
                Staff Portal
              </Link>
            </div>
          </div>

          {/* Legal Links */}
          <div className="space-y-4">
            <h3 className="font-semibold text-white">Legal</h3>
            <div className="space-y-2">
              <Link href="/terms-of-service" className="block text-sm text-gray-400 hover:text-pink-400 transition-colors">
                Terms of Service
              </Link>
              <Link href="/privacy-policy" className="block text-sm text-gray-400 hover:text-pink-400 transition-colors">
                Privacy Policy
              </Link>
              <Link href="/liability-waiver" className="block text-sm text-gray-400 hover:text-pink-400 transition-colors">
                Liability Waiver
              </Link>
            </div>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="font-semibold text-white">Contact Info</h3>
            <div className="space-y-3">
              <div className="flex items-start gap-2">
                <MapPin className="w-4 h-4 text-pink-400 mt-0.5" />
                <div className="text-sm text-gray-400">
                  <strong>Lenasia Studio:</strong><br />
                  Signet Terrace Office Park, Lenasia<br />
                  <strong>Mulbarton Studio:</strong><br />
                  34 The Broads St, Mulbarton
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="w-4 h-4 text-pink-400" />
                <span className="text-sm text-gray-400">+27 83 408 3665</span>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-pink-400" />
                <span className="text-sm text-gray-400"><EMAIL></span>
              </div>
              <div className="flex items-start gap-2">
                <Clock className="w-4 h-4 text-pink-400 mt-0.5" />
                <div className="text-sm text-gray-400">
                  Mon-Fri: 5AM-10PM<br />
                  Sat-Sun: 6AM-8PM<br />
                  <span className="text-pink-400 font-medium">24/7 Premium Access</span>
                </div>
              </div>
            </div>
          </div>

          {/* Newsletter */}
          <div className="space-y-4">
            <h3 className="font-semibold text-white">Stay Updated</h3>
            <p className="text-sm text-gray-400">
              Get the latest fitness tips, wellness advice, and gym updates delivered to your inbox.
            </p>
            <div className="space-y-2">
              <Input placeholder="Enter your email" className="text-sm bg-gray-900 border-pink-500/30 text-white placeholder:text-gray-500 focus:border-pink-400" />
              <Button className="w-full bg-pink-500 hover:bg-pink-400 text-white text-sm shadow-lg shadow-pink-500/30">
                Subscribe
              </Button>
            </div>
            <p className="text-xs text-gray-500">
              By subscribing, you agree to our Privacy Policy and Terms of Service.
            </p>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-pink-500/20 mt-12 pt-8">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <div className="text-sm text-gray-400">
              © {currentYear} Pulse20.co.za. All rights reserved.
            </div>
            <div className="flex items-center gap-6 text-sm">
              <Link href="/privacy-policy" className="text-gray-400 hover:text-pink-400 transition-colors">
                Privacy Policy
              </Link>
              <Link href="/terms-of-service" className="text-gray-400 hover:text-pink-400 transition-colors">
                Terms of Service
              </Link>
              <Link href="/liability-waiver" className="text-gray-400 hover:text-pink-400 transition-colors">
                Liability Waiver
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
