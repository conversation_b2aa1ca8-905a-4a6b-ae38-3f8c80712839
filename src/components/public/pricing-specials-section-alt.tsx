'use client'
import React, { useEffect, useState } from "react";
import Link from "next/link";

// Membership Plans based on contract pricing
const membershipPlans = {
  starter: {
    label: "Starter Package",
    duration: "3 Month Contract",
    price: "R350",
    priceNote: "per session",
    features: ["3-month commitment", "Up to 2 make-up sessions", "Professional EMS training", "Progress tracking"],
    icon: "🚀",
    color: "blue"
  },
  express: {
    label: "Express Package", 
    duration: "6 Month Contract",
    price: "R300",
    priceNote: "per session",
    features: ["6-month commitment", "Up to 4 make-up sessions", "All Starter features", "Better value per session"],
    icon: "⚡",
    color: "pink"
  },
  diamond: {
    label: "Diamond Package",
    duration: "12 Month Contract", 
    price: "R250",
    priceNote: "per session",
    features: ["12-month commitment", "Up to 6 make-up sessions", "Best value per session", "Priority booking"],
    icon: "💎",
    color: "yellow"
  }
};

const introOffer = {
  label: "Intro Consultation",
  originalPrice: "R1000",
  specialPrice: "R500",
  features: ["Full body assessment", "BMI check & fitness evaluation", "Complete EMS training session", "Personalized consultation", "No commitment required"],
};

const LiveClock = () => {
  const [time, setTime] = useState(new Date());
  useEffect(() => {
    const interval = setInterval(() => setTime(new Date()), 1000);
    return () => clearInterval(interval);
  }, []);
  const formatted = time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
  return (
    <div className="text-4xl md:text-5xl font-mono font-extrabold text-pink-400 mb-4 text-center">
      {formatted}
    </div>
  );
};

// Alternative Design Option 2: Split Screen Layout
const PricingSpecialsSectionAlt = () => (
  <section className="relative min-h-screen bg-black w-full overflow-hidden">
    {/* Split Background */}
    <div className="absolute inset-0 grid grid-cols-1 lg:grid-cols-2">
      {/* Left Side - Dark with EMS imagery */}
      <div className="relative">
        <div 
          className="absolute inset-0 bg-cover bg-center opacity-40"
          style={{ backgroundImage: 'url(/workout.jpg)' }}
        />
        <div className="absolute inset-0 bg-gradient-to-br from-black via-black/90 to-pink-900/30" />
      </div>
      
      {/* Right Side - Gradient */}
      <div className="bg-gradient-to-bl from-pink-900/20 via-black to-black" />
    </div>

    <div className="relative z-10 min-h-screen flex items-center">
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          
          {/* Left Side - Intro Offer */}
          <div className="space-y-8">
            <div>
              <div className="inline-block mb-6">
                <span className="bg-gradient-to-r from-pink-500 to-red-500 text-white px-6 py-3 rounded-full text-sm font-bold animate-pulse">
                  🔥 SPECIAL INTRO OFFER
                </span>
              </div>
              
              <h2 className="text-4xl lg:text-5xl font-extrabold text-white mb-6 leading-tight">
                Experience <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-pink-400 to-pink-600">
                  Pulse20 EMS
                </span>
              </h2>
              
              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                Revolutionary 20-minute training sessions that deliver the results of 90 minutes of traditional HIIT training.
              </p>
            </div>

            {/* Pricing Display */}
            <div className="bg-gradient-to-r from-pink-500/10 to-pink-400/5 border border-pink-500/30 rounded-2xl p-8">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <p className="text-gray-400 text-lg line-through">{introOffer.originalPrice}</p>
                  <p className="text-5xl font-extrabold text-pink-400">{introOffer.specialPrice}</p>
                  <p className="text-green-400 font-semibold">Save R500 Today!</p>
                </div>
                <div className="text-6xl opacity-20">💪</div>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                {introOffer.features.slice(0, 4).map((feature, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <span className="text-pink-400">✓</span>
                    <span className="text-gray-300 text-sm">{feature}</span>
                  </div>
                ))}
              </div>
              
              <Link href="/contact" passHref legacyBehavior>
                <button className="w-full px-8 py-4 bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-400 hover:to-pink-500 text-white rounded-xl font-bold shadow-lg transition-all transform hover:scale-105 text-lg">
                  Book Intro Session - R500
                </button>
              </Link>
            </div>
          </div>

          {/* Right Side - Membership Packages */}
          <div className="space-y-8">
            <div className="text-center lg:text-left">
              <h3 className="text-3xl lg:text-4xl font-bold text-white mb-4">
                Membership <span className="text-pink-400">Packages</span>
              </h3>
              <p className="text-gray-400 text-lg">Choose your commitment level for ongoing training</p>
            </div>

            <div className="space-y-6">
              {Object.entries(membershipPlans).map(([key, plan]) => (
                <div key={key} className={`
                  relative group cursor-pointer transition-all duration-300 hover:scale-105
                  ${key === 'express' ? 'ring-2 ring-pink-500 ring-opacity-50' : ''}
                `}>
                  <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl p-6 border border-gray-700 hover:border-pink-500/50 transition-all">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className={`
                          w-12 h-12 rounded-full flex items-center justify-center text-2xl
                          ${plan.color === 'pink' ? 'bg-pink-500/20' : 
                            plan.color === 'yellow' ? 'bg-yellow-500/20' : 'bg-blue-500/20'}
                        `}>
                          {plan.icon}
                        </div>
                        <div>
                          <h4 className="text-xl font-bold text-white">{plan.label}</h4>
                          <p className="text-gray-400 text-sm">{plan.duration}</p>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="text-2xl font-extrabold text-pink-400">{plan.price}</div>
                        <p className="text-gray-400 text-sm">{plan.priceNote}</p>
                        {key === 'express' && (
                          <span className="inline-block mt-1 bg-pink-500 text-white px-2 py-1 rounded text-xs font-semibold">
                            POPULAR
                          </span>
                        )}
                        {key === 'diamond' && (
                          <span className="inline-block mt-1 bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold">
                            BEST VALUE
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <div className="mt-4 grid grid-cols-2 gap-2">
                      {plan.features.slice(0, 2).map((feature, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <span className="text-pink-400 text-xs">•</span>
                          <span className="text-gray-300 text-xs">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {key === 'express' && (
                    <div className="absolute -top-2 -right-2">
                      <div className="bg-pink-500 text-white px-3 py-1 rounded-full text-xs font-bold animate-bounce">
                        RECOMMENDED
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            <div className="text-center">
              <Link href="/contact" passHref legacyBehavior>
                <button className="px-8 py-3 border-2 border-pink-500 text-pink-400 hover:bg-pink-500/10 rounded-xl font-semibold transition-all">
                  Compare All Packages
                </button>
              </Link>
            </div>
          </div>
        </div>

        {/* Bottom Stats */}
        <div className="mt-20 text-center">
          <LiveClock />
          <p className="text-gray-400 italic mb-8">Every second counts in your fitness journey</p>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {[
              { number: "20", label: "Minutes", unit: "min" },
              { number: "36K", label: "Contractions", unit: "" },
              { number: "150x", label: "More Effective", unit: "" },
              { number: "R500", label: "Intro Price", unit: "" }
            ].map((stat, index) => (
              <div key={index} className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-4 border border-gray-800">
                <div className="text-2xl font-bold text-pink-400">{stat.number}</div>
                <div className="text-gray-400 text-sm">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  </section>
);

export default PricingSpecialsSectionAlt;
