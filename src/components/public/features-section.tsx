"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import Image from "next/image"
import Link from "next/link"

interface FeaturesSectionProps {
  className?: string
}

const features = [
  {
    image: "/pulse-1.jpg",
    alt: "EMS Training Efficiency",
    title: "20 Minutes = 90 Minutes HIIT",
    description: "Experience 36,000 muscle contractions in just 20 minutes - equivalent to a 90-minute HIIT session with 150x more muscle activation than traditional training."
  },
  {
    image: "/pulse-2.jpg",
    alt: "Therapeutic Services",
    title: "Sports Massage & Recovery",
    description: "Accelerate recovery with our sports massages, needling & acupuncture, and dynamic cupping for enhanced circulation and pain relief."
  },
  {
    image: "/pulse-3.jpg",
    alt: "Cupping Therapy",
    title: "Comprehensive Cupping Therapy",
    description: "From Fire/Ventosa/Moxa cupping for deep relaxation to Hijama (Wet Cupping) for detoxification and functional cupping for targeted relief."
  },
  {
    image: "/pulse-4.jpg",
    alt: "Functional Movement",
    title: "Functional Movement Science",
    description: "Functional Release with Movement for enhanced mobility and Functional Fascia Movement Science for optimal alignment and posture correction."
  }
]

export function FeaturesSection({ className }: FeaturesSectionProps) {
  return (
    <section className={cn("py-20 lg:py-32 bg-black w-full", className)}>
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 text-white">
            Pulse20 <span className="text-pink-400">Wireless EMS</span> & Therapeutic Services
          </h2>
          <p className="text-lg text-gray-400 max-w-3xl mx-auto">
            Revolutionary EMS training combined with comprehensive therapeutic treatments. From 20-minute
            high-intensity sessions to sports massage, cupping, and needling - your complete wellness solution.
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card className={cn(
                "overflow-hidden transition-all duration-300 hover:shadow-xl hover:shadow-pink-500/20",
                "border-2 border-gray-800 hover:border-pink-500/50 group bg-gray-900/50 backdrop-blur-sm"
              )}>
                <div className="relative aspect-square w-full min-h-[340px] max-h-[340px]">
                  <Image
                    src={feature.image}
                    alt={feature.alt}
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-105"
                  />
                  {/* Caption Overlay */}
                  <div className="absolute bottom-0 left-0 right-0 bg-black/70 p-6">
                    <h3 className="text-lg font-bold text-pink-400 mb-1">{feature.title}</h3>
                    <p className="text-sm text-gray-200">{feature.description}</p>
                  </div>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-16"
        >
          <Card className="bg-gradient-to-r from-pink-500/10 via-pink-400/10 to-pink-500/10 border-pink-500/20 bg-gray-900/50 backdrop-blur-sm">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold mb-4 text-white">
                Ready to Experience Pulse20 EMS Training?
              </h3>
              <p className="text-gray-400 mb-6 max-w-2xl mx-auto">
                Start with a FREE EMS Training Session including full assessment. Experience 20 minutes
                of revolutionary training that delivers results equivalent to hours of conventional exercise.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/contact" passHref legacyBehavior>
                  <motion.a
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="px-8 py-3 bg-pink-500 text-white rounded-lg font-medium shadow-lg shadow-pink-500/30 hover:bg-pink-400 transition-colors cursor-pointer"
                  >
                    Contact Us
                  </motion.a>
                </Link>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
