'use client'
import React, { useEffect, useState } from "react";
import Link from "next/link";

// Membership Plans based on contract pricing
const membershipPlans = {
  starter: {
    label: "Starter Package",
    duration: "3 Month Contract",
    price: "R350",
    priceNote: "per session",
    features: ["3-month commitment", "Up to 2 make-up sessions", "Professional EMS training", "Progress tracking"],
  },
  express: {
    label: "Express Package",
    duration: "6 Month Contract",
    price: "R300",
    priceNote: "per session",
    features: ["6-month commitment", "Up to 4 make-up sessions", "All Starter features", "Better value per session"],
  },
  diamond: {
    label: "Diamond Package",
    duration: "12 Month Contract",
    price: "R250",
    priceNote: "per session",
    features: ["12-month commitment", "Up to 6 make-up sessions", "Best value per session", "Priority booking"],
  }
};

const introOffer = {
  label: "Intro Consultation",
  originalPrice: "R1000",
  specialPrice: "R500",
  features: ["Full body assessment", "BMI check & fitness evaluation", "Complete EMS training session", "Personalized consultation", "No commitment required"],
};

const LiveClock = () => {
  const [time, setTime] = useState(new Date());
  useEffect(() => {
    const interval = setInterval(() => setTime(new Date()), 1000);
    return () => clearInterval(interval);
  }, []);
  const formatted = time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
  return (
    <div className="text-4xl md:text-5xl font-mono font-extrabold text-pink-400 mb-4 text-center">
      {formatted}
    </div>
  );
};

const PricingSpecialsSection = () => (
  <section className="relative py-20 bg-black w-full overflow-hidden">
    {/* Background Image with Overlay */}
    <div className="absolute inset-0 z-0">
      <div
        className="absolute inset-0 bg-cover bg-center opacity-20"
        style={{ backgroundImage: 'url(/workout.jpg)' }}
      />
      <div className="absolute inset-0 bg-gradient-to-br from-black via-black/90 to-pink-900/20" />

      {/* Animated background elements */}
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-pink-500/5 rounded-full blur-3xl animate-pulse" />
      <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-pink-400/5 rounded-full blur-3xl animate-pulse delay-1000" />
    </div>

    <div className="relative z-10 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="text-center mb-16">
        <div className="inline-block mb-6">
          <span className="bg-gradient-to-r from-pink-500 to-pink-400 text-white px-6 py-2 rounded-full text-sm font-semibold shadow-lg flex items-center gap-2">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M20.57 14.86L22 13.43 20.57 12 17 15.57 8.43 7 12 3.43 10.57 2 9.14 3.43 7.71 2 5.57 4.14 4.14 2.71 2.71 4.14l1.43 1.43L2 7.71l1.43 1.43L2 10.57 3.43 12 7 8.43 15.57 17 12 20.57 13.43 22l1.43-1.43L16.29 22l2.14-2.14 1.43 1.43 1.43-1.43-1.43-1.43L22 16.29l-1.43-1.43z"/>
            </svg>
            PULSE20 EMS TRAINING
          </span>
        </div>
        <h2 className="text-4xl md:text-5xl lg:text-6xl font-extrabold mb-6 text-white">
          Transform Your <span className="text-transparent bg-clip-text bg-gradient-to-r from-pink-400 to-pink-600">Fitness Journey</span>
        </h2>
        <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
          Choose the package that fits your fitness journey. All packages include professional EMS training,
          progress tracking, and access to our therapeutic services.
        </p>
      </div>

      {/* Intro Offer - Featured with Image Background */}
      <div className="mb-20">
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-3 bg-gradient-to-r from-pink-500/20 to-pink-400/20 border border-pink-500/30 rounded-full px-6 py-3 mb-6">
            <svg className="w-6 h-6 text-pink-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            <span className="text-pink-400 font-semibold">SPECIAL INTRO OFFER</span>
          </div>
          <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Experience <span className="text-pink-400">Pulse20 EMS</span> Training
          </h3>
          <p className="text-gray-400 text-lg">Perfect for first-time clients - Discover the revolution</p>
        </div>

        <div className="max-w-5xl mx-auto">
          <div className="relative rounded-3xl overflow-hidden shadow-2xl">
            {/* Background Image */}
            <div className="absolute inset-0">
              <div
                className="absolute inset-0 bg-cover bg-center"
                style={{ backgroundImage: 'url(/lenasia1.jpg)' }}
              />
              <div className="absolute inset-0 bg-gradient-to-r from-black/90 via-black/70 to-pink-900/80" />
            </div>

            {/* Content */}
            <div className="relative z-10 grid grid-cols-1 lg:grid-cols-2 gap-8 p-8 lg:p-12">
              {/* Left Side - Offer Details */}
              <div className="space-y-6">
                <div className="inline-block">
                  <span className="bg-gradient-to-r from-pink-500 to-red-500 text-white px-4 py-2 rounded-full text-sm font-bold animate-pulse flex items-center gap-2">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M13 10V3L4 14h7v7l9-11h-7z"/>
                    </svg>
                    LIMITED TIME OFFER
                  </span>
                </div>

                <h4 className="text-3xl lg:text-4xl font-bold text-white">
                  {introOffer.label}
                </h4>

                <div className="flex items-baseline gap-4">
                  <span className="text-3xl text-gray-400 line-through">{introOffer.originalPrice}</span>
                  <span className="text-5xl lg:text-6xl font-extrabold text-pink-400">{introOffer.specialPrice}</span>
                </div>

                <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4">
                  <p className="text-green-400 font-semibold text-lg flex items-center gap-2">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                    Save R500 Today!
                  </p>
                  <p className="text-gray-300 text-sm">Normal price R1000 - Special intro price R500</p>
                </div>

                <Link href="/contact" passHref legacyBehavior>
                  <button className="w-full lg:w-auto px-8 py-4 bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-400 hover:to-pink-500 text-white rounded-xl font-bold shadow-lg shadow-pink-500/30 transition-all transform hover:scale-105 text-lg flex items-center justify-center gap-2">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M5 13l4 4L19 7"/>
                    </svg>
                    Book Intro Session - R500
                  </button>
                </Link>
              </div>

              {/* Right Side - Features */}
              <div className="space-y-4">
                <h5 className="text-xl font-bold text-white mb-6">What's Included:</h5>
                <div className="grid gap-4">
                  {introOffer.features.map((feature, index) => {
                    const getIcon = (iconIndex: number) => {
                      switch(iconIndex) {
                        case 0:
                          return <svg className="w-4 h-4 text-pink-400" fill="currentColor" viewBox="0 0 24 24"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/></svg>;
                        case 1:
                          return <svg className="w-4 h-4 text-pink-400" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>;
                        case 2:
                          return <svg className="w-4 h-4 text-pink-400" fill="currentColor" viewBox="0 0 24 24"><path d="M13 10V3L4 14h7v7l9-11h-7z"/></svg>;
                        case 3:
                          return <svg className="w-4 h-4 text-pink-400" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>;
                        default:
                          return <svg className="w-4 h-4 text-pink-400" fill="currentColor" viewBox="0 0 24 24"><path d="M9 11H7v9h2v-9zm4 0h-2v9h2v-9zm4 0h-2v9h2v-9zM4 22h16v-2H4v2zM19.5 4h-3V2.5c0-.83-.67-1.5-1.5-1.5h-6c-.83 0-1.5.67-1.5 1.5V4h-3C3.67 4 3 4.67 3 5.5S3.67 7 4.5 7h15c.83 0 1.5-.67 1.5-1.5S20.33 4 19.5 4z"/></svg>;
                      }
                    };
                    return (
                      <div key={index} className="flex items-start gap-3 bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
                        <div className="w-8 h-8 rounded-full bg-pink-500/20 flex items-center justify-center flex-shrink-0 mt-0.5 border border-pink-500/30">
                          {getIcon(index)}
                        </div>
                        <span className="text-gray-200 font-medium">{feature}</span>
                      </div>
                    );
                  })}
                </div>

                <div className="mt-6 p-4 bg-pink-500/10 border border-pink-500/30 rounded-lg">
                  <p className="text-pink-400 font-semibold text-sm flex items-center gap-2">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    No commitment required
                  </p>
                  <p className="text-gray-300 text-sm">Experience our training with zero pressure</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Membership Packages */}
      <div className="mb-16">
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-3 bg-gradient-to-r from-gray-800/50 to-gray-700/50 border border-gray-600/30 rounded-full px-6 py-3 mb-6">
            <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            <span className="text-white font-semibold">MEMBERSHIP PACKAGES</span>
          </div>
          <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Choose Your <span className="text-pink-400">Commitment Level</span>
          </h3>
          <p className="text-gray-400 text-lg">Ongoing training packages for committed fitness goals</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Starter Package */}
          <div className="group relative">
            {/* Background Image */}
            <div className="absolute inset-0 rounded-2xl overflow-hidden">
              <div
                className="absolute inset-0 bg-cover bg-center"
                style={{ backgroundImage: 'url(/melbuton1.jpg)' }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black via-black/80 to-black/40 group-hover:from-pink-900/90 group-hover:via-black/70 transition-all duration-500" />
            </div>

            <div className="relative z-10 p-8 h-full flex flex-col min-h-[500px]">
              {/* Header with Fitness Icon */}
              <div className="text-center mb-6">
                <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-gradient-to-br from-pink-500/20 to-pink-600/20 border-2 border-pink-500/30 flex items-center justify-center backdrop-blur-sm">
                  <svg className="w-10 h-10 text-pink-400" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.57 14.86L22 13.43 20.57 12 17 15.57 8.43 7 12 3.43 10.57 2 9.14 3.43 7.71 2 5.57 4.14 4.14 2.71 2.71 4.14l1.43 1.43L2 7.71l1.43 1.43L2 10.57 3.43 12 7 8.43 15.57 17 12 20.57 13.43 22l1.43-1.43L16.29 22l2.14-2.14 1.43 1.43 1.43-1.43-1.43-1.43L22 16.29l-1.43-1.43z"/>
                  </svg>
                </div>
                <h4 className="text-2xl font-bold text-white mb-2">{membershipPlans.starter.label}</h4>
                <p className="text-pink-300 text-sm mb-4 font-medium bg-pink-500/10 px-3 py-1 rounded-full inline-block">
                  {membershipPlans.starter.duration}
                </p>
              </div>

              {/* Pricing */}
              <div className="text-center mb-6">
                <div className="bg-black/50 backdrop-blur-sm rounded-xl p-4 border border-pink-500/20">
                  <div className="text-4xl font-extrabold text-pink-400 mb-1">{membershipPlans.starter.price}</div>
                  <p className="text-gray-300 text-sm">{membershipPlans.starter.priceNote}</p>
                </div>
              </div>

              {/* Features */}
              <div className="flex-grow space-y-3 mb-6">
                {membershipPlans.starter.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3 bg-black/30 backdrop-blur-sm rounded-lg p-3 border border-white/10">
                    <div className="w-6 h-6 rounded-full bg-pink-500 flex items-center justify-center flex-shrink-0">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span className="text-gray-200 text-sm font-medium">{feature}</span>
                  </div>
                ))}
              </div>

              {/* CTA Button */}
              <Link href="/contact" passHref legacyBehavior>
                <button className="w-full px-6 py-4 bg-gradient-to-r from-pink-500/80 to-pink-600/80 hover:from-pink-500 hover:to-pink-600 text-white rounded-xl font-bold backdrop-blur-sm border border-pink-500/30 transition-all transform hover:scale-105 shadow-lg">
                  Start Your Journey
                </button>
              </Link>
            </div>
          </div>

          {/* Express Package - Popular */}
          <div className="group relative transform scale-105">
            {/* Popular Badge */}
            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-20">
              <div className="bg-gradient-to-r from-pink-500 to-pink-600 text-white px-6 py-2 rounded-full text-sm font-bold shadow-xl border-2 border-white/20">
                <svg className="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                MOST POPULAR
              </div>
            </div>

            {/* Background Image */}
            <div className="absolute inset-0 rounded-2xl overflow-hidden">
              <div
                className="absolute inset-0 bg-cover bg-center"
                style={{ backgroundImage: 'url(/lenasia1.jpg)' }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-pink-900/95 via-pink-800/60 to-pink-900/40 group-hover:from-pink-900/100 group-hover:via-pink-800/70 transition-all duration-500" />
              {/* Glow effect */}
              <div className="absolute inset-0 bg-pink-500/10 rounded-2xl blur-xl"></div>
            </div>

            <div className="relative z-10 p-8 h-full flex flex-col min-h-[500px] pt-12">
              {/* Header with Fitness Icon */}
              <div className="text-center mb-6">
                <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-gradient-to-br from-pink-400 to-pink-600 border-2 border-pink-300/50 flex items-center justify-center shadow-xl">
                  <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                </div>
                <h4 className="text-2xl font-bold text-white mb-2">{membershipPlans.express.label}</h4>
                <p className="text-pink-200 text-sm mb-4 font-medium bg-pink-400/20 px-3 py-1 rounded-full inline-block border border-pink-300/30">
                  {membershipPlans.express.duration}
                </p>
              </div>

              {/* Pricing */}
              <div className="text-center mb-6">
                <div className="bg-black/60 backdrop-blur-sm rounded-xl p-4 border-2 border-pink-400/40 shadow-lg">
                  <div className="text-4xl font-extrabold text-pink-300 mb-1">{membershipPlans.express.price}</div>
                  <p className="text-pink-200 text-sm">{membershipPlans.express.priceNote}</p>
                  <div className="inline-block mt-2 bg-green-400/20 text-green-300 px-3 py-1 rounded-full text-xs font-semibold border border-green-400/30">
                    Save R50 per session
                  </div>
                </div>
              </div>

              {/* Features */}
              <div className="flex-grow space-y-3 mb-6">
                {membershipPlans.express.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3 bg-black/40 backdrop-blur-sm rounded-lg p-3 border border-pink-300/20">
                    <div className="w-6 h-6 rounded-full bg-pink-400 flex items-center justify-center flex-shrink-0 shadow-md">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span className="text-pink-100 text-sm font-medium">{feature}</span>
                  </div>
                ))}
              </div>

              {/* CTA Button */}
              <Link href="/contact" passHref legacyBehavior>
                <button className="w-full px-6 py-4 bg-gradient-to-r from-pink-400 to-pink-500 hover:from-pink-300 hover:to-pink-400 text-white rounded-xl font-bold shadow-xl border border-pink-300/30 transition-all transform hover:scale-105">
                  Choose Express
                </button>
              </Link>
            </div>
          </div>

          {/* Diamond Package */}
          <div className="group relative">
            {/* Best Value Badge */}
            <div className="absolute -top-3 -right-3 z-20">
              <div className="bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-2 rounded-full text-xs font-bold shadow-lg border-2 border-white/20 rotate-12">
                <svg className="w-3 h-3 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                </svg>
                BEST VALUE
              </div>
            </div>

            {/* Background Image */}
            <div className="absolute inset-0 rounded-2xl overflow-hidden">
              <div
                className="absolute inset-0 bg-cover bg-center"
                style={{ backgroundImage: 'url(/wellness.jpg)' }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black via-gray-900/80 to-gray-800/40 group-hover:from-pink-900/90 group-hover:via-gray-900/70 transition-all duration-500" />
            </div>

            <div className="relative z-10 p-8 h-full flex flex-col min-h-[500px]">
              {/* Header with Fitness Icon */}
              <div className="text-center mb-6">
                <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-gradient-to-br from-pink-500/20 to-pink-600/20 border-2 border-pink-500/30 flex items-center justify-center backdrop-blur-sm">
                  <svg className="w-10 h-10 text-pink-400" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <h4 className="text-2xl font-bold text-white mb-2">{membershipPlans.diamond.label}</h4>
                <p className="text-pink-300 text-sm mb-4 font-medium bg-pink-500/10 px-3 py-1 rounded-full inline-block">
                  {membershipPlans.diamond.duration}
                </p>
              </div>

              {/* Pricing */}
              <div className="text-center mb-6">
                <div className="bg-black/50 backdrop-blur-sm rounded-xl p-4 border border-pink-500/20">
                  <div className="text-4xl font-extrabold text-pink-400 mb-1">{membershipPlans.diamond.price}</div>
                  <p className="text-gray-300 text-sm">{membershipPlans.diamond.priceNote}</p>
                  <div className="inline-block mt-2 bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-xs font-semibold border border-green-500/30">
                    <svg className="w-3 h-3 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                    Save R100 per session
                  </div>
                </div>
              </div>

              {/* Features */}
              <div className="flex-grow space-y-3 mb-6">
                {membershipPlans.diamond.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3 bg-black/30 backdrop-blur-sm rounded-lg p-3 border border-white/10">
                    <div className="w-6 h-6 rounded-full bg-pink-500 flex items-center justify-center flex-shrink-0">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span className="text-gray-200 text-sm font-medium">{feature}</span>
                  </div>
                ))}
              </div>

              {/* CTA Button */}
              <Link href="/contact" passHref legacyBehavior>
                <button className="w-full px-6 py-4 bg-gradient-to-r from-pink-500/80 to-pink-600/80 hover:from-pink-500 hover:to-pink-600 text-white rounded-xl font-bold backdrop-blur-sm border border-pink-500/30 transition-all transform hover:scale-105 shadow-lg">
                  Choose Diamond
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Call to Action with Background */}
      <div className="relative">
        <div className="absolute inset-0 rounded-3xl overflow-hidden">
          <div
            className="absolute inset-0 bg-cover bg-center opacity-30"
            style={{ backgroundImage: 'url(/massage.jpg)' }}
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/90 via-pink-900/50 to-black/90" />
        </div>

        <div className="relative z-10 text-center py-16 px-8 rounded-3xl border border-pink-500/20">
          <div className="max-w-4xl mx-auto">
            <div className="mb-8">
              <div className="inline-block mb-6">
                <span className="bg-gradient-to-r from-pink-500 to-pink-400 text-white px-6 py-2 rounded-full text-sm font-semibold flex items-center gap-2">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"/>
                  </svg>
                  TIME IS PRECIOUS
                </span>
              </div>
              <LiveClock />
              <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed italic">
                "Every second counts - Transform your fitness with revolutionary EMS training that delivers
                90 minutes of results in just 20 minutes"
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
              <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
                <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-pink-500/20 flex items-center justify-center">
                  <svg className="w-6 h-6 text-pink-400" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M13 10V3L4 14h7v7l9-11h-7z"/>
                  </svg>
                </div>
                <div className="text-2xl font-bold text-pink-400 mb-1">20 Min</div>
                <div className="text-gray-300 text-sm">Session Duration</div>
              </div>
              <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
                <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-pink-500/20 flex items-center justify-center">
                  <svg className="w-6 h-6 text-pink-400" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.57 14.86L22 13.43 20.57 12 17 15.57 8.43 7 12 3.43 10.57 2 9.14 3.43 7.71 2 5.57 4.14 4.14 2.71 2.71 4.14l1.43 1.43L2 7.71l1.43 1.43L2 10.57 3.43 12 7 8.43 15.57 17 12 20.57 13.43 22l1.43-1.43L16.29 22l2.14-2.14 1.43 1.43 1.43-1.43-1.43-1.43L22 16.29l-1.43-1.43z"/>
                  </svg>
                </div>
                <div className="text-2xl font-bold text-pink-400 mb-1">36,000</div>
                <div className="text-gray-300 text-sm">Muscle Contractions</div>
              </div>
              <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
                <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-pink-500/20 flex items-center justify-center">
                  <svg className="w-6 h-6 text-pink-400" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <div className="text-2xl font-bold text-pink-400 mb-1">150x</div>
                <div className="text-gray-300 text-sm">More Effective</div>
              </div>
            </div>

            <div className="space-y-6">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/contact" passHref legacyBehavior>
                  <button className="px-10 py-4 bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-400 hover:to-pink-500 text-white rounded-xl font-bold shadow-lg shadow-pink-500/30 transition-all transform hover:scale-105 text-lg flex items-center justify-center gap-2">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M5 13l4 4L19 7"/>
                    </svg>
                    Book Your Consultation Now
                  </button>
                </Link>
                <Link href="/contact" passHref legacyBehavior>
                  <button className="px-10 py-4 border-2 border-pink-500 text-pink-400 hover:bg-pink-500/10 rounded-xl font-semibold transition-all text-lg flex items-center justify-center gap-2">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M6.62,10.79C8.06,13.62 10.38,15.94 13.21,17.38L15.41,15.18C15.69,14.9 16.08,14.82 16.43,14.93C17.55,15.3 18.75,15.5 20,15.5A1,1 0 0,1 21,16.5V20A1,1 0 0,1 20,21A17,17 0 0,1 3,4A1,1 0 0,1 4,3H7.5A1,1 0 0,1 8.5,4C8.5,5.25 8.7,6.45 9.07,7.57C9.18,7.92 9.1,8.31 8.82,8.59L6.62,10.79Z"/>
                    </svg>
                    Call +27 83 408 3665
                  </button>
                </Link>
              </div>

              <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50">
                <p className="text-gray-400 mb-2">
                  <span className="text-pink-400 font-semibold">✓</span> 24-hour cancellation policy
                  <span className="mx-4">•</span>
                  <span className="text-pink-400 font-semibold">✓</span> Make-up sessions available
                  <span className="mx-4">•</span>
                  <span className="text-pink-400 font-semibold">✓</span> No hidden fees
                </p>
                <p className="text-sm text-gray-500">
                  All packages include professional guidance and progress tracking.
                  <Link href="/terms-of-service" className="text-pink-400 hover:underline ml-1">View full terms & conditions</Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
);

export default PricingSpecialsSection; 