'use client'
import React, { useEffect, useState } from "react";
import Link from "next/link";

// Membership Plans based on contract pricing
const membershipPlans = {
  starter: {
    label: "Starter Package",
    duration: "3 Month Contract",
    price: "R350",
    priceNote: "per session",
    features: ["3-month commitment", "Up to 2 make-up sessions", "Professional EMS training", "Progress tracking"],
  },
  express: {
    label: "Express Package",
    duration: "6 Month Contract",
    price: "R300",
    priceNote: "per session",
    features: ["6-month commitment", "Up to 4 make-up sessions", "All Starter features", "Better value per session"],
  },
  diamond: {
    label: "Diamond Package",
    duration: "12 Month Contract",
    price: "R250",
    priceNote: "per session",
    features: ["12-month commitment", "Up to 6 make-up sessions", "Best value per session", "Priority booking"],
  }
};

const introOffer = {
  label: "Intro Consultation",
  originalPrice: "R1000",
  specialPrice: "R500",
  features: ["Full body assessment", "BMI check & fitness evaluation", "Complete EMS training session", "Personalized consultation", "No commitment required"],
};

const LiveClock = () => {
  const [time, setTime] = useState(new Date());
  useEffect(() => {
    const interval = setInterval(() => setTime(new Date()), 1000);
    return () => clearInterval(interval);
  }, []);
  const formatted = time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
  return (
    <div className="text-4xl md:text-5xl font-mono font-extrabold text-pink-400 mb-4 text-center">
      {formatted}
    </div>
  );
};

const PricingSpecialsSection = () => (
  <section className="py-20 bg-black w-full">
    <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="text-center mb-16">
        <h2 className="text-4xl md:text-5xl font-extrabold mb-4 text-pink-400">Pulse20 EMS Training Packages</h2>
        <p className="text-lg text-gray-300 max-w-3xl mx-auto">
          Choose the package that fits your fitness journey. All packages include professional EMS training,
          progress tracking, and access to our therapeutic services.
        </p>
      </div>

      {/* Intro Offer - Featured */}
      <div className="mb-16">
        <div className="text-center mb-8">
          <h3 className="text-2xl md:text-3xl font-bold text-white mb-2">🎯 Special Intro Offer</h3>
          <p className="text-gray-400">Perfect for first-time clients - Experience Pulse20 EMS training</p>
        </div>

        <div className="max-w-md mx-auto">
          <div className="bg-gradient-to-br from-pink-500/20 via-pink-400/10 to-pink-500/20 border-2 border-pink-400 rounded-2xl shadow-xl p-8 text-center relative overflow-hidden">
            {/* Glow effect */}
            <div className="absolute inset-0 bg-pink-500/5 rounded-2xl blur-xl"></div>

            <div className="relative z-10">
              <div className="mb-4">
                <span className="bg-pink-500 text-white px-3 py-1 rounded-full text-sm font-semibold">LIMITED TIME</span>
              </div>

              <h4 className="text-2xl font-bold text-pink-400 mb-2">{introOffer.label}</h4>

              <div className="mb-4">
                <div className="flex items-center justify-center gap-3 mb-2">
                  <span className="text-2xl text-gray-400 line-through">{introOffer.originalPrice}</span>
                  <span className="text-4xl font-extrabold text-pink-400">{introOffer.specialPrice}</span>
                </div>
                <p className="text-sm text-gray-300">Save R500 on your first consultation</p>
              </div>

              <ul className="text-gray-300 mb-6 space-y-2">
                {introOffer.features.map((feature, index) => (
                  <li key={index} className="flex items-center justify-center gap-2">
                    <span className="text-pink-400">✓</span>
                    {feature}
                  </li>
                ))}
              </ul>

              <Link href="/contact" passHref legacyBehavior>
                <button className="w-full px-6 py-4 bg-pink-500 hover:bg-pink-400 text-white rounded-lg font-semibold shadow-lg transition-colors text-lg">
                  Book Intro Session - R500
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Membership Packages */}
      <div className="mb-12">
        <div className="text-center mb-8">
          <h3 className="text-2xl md:text-3xl font-bold text-white mb-2">Membership Packages</h3>
          <p className="text-gray-400">Ongoing training packages for committed fitness goals</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Starter Package */}
          <div className="bg-gray-900/60 rounded-2xl shadow-lg p-8 border border-gray-800 hover:border-pink-500/50 transition-all">
            <div className="text-center">
              <h4 className="text-xl font-bold text-white mb-2">{membershipPlans.starter.label}</h4>
              <p className="text-pink-400 text-sm mb-4">{membershipPlans.starter.duration}</p>
              <div className="mb-6">
                <span className="text-3xl font-extrabold text-white">{membershipPlans.starter.price}</span>
                <span className="text-gray-400 ml-1">{membershipPlans.starter.priceNote}</span>
              </div>
              <ul className="text-gray-300 space-y-2 mb-6">
                {membershipPlans.starter.features.map((feature, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <span className="text-pink-400">•</span>
                    {feature}
                  </li>
                ))}
              </ul>
              <Link href="/contact" passHref legacyBehavior>
                <button className="w-full px-4 py-3 border border-pink-500 text-pink-400 hover:bg-pink-500/10 rounded-lg font-semibold transition-colors">
                  Get Started
                </button>
              </Link>
            </div>
          </div>

          {/* Express Package - Popular */}
          <div className="bg-gray-900/60 rounded-2xl shadow-lg p-8 border-2 border-pink-500 relative hover:border-pink-400 transition-all">
            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <span className="bg-pink-500 text-white px-4 py-1 rounded-full text-sm font-semibold">POPULAR</span>
            </div>
            <div className="text-center">
              <h4 className="text-xl font-bold text-pink-400 mb-2">{membershipPlans.express.label}</h4>
              <p className="text-pink-400 text-sm mb-4">{membershipPlans.express.duration}</p>
              <div className="mb-6">
                <span className="text-3xl font-extrabold text-pink-400">{membershipPlans.express.price}</span>
                <span className="text-gray-400 ml-1">{membershipPlans.express.priceNote}</span>
              </div>
              <ul className="text-gray-300 space-y-2 mb-6">
                {membershipPlans.express.features.map((feature, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <span className="text-pink-400">•</span>
                    {feature}
                  </li>
                ))}
              </ul>
              <Link href="/contact" passHref legacyBehavior>
                <button className="w-full px-4 py-3 bg-pink-500 hover:bg-pink-400 text-white rounded-lg font-semibold transition-colors">
                  Choose Express
                </button>
              </Link>
            </div>
          </div>

          {/* Diamond Package */}
          <div className="bg-gray-900/60 rounded-2xl shadow-lg p-8 border border-gray-800 hover:border-pink-500/50 transition-all">
            <div className="text-center">
              <h4 className="text-xl font-bold text-white mb-2">{membershipPlans.diamond.label}</h4>
              <p className="text-pink-400 text-sm mb-4">{membershipPlans.diamond.duration}</p>
              <div className="mb-6">
                <span className="text-3xl font-extrabold text-white">{membershipPlans.diamond.price}</span>
                <span className="text-gray-400 ml-1">{membershipPlans.diamond.priceNote}</span>
                <div className="text-sm text-green-400 mt-1">Best Value!</div>
              </div>
              <ul className="text-gray-300 space-y-2 mb-6">
                {membershipPlans.diamond.features.map((feature, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <span className="text-pink-400">•</span>
                    {feature}
                  </li>
                ))}
              </ul>
              <Link href="/contact" passHref legacyBehavior>
                <button className="w-full px-4 py-3 border border-pink-500 text-pink-400 hover:bg-pink-500/10 rounded-lg font-semibold transition-colors">
                  Choose Diamond
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="text-center">
        <div className="flex flex-col items-center justify-center mb-6">
          <LiveClock />
          <p className="italic text-gray-400 max-w-md">Every second counts - Transform your fitness with revolutionary EMS training</p>
        </div>

        <div className="space-y-4">
          <Link href="/contact" passHref legacyBehavior>
            <button className="px-8 py-4 bg-pink-500 hover:bg-pink-400 text-white rounded-lg font-semibold shadow-lg transition-colors text-lg mr-4">
              Book Consultation
            </button>
          </Link>
          <p className="text-sm text-gray-500">
            All packages include 24-hour cancellation policy and make-up sessions.
            <Link href="/terms-of-service" className="text-pink-400 hover:underline ml-1">View terms</Link>
          </p>
        </div>
      </div>
    </div>
  </section>
);

export default PricingSpecialsSection; 