'use client'
import React, { useEffect, useState } from "react";
import Link from "next/link";

const regularPrice = {
  label: "Standard Membership",
  price: "R499/mo",
  features: ["Unlimited EMS Sessions", "Access to All Facilities", "Member Support"],
};

const specialPrice = {
  label: "Winter Special",
  price: "R399/mo",
  features: ["All Standard Features", "1st Month Discounted", "No Joining Fee"],
};

const LiveClock = () => {
  const [time, setTime] = useState(new Date());
  useEffect(() => {
    const interval = setInterval(() => setTime(new Date()), 1000);
    return () => clearInterval(interval);
  }, []);
  const formatted = time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
  return (
    <div className="text-4xl md:text-5xl font-mono font-extrabold text-pink-400 mb-4 text-center">
      {formatted}
    </div>
  );
};

const PricingSpecialsSection = () => (
  <section className="py-20 bg-black w-full">
    <div className="w-full max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
      <h2 className="text-4xl md:text-5xl font-extrabold mb-4 text-pink-400 text-center">Pricing & Specials</h2>
      <p className="text-lg text-gray-300 text-center mb-10">Choose the plan that fits you best or take advantage of our current special!</p>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-center">
        {/* Regular Price */}
        <div className="bg-gray-900/60 rounded-2xl shadow-lg flex flex-col items-center p-8">
          <h3 className="text-xl font-bold text-pink-400 mb-2">{regularPrice.label}</h3>
          <div className="text-3xl font-extrabold text-white mb-2">{regularPrice.price}</div>
          <ul className="text-gray-300 mb-2 text-center">
            {regularPrice.features.map((f) => (
              <li key={f} className="mb-1">• {f}</li>
            ))}
          </ul>
        </div>
        {/* Live Clock and Book Appointment */}
        <div className="flex flex-col items-center justify-center">
          <LiveClock />
          <Link href="/contact" passHref legacyBehavior>
            <button className="px-6 py-3 bg-pink-500 hover:bg-pink-400 text-white rounded-lg font-semibold shadow-lg transition-colors mt-2">
              Book Appointment
            </button>
          </Link>
          <p className="italic text-gray-400 text-center mt-4 max-w-xs">Giving you back time with every EMS session.</p>
        </div>
        {/* Special Price */}
        <div className="bg-pink-500/10 border border-pink-400 rounded-2xl shadow-lg flex flex-col items-center p-8">
          <h3 className="text-xl font-bold text-pink-400 mb-2">{specialPrice.label}</h3>
          <div className="text-3xl font-extrabold text-pink-400 mb-2">{specialPrice.price}</div>
          <ul className="text-gray-300 mb-2 text-center">
            {specialPrice.features.map((f) => (
              <li key={f} className="mb-1">• {f}</li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  </section>
);

export default PricingSpecialsSection; 