'use client'
import React, { useEffect, useState } from "react";
import Link from "next/link";

// Membership Plans based on contract pricing
const membershipPlans = {
  starter: {
    label: "Starter Package",
    duration: "3 Month Contract",
    price: "R350",
    priceNote: "per session",
    features: ["3-month commitment", "Up to 2 make-up sessions", "Professional EMS training", "Progress tracking"],
  },
  express: {
    label: "Express Package",
    duration: "6 Month Contract",
    price: "R300",
    priceNote: "per session",
    features: ["6-month commitment", "Up to 4 make-up sessions", "All Starter features", "Better value per session"],
  },
  diamond: {
    label: "Diamond Package",
    duration: "12 Month Contract",
    price: "R250",
    priceNote: "per session",
    features: ["12-month commitment", "Up to 6 make-up sessions", "Best value per session", "Priority booking"],
  }
};

const introOffer = {
  label: "Intro Consultation",
  originalPrice: "R1000",
  specialPrice: "R500",
  features: ["Full body assessment", "BMI check & fitness evaluation", "Complete EMS training session", "Personalized consultation", "No commitment required"],
};

const LiveClock = () => {
  const [time, setTime] = useState(new Date());
  useEffect(() => {
    const interval = setInterval(() => setTime(new Date()), 1000);
    return () => clearInterval(interval);
  }, []);
  const formatted = time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
  return (
    <div className="text-4xl md:text-5xl font-mono font-extrabold text-pink-400 mb-4 text-center">
      {formatted}
    </div>
  );
};

const PricingSpecialsSection = () => (
  <section className="relative py-20 bg-black w-full overflow-hidden">
    {/* Background Image with Overlay */}
    <div className="absolute inset-0 z-0">
      <div
        className="absolute inset-0 bg-cover bg-center opacity-20"
        style={{ backgroundImage: 'url(/workout.jpg)' }}
      />
      <div className="absolute inset-0 bg-gradient-to-br from-black via-black/90 to-pink-900/20" />

      {/* Animated background elements */}
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-pink-500/5 rounded-full blur-3xl animate-pulse" />
      <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-pink-400/5 rounded-full blur-3xl animate-pulse delay-1000" />
    </div>

    <div className="relative z-10 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="text-center mb-16">
        <div className="inline-block mb-6">
          <span className="bg-gradient-to-r from-pink-500 to-pink-400 text-white px-6 py-2 rounded-full text-sm font-semibold shadow-lg">
            💪 PULSE20 EMS TRAINING
          </span>
        </div>
        <h2 className="text-4xl md:text-5xl lg:text-6xl font-extrabold mb-6 text-white">
          Transform Your <span className="text-transparent bg-clip-text bg-gradient-to-r from-pink-400 to-pink-600">Fitness Journey</span>
        </h2>
        <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
          Choose the package that fits your fitness journey. All packages include professional EMS training,
          progress tracking, and access to our therapeutic services.
        </p>
      </div>

      {/* Intro Offer - Featured with Image Background */}
      <div className="mb-20">
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-pink-500/20 to-pink-400/20 border border-pink-500/30 rounded-full px-6 py-3 mb-6">
            <span className="text-2xl">🎯</span>
            <span className="text-pink-400 font-semibold">SPECIAL INTRO OFFER</span>
          </div>
          <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Experience <span className="text-pink-400">Pulse20 EMS</span> Training
          </h3>
          <p className="text-gray-400 text-lg">Perfect for first-time clients - Discover the revolution</p>
        </div>

        <div className="max-w-5xl mx-auto">
          <div className="relative rounded-3xl overflow-hidden shadow-2xl">
            {/* Background Image */}
            <div className="absolute inset-0">
              <div
                className="absolute inset-0 bg-cover bg-center"
                style={{ backgroundImage: 'url(/lenasia1.jpg)' }}
              />
              <div className="absolute inset-0 bg-gradient-to-r from-black/90 via-black/70 to-pink-900/80" />
            </div>

            {/* Content */}
            <div className="relative z-10 grid grid-cols-1 lg:grid-cols-2 gap-8 p-8 lg:p-12">
              {/* Left Side - Offer Details */}
              <div className="space-y-6">
                <div className="inline-block">
                  <span className="bg-gradient-to-r from-pink-500 to-red-500 text-white px-4 py-2 rounded-full text-sm font-bold animate-pulse">
                    ⚡ LIMITED TIME OFFER
                  </span>
                </div>

                <h4 className="text-3xl lg:text-4xl font-bold text-white">
                  {introOffer.label}
                </h4>

                <div className="flex items-baseline gap-4">
                  <span className="text-3xl text-gray-400 line-through">{introOffer.originalPrice}</span>
                  <span className="text-5xl lg:text-6xl font-extrabold text-pink-400">{introOffer.specialPrice}</span>
                </div>

                <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4">
                  <p className="text-green-400 font-semibold text-lg">💰 Save R500 Today!</p>
                  <p className="text-gray-300 text-sm">Normal price R1000 - Special intro price R500</p>
                </div>

                <Link href="/contact" passHref legacyBehavior>
                  <button className="w-full lg:w-auto px-8 py-4 bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-400 hover:to-pink-500 text-white rounded-xl font-bold shadow-lg shadow-pink-500/30 transition-all transform hover:scale-105 text-lg">
                    🚀 Book Intro Session - R500
                  </button>
                </Link>
              </div>

              {/* Right Side - Features */}
              <div className="space-y-4">
                <h5 className="text-xl font-bold text-white mb-6">What's Included:</h5>
                <div className="grid gap-4">
                  {introOffer.features.map((feature, index) => (
                    <div key={index} className="flex items-start gap-3 bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
                      <div className="w-6 h-6 rounded-full bg-pink-500 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-white text-sm font-bold">✓</span>
                      </div>
                      <span className="text-gray-200 font-medium">{feature}</span>
                    </div>
                  ))}
                </div>

                <div className="mt-6 p-4 bg-pink-500/10 border border-pink-500/30 rounded-lg">
                  <p className="text-pink-400 font-semibold text-sm">⭐ No commitment required</p>
                  <p className="text-gray-300 text-sm">Experience our training with zero pressure</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Membership Packages */}
      <div className="mb-16">
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-gray-800/50 to-gray-700/50 border border-gray-600/30 rounded-full px-6 py-3 mb-6">
            <span className="text-2xl">💎</span>
            <span className="text-white font-semibold">MEMBERSHIP PACKAGES</span>
          </div>
          <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Choose Your <span className="text-pink-400">Commitment Level</span>
          </h3>
          <p className="text-gray-400 text-lg">Ongoing training packages for committed fitness goals</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Starter Package */}
          <div className="group relative">
            <div className="absolute inset-0 bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-2xl blur-xl group-hover:blur-2xl transition-all"></div>
            <div className="relative bg-gray-900/80 backdrop-blur-sm rounded-2xl shadow-xl p-8 border border-gray-700 hover:border-pink-500/50 transition-all transform hover:scale-105">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-gray-600 to-gray-700 flex items-center justify-center">
                  <span className="text-2xl">🚀</span>
                </div>
                <h4 className="text-2xl font-bold text-white mb-2">{membershipPlans.starter.label}</h4>
                <p className="text-pink-400 text-sm mb-6 font-medium">{membershipPlans.starter.duration}</p>

                <div className="mb-8">
                  <div className="text-4xl font-extrabold text-white mb-2">{membershipPlans.starter.price}</div>
                  <p className="text-gray-400">{membershipPlans.starter.priceNote}</p>
                </div>

                <div className="space-y-3 mb-8">
                  {membershipPlans.starter.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-3 text-left">
                      <div className="w-5 h-5 rounded-full bg-pink-500/20 flex items-center justify-center flex-shrink-0">
                        <span className="text-pink-400 text-xs">✓</span>
                      </div>
                      <span className="text-gray-300 text-sm">{feature}</span>
                    </div>
                  ))}
                </div>

                <Link href="/contact" passHref legacyBehavior>
                  <button className="w-full px-6 py-3 border-2 border-pink-500 text-pink-400 hover:bg-pink-500/10 rounded-xl font-semibold transition-all">
                    Get Started
                  </button>
                </Link>
              </div>
            </div>
          </div>

          {/* Express Package - Popular */}
          <div className="group relative">
            <div className="absolute inset-0 bg-gradient-to-br from-pink-500/20 to-pink-600/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all"></div>
            <div className="relative bg-gradient-to-br from-gray-900/90 to-pink-900/20 backdrop-blur-sm rounded-2xl shadow-2xl p-8 border-2 border-pink-500 transform hover:scale-105 transition-all">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-gradient-to-r from-pink-500 to-pink-600 text-white px-6 py-2 rounded-full text-sm font-bold shadow-lg">
                  ⭐ MOST POPULAR
                </span>
              </div>

              <div className="text-center pt-4">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-pink-500 to-pink-600 flex items-center justify-center shadow-lg">
                  <span className="text-2xl">⚡</span>
                </div>
                <h4 className="text-2xl font-bold text-pink-400 mb-2">{membershipPlans.express.label}</h4>
                <p className="text-pink-300 text-sm mb-6 font-medium">{membershipPlans.express.duration}</p>

                <div className="mb-8">
                  <div className="text-4xl font-extrabold text-pink-400 mb-2">{membershipPlans.express.price}</div>
                  <p className="text-gray-300">{membershipPlans.express.priceNote}</p>
                  <div className="inline-block mt-2 bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-xs font-semibold">
                    Save R50 per session
                  </div>
                </div>

                <div className="space-y-3 mb-8">
                  {membershipPlans.express.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-3 text-left">
                      <div className="w-5 h-5 rounded-full bg-pink-500 flex items-center justify-center flex-shrink-0">
                        <span className="text-white text-xs">✓</span>
                      </div>
                      <span className="text-gray-200 text-sm font-medium">{feature}</span>
                    </div>
                  ))}
                </div>

                <Link href="/contact" passHref legacyBehavior>
                  <button className="w-full px-6 py-3 bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-400 hover:to-pink-500 text-white rounded-xl font-bold shadow-lg transition-all">
                    Choose Express
                  </button>
                </Link>
              </div>
            </div>
          </div>

          {/* Diamond Package */}
          <div className="group relative">
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/10 to-yellow-600/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all"></div>
            <div className="relative bg-gray-900/80 backdrop-blur-sm rounded-2xl shadow-xl p-8 border border-gray-700 hover:border-yellow-500/50 transition-all transform hover:scale-105">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-yellow-500 to-yellow-600 flex items-center justify-center shadow-lg">
                  <span className="text-2xl">💎</span>
                </div>
                <h4 className="text-2xl font-bold text-white mb-2">{membershipPlans.diamond.label}</h4>
                <p className="text-yellow-400 text-sm mb-6 font-medium">{membershipPlans.diamond.duration}</p>

                <div className="mb-8">
                  <div className="text-4xl font-extrabold text-white mb-2">{membershipPlans.diamond.price}</div>
                  <p className="text-gray-400">{membershipPlans.diamond.priceNote}</p>
                  <div className="inline-block mt-2 bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-xs font-semibold">
                    💰 Best Value - Save R100!
                  </div>
                </div>

                <div className="space-y-3 mb-8">
                  {membershipPlans.diamond.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-3 text-left">
                      <div className="w-5 h-5 rounded-full bg-yellow-500/20 flex items-center justify-center flex-shrink-0">
                        <span className="text-yellow-400 text-xs">✓</span>
                      </div>
                      <span className="text-gray-300 text-sm">{feature}</span>
                    </div>
                  ))}
                </div>

                <Link href="/contact" passHref legacyBehavior>
                  <button className="w-full px-6 py-3 border-2 border-yellow-500 text-yellow-400 hover:bg-yellow-500/10 rounded-xl font-semibold transition-all">
                    Choose Diamond
                  </button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Call to Action with Background */}
      <div className="relative">
        <div className="absolute inset-0 rounded-3xl overflow-hidden">
          <div
            className="absolute inset-0 bg-cover bg-center opacity-30"
            style={{ backgroundImage: 'url(/massage.jpg)' }}
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/90 via-pink-900/50 to-black/90" />
        </div>

        <div className="relative z-10 text-center py-16 px-8 rounded-3xl border border-pink-500/20">
          <div className="max-w-4xl mx-auto">
            <div className="mb-8">
              <div className="inline-block mb-6">
                <span className="bg-gradient-to-r from-pink-500 to-pink-400 text-white px-6 py-2 rounded-full text-sm font-semibold">
                  ⏰ TIME IS PRECIOUS
                </span>
              </div>
              <LiveClock />
              <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed italic">
                "Every second counts - Transform your fitness with revolutionary EMS training that delivers
                90 minutes of results in just 20 minutes"
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
              <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
                <div className="text-3xl mb-2">⚡</div>
                <div className="text-2xl font-bold text-pink-400 mb-1">20 Min</div>
                <div className="text-gray-300 text-sm">Session Duration</div>
              </div>
              <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
                <div className="text-3xl mb-2">💪</div>
                <div className="text-2xl font-bold text-pink-400 mb-1">36,000</div>
                <div className="text-gray-300 text-sm">Muscle Contractions</div>
              </div>
              <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
                <div className="text-3xl mb-2">🎯</div>
                <div className="text-2xl font-bold text-pink-400 mb-1">150x</div>
                <div className="text-gray-300 text-sm">More Effective</div>
              </div>
            </div>

            <div className="space-y-6">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/contact" passHref legacyBehavior>
                  <button className="px-10 py-4 bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-400 hover:to-pink-500 text-white rounded-xl font-bold shadow-lg shadow-pink-500/30 transition-all transform hover:scale-105 text-lg">
                    🚀 Book Your Consultation Now
                  </button>
                </Link>
                <Link href="/contact" passHref legacyBehavior>
                  <button className="px-10 py-4 border-2 border-pink-500 text-pink-400 hover:bg-pink-500/10 rounded-xl font-semibold transition-all text-lg">
                    📞 Call +27 83 408 3665
                  </button>
                </Link>
              </div>

              <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50">
                <p className="text-gray-400 mb-2">
                  <span className="text-pink-400 font-semibold">✓</span> 24-hour cancellation policy
                  <span className="mx-4">•</span>
                  <span className="text-pink-400 font-semibold">✓</span> Make-up sessions available
                  <span className="mx-4">•</span>
                  <span className="text-pink-400 font-semibold">✓</span> No hidden fees
                </p>
                <p className="text-sm text-gray-500">
                  All packages include professional guidance and progress tracking.
                  <Link href="/terms-of-service" className="text-pink-400 hover:underline ml-1">View full terms & conditions</Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
);

export default PricingSpecialsSection; 