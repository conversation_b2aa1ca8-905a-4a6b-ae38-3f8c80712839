"use client"

import { <PERSON><PERSON>, Ava<PERSON>Fallback, AvatarImage } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Marketing Manager",
    avatar: "/testimonials/sarah.jpg",
    content:
      "Pulse20.co.za has completely transformed my fitness routine. The one-touch check-in and progress tracking keep me motivated every day. I've never been more consistent with my workouts!",
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Software Developer",
    avatar: "/testimonials/michael.jpg",
    content:
      "As a tech person, I love how intuitive the platform is. The achievement system and streak tracking gamify my workouts in the best way possible. Plus, the 24/7 access fits my schedule perfectly.",
  },
  {
    id: 3,
    name: "<PERSON>",
    role: "Fitness Enthusiast",
    avatar: "/testimonials/emma.jpg",
    content:
      "The community at Pulse is incredible. The trainers are knowledgeable and supportive, and the feedback system ensures my voice is always heard. It's more than a gym - it's a lifestyle.",
  },
  {
    id: 4,
    name: "<PERSON>",
    role: "Business Owner",
    avatar: "/testimonials/david.jpg",
    content:
      "The seamless payment system and transparent billing make managing my membership effortless. I can focus on my workouts without worrying about administrative hassles.",
  },
  {
    id: 5,
    name: "Lisa Rodriguez",
    role: "Personal Trainer",
    avatar: "/testimonials/lisa.jpg",
    content:
      "Working at Pulse has been amazing. The admin tools help me track client progress effectively, and the member feedback system helps us continuously improve our services.",
  },
  {
    id: 6,
    name: "James Wilson",
    role: "Student",
    avatar: "/testimonials/james.jpg",
    content:
      "The helpful materials and wellness resources have educated me so much about proper nutrition and recovery. Pulse doesn't just help you work out - it helps you live better.",
  },
]

export function TestimonialsSection({ className }: { className?: string }) {
  // Duplicate testimonials for seamless marquee
  const marqueeTestimonials = [...testimonials, ...testimonials]

  return (
    <section className={cn("py-20 bg-gray-900/30 w-full", className)}>
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-10">
          <h2 className="text-4xl md:text-5xl font-extrabold mb-2 text-white">
            What Our <span className="text-pink-400">Community</span> Says
          </h2>
          <p className="text-lg text-gray-400 max-w-2xl mx-auto">
            Real stories from real members who have transformed their lives with Pulse20.co.za.
          </p>
        </div>
        {/* Marquee */}
        <div className="overflow-x-hidden">
          <div
            className="flex gap-10 animate-marquee"
            style={{ animation: "marquee 40s linear infinite" }}
          >
            {marqueeTestimonials.map((testimonial, idx) => (
              <div
                key={testimonial.id + "-" + idx}
                className="min-w-[380px] max-w-sm bg-gray-900/80 border border-gray-800 rounded-xl shadow-md px-8 py-4 flex flex-col gap-3 items-start justify-between hover:border-pink-400 transition-colors"
              >
                <div className="flex items-center gap-3 mb-2">
                  <Avatar className="w-10 h-10">
                    <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                    <AvatarFallback className="bg-pink-500/20 text-pink-400 border border-pink-500/30">
                      {testimonial.name.split(" ").map((n) => n[0]).join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-semibold text-white text-base leading-tight">
                      {testimonial.name}
                    </div>
                    <div className="text-xs text-pink-400">{testimonial.role}</div>
                  </div>
                </div>
                <div className="text-gray-300 text-sm leading-relaxed line-clamp-5">
                  {testimonial.content}
                </div>
              </div>
            ))}
          </div>
        </div>
        {/* Marquee animation keyframes */}
        <style>{`
          @keyframes marquee {
            0% { transform: translateX(0); }
            100% { transform: translateX(-50%); }
          }
        `}</style>
      </div>
    </section>
  )
}
