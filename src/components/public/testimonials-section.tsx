"use client"

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { Star } from "lucide-react"

// Star Rating Component
const StarRating = ({ rating }: { rating: number }) => {
  return (
    <div className="flex gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={cn(
            "w-4 h-4",
            star <= rating
              ? "text-yellow-400 fill-yellow-400"
              : "text-gray-600"
          )}
        />
      ))}
    </div>
  )
}

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Marketing Manager",
    avatar: "/testimonials/sarah.jpg",
    bgImage: "/lenasia1.jpg",
    rating: 5,
    memberSince: "6 months",
    achievement: "Lost 8kg",
    content:
      "Pulse20 EMS training has completely transformed my body and confidence. In just 20 minutes, I get results that used to take me hours at traditional gyms. The wireless technology is incredible!",
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Software Developer",
    avatar: "/testimonials/michael.jpg",
    bgImage: "/melbuton1.jpg",
    rating: 5,
    memberSince: "4 months",
    achievement: "Gained 5kg muscle",
    content:
      "As someone who sits at a desk all day, Pulse20 EMS has been a game-changer. The efficiency is unmatched - 20 minutes equals 90 minutes of HIIT. Perfect for my busy schedule!",
  },
  {
    id: 3,
    name: "Emma Williams",
    role: "Fitness Enthusiast",
    avatar: "/testimonials/emma.jpg",
    bgImage: "/wellness.jpg",
    rating: 5,
    memberSince: "8 months",
    achievement: "Improved posture",
    content:
      "The therapeutic services combined with EMS training have completely eliminated my chronic back pain. The sports massage and needling therapy are exceptional. Highly recommend!",
  },
  {
    id: 4,
    name: "David Brown",
    role: "Business Owner",
    avatar: "/testimonials/david.jpg",
    bgImage: "/workout.jpg",
    rating: 5,
    memberSince: "1 year",
    achievement: "Reduced waist 12cm",
    content:
      "Pulse20 fits perfectly into my busy lifestyle. The results speak for themselves - I've never been in better shape. The team is professional and the technology is cutting-edge.",
  },
  {
    id: 5,
    name: "Lisa Rodriguez",
    role: "Personal Trainer",
    avatar: "/testimonials/lisa.jpg",
    bgImage: "/massage.jpg",
    rating: 5,
    memberSince: "10 months",
    achievement: "Enhanced recovery",
    content:
      "As a fitness professional, I'm amazed by the science behind Pulse20 EMS. The 36,000 muscle contractions per session deliver results that traditional training simply can't match.",
  },
  {
    id: 6,
    name: "James Wilson",
    role: "Student",
    avatar: "/testimonials/james.jpg",
    bgImage: "/lenasia1.jpg",
    rating: 5,
    memberSince: "3 months",
    achievement: "Increased strength",
    content:
      "The intro consultation was amazing - R500 well spent! The full body assessment and EMS session showed me exactly what my body needed. Now I'm hooked on this revolutionary training.",
  },
]

export function TestimonialsSection({ className }: { className?: string }) {
  // Duplicate testimonials for seamless marquee
  const marqueeTestimonials = [...testimonials, ...testimonials]

  return (
    <section className={cn("py-20 bg-gray-900/30 w-full", className)}>
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <div className="inline-block mb-6">
            <Badge className="bg-pink-500/20 text-pink-400 border-pink-500/30 px-6 py-2">
              <Star className="w-4 h-4 mr-2" />
              Member Success Stories
            </Badge>
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-extrabold mb-6 text-white">
            What Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-pink-400 to-pink-600">Community</span> Says
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Real transformations from real members who have experienced the revolutionary power of Pulse20 EMS training.
          </p>
          <div className="flex items-center justify-center gap-2 mt-6">
            <div className="flex">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star key={star} className="w-5 h-5 text-yellow-400 fill-yellow-400" />
              ))}
            </div>
            <span className="text-gray-400 ml-2">5.0 average rating from 200+ members</span>
          </div>
        </div>
        {/* Enhanced Marquee */}
        <div className="overflow-x-hidden">
          <div
            className="flex gap-8 animate-marquee hover:pause"
            style={{ animation: "marquee 50s linear infinite" }}
          >
            {marqueeTestimonials.map((testimonial, idx) => (
              <div
                key={testimonial.id + "-" + idx}
                className="min-w-[420px] max-w-md relative overflow-hidden rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 group"
              >
                {/* Background Image */}
                <div className="absolute inset-0">
                  <div
                    className="absolute inset-0 bg-cover bg-center"
                    style={{ backgroundImage: `url(${testimonial.bgImage})` }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/95 via-black/70 to-black/40" />
                </div>

                {/* Content */}
                <div className="relative z-10 p-6 h-full flex flex-col justify-between min-h-[280px]">
                  {/* Header */}
                  <div className="space-y-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar className="w-12 h-12 border-2 border-pink-500/30">
                          <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                          <AvatarFallback className="bg-pink-500/20 text-pink-400 border border-pink-500/30">
                            {testimonial.name.split(" ").map((n) => n[0]).join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-semibold text-white text-base leading-tight">
                            {testimonial.name}
                          </div>
                          <div className="text-xs text-pink-400">{testimonial.role}</div>
                        </div>
                      </div>
                      <StarRating rating={testimonial.rating} />
                    </div>

                    {/* Achievement Badges */}
                    <div className="flex gap-2 flex-wrap">
                      <Badge className="bg-pink-500/20 text-pink-300 border-pink-500/30 text-xs">
                        {testimonial.achievement}
                      </Badge>
                      <Badge className="bg-gray-700/50 text-gray-300 border-gray-600/30 text-xs">
                        Member {testimonial.memberSince}
                      </Badge>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="flex-grow flex items-center">
                    <blockquote className="text-gray-200 text-sm leading-relaxed italic">
                      "{testimonial.content}"
                    </blockquote>
                  </div>

                  {/* Footer */}
                  <div className="pt-4 border-t border-pink-500/20">
                    <div className="flex items-center gap-2 text-xs text-pink-400">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                      </svg>
                      Verified Pulse20 Member
                    </div>
                  </div>
                </div>

                {/* Hover Glow Effect */}
                <div className="absolute inset-0 bg-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl blur-xl pointer-events-none" />
              </div>
            ))}
          </div>
        </div>
        {/* Enhanced Marquee animation keyframes */}
        <style>{`
          @keyframes marquee {
            0% { transform: translateX(0); }
            100% { transform: translateX(-50%); }
          }
          .animate-marquee:hover {
            animation-play-state: paused;
          }
        `}</style>
      </div>
    </section>
  )
}
