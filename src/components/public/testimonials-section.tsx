"use client"

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { Star } from "lucide-react"

// Star Rating Component
const StarRating = ({ rating }: { rating: number }) => {
  return (
    <div className="flex gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={cn(
            "w-4 h-4",
            star <= rating
              ? "text-yellow-400 fill-yellow-400"
              : "text-gray-600"
          )}
        />
      ))}
    </div>
  )
}

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Marketing Manager",
    avatar: "/testimonials/sarah.jpg",
    bgImage: "/lenasia1.jpg",
    rating: 5,
    memberSince: "6 months",
    achievement: "Lost 8kg",
    content:
      "Pulse20 EMS training has completely transformed my body and confidence. In just 20 minutes, I get results that used to take me hours at traditional gyms. The wireless technology is incredible!",
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Software Developer",
    avatar: "/testimonials/michael.jpg",
    bgImage: "/melbuton1.jpg",
    rating: 5,
    memberSince: "4 months",
    achievement: "Gained 5kg muscle",
    content:
      "As someone who sits at a desk all day, Pulse20 EMS has been a game-changer. The efficiency is unmatched - 20 minutes equals 90 minutes of HIIT. Perfect for my busy schedule!",
  },
  {
    id: 3,
    name: "Emma Williams",
    role: "Fitness Enthusiast",
    avatar: "/testimonials/emma.jpg",
    bgImage: "/wellness.jpg",
    rating: 5,
    memberSince: "8 months",
    achievement: "Improved posture",
    content:
      "The therapeutic services combined with EMS training have completely eliminated my chronic back pain. The sports massage and needling therapy are exceptional. Highly recommend!",
  },
  {
    id: 4,
    name: "David Brown",
    role: "Business Owner",
    avatar: "/testimonials/david.jpg",
    bgImage: "/workout.jpg",
    rating: 5,
    memberSince: "1 year",
    achievement: "Reduced waist 12cm",
    content:
      "Pulse20 fits perfectly into my busy lifestyle. The results speak for themselves - I've never been in better shape. The team is professional and the technology is cutting-edge.",
  },
  {
    id: 5,
    name: "Lisa Rodriguez",
    role: "Personal Trainer",
    avatar: "/testimonials/lisa.jpg",
    bgImage: "/massage.jpg",
    rating: 5,
    memberSince: "10 months",
    achievement: "Enhanced recovery",
    content:
      "As a fitness professional, I'm amazed by the science behind Pulse20 EMS. The 36,000 muscle contractions per session deliver results that traditional training simply can't match.",
  },
  {
    id: 6,
    name: "James Wilson",
    role: "Student",
    avatar: "/testimonials/james.jpg",
    bgImage: "/lenasia1.jpg",
    rating: 5,
    memberSince: "3 months",
    achievement: "Increased strength",
    content:
      "The intro consultation was amazing - R500 well spent! The full body assessment and EMS session showed me exactly what my body needed. Now I'm hooked on this revolutionary training.",
  },
]

export function TestimonialsSection({ className }: { className?: string }) {
  // Duplicate testimonials for seamless marquee
  const marqueeTestimonials = [...testimonials, ...testimonials]

  return (
    <section className={cn("relative py-20 bg-black w-full overflow-hidden", className)}>
      {/* Background Elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-pink-500/5 via-black to-pink-400/5" />
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-pink-500/5 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-pink-400/5 rounded-full blur-3xl animate-pulse delay-1000" />
      </div>

      <div className="relative z-10 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <div className="inline-block mb-6">
            <Badge className="bg-pink-500/20 text-pink-400 border-pink-500/30 px-4 py-2">
              <Star className="w-4 h-4 mr-2" />
              Member Success Stories
            </Badge>
          </div>

          <h2 className="text-3xl md:text-4xl lg:text-5xl font-extrabold mb-6 text-white">
            What Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-pink-400 to-pink-600">Community</span> Says
          </h2>

          <p className="text-lg text-gray-300 max-w-2xl mx-auto mb-6">
            Real transformations from real members who have experienced the power of Pulse20 EMS training.
          </p>

          {/* Compact Stats */}
          <div className="flex items-center justify-center gap-6">
            <div className="flex items-center gap-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star key={star} className="w-4 h-4 text-yellow-400 fill-yellow-400" />
              ))}
              <span className="text-gray-400 ml-2 text-sm">5.0 (200+ reviews)</span>
            </div>
          </div>
        </div>
        {/* Compact Testimonial Design */}
        <div className="overflow-x-hidden">
          <div
            className="flex gap-4 animate-marquee hover:pause"
            style={{ animation: "marquee 45s linear infinite" }}
          >
            {marqueeTestimonials.map((testimonial, idx) => (
              <div
                key={testimonial.id + "-" + idx}
                className="min-w-[280px] max-w-xs relative overflow-hidden rounded-xl shadow-lg transition-all duration-300 group bg-gray-900/80 backdrop-blur-sm border border-gray-800 hover:border-pink-500/50"
              >
                {/* Simple Background */}
                <div className="absolute inset-0 bg-gradient-to-br from-pink-500/5 to-pink-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                {/* Content */}
                <div className="relative z-10 p-4">
                  {/* Header */}
                  <div className="flex items-center gap-3 mb-3">
                    <Avatar className="w-10 h-10 border border-pink-500/30">
                      <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                      <AvatarFallback className="bg-pink-500/20 text-pink-400 text-sm">
                        {testimonial.name.split(" ").map((n) => n[0]).join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="font-semibold text-white text-sm">{testimonial.name}</div>
                      <div className="text-xs text-gray-400">{testimonial.role}</div>
                    </div>
                    <StarRating rating={testimonial.rating} />
                  </div>

                  {/* Quote */}
                  <blockquote className="text-gray-300 text-sm leading-relaxed mb-3 line-clamp-4">
                    "{testimonial.content}"
                  </blockquote>

                  {/* Achievement */}
                  <div className="flex items-center justify-between text-xs">
                    <Badge className="bg-pink-500/20 text-pink-400 border-pink-500/30 px-2 py-1">
                      {testimonial.achievement}
                    </Badge>
                    <span className="text-gray-500">{testimonial.memberSince}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        {/* Enhanced Marquee animation keyframes */}
        <style>{`
          @keyframes marquee {
            0% { transform: translateX(0); }
            100% { transform: translateX(-50%); }
          }
          .animate-marquee:hover {
            animation-play-state: paused;
          }
        `}</style>
      </div>
    </section>
  )
}
