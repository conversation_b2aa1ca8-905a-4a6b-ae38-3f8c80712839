"use client"

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { Star } from "lucide-react"

// Star Rating Component
const StarRating = ({ rating }: { rating: number }) => {
  return (
    <div className="flex gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={cn(
            "w-4 h-4",
            star <= rating
              ? "text-yellow-400 fill-yellow-400"
              : "text-gray-600"
          )}
        />
      ))}
    </div>
  )
}

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Marketing Manager",
    avatar: "/testimonials/sarah.jpg",
    bgImage: "/lenasia1.jpg",
    rating: 5,
    memberSince: "6 months",
    achievement: "Lost 8kg",
    content:
      "Pulse20 EMS training has completely transformed my body and confidence. In just 20 minutes, I get results that used to take me hours at traditional gyms. The wireless technology is incredible!",
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Software Developer",
    avatar: "/testimonials/michael.jpg",
    bgImage: "/melbuton1.jpg",
    rating: 5,
    memberSince: "4 months",
    achievement: "Gained 5kg muscle",
    content:
      "As someone who sits at a desk all day, Pulse20 EMS has been a game-changer. The efficiency is unmatched - 20 minutes equals 90 minutes of HIIT. Perfect for my busy schedule!",
  },
  {
    id: 3,
    name: "Emma Williams",
    role: "Fitness Enthusiast",
    avatar: "/testimonials/emma.jpg",
    bgImage: "/wellness.jpg",
    rating: 5,
    memberSince: "8 months",
    achievement: "Improved posture",
    content:
      "The therapeutic services combined with EMS training have completely eliminated my chronic back pain. The sports massage and needling therapy are exceptional. Highly recommend!",
  },
  {
    id: 4,
    name: "David Brown",
    role: "Business Owner",
    avatar: "/testimonials/david.jpg",
    bgImage: "/workout.jpg",
    rating: 5,
    memberSince: "1 year",
    achievement: "Reduced waist 12cm",
    content:
      "Pulse20 fits perfectly into my busy lifestyle. The results speak for themselves - I've never been in better shape. The team is professional and the technology is cutting-edge.",
  },
  {
    id: 5,
    name: "Lisa Rodriguez",
    role: "Personal Trainer",
    avatar: "/testimonials/lisa.jpg",
    bgImage: "/massage.jpg",
    rating: 5,
    memberSince: "10 months",
    achievement: "Enhanced recovery",
    content:
      "As a fitness professional, I'm amazed by the science behind Pulse20 EMS. The 36,000 muscle contractions per session deliver results that traditional training simply can't match.",
  },
  {
    id: 6,
    name: "James Wilson",
    role: "Student",
    avatar: "/testimonials/james.jpg",
    bgImage: "/lenasia1.jpg",
    rating: 5,
    memberSince: "3 months",
    achievement: "Increased strength",
    content:
      "The intro consultation was amazing - R500 well spent! The full body assessment and EMS session showed me exactly what my body needed. Now I'm hooked on this revolutionary training.",
  },
]

export function TestimonialsSection({ className }: { className?: string }) {
  // Duplicate testimonials for seamless marquee
  const marqueeTestimonials = [...testimonials, ...testimonials]

  return (
    <section className={cn("relative py-20 bg-black w-full overflow-hidden", className)}>
      {/* Background Elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-pink-500/5 via-black to-pink-400/5" />
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-pink-500/5 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-pink-400/5 rounded-full blur-3xl animate-pulse delay-1000" />
      </div>

      <div className="relative z-10 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-20">
          <div className="inline-block mb-8">
            <div className="bg-gradient-to-r from-pink-500/20 to-pink-400/20 border border-pink-500/30 rounded-full px-8 py-3 backdrop-blur-sm">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-full bg-pink-500/30 flex items-center justify-center">
                  <Star className="w-4 h-4 text-pink-400 fill-pink-400" />
                </div>
                <span className="text-pink-400 font-bold text-lg">MEMBER SUCCESS STORIES</span>
                <div className="w-8 h-8 rounded-full bg-pink-500/30 flex items-center justify-center">
                  <svg className="w-4 h-4 text-pink-400" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>

          <h2 className="text-5xl md:text-6xl lg:text-7xl font-extrabold mb-8 text-white leading-tight">
            Real People,{" "}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-pink-400 via-pink-500 to-pink-600">
              Real Results
            </span>
          </h2>

          <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed mb-8">
            Discover how Pulse20 EMS training has transformed lives in just 20 minutes per session.
            These are real stories from real members who chose to revolutionize their fitness journey.
          </p>

          {/* Stats Row */}
          <div className="flex items-center justify-center gap-8 mb-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-pink-400 mb-1">200+</div>
              <div className="text-sm text-gray-400">Happy Members</div>
            </div>
            <div className="w-px h-12 bg-gray-700"></div>
            <div className="text-center">
              <div className="flex justify-center mb-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star key={star} className="w-6 h-6 text-yellow-400 fill-yellow-400" />
                ))}
              </div>
              <div className="text-sm text-gray-400">5.0 Average Rating</div>
            </div>
            <div className="w-px h-12 bg-gray-700"></div>
            <div className="text-center">
              <div className="text-3xl font-bold text-pink-400 mb-1">98%</div>
              <div className="text-sm text-gray-400">Success Rate</div>
            </div>
          </div>
        </div>
        {/* Revolutionary Testimonial Design */}
        <div className="overflow-x-hidden">
          <div
            className="flex gap-8 animate-marquee hover:pause"
            style={{ animation: "marquee 60s linear infinite" }}
          >
            {marqueeTestimonials.map((testimonial, idx) => {
              const isEven = idx % 2 === 0;
              return (
                <div
                  key={testimonial.id + "-" + idx}
                  className={cn(
                    "min-w-[320px] max-w-sm relative overflow-hidden shadow-2xl transition-all duration-700 group",
                    isEven ? "rounded-2xl" : "rounded-xl rotate-1 hover:rotate-0"
                  )}
                >
                  {/* Dynamic Background with Parallax Effect */}
                  <div className="absolute inset-0">
                    <div
                      className="absolute inset-0 bg-cover bg-center transform group-hover:scale-110 transition-transform duration-1000"
                      style={{ backgroundImage: `url(${testimonial.bgImage})` }}
                    />
                    <div className={cn(
                      "absolute inset-0 transition-all duration-700",
                      isEven
                        ? "bg-gradient-to-br from-pink-900/95 via-black/80 to-pink-800/90"
                        : "bg-gradient-to-tl from-black/95 via-pink-900/70 to-black/85"
                    )} />

                    {/* Animated Glow Effects */}
                    <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-pink-400 to-transparent opacity-60 group-hover:opacity-100 transition-opacity" />
                    <div className="absolute bottom-0 right-0 w-1 h-full bg-gradient-to-t from-transparent via-pink-400 to-transparent opacity-60 group-hover:opacity-100 transition-opacity" />
                  </div>

                  {/* Floating Elements */}
                  <div className="absolute top-3 right-3 w-12 h-12 bg-pink-500/10 rounded-full blur-lg animate-pulse" />
                  <div className="absolute bottom-4 left-4 w-10 h-10 bg-pink-400/10 rounded-full blur-md animate-pulse delay-500" />

                  {/* Main Content */}
                  <div className="relative z-10 p-5 h-full flex flex-col min-h-[280px]">
                    {/* Quote Icon */}
                    <div className="absolute top-4 left-4 text-pink-400/30 group-hover:text-pink-400/50 transition-colors">
                      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M14,17H17L19,13V7H13V13H16M6,17H9L11,13V7H5V13H8L6,17Z" />
                      </svg>
                    </div>

                    {/* Header Section */}
                    <div className="flex items-start justify-between mb-4 mt-6">
                      <div className="flex items-center gap-3">
                        <div className="relative">
                          <Avatar className="w-12 h-12 border-2 border-pink-400/50 shadow-lg">
                            <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                            <AvatarFallback className="bg-pink-500/30 text-pink-200 text-sm font-bold">
                              {testimonial.name.split(" ").map((n) => n[0]).join("")}
                            </AvatarFallback>
                          </Avatar>
                          {/* Online Indicator */}
                          <div className="absolute -bottom-0.5 -right-0.5 w-4 h-4 bg-green-500 rounded-full border-2 border-black animate-pulse" />
                        </div>
                        <div>
                          <div className="font-bold text-white text-base leading-tight mb-1">
                            {testimonial.name}
                          </div>
                          <div className="text-xs text-pink-300 mb-1">{testimonial.role}</div>
                          <StarRating rating={testimonial.rating} />
                        </div>
                      </div>
                    </div>

                    {/* Achievement Showcase */}
                    <div className="mb-4">
                      <div className="grid grid-cols-2 gap-2">
                        <div className="bg-black/50 backdrop-blur-sm rounded-lg p-2 border border-pink-500/20">
                          <div className="text-pink-400 text-xs font-medium">{testimonial.achievement}</div>
                        </div>
                        <div className="bg-black/50 backdrop-blur-sm rounded-lg p-2 border border-pink-500/20">
                          <div className="text-pink-400 text-xs font-medium">{testimonial.memberSince}</div>
                        </div>
                      </div>
                    </div>

                    {/* Testimonial Content */}
                    <div className="flex-grow flex items-center mb-4">
                      <blockquote className="text-gray-100 text-sm leading-relaxed">
                        "{testimonial.content}"
                      </blockquote>
                    </div>

                    {/* Footer with Verification */}
                    <div className="border-t border-pink-500/20 pt-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-1 text-xs text-pink-300">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                          </svg>
                          Verified
                        </div>
                        <div className="text-xs text-gray-400">
                          Pulse20
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Hover Effects */}
                  <div className="absolute inset-0 bg-gradient-to-t from-pink-500/20 via-transparent to-pink-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-700 pointer-events-none" />

                  {/* Animated Border */}
                  <div className="absolute inset-0 rounded-inherit">
                    <div className="absolute inset-0 rounded-inherit border-2 border-pink-500/0 group-hover:border-pink-500/50 transition-all duration-700" />
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        {/* Enhanced Marquee animation keyframes */}
        <style>{`
          @keyframes marquee {
            0% { transform: translateX(0); }
            100% { transform: translateX(-50%); }
          }
          .animate-marquee:hover {
            animation-play-state: paused;
          }
        `}</style>
      </div>
    </section>
  )
}
