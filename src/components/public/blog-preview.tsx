"use client"

import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, ArrowRight, User } from "lucide-react"
import Link from "next/link"
import { cn } from "@/lib/utils"

interface BlogPost {
  id: string
  title: string
  excerpt: string
  category: string
  author: string
  publishedAt: Date
  readTime: number
  image?: string
  featured?: boolean
}

interface BlogPreviewProps {
  className?: string
}

const blogPosts: BlogPost[] = [
  {
    id: "1",
    title: "10 Essential Exercises for Building Core Strength",
    excerpt: "Discover the most effective core exercises that will help you build a strong foundation for all your workouts. From planks to dead bugs, we cover everything you need to know.",
    category: "Workout Tips",
    author: "<PERSON>",
    publishedAt: new Date(2024, 11, 15),
    readTime: 8,
    featured: true
  },
  {
    id: "2",
    title: "Nutrition Timing: When to Eat for Optimal Performance",
    excerpt: "Learn how proper nutrition timing can enhance your workout performance and recovery. We break down pre-workout, post-workout, and daily nutrition strategies.",
    category: "Nutrition",
    author: "Dr. <PERSON>",
    publishedAt: new Date(2024, 11, 12),
    readTime: 6
  },
  {
    id: "3",
    title: "The Science of Recovery: Why Rest Days Matter",
    excerpt: "Understanding the importance of recovery in your fitness journey. Explore how proper rest, sleep, and active recovery can accelerate your progress.",
    category: "Recovery",
    author: "Emma Rodriguez",
    publishedAt: new Date(2024, 11, 10),
    readTime: 5
  },
  {
    id: "4",
    title: "Setting Realistic Fitness Goals That Stick",
    excerpt: "Goal setting strategies that actually work. Learn how to create achievable milestones and maintain motivation throughout your fitness journey.",
    category: "Motivation",
    author: "James Wilson",
    publishedAt: new Date(2024, 11, 8),
    readTime: 7
  }
]

export function BlogPreview({ className }: BlogPreviewProps) {
  const featuredPost = blogPosts.find(post => post.featured)
  const regularPosts = blogPosts.filter(post => !post.featured)

  return (
    <section className={cn("py-20 lg:py-32 bg-black w-full", className)}>
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 text-white">
            Latest from Our{" "}
            <span className="text-pink-400">
              Wellness Blog
            </span>
          </h2>
          <p className="text-lg text-gray-400 max-w-2xl mx-auto">
            Expert insights, workout tips, and wellness advice to support your fitness journey.
            Stay informed with the latest trends and science-backed strategies.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Featured Post */}
          {featuredPost && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="lg:col-span-2"
            >
              <Card className="h-full overflow-hidden hover:shadow-lg hover:shadow-pink-500/20 transition-all duration-300 group bg-gray-900/50 backdrop-blur-sm border-gray-800 hover:border-pink-500/50">
                <div className="aspect-video bg-gradient-to-br from-pink-500/20 to-pink-400/10 relative">
                  {/* Placeholder for featured image */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-16 h-16 mx-auto rounded-full bg-pink-500/30 flex items-center justify-center mb-4">
                        <User className="w-8 h-8 text-pink-400" />
                      </div>
                      <p className="text-pink-400 font-medium">Featured Article</p>
                    </div>
                  </div>
                  <Badge className="absolute top-4 left-4 bg-pink-500 text-white">
                    Featured
                  </Badge>
                </div>

                <CardContent className="p-6">
                  <div className="flex items-center gap-4 text-sm text-gray-400 mb-3">
                    <Badge variant="outline" className="text-pink-400 border-pink-500/30">
                      {featuredPost.category}
                    </Badge>
                    <span className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      {featuredPost.publishedAt.toLocaleDateString()}
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {featuredPost.readTime} min read
                    </span>
                  </div>

                  <h3 className="text-xl font-bold mb-3 group-hover:text-pink-400 transition-colors text-white">
                    {featuredPost.title}
                  </h3>

                  <p className="text-gray-400 mb-4 leading-relaxed">
                    {featuredPost.excerpt}
                  </p>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-400">
                      By {featuredPost.author}
                    </span>
                    <Button variant="ghost" size="sm" className="group/btn text-pink-400 hover:text-pink-300 hover:bg-pink-500/10" asChild>
                      <Link href={`/blog/${featuredPost.id}`}>
                        Read More
                        <ArrowRight className="w-4 h-4 ml-2 group-hover/btn:translate-x-1 transition-transform" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Regular Posts */}
          <div className="space-y-6">
            {regularPosts.slice(0, 3).map((post, index) => (
              <motion.div
                key={post.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="hover:shadow-md hover:shadow-pink-500/20 transition-all duration-300 group bg-gray-900/50 backdrop-blur-sm border-gray-800 hover:border-pink-500/50">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 text-xs text-gray-400 mb-2">
                      <Badge variant="outline" className="text-xs text-pink-400 border-pink-500/30">
                        {post.category}
                      </Badge>
                      <span className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        {post.publishedAt.toLocaleDateString()}
                      </span>
                    </div>

                    <h4 className="font-semibold mb-2 group-hover:text-pink-400 transition-colors line-clamp-2 text-white">
                      {post.title}
                    </h4>

                    <p className="text-sm text-gray-400 mb-3 line-clamp-2">
                      {post.excerpt}
                    </p>

                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-400">
                        {post.readTime} min read
                      </span>
                      <Link
                        href={`/blog/${post.id}`}
                        className="text-pink-400 hover:text-pink-300 font-medium"
                      >
                        Read →
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>

        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-12"
        >
          <Button variant="outline" size="lg" className="group border-pink-500/30 text-pink-400 hover:bg-pink-500/10" asChild>
            <Link href="/blog">
              View All Articles
              <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </Link>
          </Button>
        </motion.div>
      </div>
    </section>
  )
}
