"use client";

import React from "react"
// import { Clerk<PERSON>rovider } from "@clerk/nextjs"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ThemeProvider } from "next-themes";

interface Props {
    children: React.ReactNode;
}

const client = new QueryClient();

const Providers = ({ children }: Props) => {
    return (
        <QueryClientProvider client={client}>
            <ThemeProvider
                attribute="class"
                defaultTheme="dark"
                enableSystem
                disableTransitionOnChange
            >
                {/* Supabase Auth is handled via middleware and client-side hooks */}
                {/* ClerkProvider can be added later for enhanced authentication */}
                {children}
            </ThemeProvider>
        </QueryClientProvider>
    );
};

export default Providers
