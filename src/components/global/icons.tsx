import { LucideProps, ProportionsIcon } from "lucide-react";

const Icons = {
    logo: (props: any) => (
        <img src="/logo.jpg" alt="Pulse 20 Future Fitness Logo" style={{ width: props?.width || 32, height: props?.height || 32, ...props?.style }} className={props?.className} />
    ),
    google: (props: LucideProps) => (
        <svg {...props} width="256" height="262" viewBox="0 0 256 262" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMid">
            <path
                d="M255.878 133.451c0-10.734-.871-18.567-2.756-26.69H130.55v48.448h71.947c-1.45 12.04-9.283 30.172-26.69 42.356l-.244 1.622 38.755 30.023 2.685.268c24.659-22.774 38.875-56.282 38.875-96.027"
                fill="#4285F4" />
            <path
                d="M130.55 261.1c35.248 0 64.839-11.605 86.453-31.622l-41.196-31.913c-11.024 7.688-25.82 13.055-45.257 13.055-34.523 0-63.824-22.773-74.269-54.25l-1.531.13-40.298 31.187-.527 1.465C35.393 231.798 79.49 261.1 130.55 261.1"
                fill="#34A853" />
            <path
                d="M56.281 156.37c-2.756-8.123-4.351-16.827-4.351-25.82 0-8.994 1.595-17.697 4.206-25.82l-.073-1.73L15.26 71.312l-1.335.635C5.077 89.644 0 109.517 0 130.55s5.077 40.905 13.925 58.602l42.356-32.782"
                fill="#FBBC05" />
            <path
                d="M130.55 50.479c24.514 0 41.05 10.589 50.479 19.438l36.844-35.974C195.245 12.91 165.798 0 130.55 0 79.49 0 35.393 29.301 13.925 71.947l42.211 32.783c10.59-31.477 39.891-54.251 74.414-54.251"
                fill="#EB4335" />
        </svg>
    ),
    bot: (props: LucideProps) => (
        <svg {...props} width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_201_371)">
                <path d="M14.4002 6.60002C14.0819 6.60002 13.7767 6.72645 13.5517 6.9515C13.3266 7.17654 13.2002 7.48176 13.2002 7.80002C13.2002 8.11828 13.3266 8.42351 13.5517 8.64855C13.7767 8.8736 14.0819 9.00002 14.4002 9.00002C14.7185 9.00002 15.0237 8.8736 15.2487 8.64855C15.4738 8.42351 15.6002 8.11828 15.6002 7.80002C15.6002 7.48176 15.4738 7.17654 15.2487 6.9515C15.0237 6.72645 14.7185 6.60002 14.4002 6.60002ZM8.4002 7.80002C8.4002 7.48176 8.52662 7.17654 8.75167 6.9515C8.97671 6.72645 9.28194 6.60002 9.6002 6.60002C9.91846 6.60002 10.2237 6.72645 10.4487 6.9515C10.6738 7.17654 10.8002 7.48176 10.8002 7.80002C10.8002 8.11828 10.6738 8.42351 10.4487 8.64855C10.2237 8.8736 9.91846 9.00002 9.6002 9.00002C9.28194 9.00002 8.97671 8.8736 8.75167 8.64855C8.52662 8.42351 8.4002 8.11828 8.4002 7.80002ZM12.6002 3.00002C12.6002 2.84089 12.537 2.68828 12.4245 2.57576C12.3119 2.46324 12.1593 2.40002 12.0002 2.40002C11.8411 2.40002 11.6885 2.46324 11.5759 2.57576C11.4634 2.68828 11.4002 2.84089 11.4002 3.00002V3.60002H7.8002C7.32281 3.60002 6.86497 3.78967 6.5274 4.12723C6.18984 4.4648 6.0002 4.92263 6.0002 5.40002V10.2C6.0002 10.6774 6.18984 11.1353 6.5274 11.4728C6.86497 11.8104 7.32281 12 7.8002 12H15.353L15.7442 10.8H7.8002C7.64107 10.8 7.48845 10.7368 7.37593 10.6243C7.26341 10.5118 7.2002 10.3592 7.2002 10.2V5.40002C7.2002 5.24089 7.26341 5.08828 7.37593 4.97576C7.48845 4.86324 7.64107 4.80002 7.8002 4.80002H16.2002C16.3593 4.80002 16.5119 4.86324 16.6245 4.97576C16.737 5.08828 16.8002 5.24089 16.8002 5.40002V9.69962C17.1893 9.56029 17.6158 9.56711 18.0002 9.71882V5.40002C18.0002 4.92263 17.8106 4.4648 17.473 4.12723C17.1354 3.78967 16.6776 3.60002 16.2002 3.60002H12.6002V3.00002ZM10.2002 13.8H12.4898C12.1758 14.119 11.9999 14.5488 12.0002 14.9964V15.0036H6.3722C6.1144 15.0036 5.86717 15.106 5.68489 15.2883C5.5026 15.4706 5.4002 15.7178 5.4002 15.9756V16.5C5.4002 17.328 5.5574 18.2472 6.3626 18.9828C7.1882 19.7352 8.8082 20.4 12.0002 20.4C13.8602 20.4 15.1862 20.1744 16.133 19.836C16.3498 20.0789 16.6324 20.2536 16.9466 20.3388C16.8692 20.5048 16.8217 20.6831 16.8062 20.8656C15.707 21.306 14.2418 21.5736 12.3002 21.5976V21.6H11.7002V21.5976C8.561 21.558 6.6662 20.8836 5.5538 19.8696C4.5038 18.9096 4.247 17.7372 4.2062 16.8024H4.2002V15.9744C4.2002 14.7756 5.1722 13.8036 6.3722 13.8036H10.2002V13.8ZM17.8538 11.1384L18.2714 12.4236C18.401 12.8147 18.6204 13.1699 18.9119 13.461C19.2034 13.7521 19.559 13.971 19.9502 14.1L21.2354 14.5176L21.2606 14.5248C21.3597 14.5597 21.4456 14.6245 21.5063 14.7103C21.567 14.796 21.5996 14.8985 21.5996 15.0036C21.5996 15.1087 21.567 15.2112 21.5063 15.297C21.4456 15.3827 21.3597 15.4475 21.2606 15.4824L19.9754 15.9C19.5842 16.0291 19.2286 16.2479 18.9371 16.539C18.6456 16.8301 18.4262 17.1854 18.2966 17.5764L17.879 18.8604C17.8441 18.9595 17.7793 19.0454 17.6935 19.1061C17.6078 19.1669 17.5053 19.1995 17.4002 19.1995C17.2951 19.1995 17.1926 19.1669 17.1069 19.1061C17.0211 19.0454 16.9563 18.9595 16.9214 18.8604L16.5026 17.5764C16.3535 17.1222 16.0839 16.717 15.7226 16.404C15.46 16.1762 15.1549 16.0025 14.825 15.8928L13.5398 15.4752C13.4407 15.4403 13.3548 15.3755 13.2941 15.2898C13.2334 15.204 13.2007 15.1015 13.2007 14.9964C13.2007 14.8913 13.2334 14.7888 13.2941 14.7031C13.3548 14.6173 13.4407 14.5525 13.5398 14.5176L14.825 14.1C15.2113 13.9675 15.5616 13.7471 15.8484 13.4562C16.1351 13.1653 16.3504 12.8118 16.4774 12.4236L16.895 11.1396C16.9296 11.0401 16.9944 10.9538 17.0803 10.8927C17.1662 10.8317 17.269 10.7989 17.3744 10.7989C17.4798 10.7989 17.5826 10.8317 17.6685 10.8927C17.7544 10.9538 17.8192 11.0401 17.8538 11.1396M23.7398 20.6568L22.8206 20.3592C22.5418 20.2661 22.2885 20.1094 22.0806 19.9016C21.8728 19.6937 21.7161 19.4404 21.623 19.1616L21.323 18.2436C21.2982 18.1727 21.2519 18.1113 21.1906 18.0679C21.1294 18.0244 21.0561 18.0011 20.981 18.0011C20.9059 18.0011 20.8326 18.0244 20.7714 18.0679C20.7101 18.1113 20.6638 18.1727 20.639 18.2436L20.3414 19.1616C20.25 19.4386 20.0959 19.6908 19.8911 19.8985C19.6862 20.1062 19.4363 20.2639 19.1606 20.3592L18.2426 20.6568C18.1898 20.6756 18.142 20.7064 18.103 20.7468C18.0641 20.7871 18.035 20.8359 18.018 20.8894C18.0011 20.9428 17.9967 20.9995 18.0053 21.0549C18.0138 21.1103 18.0351 21.163 18.0674 21.2088C18.1106 21.2688 18.1718 21.3168 18.2426 21.3408L19.1606 21.6396C19.4403 21.7329 19.6945 21.8902 19.9028 22.0989C20.1111 22.3076 20.2679 22.5621 20.3606 22.842L20.6582 23.7588C20.683 23.8297 20.7293 23.8911 20.7906 23.9346C20.8518 23.978 20.9251 24.0014 21.0002 24.0014C21.0753 24.0014 21.1486 23.978 21.2098 23.9346C21.2711 23.8911 21.3174 23.8297 21.3422 23.7588L21.641 22.842C21.7341 22.5629 21.8909 22.3092 22.0989 22.1012C22.307 21.8931 22.5607 21.7363 22.8398 21.6432L23.7578 21.3456C23.8287 21.3208 23.8901 21.2745 23.9335 21.2133C23.977 21.152 24.0003 21.0787 24.0003 21.0036C24.0003 20.9285 23.977 20.8553 23.9335 20.794C23.8901 20.7327 23.8287 20.6865 23.7578 20.6616L23.7398 20.6568Z" fill="#8b5cf6" />
            </g>
            <defs>
                <clipPath id="clip0_201_371">
                    <rect width="24" height="24" fill="white" />
                </clipPath>
            </defs>
        </svg>
    ),
    sparkles: (props: LucideProps) => (
        <svg {...props} width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" clipRule="evenodd" d="M9.00044 4.5C9.1634 4.50003 9.32192 4.55315 9.45201 4.6513C9.5821 4.74945 9.67667 4.8873 9.72144 5.044L10.5344 7.89C10.7095 8.50292 11.0379 9.0611 11.4886 9.51183C11.9393 9.96255 12.4975 10.291 13.1104 10.466L15.9564 11.279C16.113 11.3239 16.2508 11.4185 16.3488 11.5486C16.4469 11.6786 16.4999 11.8371 16.4999 12C16.4999 12.1629 16.4469 12.3214 16.3488 12.4514C16.2508 12.5815 16.113 12.6761 15.9564 12.721L13.1104 13.534C12.4975 13.709 11.9393 14.0374 11.4886 14.4882C11.0379 14.9389 10.7095 15.4971 10.5344 16.11L9.72144 18.956C9.67658 19.1126 9.58195 19.2503 9.45187 19.3484C9.32179 19.4464 9.16333 19.4995 9.00044 19.4995C8.83754 19.4995 8.67908 19.4464 8.549 19.3484C8.41892 19.2503 8.32429 19.1126 8.27944 18.956L7.46644 16.11C7.29139 15.4971 6.96299 14.9389 6.51226 14.4882C6.06154 14.0374 5.50335 13.709 4.89044 13.534L2.04444 12.721C1.88784 12.6761 1.75011 12.5815 1.65206 12.4514C1.55401 12.3214 1.50098 12.1629 1.50098 12C1.50098 11.8371 1.55401 11.6786 1.65206 11.5486C1.75011 11.4185 1.88784 11.3239 2.04444 11.279L4.89044 10.466C5.50335 10.291 6.06154 9.96255 6.51226 9.51183C6.96299 9.0611 7.29139 8.50292 7.46644 7.89L8.27944 5.044C8.3242 4.8873 8.41878 4.74945 8.54887 4.6513C8.67895 4.55315 8.83747 4.50003 9.00044 4.5ZM18.0004 1.5C18.1678 1.49991 18.3303 1.55576 18.4622 1.65869C18.5941 1.76161 18.6878 1.90569 18.7284 2.068L18.9864 3.104C19.1023 3.56533 19.3412 3.98659 19.6775 4.32294C20.0138 4.65928 20.4351 4.89811 20.8964 5.014L21.9324 5.272C22.0951 5.31228 22.2395 5.40586 22.3428 5.5378C22.446 5.66974 22.5021 5.83246 22.5021 6C22.5021 6.16754 22.446 6.33026 22.3428 6.4622C22.2395 6.59414 22.0951 6.68772 21.9324 6.728L20.8964 6.986C20.4351 7.10189 20.0138 7.34072 19.6775 7.67706C19.3412 8.01341 19.1023 8.43467 18.9864 8.896L18.7284 9.932C18.6882 10.0946 18.5946 10.2391 18.4626 10.3423C18.3307 10.4456 18.168 10.5017 18.0004 10.5017C17.8329 10.5017 17.6702 10.4456 17.5382 10.3423C17.4063 10.2391 17.3127 10.0946 17.2724 9.932L17.0144 8.896C16.8985 8.43467 16.6597 8.01341 16.3234 7.67706C15.987 7.34072 15.5658 7.10189 15.1044 6.986L14.0684 6.728C13.9058 6.68772 13.7614 6.59414 13.6581 6.4622C13.5549 6.33026 13.4988 6.16754 13.4988 6C13.4988 5.83246 13.5549 5.66974 13.6581 5.5378C13.7614 5.40586 13.9058 5.31228 14.0684 5.272L15.1044 5.014C15.5658 4.89811 15.987 4.65928 16.3234 4.32294C16.6597 3.98659 16.8985 3.56533 17.0144 3.104L17.2724 2.068C17.313 1.90569 17.4067 1.76161 17.5387 1.65869C17.6706 1.55576 17.8331 1.49991 18.0004 1.5ZM16.5004 15C16.658 14.9999 16.8115 15.0494 16.9393 15.1415C17.0671 15.2336 17.1627 15.3636 17.2124 15.513L17.6064 16.696C17.7564 17.143 18.1064 17.495 18.5544 17.644L19.7374 18.039C19.8864 18.089 20.0159 18.1845 20.1076 18.3121C20.1994 18.4397 20.2487 18.5929 20.2487 18.75C20.2487 18.9071 20.1994 19.0603 20.1076 19.1879C20.0159 19.3155 19.8864 19.411 19.7374 19.461L18.5544 19.856C18.3337 19.9297 18.1332 20.0537 17.9686 20.2182C17.8041 20.3827 17.6801 20.5833 17.6064 20.804L17.2114 21.987C17.1614 22.136 17.0659 22.2655 16.9383 22.3572C16.8107 22.4489 16.6576 22.4983 16.5004 22.4983C16.3433 22.4983 16.1901 22.4489 16.0626 22.3572C15.935 22.2655 15.8394 22.136 15.7894 21.987L15.3944 20.804C15.3208 20.5833 15.1968 20.3827 15.0322 20.2182C14.8677 20.0537 14.6672 19.9297 14.4464 19.856L13.2634 19.461C13.1145 19.411 12.985 19.3155 12.8932 19.1879C12.8015 19.0603 12.7521 18.9071 12.7521 18.75C12.7521 18.5929 12.8015 18.4397 12.8932 18.3121C12.985 18.1845 13.1145 18.089 13.2634 18.039L14.4464 17.644C14.6672 17.5703 14.8677 17.4463 15.0322 17.2818C15.1968 17.1173 15.3208 16.9167 15.3944 16.696L15.7894 15.513C15.8392 15.3637 15.9346 15.2339 16.0622 15.1418C16.1898 15.0497 16.3431 15.0001 16.5004 15Z" fill="#6366f1" />
        </svg>
    ),
    store: (props: LucideProps) => (
        <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
            <path d="m12.954 11.616 2.957-2.957L6.36 3.291c-.633-.342-1.226-.39-1.746-.016l8.34 8.341zm3.461 3.462 3.074-1.729c.6-.336.929-.812.929-1.34 0-.527-.329-1.004-.928-1.34l-2.783-1.563-3.133 3.132 2.841 2.84zM4.1 4.002c-.064.197-.1.417-.1.658v14.705c0 .381.084.709.236.97l8.097-8.098L4.1 4.002zm8.854 8.855L4.902 20.91c.154.059.32.09.495.09.312 0 .637-.092.968-.276l9.255-5.197-2.666-2.67z" fill="currentColor" />
        </svg>
    ),
    apple: (props: LucideProps) => (
        <svg {...props} xmlns="http://www.w3.org/2000/svg" width="256" height="314.4" viewBox="0 0 256 314.4" preserveAspectRatio="xMidYMid">
            <g>
                <path d="M213.803394,167.030943 C214.2452,214.609646 255.542482,230.442639 256,230.644727 C255.650812,231.761357 249.401383,253.208293 234.24263,275.361446 C221.138555,294.513969 207.538253,313.596333 186.113759,313.991545 C165.062051,314.379442 158.292752,301.507828 134.22469,301.507828 C110.163898,301.507828 102.642899,313.596301 82.7151126,314.379442 C62.0350407,315.16201 46.2873831,293.668525 33.0744079,274.586162 C6.07529317,235.552544 -14.5576169,164.286328 13.147166,116.18047 C26.9103111,92.2909053 51.5060917,77.1630356 78.2026125,76.7751096 C98.5099145,76.3877456 117.677594,90.4371851 130.091705,90.4371851 C142.497945,90.4371851 165.790755,73.5415029 190.277627,76.0228474 C200.528668,76.4495055 229.303509,80.1636878 247.780625,107.209389 C246.291825,108.132333 213.44635,127.253405 213.803394,167.030988 M174.239142,50.1987033 C185.218331,36.9088319 192.607958,18.4081019 190.591988,0 C174.766312,0.636050225 155.629514,10.5457909 144.278109,23.8283506 C134.10507,35.5906758 125.195775,54.4170275 127.599657,72.4607932 C145.239231,73.8255433 163.259413,63.4970262 174.239142,50.1987249" fill="#000000" />
            </g>
        </svg>
    )
};

export default Icons;