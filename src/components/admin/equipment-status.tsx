"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Shi<PERSON>, 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  RotateCcw,
  Calendar,
  User
} from "lucide-react"

interface EMSEquipment {
  id: number
  name: string
  status: 'available' | 'in-use' | 'maintenance'
  lastCleaned: string
  nextMaintenance: string
  usageCount: number
  currentUser?: string
  size: 'S' | 'M' | 'L' | 'XL'
  batteryLevel: number
}

export function EquipmentStatus() {
  // Mock data for EMS equipment
  const [equipment, setEquipment] = useState<EMSEquipment[]>([
    {
      id: 1,
      name: "EMS Suit #1",
      status: 'available',
      lastCleaned: "2023-05-15T10:30:00",
      nextMaintenance: "2023-06-15T10:30:00",
      usageCount: 42,
      size: 'M',
      batteryLevel: 100
    },
    {
      id: 2,
      name: "EMS Suit #2",
      status: 'in-use',
      lastCleaned: "2023-05-14T14:15:00",
      nextMaintenance: "2023-06-14T14:15:00",
      usageCount: 38,
      currentUser: "<PERSON>",
      size: 'S',
      batteryLevel: 78
    },
    {
      id: 3,
      name: "EMS Suit #3",
      status: 'maintenance',
      lastCleaned: "2023-05-10T09:00:00",
      nextMaintenance: "2023-05-17T09:00:00",
      usageCount: 51,
      size: 'L',
      batteryLevel: 25
    },
    {
      id: 4,
      name: "EMS Suit #4",
      status: 'available',
      lastCleaned: "2023-05-15T16:45:00",
      nextMaintenance: "2023-06-15T16:45:00",
      usageCount: 29,
      size: 'M',
      batteryLevel: 92
    },
    {
      id: 5,
      name: "EMS Suit #5",
      status: 'in-use',
      lastCleaned: "2023-05-13T11:20:00",
      nextMaintenance: "2023-06-13T11:20:00",
      usageCount: 45,
      currentUser: "Michael Brown",
      size: 'XL',
      batteryLevel: 65
    }
  ])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-500/20 text-green-400'
      case 'in-use': return 'bg-blue-500/20 text-blue-400'
      case 'maintenance': return 'bg-yellow-500/20 text-yellow-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available': return <CheckCircle className="w-4 h-4" />
      case 'in-use': return <User className="w-4 h-4" />
      case 'maintenance': return <AlertCircle className="w-4 h-4" />
      default: return <Clock className="w-4 h-4" />
    }
  }

  const getBatteryColor = (level: number) => {
    if (level > 70) return 'bg-green-500'
    if (level > 30) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  const markAsClean = (id: number) => {
    setEquipment(equipment.map(item => 
      item.id === id 
        ? { ...item, lastCleaned: new Date().toISOString(), status: 'available' as const } 
        : item
    ))
  }

  const markAsMaintenance = (id: number) => {
    setEquipment(equipment.map(item => 
      item.id === id 
        ? { ...item, status: 'maintenance' as const } 
        : item
    ))
  }

  return (
    <Card className="bg-gray-900/50 border-gray-800">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <Shirt className="w-5 h-5 text-pink-400" />
          EMS Equipment Status
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Summary Stats */}
          <div className="grid grid-cols-3 gap-4 mb-6">
            <div className="p-4 bg-gray-800/50 rounded-lg text-center">
              <div className="text-2xl font-bold text-green-400">{equipment.filter(e => e.status === 'available').length}</div>
              <div className="text-xs text-gray-400">Available</div>
            </div>
            <div className="p-4 bg-gray-800/50 rounded-lg text-center">
              <div className="text-2xl font-bold text-blue-400">{equipment.filter(e => e.status === 'in-use').length}</div>
              <div className="text-xs text-gray-400">In Use</div>
            </div>
            <div className="p-4 bg-gray-800/50 rounded-lg text-center">
              <div className="text-2xl font-bold text-yellow-400">{equipment.filter(e => e.status === 'maintenance').length}</div>
              <div className="text-xs text-gray-400">Maintenance</div>
            </div>
          </div>

          {/* Equipment List */}
          <div className="space-y-4">
            {equipment.map((item) => (
              <div key={item.id} className="p-4 border border-gray-800 rounded-lg">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-pink-500/20 flex items-center justify-center">
                      <Shirt className="w-5 h-5 text-pink-400" />
                    </div>
                    <div>
                      <div className="font-medium text-white">{item.name}</div>
                      <div className="text-xs text-gray-400">Size: {item.size} • Used {item.usageCount} times</div>
                    </div>
                  </div>
                  <Badge className={getStatusColor(item.status)}>
                    <span className="flex items-center gap-1">
                      {getStatusIcon(item.status)}
                      {item.status === 'available' ? 'Available' : 
                       item.status === 'in-use' ? 'In Use' : 'Maintenance'}
                    </span>
                  </Badge>
                </div>

                {/* Battery Level */}
                <div className="mb-4">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs text-gray-400">Battery Level</span>
                    <span className="text-xs font-medium text-white">{item.batteryLevel}%</span>
                  </div>
                  <div className="h-2 bg-gray-800 rounded-full overflow-hidden">
                    <div 
                      className={`h-full ${getBatteryColor(item.batteryLevel)} rounded-full`}
                      style={{ width: `${item.batteryLevel}%` }}
                    ></div>
                  </div>
                </div>

                {/* Additional Info */}
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <div className="flex items-center gap-1 mb-1">
                      <Clock className="w-3 h-3 text-gray-400" />
                      <span className="text-xs text-gray-400">Last Cleaned</span>
                    </div>
                    <div className="text-sm text-white">
                      {new Date(item.lastCleaned).toLocaleDateString()}
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center gap-1 mb-1">
                      <Calendar className="w-3 h-3 text-gray-400" />
                      <span className="text-xs text-gray-400">Next Maintenance</span>
                    </div>
                    <div className="text-sm text-white">
                      {new Date(item.nextMaintenance).toLocaleDateString()}
                    </div>
                  </div>
                </div>

                {/* Current User (if in use) */}
                {item.status === 'in-use' && item.currentUser && (
                  <div className="mb-4 p-2 bg-blue-500/10 border border-blue-500/20 rounded">
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4 text-blue-400" />
                      <span className="text-sm text-blue-400">Currently used by: {item.currentUser}</span>
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex gap-2 justify-end">
                  {item.status !== 'in-use' && (
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => markAsClean(item.id)}
                      className="text-green-400 border-green-500/50"
                    >
                      <RotateCcw className="w-4 h-4 mr-1" />
                      Mark as Cleaned
                    </Button>
                  )}
                  {item.status === 'available' && (
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => markAsMaintenance(item.id)}
                      className="text-yellow-400 border-yellow-500/50"
                    >
                      <AlertCircle className="w-4 h-4 mr-1" />
                      Schedule Maintenance
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}