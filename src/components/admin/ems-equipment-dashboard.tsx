"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { supabase } from "@/lib/supabase/client"
import { toast } from "sonner"
import { 
  Shirt, 
  Battery, 
  AlertCircle, 
  CheckCircle,
  Loader2,
  RotateCcw,
  Calendar,
  User,
  Zap
} from "lucide-react"

interface EMSEquipment {
  id: number
  name: string
  status: 'available' | 'in-use' | 'maintenance'
  lastCleaned: string
  nextMaintenance: string
  usageCount: number
  currentUser?: string
  size: 'S' | 'M' | 'L' | 'XL'
  batteryLevel: number
}

export function EMSEquipmentDashboard() {
  const [loading, setLoading] = useState(true)
  const [equipment, setEquipment] = useState<EMSEquipment[]>([])
  const [activeCheckIns, setActiveCheckIns] = useState<any[]>([])

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get active check-ins
        const { data: checkIns, error: checkInsError } = await supabase
          .from('check_ins')
          .select('*, profiles(first_name, last_name)')
          .is('check_out_time', null)
        
        if (checkInsError) throw checkInsError
        
        setActiveCheckIns(checkIns || [])
        
        // Mock equipment data - in a real app, this would come from a database
        const mockEquipment: EMSEquipment[] = [
          {
            id: 1,
            name: "EMS Suit #1",
            status: 'available',
            lastCleaned: new Date().toISOString(),
            nextMaintenance: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            usageCount: 42,
            size: 'M',
            batteryLevel: 100
          },
          {
            id: 2,
            name: "EMS Suit #2",
            status: checkIns && checkIns.length > 0 ? 'in-use' : 'available',
            lastCleaned: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            nextMaintenance: new Date(Date.now() + 29 * 24 * 60 * 60 * 1000).toISOString(),
            usageCount: 38,
            currentUser: checkIns && checkIns.length > 0 ? 
              `${checkIns[0].profiles.first_name} ${checkIns[0].profiles.last_name}` : 
              undefined,
            size: 'S',
            batteryLevel: 78
          },
          {
            id: 3,
            name: "EMS Suit #3",
            status: 'maintenance',
            lastCleaned: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
            nextMaintenance: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
            usageCount: 51,
            size: 'L',
            batteryLevel: 25
          },
          {
            id: 4,
            name: "EMS Suit #4",
            status: 'available',
            lastCleaned: new Date().toISOString(),
            nextMaintenance: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            usageCount: 29,
            size: 'M',
            batteryLevel: 92
          },
          {
            id: 5,
            name: "EMS Suit #5",
            status: checkIns && checkIns.length > 1 ? 'in-use' : 'available',
            lastCleaned: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            nextMaintenance: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000).toISOString(),
            usageCount: 45,
            currentUser: checkIns && checkIns.length > 1 ? 
              `${checkIns[1].profiles.first_name} ${checkIns[1].profiles.last_name}` : 
              undefined,
            size: 'XL',
            batteryLevel: 65
          }
        ]
        
        setEquipment(mockEquipment)
      } catch (error) {
        console.error("Error fetching equipment data:", error)
      } finally {
        setLoading(false)
      }
    }
    
    fetchData()
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-500/20 text-green-400'
      case 'in-use': return 'bg-blue-500/20 text-blue-400'
      case 'maintenance': return 'bg-yellow-500/20 text-yellow-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available': return <CheckCircle className="w-4 h-4" />
      case 'in-use': return <User className="w-4 h-4" />
      case 'maintenance': return <AlertCircle className="w-4 h-4" />
      default: return null
    }
  }

  const getBatteryColor = (level: number) => {
    if (level > 70) return 'bg-green-500'
    if (level > 30) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  const markAsClean = (id: number) => {
    setEquipment(equipment.map(item => 
      item.id === id 
        ? { ...item, lastCleaned: new Date().toISOString(), status: 'available' } 
        : item
    ))
    toast.success(`${equipment.find(e => e.id === id)?.name} marked as cleaned`)
  }

  const markAsMaintenance = (id: number) => {
    setEquipment(equipment.map(item => 
      item.id === id 
        ? { ...item, status: 'maintenance' } 
        : item
    ))
    toast.success(`${equipment.find(e => e.id === id)?.name} scheduled for maintenance`)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 animate-spin text-pink-400" />
      </div>
    )
  }

  // Count equipment by status
  const availableCount = equipment.filter(e => e.status === 'available').length
  const inUseCount = equipment.filter(e => e.status === 'in-use').length
  const maintenanceCount = equipment.filter(e => e.status === 'maintenance').length
  const totalCount = equipment.length

  return (
    <div className="space-y-8">
      {/* Summary Stats */}
      <div className="grid grid-cols-3 gap-4">
        <Card className="bg-gray-900/50 border-gray-800">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-400">{availableCount}</div>
            <div className="text-xs text-gray-400">Available</div>
          </CardContent>
        </Card>
        <Card className="bg-gray-900/50 border-gray-800">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-400">{inUseCount}</div>
            <div className="text-xs text-gray-400">In Use</div>
          </CardContent>
        </Card>
        <Card className="bg-gray-900/50 border-gray-800">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-400">{maintenanceCount}</div>
            <div className="text-xs text-gray-400">Maintenance</div>
          </CardContent>
        </Card>
      </div>

      {/* Equipment Status Bars */}
      <Card className="bg-gray-900/50 border-gray-800">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Zap className="w-5 h-5 text-pink-400" />
            EMS Equipment Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div>
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center gap-2">
                  <Battery className="w-4 h-4 text-green-400" />
                  <span className="text-sm font-medium text-gray-300">Available EMS Suits</span>
                </div>
                <span className="text-sm font-medium text-green-400">
                  {availableCount} / {totalCount}
                </span>
              </div>
              <Progress 
                value={(availableCount / totalCount) * 100} 
                className="h-2 bg-gray-800"
                indicatorClassName="bg-green-500"
              />
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4 text-blue-400" />
                  <span className="text-sm font-medium text-gray-300">EMS Suits In Use</span>
                </div>
                <span className="text-sm font-medium text-blue-400">
                  {inUseCount} / {totalCount}
                </span>
              </div>
              <Progress 
                value={(inUseCount / totalCount) * 100} 
                className="h-2 bg-gray-800"
                indicatorClassName="bg-blue-500"
              />
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center gap-2">
                  <AlertCircle className="w-4 h-4 text-yellow-400" />
                  <span className="text-sm font-medium text-gray-300">EMS Suits In Maintenance</span>
                </div>
                <span className="text-sm font-medium text-yellow-400">
                  {maintenanceCount} / {totalCount}
                </span>
              </div>
              <Progress 
                value={(maintenanceCount / totalCount) * 100} 
                className="h-2 bg-gray-800"
                indicatorClassName="bg-yellow-500"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Equipment List */}
      <div className="space-y-4">
        {equipment.map((item) => (
          <Card key={item.id} className="bg-gray-900/50 border-gray-800">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-pink-500/20 flex items-center justify-center">
                    <Shirt className="w-5 h-5 text-pink-400" />
                  </div>
                  <div>
                    <div className="font-medium text-white">{item.name}</div>
                    <div className="text-xs text-gray-400">Size: {item.size} • Used {item.usageCount} times</div>
                  </div>
                </div>
                <Badge className={getStatusColor(item.status)}>
                  <span className="flex items-center gap-1">
                    {getStatusIcon(item.status)}
                    {item.status === 'available' ? 'Available' : 
                     item.status === 'in-use' ? 'In Use' : 'Maintenance'}
                  </span>
                </Badge>
              </div>

              {/* Battery Level */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-xs text-gray-400">Battery Level</span>
                  <span className="text-xs font-medium text-white">{item.batteryLevel}%</span>
                </div>
                <div className="h-2 bg-gray-800 rounded-full overflow-hidden">
                  <div 
                    className={`h-full ${getBatteryColor(item.batteryLevel)} rounded-full`}
                    style={{ width: `${item.batteryLevel}%` }}
                  ></div>
                </div>
              </div>

              {/* Additional Info */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <div className="flex items-center gap-1 mb-1">
                    <RotateCcw className="w-3 h-3 text-gray-400" />
                    <span className="text-xs text-gray-400">Last Cleaned</span>
                  </div>
                  <div className="text-sm text-white">
                    {new Date(item.lastCleaned).toLocaleDateString()}
                  </div>
                </div>
                <div>
                  <div className="flex items-center gap-1 mb-1">
                    <Calendar className="w-3 h-3 text-gray-400" />
                    <span className="text-xs text-gray-400">Next Maintenance</span>
                  </div>
                  <div className="text-sm text-white">
                    {new Date(item.nextMaintenance).toLocaleDateString()}
                  </div>
                </div>
              </div>

              {/* Current User (if in use) */}
              {item.status === 'in-use' && item.currentUser && (
                <div className="mb-4 p-2 bg-blue-500/10 border border-blue-500/20 rounded">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 text-blue-400" />
                    <span className="text-sm text-blue-400">Currently used by: {item.currentUser}</span>
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex gap-2 justify-end">
                {item.status !== 'in-use' && (
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => markAsClean(item.id)}
                    className="text-green-400 border-green-500/50"
                  >
                    <RotateCcw className="w-4 h-4 mr-1" />
                    Mark as Cleaned
                  </Button>
                )}
                {item.status === 'available' && (
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => markAsMaintenance(item.id)}
                    className="text-yellow-400 border-yellow-500/50"
                  >
                    <AlertCircle className="w-4 h-4 mr-1" />
                    Schedule Maintenance
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}