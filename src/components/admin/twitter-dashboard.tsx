"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Users, 
  TrendingUp, 
  Activity, 
  DollarSign,
  MessageSquare,
  Calendar,
  MoreHorizontal,
  Heart,
  MessageCircle,
  Repeat2,
  Share
} from "lucide-react"
import { motion } from "framer-motion"
import { supabase } from "@/lib/supabase/client"

interface DashboardStats {
  totalMembers: number
  activeToday: number
  monthlyRevenue: number
  pendingFeedback: number
}

interface ActivityItem {
  id: string
  type: 'checkin' | 'signup' | 'payment' | 'feedback'
  user: string
  action: string
  time: string
  avatar?: string
}

export function TwitterDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalMembers: 0,
    activeToday: 0,
    monthlyRevenue: 0,
    pendingFeedback: 0
  })
  const [activities, setActivities] = useState<ActivityItem[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      // Fetch real stats from Supabase
      const [profilesResponse, checkInsResponse, paymentsResponse, feedbackResponse] = await Promise.all([
        supabase.from('profiles').select('*'),
        supabase.from('check_ins').select('*').gte('check_in_time', new Date().toISOString().split('T')[0]),
        supabase.from('payments').select('amount').gte('created_at', new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString()),
        supabase.from('feedback').select('*').eq('status', 'pending')
      ])

      const totalMembers = profilesResponse.data?.length || 0
      const activeToday = checkInsResponse.data?.length || 0
      const monthlyRevenue = paymentsResponse.data?.reduce((sum, payment) => sum + payment.amount, 0) || 0
      const pendingFeedback = feedbackResponse.data?.length || 0

      setStats({
        totalMembers,
        activeToday,
        monthlyRevenue,
        pendingFeedback
      })

      // Fetch recent activities
      const recentCheckIns = await supabase
        .from('check_ins')
        .select(`
          *,
          profiles:user_id (first_name, last_name, email)
        `)
        .order('check_in_time', { ascending: false })
        .limit(10)

      const activityItems: ActivityItem[] = recentCheckIns.data?.map(checkin => ({
        id: checkin.id,
        type: 'checkin' as const,
        user: `${checkin.profiles?.first_name || 'Member'} ${checkin.profiles?.last_name || ''}`.trim(),
        action: 'checked in to the gym',
        time: new Date(checkin.check_in_time).toLocaleTimeString(),
        avatar: `https://api.dicebear.com/9.x/adventurer/svg?seed=${checkin.user_id}`
      })) || []

      setActivities(activityItems)
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'pink':
        return { bg: 'bg-pink-500/10', text: 'text-pink-400' }
      case 'blue':
        return { bg: 'bg-blue-500/10', text: 'text-blue-400' }
      case 'green':
        return { bg: 'bg-green-500/10', text: 'text-green-400' }
      case 'orange':
        return { bg: 'bg-orange-500/10', text: 'text-orange-400' }
      default:
        return { bg: 'bg-gray-500/10', text: 'text-gray-400' }
    }
  }

  const StatCard = ({ icon: Icon, title, value, change, color }: any) => {
    const colorClasses = getColorClasses(color)

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-card rounded-2xl p-4 lg:p-6 border border-border hover:border-pink-500/30 transition-all duration-200"
      >
        <div className="flex items-center justify-between mb-4">
          <div className={`p-2 rounded-full ${colorClasses.bg}`}>
            <Icon className={`w-5 h-5 lg:w-6 lg:h-6 ${colorClasses.text}`} />
          </div>
          <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>
        <div className="space-y-1">
          <p className="text-xl lg:text-2xl font-bold text-foreground">{value}</p>
          <p className="text-xs lg:text-sm text-muted-foreground">{title}</p>
          {change && (
            <div className="flex items-center gap-1">
              <TrendingUp className="w-3 h-3 text-green-400" />
              <span className="text-xs text-green-400">{change}</span>
            </div>
          )}
        </div>
      </motion.div>
    )
  }

  const ActivityFeed = () => (
    <div className="space-y-4">
      <h3 className="text-lg lg:text-xl font-bold text-foreground mb-4">Recent Activity</h3>
      {activities.length === 0 ? (
        <p className="text-muted-foreground text-center py-8">No recent activity</p>
      ) : (
        activities.map((activity, index) => (
          <motion.div
            key={activity.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-muted/30 rounded-2xl p-3 lg:p-4 border border-border hover:border-muted transition-all duration-200"
          >
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 lg:w-10 lg:h-10 rounded-full bg-gradient-to-br from-pink-500 to-pink-400 flex items-center justify-center shrink-0">
                <Activity className="w-4 h-4 lg:w-5 lg:h-5 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 mb-1">
                  <span className="font-medium text-foreground text-sm lg:text-base truncate">{activity.user}</span>
                  <span className="text-muted-foreground text-xs lg:text-sm">{activity.action}</span>
                </div>
                <p className="text-xs text-muted-foreground">{activity.time}</p>
              </div>
              <div className="flex items-center gap-1 lg:gap-2 shrink-0">
                <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-pink-400 h-8 w-8 p-0">
                  <Heart className="w-3 h-3 lg:w-4 lg:h-4" />
                </Button>
                <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-blue-400 h-8 w-8 p-0">
                  <MessageCircle className="w-3 h-3 lg:w-4 lg:h-4" />
                </Button>
              </div>
            </div>
          </motion.div>
        ))
      )}
    </div>
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-2 border-pink-500 border-t-transparent rounded-full animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-4 lg:space-y-6 w-full">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 lg:gap-6">
        <StatCard
          icon={Users}
          title="Total Members"
          value={stats.totalMembers}
          change={null}
          color="pink"
        />
        <StatCard
          icon={Activity}
          title="Active Today"
          value={stats.activeToday}
          change={null}
          color="blue"
        />
        <StatCard
          icon={DollarSign}
          title="Monthly Revenue"
          value={`R${stats.monthlyRevenue.toLocaleString()}`}
          change={null}
          color="green"
        />
        <StatCard
          icon={MessageSquare}
          title="Pending Feedback"
          value={stats.pendingFeedback}
          change={null}
          color="orange"
        />
      </div>

      {/* Activity Feed */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 lg:gap-6">
        <div className="xl:col-span-2">
          <Card className="border border-border h-full">
            <CardHeader className="pb-3 lg:pb-6">
              <CardTitle className="text-lg lg:text-xl">Member Activity</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <ActivityFeed />
            </CardContent>
          </Card>
        </div>
        <div className="xl:col-span-1">
          <Card className="border border-border h-full">
            <CardHeader className="pb-3 lg:pb-6">
              <CardTitle className="text-lg lg:text-xl">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 lg:space-y-4 pt-0">
              <Button className="w-full bg-gradient-to-r from-pink-500 to-pink-400 hover:from-pink-600 hover:to-pink-500 text-white">
                Add New Member
              </Button>
              <Button variant="outline" className="w-full">
                Send Announcement
              </Button>
              <Button variant="outline" className="w-full">
                Generate Reports
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
