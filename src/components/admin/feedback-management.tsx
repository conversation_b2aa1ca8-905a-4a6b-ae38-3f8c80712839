"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { NeonButton } from "@/components/ui/neon-button"
import { supabase } from "@/lib/supabase/client"
import {
  MessageSquare,
  Star,
  Clock,
  Send,
  Filter,
  CheckCircle,
  AlertCircle,
  Loader2,
  Trash2
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface FeedbackItem {
  id: string
  memberName: string
  memberAvatar?: string
  category: string
  rating: number
  comment: string
  submittedAt: Date
  status: "pending" | "responded" | "resolved"
  response?: string
  respondedAt?: Date
  respondedBy?: string
}

interface FeedbackManagementProps {
  className?: string
}

export function FeedbackManagement({ className }: FeedbackManagementProps) {
  const [filterStatus, setFilterStatus] = useState<string>("all")
  const [responseText, setResponseText] = useState<{ [key: string]: string }>({})
  const [feedbackItems, setFeedbackItems] = useState<FeedbackItem[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchFeedback()
  }, [])

  const fetchFeedback = async () => {
    try {
      const { data: contactSubmissions, error } = await supabase
        .from('contact_submissions')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error

      // Transform contact submissions to feedback format
      const feedback: FeedbackItem[] = (contactSubmissions || []).map(submission => ({
        id: submission.id,
        memberName: `${submission.first_name} ${submission.last_name}`,
        category: submission.subject.toLowerCase().includes('workout') ? 'workout' :
                 submission.subject.toLowerCase().includes('facility') ? 'facility' :
                 submission.subject.toLowerCase().includes('trainer') ? 'trainer' : 'general',
        rating: submission.rating || 0,
        comment: submission.message,
        submittedAt: new Date(submission.created_at),
        status: submission.status === 'unread' ? 'pending' :
               submission.status === 'read' ? 'responded' : 'resolved',
        response: submission.admin_notes || undefined,
        respondedAt: submission.responded_at ? new Date(submission.responded_at) : undefined,
        respondedBy: submission.responded_by ? 'Admin' : undefined
      }))

      setFeedbackItems(feedback)
    } catch (error) {
      console.error('Error fetching feedback:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredFeedback = feedbackItems.filter(item => {
    return filterStatus === "all" || item.status === filterStatus
  })

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "workout": return "bg-member/20 text-member"
      case "facility": return "bg-admin/20 text-admin"
      case "trainer": return "bg-blog/20 text-blog"
      case "general": return "bg-communication/20 text-communication"
      default: return "bg-secondary text-secondary-foreground"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending": return "bg-marketing text-marketing-foreground"
      case "responded": return "bg-admin text-admin-foreground"
      case "resolved": return "bg-blog text-blog-foreground"
      default: return "bg-secondary text-secondary-foreground"
    }
  }

  const handleResponse = async (feedbackId: string) => {
    const response = responseText[feedbackId]
    if (!response?.trim()) return

    try {
      const { data: { user } } = await supabase.auth.getUser()

      const { error } = await supabase
        .from('contact_submissions')
        .update({
          status: 'responded',
          admin_notes: response,
          responded_by: user?.id,
          responded_at: new Date().toISOString()
        })
        .eq('id', feedbackId)

      if (error) throw error

      // Clear the response text
      setResponseText(prev => ({ ...prev, [feedbackId]: "" }))

      // Refresh feedback data
      await fetchFeedback()

      alert("Response sent successfully!")
    } catch (error) {
      console.error('Error sending response:', error)
      alert("Failed to send response. Please try again.")
    }
  }

  const handleDeleteFeedback = async (feedbackId: string) => {
    if (!confirm("Are you sure you want to delete this feedback?")) return

    try {
      const { error } = await supabase
        .from('contact_submissions')
        .delete()
        .eq('id', feedbackId)

      if (error) throw error
      await fetchFeedback()
      alert("Feedback deleted successfully!")
    } catch (error) {
      console.error('Error deleting feedback:', error)
      alert("Failed to delete feedback. Please try again.")
    }
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < rating ? "fill-member text-member" : "text-muted-foreground"
        }`}
      />
    ))
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-64">
          <Loader2 className="w-8 h-8 animate-spin text-pink-400" />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="w-5 h-5 text-communication" />
            Feedback Management
          </CardTitle>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                Filter: {filterStatus === "all" ? "All" : filterStatus}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setFilterStatus("all")}>
                All Feedback
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus("pending")}>
                Pending
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus("responded")}>
                Responded
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus("resolved")}>
                Resolved
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-6">
          {filteredFeedback.map((feedback) => (
            <div key={feedback.id} className="border rounded-lg p-4 space-y-4">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="w-10 h-10">
                    <AvatarImage src={feedback.memberAvatar} alt={feedback.memberName} />
                    <AvatarFallback>
                      {feedback.memberName.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h4 className="font-medium">{feedback.memberName}</h4>
                    <div className="flex items-center gap-2">
                      <Badge className={getCategoryColor(feedback.category)}>
                        {feedback.category}
                      </Badge>
                      <div className="flex">{renderStars(feedback.rating)}</div>
                    </div>
                  </div>
                </div>
                
                <div className="text-right">
                  <Badge className={getStatusColor(feedback.status)}>
                    {feedback.status}
                  </Badge>
                  <div className="text-xs text-muted-foreground mt-1 flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {feedback.submittedAt.toLocaleString()}
                  </div>
                </div>
              </div>
              
              {/* Feedback Content */}
              <div className="bg-muted/50 rounded-lg p-3">
                <p className="text-sm">{feedback.comment}</p>
              </div>
              
              {/* Admin Actions */}
              <div className="flex justify-end gap-2">
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => handleDeleteFeedback(feedback.id)}
                >
                  <Trash2 className="w-4 h-4 mr-1" />
                  Delete
                </Button>
              </div>
              
              {/* Response Section */}
              {feedback.response && (
                <div className="bg-communication/10 rounded-lg p-3 border-l-2 border-communication">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="w-4 h-4 text-communication" />
                    <span className="text-sm font-medium text-communication">
                      Response from {feedback.respondedBy}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {feedback.respondedAt?.toLocaleString()}
                    </span>
                  </div>
                  <p className="text-sm">{feedback.response}</p>
                </div>
              )}
              
              {/* Response Form for Pending Items */}
              {feedback.status === "pending" && (
                <div className="space-y-3">
                  <Textarea
                    placeholder="Write your response..."
                    value={responseText[feedback.id] || ""}
                    onChange={(e) => setResponseText(prev => ({
                      ...prev,
                      [feedback.id]: e.target.value
                    }))}
                    className="min-h-[80px]"
                  />
                  <div className="flex justify-end">
                    <NeonButton
                      variant="communication"
                      size="sm"
                      onClick={() => handleResponse(feedback.id)}
                      disabled={!responseText[feedback.id]?.trim()}
                    >
                      <Send className="w-4 h-4 mr-2" />
                      Send Response
                    </NeonButton>
                  </div>
                </div>
              )}
            </div>
          ))}
          
          {filteredFeedback.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No feedback found for the selected filter.</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
