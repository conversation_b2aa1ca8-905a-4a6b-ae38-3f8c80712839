"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { supabase } from "@/lib/supabase/client"
import Link from "next/link"
import { 
  Users, 
  Activity, 
  Zap, 
  Calendar, 
  Clock, 
  Battery, 
  AlertCircle,
  CheckCircle,
  Loader2,
  ArrowRight,
  MessageSquare,
  DollarSign,
  Shirt
} from "lucide-react"

interface DashboardStats {
  totalMembers: number
  activeMembers: number
  todayCheckIns: number
  pendingFeedback: number
  equipmentStatus: {
    available: number
    inUse: number
    maintenance: number
  }
  recentCheckIns: { id: string; check_in_time: string; profiles: { first_name: string; last_name: string } }[];
  contactSubmissions: number
}

export function EMSDashboard() {
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState<DashboardStats>({
    totalMembers: 0,
    activeMembers: 0,
    todayCheckIns: 0,
    pendingFeedback: 0,
    equipmentStatus: {
      available: 0,
      inUse: 0,
      maintenance: 0
    },
    recentCheckIns: [],
    contactSubmissions: 0
  })

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch profiles
        const { data: profiles, error: profilesError } = await supabase
          .from('profiles')
          .select('*')
        
        if (profilesError) throw profilesError

        // Fetch today's check-ins
        const today = new Date()
        today.setHours(0, 0, 0, 0)
        
        const { data: checkIns, error: checkInsError } = await supabase
          .from('check_ins')
          .select('*')
          .gte('check_in_time', today.toISOString())
        
        if (checkInsError) throw checkInsError

        // Fetch pending feedback
        const { data: feedback, error: feedbackError } = await supabase
          .from('feedback')
          .select('*')
          .eq('status', 'pending')
        
        if (feedbackError) throw feedbackError

        // Fetch contact submissions
        const { data: contactSubmissions, error: contactError } = await supabase
          .from('contact_submissions')
          .select('*')
          .eq('status', 'unread')
        
        if (contactError) throw contactError

        // Get recent check-ins
        const { data: recentCheckIns, error: recentError } = await supabase
          .from('check_ins')
          .select('*, profiles(first_name, last_name)')
          .order('check_in_time', { ascending: false })
          .limit(5)
        
        if (recentError) throw recentError

        setStats({
          totalMembers: profiles?.length || 0,
          activeMembers: profiles?.filter(p => p.membership_status === 'active').length || 0,
          todayCheckIns: checkIns?.length || 0,
          pendingFeedback: feedback?.length || 0,
          equipmentStatus: {
            available: 8, // Simulated EMS equipment data
            inUse: checkIns?.filter(c => !c.check_out_time).length || 0,
            maintenance: 2  // Simulated maintenance data
          },
          recentCheckIns: recentCheckIns || [],
          contactSubmissions: contactSubmissions?.length || 0
        })
      } catch (error) {
        console.error('Error fetching dashboard data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 animate-spin text-pink-400" />
      </div>
    )
  }

  return (
    <div className="space-y-6 lg:space-y-8 w-full">
      {/* Stats Overview */}
      <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 lg:gap-6">
        <Card className="border border-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2 text-muted-foreground">
              <Users className="w-4 h-4 text-pink-400" />
              Total Members
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xl lg:text-2xl font-bold text-foreground">{stats.totalMembers}</div>
            <p className="text-xs text-muted-foreground mt-1">
              {stats.activeMembers} active ({stats.totalMembers > 0 ? Math.round((stats.activeMembers / stats.totalMembers) * 100) : 0}%)
            </p>
          </CardContent>
        </Card>

        <Card className="border border-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2 text-muted-foreground">
              <Activity className="w-4 h-4 text-green-400" />
              Today's Sessions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xl lg:text-2xl font-bold text-foreground">{stats.todayCheckIns}</div>
            <p className="text-xs text-muted-foreground mt-1">
              {stats.equipmentStatus.inUse} sessions in progress
            </p>
          </CardContent>
        </Card>

        <Card className="border border-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2 text-muted-foreground">
              <MessageSquare className="w-4 h-4 text-yellow-400" />
              Pending Items
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xl lg:text-2xl font-bold text-foreground">
              {stats.pendingFeedback + stats.contactSubmissions}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {stats.pendingFeedback} feedback, {stats.contactSubmissions} contact
            </p>
          </CardContent>
        </Card>

        <Card className="border border-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2 text-muted-foreground">
              <Shirt className="w-4 h-4 text-blue-400" />
              Equipment Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xl lg:text-2xl font-bold text-foreground">
              {stats.equipmentStatus.available} Available
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {stats.equipmentStatus.inUse} in use, {stats.equipmentStatus.maintenance} maintenance
            </p>
          </CardContent>
        </Card>
      </div>

      {/* EMS Equipment Status */}
      <Card className="bg-gray-900/50 border-gray-800">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Zap className="w-5 h-5 text-pink-400" />
            EMS Equipment Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div>
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center gap-2">
                  <Battery className="w-4 h-4 text-green-400" />
                  <span className="text-sm font-medium text-gray-300">Available EMS Suits</span>
                </div>
                <span className="text-sm font-medium text-green-400">
                  {stats.equipmentStatus.available} / {stats.equipmentStatus.available + stats.equipmentStatus.inUse + stats.equipmentStatus.maintenance}
                </span>
              </div>
              <Progress 
                value={(stats.equipmentStatus.available / (stats.equipmentStatus.available + stats.equipmentStatus.inUse + stats.equipmentStatus.maintenance)) * 100} 
                className="h-2 bg-gray-800"
                indicatorClassName="bg-green-500"
              />
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center gap-2">
                  <Activity className="w-4 h-4 text-blue-400" />
                  <span className="text-sm font-medium text-gray-300">EMS Suits In Use</span>
                </div>
                <span className="text-sm font-medium text-blue-400">
                  {stats.equipmentStatus.inUse} / {stats.equipmentStatus.available + stats.equipmentStatus.inUse + stats.equipmentStatus.maintenance}
                </span>
              </div>
              <Progress 
                value={(stats.equipmentStatus.inUse / (stats.equipmentStatus.available + stats.equipmentStatus.inUse + stats.equipmentStatus.maintenance)) * 100} 
                className="h-2 bg-gray-800"
                indicatorClassName="bg-blue-500"
              />
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center gap-2">
                  <AlertCircle className="w-4 h-4 text-yellow-400" />
                  <span className="text-sm font-medium text-gray-300">EMS Suits In Maintenance</span>
                </div>
                <span className="text-sm font-medium text-yellow-400">
                  {stats.equipmentStatus.maintenance} / {stats.equipmentStatus.available + stats.equipmentStatus.inUse + stats.equipmentStatus.maintenance}
                </span>
              </div>
              <Progress 
                value={(stats.equipmentStatus.maintenance / (stats.equipmentStatus.available + stats.equipmentStatus.inUse + stats.equipmentStatus.maintenance)) * 100} 
                className="h-2 bg-gray-800"
                indicatorClassName="bg-yellow-500"
              />
            </div>

            <div className="flex justify-end">
              <Button variant="outline" size="sm" className="text-pink-400 border-pink-500/50">
                Manage Equipment
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent EMS Check-ins */}
      <Card className="bg-gray-900/50 border-gray-800">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Calendar className="w-5 h-5 text-purple-400" />
            Recent EMS Check-ins
          </CardTitle>
        </CardHeader>
        <CardContent>
          {stats.recentCheckIns.length === 0 ? (
            <p className="text-gray-500 text-center py-4">
              No recent check-ins. Members will appear here after their sessions.
            </p>
          ) : (
            <div className="space-y-4">
              {stats.recentCheckIns.map((checkIn) => (
                <div key={checkIn.id} className="flex items-center justify-between bg-gray-800/50 p-3 rounded-lg border border-gray-700">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full bg-pink-500/20 flex items-center justify-center text-white text-sm font-bold">
                      {(checkIn.profiles?.first_name || "N").charAt(0)}{(checkIn.profiles?.last_name || "A").charAt(0)}
                    </div>
                    <div>
                      <p className="font-medium text-white">{checkIn.profiles?.first_name} {checkIn.profiles?.last_name}</p>
                      <p className="text-sm text-gray-400">
                        Checked in at {new Date(checkIn.check_in_time).toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                  <Badge variant="secondary" className="bg-green-500/20 text-green-400 border-green-500/30">
                    Active
                  </Badge>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Contact Form Submissions / Feedback Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="bg-gray-900/50 border-gray-800">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <MessageSquare className="w-5 h-5 text-yellow-400" />
              Recent Feedback
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-500 text-center py-4">
              No recent feedback submissions.
              <Link href="/admin/feedback" className="block mt-2 text-pink-400 hover:underline">
                View all feedback
              </Link>
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gray-900/50 border-gray-800">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <DollarSign className="w-5 h-5 text-green-400" />
              Payment Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-500 text-center py-4">
              No recent payment data.
              <Link href="/admin/payments" className="block mt-2 text-pink-400 hover:underline">
                View all payments
              </Link>
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}