"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { supabase } from "@/lib/supabase/client"
import {
  Search,
  Filter,
  MoreHorizontal,
  Mail,
  Phone,
  Calendar,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Loader2,
  Edit,
  Trash2,
  Save
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import {
  Users
} from "lucide-react"

interface Member {
  id: string
  name: string
  firstName?: string
  lastName?: string
  email: string
  phone: string
  joinDate: Date
  lastVisit: Date
  status: "active" | "inactive" | "suspended"
  membershipType: "basic" | "premium" | "vip"
  attendanceRate: number
  totalVisits: number
  avatar?: string
}

interface MemberManagementProps {
  className?: string
  onAddMemberClick: () => void
}

export function MemberManagement({ className, onAddMemberClick }: MemberManagementProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [filterStatus, setFilterStatus] = useState<string>("all")
  const [members, setMembers] = useState<Member[]>([])
  const [loading, setLoading] = useState(true)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [currentEditingMember, setCurrentEditingMember] = useState<Partial<Member> | null>(null)
  const [formLoading, setFormLoading] = useState(false)

  useEffect(() => {
    fetchMembers()
  }, [])

  const fetchMembers = async () => {
    try {
      setLoading(true);

      // Fetch profiles with better error handling
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false })

      if (profilesError) {
        console.error('Error fetching profiles:', profilesError);
        throw new Error(`Failed to fetch profiles: ${profilesError.message}`);
      }

      // Fetch check-ins to calculate attendance and last visit
      const { data: checkIns, error: checkInsError } = await supabase
        .from('check_ins')
        .select('user_id, check_in_time, check_out_time')

      if (checkInsError) {
        console.error('Error fetching check-ins:', checkInsError);
        // Don't throw error for check-ins, just log it and continue with empty array
        console.warn('Continuing without check-in data');
      }

      // Transform profiles to members format with better error handling
      const membersData: Member[] = (profiles || []).map(profile => {
        const userCheckIns = (checkIns || []).filter(checkIn => checkIn.user_id === profile.id)
        const totalVisits = userCheckIns.length

        // Calculate last visit with fallback
        let lastVisit: Date;
        try {
          const lastCheckIn = userCheckIns.sort((a, b) =>
            new Date(b.check_in_time).getTime() - new Date(a.check_in_time).getTime()
          )[0]
          lastVisit = lastCheckIn ? new Date(lastCheckIn.check_in_time) : new Date(profile.join_date || profile.created_at)
        } catch (error) {
          console.warn('Error calculating last visit for user', profile.id, error);
          lastVisit = new Date(profile.join_date || profile.created_at || Date.now());
        }

        // Calculate attendance rate (visits this month / days in month) with error handling
        let attendanceRate = 0;
        try {
          const currentMonth = new Date().getMonth()
          const currentYear = new Date().getFullYear()
          const monthlyVisits = userCheckIns.filter(checkIn => {
            try {
              const checkInDate = new Date(checkIn.check_in_time)
              return checkInDate.getMonth() === currentMonth && checkInDate.getFullYear() === currentYear
            } catch (error) {
              console.warn('Invalid check-in date:', checkIn.check_in_time);
              return false;
            }
          }).length

          const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate()
          attendanceRate = Math.round((monthlyVisits / daysInMonth) * 100)
        } catch (error) {
          console.warn('Error calculating attendance rate for user', profile.id, error);
          attendanceRate = 0;
        }

        return {
          id: profile.id,
          name: `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || 'Unknown User',
          email: profile.email || 'No email',
          phone: profile.phone || 'N/A',
          joinDate: new Date(profile.join_date || profile.created_at || Date.now()),
          lastVisit,
          status: (profile.membership_status as "active" | "inactive" | "suspended") || "inactive",
          membershipType: (profile.membership_type as "basic" | "premium" | "vip") || "basic",
          attendanceRate: Math.min(Math.max(attendanceRate, 0), 100),
          totalVisits,
          avatar: profile.avatar_url
        }
      })

      setMembers(membersData)
    } catch (error) {
      console.error('Error fetching members:', error)
      // Set empty array on error to prevent crashes
      setMembers([])
      // You could also show a toast notification here
    } finally {
      setLoading(false)
    }
  }

  const handleEditMember = (member: Member) => {
    setCurrentEditingMember({
      id: member.id,
      name: member.name,
      firstName: member.name.split(' ')[0] || '',
      lastName: member.name.split(' ').slice(1).join(' ') || '',
      email: member.email,
      phone: member.phone === 'N/A' ? '' : member.phone,
      membershipType: member.membershipType,
      status: member.status,
    });
    setIsEditDialogOpen(true);
  };

  const handleUpdateMember = async () => {
    if (!currentEditingMember?.id || !currentEditingMember?.email) {
      alert("Member ID and Email are required to update.");
      return;
    }

    setFormLoading(true);
    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          first_name: currentEditingMember.firstName || null,
          last_name: currentEditingMember.lastName || null,
          email: currentEditingMember.email,
          phone: currentEditingMember.phone || null,
          membership_type: currentEditingMember.membershipType,
          membership_status: currentEditingMember.status,
        })
        .eq('id', currentEditingMember.id);

      if (error) throw error;

      alert("Member updated successfully!");
      setIsEditDialogOpen(false);
      setCurrentEditingMember(null);
      fetchMembers();
    } catch (error: any) {
      console.error("Error updating member:", error);
      alert(`Failed to update member: ${error.message || "Unknown error"}`);
    } finally {
      setFormLoading(false);
    }
  };

  const handleDeleteMember = async (memberId: string) => {
    if (!confirm("Are you sure you want to delete this member? This action cannot be undone.")) return;

    setLoading(true);
    try {
      const { error } = await supabase
        .from('profiles')
        .delete()
        .eq('id', memberId);

      if (error) throw error;

      alert("Member deleted successfully!");
      fetchMembers();
    } catch (error: any) {
      console.error("Error deleting member:", error);
      alert(`Failed to delete member: ${error.message || "Unknown error"}`);
    } finally {
      setLoading(false);
    }
  };

  const filteredMembers = members.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = filterStatus === "all" || member.status === filterStatus
    return matchesSearch && matchesFilter
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-500/10 text-green-400 border-green-500/20"
      case "inactive": return "bg-gray-500/10 text-gray-400 border-gray-500/20"
      case "suspended": return "bg-red-500/10 text-red-400 border-red-500/20"
      default: return "bg-secondary text-secondary-foreground"
    }
  }

  const getMembershipColor = (type: string) => {
    switch (type) {
      case "basic": return "bg-blue-500/10 text-blue-400 border-blue-500/20"
      case "premium": return "bg-purple-500/10 text-purple-400 border-purple-500/20"
      case "vip": return "bg-pink-500/10 text-pink-400 border-pink-500/20"
      default: return "bg-secondary text-secondary-foreground"
    }
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-64">
          <Loader2 className="w-8 h-8 animate-spin text-pink-400" />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-admin" />
            Member Management
          </CardTitle>
          <Button variant="outline" size="sm" onClick={onAddMemberClick}>
            Add Member
          </Button>
        </div>
        
        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 mt-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Search members..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="w-full sm:w-auto">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setFilterStatus("all")}>
                All Members
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus("active")}>
                Active
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus("inactive")}>
                Inactive
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus("suspended")}>
                Suspended
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {filteredMembers.map((member) => (
            <div key={member.id} className="border rounded-lg p-3 lg:p-4 hover:bg-muted/50 transition-colors">
              <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-3">
                <div className="flex items-center gap-3 lg:gap-4 min-w-0 flex-1">
                  <Avatar className="w-10 h-10 lg:w-12 lg:h-12 shrink-0">
                    <AvatarImage src={member.avatar} alt={member.name} />
                    <AvatarFallback>
                      {member.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>

                  <div className="min-w-0 flex-1">
                    <h4 className="font-medium text-sm lg:text-base truncate">{member.name}</h4>
                    <div className="flex flex-col lg:flex-row lg:items-center gap-1 lg:gap-4 text-xs lg:text-sm text-muted-foreground">
                      <span className="flex items-center gap-1 truncate">
                        <Mail className="w-3 h-3 shrink-0" />
                        <span className="truncate">{member.email}</span>
                      </span>
                      <span className="flex items-center gap-1">
                        <Phone className="w-3 h-3 shrink-0" />
                        {member.phone}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between lg:justify-end gap-3 lg:gap-4">
                  <div className="flex-1 lg:text-right">
                    <div className="flex flex-wrap lg:justify-end items-center gap-2 mb-1">
                      <Badge className={`${getStatusColor(member.status)} text-xs`}>
                        {member.status}
                      </Badge>
                      <Badge variant="outline" className={`${getMembershipColor(member.membershipType)} text-xs`}>
                        {member.membershipType}
                      </Badge>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {member.attendanceRate}% attendance • {member.totalVisits} visits
                    </div>
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="shrink-0">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => handleEditMember(member)}>
                        <Edit className="w-4 h-4 mr-2" /> Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleDeleteMember(member.id)} className="text-destructive">
                        <Trash2 className="w-4 h-4 mr-2" /> Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>

              {/* Additional Info */}
              <div className="mt-3 pt-3 border-t flex flex-col sm:flex-row sm:items-center justify-between gap-2 text-xs text-muted-foreground">
                <span className="flex items-center gap-1">
                  <Calendar className="w-3 h-3" />
                  Joined {member.joinDate.toLocaleDateString()}
                </span>
                <span className="flex items-center gap-1">
                  {member.status === "active" ? (
                    <CheckCircle className="w-3 h-3 text-green-400" />
                  ) : (
                    <AlertCircle className="w-3 h-3 text-red-400" />
                  )}
                  Last visit {member.lastVisit.toLocaleDateString()}
                </span>
              </div>
            </div>
          ))}
        </div>
        
        {filteredMembers.length === 0 && !loading && (
          <div className="text-center py-8 text-muted-foreground">
            <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>No members found matching your criteria.</p>
          </div>
        )}
      </CardContent>

      {/* Edit Member Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Member</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <Input
              placeholder="First Name"
              value={currentEditingMember?.firstName || ''}
              onChange={(e) => setCurrentEditingMember({ ...currentEditingMember, firstName: e.target.value })}
              disabled={formLoading}
            />
            <Input
              placeholder="Last Name"
              value={currentEditingMember?.lastName || ''}
              onChange={(e) => setCurrentEditingMember({ ...currentEditingMember, lastName: e.target.value })}
              disabled={formLoading}
            />
            <Input
              placeholder="Email"
              type="email"
              value={currentEditingMember?.email || ''}
              onChange={(e) => setCurrentEditingMember({ ...currentEditingMember, email: e.target.value })}
              disabled={formLoading}
            />
            <Input
              placeholder="Phone (optional)"
              value={currentEditingMember?.phone || ''}
              onChange={(e) => setCurrentEditingMember({ ...currentEditingMember, phone: e.target.value })}
              disabled={formLoading}
            />
            <Select
              value={currentEditingMember?.membershipType || ''}
              onValueChange={(value) => setCurrentEditingMember({ ...currentEditingMember, membershipType: value as "basic" | "premium" | "vip" })}
              disabled={formLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Membership Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="basic">Basic</SelectItem>
                <SelectItem value="premium">Premium</SelectItem>
                <SelectItem value="vip">VIP</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={currentEditingMember?.status || ''}
              onValueChange={(value) => setCurrentEditingMember({ ...currentEditingMember, status: value as "active" | "inactive" | "suspended" })}
              disabled={formLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Membership Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="suspended">Suspended</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <DialogFooter>
            <Button onClick={() => setIsEditDialogOpen(false)} variant="outline">
              Cancel
            </Button>
            <Button onClick={handleUpdateMember} disabled={formLoading}>
              {formLoading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}
