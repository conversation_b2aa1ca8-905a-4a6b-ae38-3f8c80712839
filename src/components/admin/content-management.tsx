"use client"

import { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { NeonButton } from "@/components/ui/neon-button"
import { supabase } from "@/lib/supabase/client"
import {
  FileText,
  Plus,
  Edit,
  Trash2,
  Eye,
  Calendar,
  User,
  Search,
  Loader2,
  Save
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, <PERSON>alog<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, DialogFooter } from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"

interface BlogPost {
  id: string
  title: string
  slug: string
  excerpt: string
  content: string
  category: string
  author_id: string
  featured: boolean
  published: boolean
  published_at: string | null
  read_time: number
  view_count: number
  created_at: string
  updated_at: string
}

interface HelpfulMaterial {
  id: string
  title: string
  description: string
  type: 'pdf' | 'video' | 'audio' | 'guide'
  category: string
  file_url: string | null
  file_size: string | null
  duration: string | null
  download_count: number
  featured: boolean
  created_at: string
  updated_at: string
}

interface ContentManagementProps {
  className?: string
}

export function ContentManagement({ className }: ContentManagementProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [filterType, setFilterType] = useState<string>("all")
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingItem, setEditingItem] = useState<BlogPost | HelpfulMaterial | null>(null)
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([])
  const [helpfulMaterials, setHelpfulMaterials] = useState<HelpfulMaterial[]>([])
  const [loading, setLoading] = useState(true)
  const [formLoading, setFormLoading] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    excerpt: '',
    content: '',
    category: '',
    contentType: 'blog' as 'blog' | 'helpful-material',
    materialType: 'pdf' as 'pdf' | 'video' | 'audio' | 'guide',
    description: '',
    file_url: '',
    file_size: '',
    duration: ''
  })

  // Fetch data from Supabase
  useEffect(() => {
    fetchContent()
  }, [])

  const fetchContent = async () => {
    setLoading(true)
    try {
      const [blogResponse, materialsResponse] = await Promise.all([
        supabase.from('blogs').select('*').order('created_at', { ascending: false }),
        supabase.from('helpful_materials').select('*').order('created_at', { ascending: false })
      ])

      if (blogResponse.error) throw blogResponse.error
      if (materialsResponse.error) throw materialsResponse.error

      console.log("Blog Posts Response Data:", blogResponse.data);
      console.log("Helpful Materials Response Data:", materialsResponse.data);

      setBlogPosts(blogResponse.data || [])
      setHelpfulMaterials(materialsResponse.data || [])
    } catch (error) {
      console.error('Error fetching content:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (formData.contentType === 'blog') {
        const slug = formData.title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')

        if (editingItem) {
          const { error } = await supabase
            .from('blog_posts')
            .update({
              title: formData.title,
              slug,
              excerpt: formData.excerpt,
              content: formData.content,
              category: formData.category,
            })
            .eq('id', editingItem.id)

          if (error) throw error
        } else {
          const { data: { user } } = await supabase.auth.getUser()
          if (!user) throw new Error('User not authenticated')

          const { error } = await supabase
            .from('blog_posts')
            .insert([{
              title: formData.title,
              slug,
              excerpt: formData.excerpt,
              content: formData.content,
              category: formData.category,
              author_id: user.id,
              read_time: Math.ceil(formData.content.split(' ').length / 200)
            }])

          if (error) throw error
        }
      } else {
        if (editingItem) {
          const { error } = await supabase
            .from('helpful_materials')
            .update({
              title: formData.title,
              description: formData.description,
              type: formData.materialType,
              category: formData.category,
              file_url: formData.file_url,
              file_size: formData.file_size,
              duration: formData.duration,
              featured: editingItem.featured,
              updated_at: new Date().toISOString(),
            })
            .eq('id', editingItem.id)

          if (error) throw error
        } else {
          const { error } = await supabase
            .from('helpful_materials')
            .insert([{
              title: formData.title,
              description: formData.description,
              type: formData.materialType,
              category: formData.category,
              file_url: formData.file_url,
              file_size: formData.file_size,
              duration: formData.duration,
              featured: false,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            }])

          if (error) throw error
        }
      }

      await fetchContent()
      resetForm()
    } catch (error) {
      console.error('Error saving content:', error)
    }
  }

  const handleDelete = async (id: string, type: 'blog' | 'helpful-material') => {
    if (!confirm('Are you sure you want to delete this item?')) return

    try {
      const table = type === 'blog' ? 'blogs' : 'helpful_materials'
      const { error } = await supabase.from(table).delete().eq('id', id)

      if (error) throw error
      await fetchContent()
    } catch (error) {
      console.error('Error deleting content:', error)
    }
  }

  const handleEdit = (item: BlogPost | HelpfulMaterial) => {
    setEditingItem(item)

    if ('slug' in item) {
      // It's a blog post
      setFormData({
        title: item.title,
        excerpt: item.excerpt,
        content: item.content,
        category: item.category,
        contentType: 'blog',
        materialType: 'pdf',
        description: '',
        file_url: '',
        file_size: '',
        duration: ''
      })
    } else {
      // It's a helpful material
      setFormData({
        title: item.title,
        excerpt: '',
        content: '',
        category: item.category,
        contentType: 'helpful-material',
        materialType: item.type,
        description: item.description,
        file_url: item.file_url || '',
        file_size: item.file_size || '',
        duration: item.duration || ''
      })
    }

    setIsDialogOpen(true)
  }

  const resetForm = () => {
    setFormData({
      title: '',
      excerpt: '',
      content: '',
      category: '',
      contentType: 'blog',
      materialType: 'pdf',
      description: '',
      file_url: '',
      file_size: '',
      duration: ''
    })
    setEditingItem(null)
    setIsDialogOpen(false)
  }

  const togglePublish = async (item: BlogPost) => {
    try {
      const { error } = await supabase
        .from('blog_posts')
        .update({
          published: !item.published,
          published_at: !item.published ? new Date().toISOString() : null
        })
        .eq('id', item.id)

      if (error) throw error
      await fetchContent()
    } catch (error) {
      console.error('Error updating publish status:', error)
    }
  }

  // Combine and filter content
  const allContent = [
    ...blogPosts.map(post => ({
      ...post,
      contentType: 'blog' as const,
      itemType: 'blog' as const,
      excerpt: post.excerpt,
      status: post.published ? 'published' : 'draft',
      author: 'Admin', // Default author if not available
      createdAt: new Date(post.created_at), // Convert string date to Date object
      views: post.view_count || 0 // Use view_count or default to 0
    })),
    ...helpfulMaterials.map(material => ({
      ...material,
      contentType: 'helpful-material' as const,
      itemType: 'helpful-material' as const,
      excerpt: material.description,
      status: 'published',
      author: 'Admin', // Default author if not available
      createdAt: new Date(material.created_at), // Convert string date to Date object
      views: material.download_count || 0 // Use download_count as views or default to 0
    }))
  ]

  const filteredContent = allContent.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.excerpt.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = filterType === "all" || item.itemType === filterType
    return matchesSearch && matchesFilter
  })

  console.log("Filtered Content for Display:", filteredContent);

  const getTypeColor = (type: string) => {
    switch (type) {
      case "blog": return "bg-blog/20 text-blog"
      case "helpful-material": return "bg-marketing/20 text-marketing"
      case "announcement": return "bg-communication/20 text-communication"
      default: return "bg-secondary text-secondary-foreground"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "published": return "bg-blog text-blog-foreground"
      case "draft": return "bg-marketing text-marketing-foreground"
      case "archived": return "bg-muted text-muted-foreground"
      default: return "bg-secondary text-secondary-foreground"
    }
  }

  return (
    <div className={className}>
      {/* Header and Actions */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold">Content Management</h2>
          <p className="text-muted-foreground">Create and manage blog posts, helpful materials, and announcements</p>
        </div>
        <NeonButton 
          variant="blog" 
          onClick={() => {
            setEditingItem(null); // Clear previous editing item
            setIsDialogOpen(true); // Open dialog
            setFormData({
              title: '',
              excerpt: '',
              content: '',
              category: '',
              contentType: 'blog',
              materialType: 'pdf',
              description: '',
              file_url: '',
              file_size: '',
              duration: ''
            })
          }}
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Content
        </NeonButton>
      </div>

      {/* Search and Filter */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search content..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  Filter: {filterType === "all" ? "All Types" : filterType.replace("-", " ")}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setFilterType("all")}>
                  All Types
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilterType("blog")}>
                  Blog Posts
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilterType("helpful-material")}>
                  Helpful Materials
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilterType("announcement")}>
                  Announcements
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardContent>
      </Card>

      {/* Content List */}
      <div className="space-y-4">
        {filteredContent.map((item) => (
          <Card key={item.id} className="hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="text-lg font-semibold">{item.title}</h3>
                    <Badge className={getTypeColor(item.itemType)}>
                      {item.itemType.replace("-", " ")}
                    </Badge>
                    <Badge className={getStatusColor(item.status)}>
                      {item.status}
                    </Badge>
                  </div>
                  
                  <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
                    {item.excerpt}
                  </p>
                  
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <User className="w-3 h-3" />
                      {item.author}
                    </span>
                    <span className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      {item.createdAt ? (typeof item.createdAt === 'string' ? new Date(item.createdAt).toLocaleDateString() : item.createdAt.toLocaleDateString()) : 'Unknown date'}
                    </span>
                    {item.views !== undefined && (
                      <span className="flex items-center gap-1">
                        <Eye className="w-3 h-3" />
                        {item.views} views
                      </span>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-2 ml-4">
                  <Button variant="ghost" size="sm" onClick={() => {
                    // View functionality, if applicable
                    if (item.contentType === 'blog') {
                      window.open(`/blog/${(item as BlogPost).slug}`, '_blank');
                    } else if (item.file_url) {
                      window.open(item.file_url, '_blank');
                    }
                  }}>
                    <Eye className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm" onClick={() => handleEdit(item)}>
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="text-destructive hover:text-destructive" onClick={() => handleDelete(item.id, item.itemType)}>
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
        
        {filteredContent.length === 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No content found matching your criteria.</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Create/Edit Content Form Modal */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <NeonButton 
            variant="blog" 
            onClick={() => {
              setEditingItem(null); // Clear previous editing item
              setIsDialogOpen(true); // Open dialog
              setFormData({
                title: '',
                excerpt: '',
                content: '',
                category: '',
                contentType: 'blog',
                materialType: 'pdf',
                description: '',
                file_url: '',
                file_size: '',
                duration: ''
              })
            }}
          >
            <Plus className="w-4 h-4 mr-2" />
            Create Content
          </NeonButton>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>{editingItem ? "Edit Content" : "Create New Content"}</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="grid gap-4 py-4">
            {/* Type Selection */}
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="contentType" className="text-right">Content Type</label>
              <Select value={formData.contentType} onValueChange={(value: 'blog' | 'helpful-material') => setFormData(prev => ({ ...prev, contentType: value }))}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select content type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="blog">Blog Post</SelectItem>
                  <SelectItem value="helpful-material">Helpful Material</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Common Fields */}
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="title" className="text-right">Title</label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="category" className="text-right">Category</label>
              <Input
                id="category"
                value={formData.category}
                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                className="col-span-3"
                required
              />
            </div>

            {formData.contentType === 'blog' && (
              <>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="excerpt" className="text-right">Excerpt</label>
                  <Textarea
                    id="excerpt"
                    value={formData.excerpt}
                    onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="content" className="text-right">Content</label>
                  <Textarea
                    id="content"
                    value={formData.content}
                    onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                    className="col-span-3 min-h-[200px]"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="published" className="text-right">Published</label>
                  <Checkbox
                    id="published"
                    checked={(editingItem as BlogPost)?.published || false}
                    onCheckedChange={(checked) => {
                      if (editingItem && 'slug' in editingItem) {
                        setEditingItem(prev => ({
                          ...(prev as BlogPost),
                          published: Boolean(checked)
                        }));
                      }
                    }}
                    className="col-span-3"
                  />
                </div>
              </>
            )}

            {formData.contentType === 'helpful-material' && (
              <>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="description" className="text-right">Description</label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="materialType" className="text-right">Material Type</label>
                  <Select value={formData.materialType} onValueChange={(value: 'pdf' | 'video' | 'audio' | 'guide') => setFormData(prev => ({ ...prev, materialType: value }))}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select material type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pdf">PDF</SelectItem>
                      <SelectItem value="video">Video</SelectItem>
                      <SelectItem value="audio">Audio</SelectItem>
                      <SelectItem value="guide">Guide</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="file_url" className="text-right">File URL</label>
                  <Input
                    id="file_url"
                    value={formData.file_url}
                    onChange={(e) => setFormData(prev => ({ ...prev, file_url: e.target.value }))}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="file_size" className="text-right">File Size</label>
                  <Input
                    id="file_size"
                    value={formData.file_size}
                    onChange={(e) => setFormData(prev => ({ ...prev, file_size: e.target.value }))}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="duration" className="text-right">Duration</label>
                  <Input
                    id="duration"
                    value={formData.duration}
                    onChange={(e) => setFormData(prev => ({ ...prev, duration: e.target.value }))}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="featured" className="text-right">Featured</label>
                  <Checkbox
                    id="featured"
                    checked={(editingItem as HelpfulMaterial)?.featured || false}
                    onCheckedChange={(checked) => {
                      if (editingItem && 'type' in editingItem) {
                        setEditingItem(prev => ({
                          ...(prev as HelpfulMaterial),
                          featured: Boolean(checked)
                        }));
                      }
                    }}
                    className="col-span-3"
                  />
                </div>
              </>
            )}
            <DialogFooter>
              <Button type="button" variant="outline" onClick={resetForm}>
                Cancel
              </Button>
              <NeonButton type="submit" variant="blog" disabled={formLoading}>
                {formLoading ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                {editingItem ? "Save Changes" : "Create Content"}
              </NeonButton>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
