"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { ProgressRing } from "@/components/ui/progress-ring"
import { Badge } from "@/components/ui/badge"
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  Users, 
  Calendar,
  Clock,
  Target,
  Star
} from "lucide-react"

interface AnalyticsData {
  memberGrowth: {
    current: number
    previous: number
    percentage: number
  }
  attendanceRate: {
    current: number
    target: number
    trend: "up" | "down"
  }
  revenueMetrics: {
    monthly: number
    growth: number
    target: number
  }
  satisfactionScore: {
    average: number
    totalResponses: number
    distribution: { [key: number]: number }
  }
  peakHours: Array<{
    hour: string
    count: number
    percentage: number
  }>
  membershipTypes: Array<{
    type: string
    count: number
    percentage: number
    revenue: number
  }>
}

interface AnalyticsDashboardProps {
  data: AnalyticsData
  className?: string
}

export function AnalyticsDashboard({ data, className }: AnalyticsDashboardProps) {
  const {
    memberGrowth,
    attendanceRate,
    revenueMetrics,
    satisfactionScore,
    peakHours,
    membershipTypes
  } = data

  const getTrendIcon = (trend: "up" | "down") => {
    return trend === "up" ? TrendingUp : TrendingDown
  }

  const getTrendColor = (trend: "up" | "down") => {
    return trend === "up" ? "text-blog" : "text-destructive"
  }

  return (
    <div className={className}>
      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Users className="w-4 h-4 text-admin" />
              Member Growth
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-admin">{memberGrowth.current}</div>
            <div className="flex items-center gap-1 text-xs">
              <TrendingUp className="w-3 h-3 text-blog" />
              <span className="text-blog">+{memberGrowth.percentage}%</span>
              <span className="text-muted-foreground">vs last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Calendar className="w-4 h-4 text-member" />
              Attendance Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-member">{attendanceRate.current}%</div>
            <div className="flex items-center gap-1 text-xs">
              {getTrendIcon(attendanceRate.trend)({ 
                className: `w-3 h-3 ${getTrendColor(attendanceRate.trend)}` 
              })}
              <span className={getTrendColor(attendanceRate.trend)}>
                Target: {attendanceRate.target}%
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <BarChart3 className="w-4 h-4 text-marketing" />
              Monthly Revenue
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-marketing">
              R{revenueMetrics.monthly.toLocaleString()}
            </div>
            <div className="flex items-center gap-1 text-xs">
              <TrendingUp className="w-3 h-3 text-blog" />
              <span className="text-blog">+{revenueMetrics.growth}%</span>
              <span className="text-muted-foreground">growth</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Star className="w-4 h-4 text-communication" />
              Satisfaction
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-communication">
              {satisfactionScore.average.toFixed(1)}
            </div>
            <div className="text-xs text-muted-foreground">
              {satisfactionScore.totalResponses} responses
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Attendance Progress */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5 text-admin" />
              Attendance vs Target
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-center">
              <ProgressRing
                progress={(attendanceRate.current / attendanceRate.target) * 100}
                variant="admin"
                size="lg"
                label={`${attendanceRate.current}% of ${attendanceRate.target}%`}
              />
            </div>
            <div className="mt-4 text-center text-sm text-muted-foreground">
              {attendanceRate.current >= attendanceRate.target ? (
                <span className="text-blog">🎉 Target exceeded!</span>
              ) : (
                <span>
                  {(attendanceRate.target - attendanceRate.current).toFixed(1)}% to reach target
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Revenue Progress */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-marketing" />
              Revenue vs Target
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-center">
              <ProgressRing
                progress={(revenueMetrics.monthly / revenueMetrics.target) * 100}
                variant="marketing"
                size="lg"
                label={`R${revenueMetrics.monthly.toLocaleString()}`}
              />
            </div>
            <div className="mt-4 text-center text-sm text-muted-foreground">
              Target: R{revenueMetrics.target.toLocaleString()}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Peak Hours Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-blog" />
              Peak Hours
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {peakHours.map((hour, index) => (
                <div key={hour.hour} className="flex items-center justify-between">
                  <span className="text-sm font-medium">{hour.hour}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-24 bg-muted rounded-full h-2">
                      <div 
                        className="bg-blog h-2 rounded-full transition-all duration-500"
                        style={{ width: `${hour.percentage}%` }}
                      />
                    </div>
                    <span className="text-sm text-muted-foreground w-12 text-right">
                      {hour.count}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Membership Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5 text-member" />
              Membership Types
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {membershipTypes.map((type) => (
                <div key={type.type} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium capitalize">{type.type}</span>
                    <Badge variant="outline">
                      {type.count} members ({type.percentage}%)
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="flex-1 bg-muted rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all duration-500 ${
                          type.type === 'basic' ? 'bg-admin' :
                          type.type === 'premium' ? 'bg-member' :
                          'bg-communication'
                        }`}
                        style={{ width: `${type.percentage}%` }}
                      />
                    </div>
                    <span className="text-xs text-muted-foreground">
                      R{type.revenue.toLocaleString()}/mo
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Satisfaction Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="w-5 h-5 text-communication" />
            Member Satisfaction Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-5 gap-4">
            {[5, 4, 3, 2, 1].map((rating) => (
              <div key={rating} className="text-center">
                <div className="flex justify-center mb-2">
                  {Array.from({ length: rating }, (_, i) => (
                    <Star key={i} className="w-4 h-4 fill-member text-member" />
                  ))}
                </div>
                <div className="text-2xl font-bold text-member">
                  {satisfactionScore.distribution[rating] || 0}
                </div>
                <div className="text-xs text-muted-foreground">
                  {rating} star{rating !== 1 ? 's' : ''}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
