"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { supabase } from "@/lib/supabase/client"
import Link from "next/link"
import { 
  Users, 
  Search, 
  Loader2, 
  ChevronRight,
  Zap,
  Calendar,
  CheckCircle,
  XCircle
} from "lucide-react"

interface Member {
  id: string
  first_name: string
  last_name: string
  email: string
  membership_type: string
  membership_status: string
  total_visits: number
  last_visit: string | null
  join_date: string
  isCheckedIn?: boolean
}

export function EMSMemberList() {
  const [loading, setLoading] = useState(true)
  const [members, setMembers] = useState<Member[]>([])
  const [filteredMembers, setFilteredMembers] = useState<Member[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [activeCheckIns, setActiveCheckIns] = useState<string[]>([])

  useEffect(() => {
    const fetchMembers = async () => {
      try {
        // Get all profiles
        const { data: profiles, error: profilesError } = await supabase
          .from('profiles')
          .select('*')
          .order('last_name', { ascending: true })
        
        if (profilesError) throw profilesError

        // Get active check-ins
        const { data: checkIns, error: checkInsError } = await supabase
          .from('check_ins')
          .select('user_id')
          .is('check_out_time', null)
        
        if (checkInsError) throw checkInsError

        // Create a set of user IDs with active check-ins
        const activeUserIds = new Set((checkIns || []).map(c => c.user_id))
        
        // Map profiles to members with check-in status
        const memberData = (profiles || []).map(profile => ({
          id: profile.id,
          first_name: profile.first_name || '',
          last_name: profile.last_name || '',
          email: profile.email,
          membership_type: profile.membership_type,
          membership_status: profile.membership_status,
          total_visits: profile.total_visits || 0,
          last_visit: profile.last_visit,
          join_date: profile.join_date,
          isCheckedIn: activeUserIds.has(profile.id)
        }))
        
        setMembers(memberData)
        setFilteredMembers(memberData)
        setActiveCheckIns(Array.from(activeUserIds))
      } catch (error) {
        console.error("Error fetching members:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchMembers()
  }, [])

  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredMembers(members)
    } else {
      const query = searchQuery.toLowerCase()
      const filtered = members.filter(member => 
        member.first_name.toLowerCase().includes(query) ||
        member.last_name.toLowerCase().includes(query) ||
        member.email.toLowerCase().includes(query)
      )
      setFilteredMembers(filtered)
    }
  }, [searchQuery, members])

  const getMembershipTypeColor = (type: string) => {
    switch (type) {
      case 'basic': return 'bg-gray-500/20 text-gray-400'
      case 'premium': return 'bg-purple-500/20 text-purple-400'
      case 'vip': return 'bg-pink-500/20 text-pink-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  const getMembershipStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500/20 text-green-400'
      case 'inactive': return 'bg-red-500/20 text-red-400'
      case 'suspended': return 'bg-yellow-500/20 text-yellow-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 animate-spin text-pink-400" />
      </div>
    )
  }

  return (
    <Card className="bg-gray-900/50 border-gray-800">
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <CardTitle className="text-white flex items-center gap-2">
            <Users className="w-5 h-5 text-pink-400" />
            EMS Members
          </CardTitle>
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search members..."
              className="pl-8 bg-gray-800 border-gray-700 text-white"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {filteredMembers.length > 0 ? (
            filteredMembers.map(member => (
              <div key={member.id} className="p-4 border border-gray-800 rounded-lg hover:border-pink-500/50 hover:bg-gray-800/50 transition-all duration-300">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-pink-500/20 flex items-center justify-center">
                      {member.isCheckedIn ? (
                        <Zap className="w-5 h-5 text-pink-400" />
                      ) : (
                        <Users className="w-5 h-5 text-gray-400" />
                      )}
                    </div>
                    <div>
                      <div className="font-medium text-white">
                        {member.first_name} {member.last_name}
                      </div>
                      <div className="text-xs text-gray-400">
                        {member.email}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap items-center gap-2">
                    <Badge className={getMembershipTypeColor(member.membership_type)}>
                      {member.membership_type}
                    </Badge>
                    <Badge className={getMembershipStatusColor(member.membership_status)}>
                      {member.membership_status}
                    </Badge>
                    {member.isCheckedIn && (
                      <Badge className="bg-blue-500/20 text-blue-400">
                        EMS Active
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-4">
                    <div className="hidden md:block text-right">
                      <div className="text-sm text-white">{member.total_visits} sessions</div>
                      <div className="text-xs text-gray-400">
                        {member.last_visit ? `Last: ${new Date(member.last_visit).toLocaleDateString()}` : 'No sessions yet'}
                      </div>
                    </div>
                    
                    <Link href={`/admin/members/${member.id}`}>
                      <Button variant="outline" size="sm" className="text-pink-400 border-pink-500/50">
                        <ChevronRight className="w-4 h-4" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              No members found matching your search
            </div>
          )}
        </div>
        
        {/* Summary Stats */}
        <div className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="p-4 bg-gray-800/50 rounded-lg text-center">
            <div className="text-2xl font-bold text-white">{members.length}</div>
            <div className="text-xs text-gray-400">Total Members</div>
          </div>
          <div className="p-4 bg-gray-800/50 rounded-lg text-center">
            <div className="text-2xl font-bold text-white">
              {members.filter(m => m.membership_status === 'active').length}
            </div>
            <div className="text-xs text-gray-400">Active Members</div>
          </div>
          <div className="p-4 bg-gray-800/50 rounded-lg text-center">
            <div className="text-2xl font-bold text-white">{activeCheckIns.length}</div>
            <div className="text-xs text-gray-400">Current EMS Sessions</div>
          </div>
          <div className="p-4 bg-gray-800/50 rounded-lg text-center">
            <div className="text-2xl font-bold text-white">
              {members.reduce((sum, member) => sum + member.total_visits, 0)}
            </div>
            <div className="text-xs text-gray-400">Total EMS Sessions</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}