"use client"

import { cn } from "@/lib/utils"
import { useEffect, useState } from "react"

interface ProgressRingProps {
  progress: number // 0-100
  size?: "sm" | "md" | "lg" | "xl"
  variant?: "member" | "admin" | "blog" | "marketing" | "communication"
  showPercentage?: boolean
  label?: string
  className?: string
}

const sizeStyles = {
  sm: { size: 60, strokeWidth: 4, fontSize: "text-xs" },
  md: { size: 80, strokeWidth: 6, fontSize: "text-sm" },
  lg: { size: 120, strokeWidth: 8, fontSize: "text-base" },
  xl: { size: 160, strokeWidth: 10, fontSize: "text-lg" }
}

const variantColors = {
  member: "#FF10F0",
  admin: "#FF10F0",
  blog: "#FF10F0",
  marketing: "#FF10F0",
  communication: "#FF10F0"
}

export function ProgressRing({ 
  progress, 
  size = "md", 
  variant = "member", 
  showPercentage = true,
  label,
  className 
}: ProgressRingProps) {
  const [animatedProgress, setAnimatedProgress] = useState(0)
  const { size: ringSize, strokeWidth, fontSize } = sizeStyles[size]
  const color = variantColors[variant]
  
  const radius = (ringSize - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (animatedProgress / 100) * circumference

  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedProgress(progress)
    }, 100)
    return () => clearTimeout(timer)
  }, [progress])

  return (
    <div className={cn("flex flex-col items-center gap-2", className)}>
      <div className="relative">
        <svg
          width={ringSize}
          height={ringSize}
          className="transform -rotate-90"
        >
          {/* Background circle */}
          <circle
            cx={ringSize / 2}
            cy={ringSize / 2}
            r={radius}
            stroke="currentColor"
            strokeWidth={strokeWidth}
            fill="transparent"
            className="text-muted-foreground/20"
          />
          {/* Progress circle */}
          <circle
            cx={ringSize / 2}
            cy={ringSize / 2}
            r={radius}
            stroke={color}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className="transition-all duration-1000 ease-out"
            style={{
              filter: `drop-shadow(0 0 6px ${color})`
            }}
          />
        </svg>
        
        {showPercentage && (
          <div className="absolute inset-0 flex items-center justify-center">
            <span className={cn("font-bold", fontSize)} style={{ color }}>
              {Math.round(animatedProgress)}%
            </span>
          </div>
        )}
      </div>
      
      {label && (
        <span className="text-sm text-muted-foreground text-center">
          {label}
        </span>
      )}
    </div>
  )
}
