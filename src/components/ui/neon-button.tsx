import { cn } from "@/lib/utils"
import { ButtonHTMLAttributes, forwardRef } from "react"
import { Slot } from "@radix-ui/react-slot"

interface NeonButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "member" | "admin" | "blog" | "marketing" | "communication"
  size?: "sm" | "md" | "lg" | "xl"
  glow?: boolean
  pulse?: boolean
  asChild?: boolean
}

const variantStyles = {
  member: "bg-member hover:bg-member/90 text-white border-member neon-glow-pink",
  admin: "bg-admin hover:bg-admin/90 text-white border-admin neon-glow-pink",
  blog: "bg-blog hover:bg-blog/90 text-white border-blog neon-glow-pink",
  marketing: "bg-marketing hover:bg-marketing/90 text-white border-marketing neon-glow-pink",
  communication: "bg-communication hover:bg-communication/90 text-white border-communication neon-glow-pink"
}

const sizeStyles = {
  sm: "px-3 py-1.5 text-sm",
  md: "px-4 py-2 text-base",
  lg: "px-6 py-3 text-lg",
  xl: "px-8 py-4 text-xl"
}

export const NeonButton = forwardRef<HTMLButtonElement, NeonButtonProps>(
  ({ className, variant = "member", size = "md", glow = true, pulse = false, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(
          "inline-flex items-center justify-center rounded-md border font-medium transition-all duration-200",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
          "disabled:pointer-events-none disabled:opacity-50",
          variantStyles[variant],
          sizeStyles[size],
          glow && "shadow-lg",
          pulse && "animate-neon-pulse",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)

NeonButton.displayName = "NeonButton"
