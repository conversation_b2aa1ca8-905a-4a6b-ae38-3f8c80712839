"use client"

import { cn } from "@/lib/utils"
import { motion } from "framer-motion"
import { Trophy, Star, Target, Zap } from "lucide-react"

interface AchievementBadgeProps {
  type: "streak" | "milestone" | "goal" | "special"
  level: "bronze" | "silver" | "gold" | "platinum"
  earned?: boolean
  title: string
  description?: string
  className?: string
  animate?: boolean
}

const typeIcons = {
  streak: Star,
  milestone: Trophy,
  goal: Target,
  special: Zap
}

const levelStyles = {
  bronze: {
    bg: "bg-gradient-to-br from-amber-600 to-amber-800",
    glow: "shadow-lg shadow-amber-500/50",
    text: "text-amber-100"
  },
  silver: {
    bg: "bg-gradient-to-br from-gray-400 to-gray-600", 
    glow: "shadow-lg shadow-cyan-500/50",
    text: "text-gray-100"
  },
  gold: {
    bg: "bg-gradient-to-br from-yellow-400 to-yellow-600",
    glow: "shadow-lg shadow-yellow-500/50", 
    text: "text-yellow-900"
  },
  platinum: {
    bg: "bg-gradient-to-br from-purple-600 to-indigo-800",
    glow: "shadow-lg shadow-purple-500/50, shadow-pink-500/50",
    text: "text-white"
  }
}

export function AchievementBadge({
  type,
  level,
  earned = false,
  title,
  description,
  className,
  animate = false
}: AchievementBadgeProps) {
  const Icon = typeIcons[type]
  const styles = levelStyles[level]

  return (
    <motion.div
      className={cn(
        "relative flex flex-col items-center p-4 rounded-lg border",
        earned ? styles.bg : "bg-muted/50 border-muted",
        earned ? styles.glow : "",
        earned ? "border-transparent" : "border-dashed",
        !earned && "opacity-60",
        className
      )}
      initial={animate ? { scale: 0, rotate: -180 } : false}
      animate={animate ? { scale: 1, rotate: 0 } : false}
      transition={{ 
        type: "spring", 
        stiffness: 260, 
        damping: 20,
        delay: 0.1 
      }}
    >
      {earned && animate && (
        <motion.div
          className="absolute inset-0 rounded-lg"
          initial={{ opacity: 0 }}
          animate={{ opacity: [0, 1, 0] }}
          transition={{ duration: 1, repeat: 2 }}
          style={{
            background: `radial-gradient(circle, ${level === 'platinum' ? 'rgba(255, 16, 240, 0.4)' : '#FFD700'} 0%, ${level === 'platinum' ? 'rgba(0, 255, 255, 0.4)' : 'transparent'} 30%, transparent 70%)`
          }}
        />
      )}
      
      <div className={cn(
        "flex items-center justify-center w-12 h-12 rounded-full mb-2",
        earned ? styles.text : "text-muted-foreground"
      )}>
        <Icon className="w-6 h-6" />
      </div>
      
      <h3 className={cn(
        "font-semibold text-sm text-center",
        earned ? styles.text : "text-muted-foreground"
      )}>
        {title}
      </h3>
      
      {description && (
        <p className={cn(
          "text-xs text-center mt-1",
          earned ? styles.text : "text-muted-foreground"
        )}>
          {description}
        </p>
      )}
      
      {!earned && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-8 h-8 rounded-full border-2 border-dashed border-muted-foreground/50" />
        </div>
      )}
    </motion.div>
  )
}
