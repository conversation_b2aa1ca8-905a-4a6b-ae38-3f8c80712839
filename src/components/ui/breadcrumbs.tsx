"use client"

import Link from "next/link"
import { ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"

interface BreadcrumbItem {
  label: string
  href: string
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[]
  className?: string
}

export function Breadcrumbs({
  items,
  className
}: BreadcrumbsProps) {
  return (
    <nav aria-label="breadcrumb" className={cn("flex items-center space-x-2 text-sm", className)}>
      {
        items.map((item, index) => (
          <div key={item.href} className="flex items-center">
            <Link
              href={item.href}
              className={cn(
                "font-medium text-muted-foreground hover:text-foreground transition-colors",
                index === items.length - 1 && "text-foreground pointer-events-none"
              )}
            >
              {item.label}
            </Link>
            {index < items.length - 1 && (
              <ChevronRight className="h-4 w-4 text-muted-foreground mx-1" />
            )}
          </div>
        ))
      }
    </nav>
  )
} 