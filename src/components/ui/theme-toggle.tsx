"use client"

import { Moon, Sun } from "lucide-react"
import { useTheme } from "next-themes"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { useEffect, useState } from "react"

interface ThemeToggleProps {
  className?: string
  variant?: "default" | "neon"
}

export function ThemeToggle({ className, variant = "default" }: ThemeToggleProps) {
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }

  const isDark = theme === "dark"

  if (variant === "neon") {
    return (
      <button
        onClick={() => setTheme(isDark ? "light" : "dark")}
        className={cn(
          "relative inline-flex h-10 w-20 items-center rounded-full transition-all duration-300",
          "bg-gradient-to-r from-member/20 to-member/40 border border-member/30",
          "hover:from-member/30 hover:to-member/50",
          "focus:outline-none focus:ring-2 focus:ring-member focus:ring-offset-2",
          className
        )}
      >
        <span className="sr-only">Toggle theme</span>
        
        {/* Sliding indicator */}
        <span
          className={cn(
            "absolute h-8 w-8 rounded-full transition-all duration-300 ease-in-out",
            "bg-gradient-to-br from-member to-member/80 shadow-lg",
            "flex items-center justify-center",
            isDark ? "translate-x-10 neon-glow-pink" : "translate-x-1"
          )}
        >
          {isDark ? (
            <Moon className="h-4 w-4 text-white" />
          ) : (
            <Sun className="h-4 w-4 text-white" />
          )}
        </span>
        
        {/* Background icons */}
        <div className="flex w-full items-center justify-between px-2">
          <Sun className={cn("h-4 w-4 transition-opacity", isDark ? "opacity-40" : "opacity-0")} />
          <Moon className={cn("h-4 w-4 transition-opacity", isDark ? "opacity-0" : "opacity-40")} />
        </div>
      </button>
    )
  }

  return (
    <Button
      variant="outline"
      size="icon"
      onClick={() => setTheme(isDark ? "light" : "dark")}
      className={cn(
        "relative overflow-hidden transition-all duration-200",
        "hover:bg-member/10 hover:border-member/30",
        className
      )}
    >
      <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
      <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
      <span className="sr-only">Toggle theme</span>
    </Button>
  )
}
