import { cn } from "@/lib/utils"
import { ReactNode } from "react"

interface SectionContainerProps {
  children: ReactNode
  section: "member" | "admin" | "blog" | "marketing" | "communication"
  className?: string
}

const sectionStyles = {
  member: "section-member border-member/20",
  admin: "section-admin border-admin/20", 
  blog: "section-blog border-blog/20",
  marketing: "section-marketing border-marketing/20",
  communication: "bg-gradient-to-br from-communication/10 to-communication/5 border-communication/20"
}

export function SectionContainer({ children, section, className }: SectionContainerProps) {
  return (
    <div className={cn(
      "rounded-lg border p-6",
      sectionStyles[section],
      className
    )}>
      {children}
    </div>
  )
}
