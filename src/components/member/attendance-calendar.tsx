"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon } from "lucide-react"
import { cn } from "@/lib/utils"

interface AttendanceDay {
  date: Date
  attended: boolean
  duration?: number // in minutes
}

interface AttendanceCalendarProps {
  attendanceData: AttendanceDay[]
  className?: string
}

export function AttendanceCalendar({ attendanceData, className }: AttendanceCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date())
  
  const currentMonth = currentDate.getMonth()
  const currentYear = currentDate.getFullYear()
  
  // Get first day of month and number of days
  const firstDayOfMonth = new Date(currentYear, currentMonth, 1)
  const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0)
  const daysInMonth = lastDayOfMonth.getDate()
  const startingDayOfWeek = firstDayOfMonth.getDay()
  
  // Create calendar grid
  const calendarDays = []
  
  // Add empty cells for days before month starts
  for (let i = 0; i < startingDayOfWeek; i++) {
    calendarDays.push(null)
  }
  
  // Add days of the month
  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(currentYear, currentMonth, day)
    const attendanceRecord = attendanceData.find(
      record => record.date.toDateString() === date.toDateString()
    )
    
    calendarDays.push({
      day,
      date,
      attended: attendanceRecord?.attended || false,
      duration: attendanceRecord?.duration
    })
  }
  
  const monthNames = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ]
  
  const weekDays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]
  
  const goToPreviousMonth = () => {
    setCurrentDate(new Date(currentYear, currentMonth - 1, 1))
  }
  
  const goToNextMonth = () => {
    setCurrentDate(new Date(currentYear, currentMonth + 1, 1))
  }
  
  const attendedDaysThisMonth = calendarDays.filter(day => day?.attended).length
  
  return (
    <div className={className}>
      <div className="space-y-4">
        {/* Modern Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
          <div className="text-xs text-slate-600">
            {attendedDaysThisMonth} workout{attendedDaysThisMonth !== 1 ? 's' : ''} this month
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={goToPreviousMonth}
              className="h-8 w-8 p-0 border-slate-200 hover:bg-slate-50"
            >
              <ChevronLeft className="w-4 h-4 text-slate-600" />
            </Button>
            <span className="text-sm font-medium min-w-[120px] text-center text-slate-900">
              {monthNames[currentMonth]} {currentYear}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={goToNextMonth}
              className="h-8 w-8 p-0 border-slate-200 hover:bg-slate-50"
            >
              <ChevronRight className="w-4 h-4 text-slate-600" />
            </Button>
          </div>
        </div>

        {/* Modern Calendar Content */}
        <div className="bg-slate-50 rounded-xl p-4">
          {/* Week day headers */}
          <div className="grid grid-cols-7 gap-1 mb-3">
            {weekDays.map(day => (
              <div key={day} className="text-center text-xs font-medium text-slate-600 p-2">
                {day}
              </div>
            ))}
          </div>

          {/* Calendar grid */}
          <div className="grid grid-cols-7 gap-1">
            {calendarDays.map((day, index) => (
              <div
                key={index}
                className={cn(
                  "aspect-square flex items-center justify-center text-sm rounded-lg transition-all relative",
                  "hover:bg-slate-200",
                  day === null && "invisible",
                  day?.attended && "bg-emerald-100 text-emerald-700 font-medium border border-emerald-200",
                  !day?.attended && day !== null && "text-slate-600 bg-white border border-slate-200",
                  day?.date.toDateString() === new Date().toDateString() &&
                    "ring-2 ring-blue-500 ring-offset-1"
                )}
                title={day?.attended ? `Workout: ${day.duration || 60} minutes` : undefined}
              >
                {day?.day}
                {day?.attended && (
                  <div className="absolute bottom-1 right-1 w-1.5 h-1.5 bg-emerald-500 rounded-full" />
                )}
              </div>
            ))}
          </div>

          {/* Modern Legend */}
          <div className="flex items-center justify-center gap-6 mt-4 text-xs text-slate-600">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded bg-emerald-100 border border-emerald-200" />
              <span>Workout day</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded bg-white border border-slate-200" />
              <span>Rest day</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded border-2 border-blue-500" />
              <span>Today</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
