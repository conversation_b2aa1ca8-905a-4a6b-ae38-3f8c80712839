"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { AlertCircle, CheckCircle, Zap } from "lucide-react"

interface EMSSafetyChecklistProps {
  onComplete: () => void
}

export function EMSSafetyChecklist({ onComplete }: EMSSafetyChecklistProps) {
  const [checklist, setChecklist] = useState({
    hydrated: false,
    noJewelry: false,
    properClothing: false,
    medicalClearance: false,
    understoodInstructions: false
  })
  
  const allChecked = Object.values(checklist).every(Boolean)
  
  const handleCheck = (key: keyof typeof checklist) => {
    setChecklist(prev => ({
      ...prev,
      [key]: !prev[key]
    }))
  }
  
  return (
    <Card className="bg-gray-900/80 backdrop-blur-sm border border-yellow-500/50 shadow-lg shadow-yellow-500/20">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <AlertCircle className="w-5 h-5 text-yellow-400" />
          EMS Safety Checklist
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
          <p className="text-sm text-yellow-300">
            Please confirm the following safety items before starting your EMS session.
          </p>
        </div>
        
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <Checkbox 
              id="hydrated" 
              checked={checklist.hydrated}
              onCheckedChange={() => handleCheck('hydrated')}
              className="data-[state=checked]:bg-yellow-500 data-[state=checked]:border-yellow-500"
            />
            <div className="grid gap-1.5">
              <label 
                htmlFor="hydrated" 
                className="text-sm font-medium leading-none text-white cursor-pointer"
              >
                I am properly hydrated
              </label>
              <p className="text-xs text-gray-400">
                Proper hydration is essential for safe EMS training
              </p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <Checkbox 
              id="noJewelry" 
              checked={checklist.noJewelry}
              onCheckedChange={() => handleCheck('noJewelry')}
              className="data-[state=checked]:bg-yellow-500 data-[state=checked]:border-yellow-500"
            />
            <div className="grid gap-1.5">
              <label 
                htmlFor="noJewelry" 
                className="text-sm font-medium leading-none text-white cursor-pointer"
              >
                I have removed all jewelry and metal objects
              </label>
              <p className="text-xs text-gray-400">
                Metal objects can interfere with EMS signals
              </p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <Checkbox 
              id="properClothing" 
              checked={checklist.properClothing}
              onCheckedChange={() => handleCheck('properClothing')}
              className="data-[state=checked]:bg-yellow-500 data-[state=checked]:border-yellow-500"
            />
            <div className="grid gap-1.5">
              <label 
                htmlFor="properClothing" 
                className="text-sm font-medium leading-none text-white cursor-pointer"
              >
                I am wearing appropriate EMS training clothing
              </label>
              <p className="text-xs text-gray-400">
                Cotton clothing that allows proper electrode contact
              </p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <Checkbox 
              id="medicalClearance" 
              checked={checklist.medicalClearance}
              onCheckedChange={() => handleCheck('medicalClearance')}
              className="data-[state=checked]:bg-yellow-500 data-[state=checked]:border-yellow-500"
            />
            <div className="grid gap-1.5">
              <label 
                htmlFor="medicalClearance" 
                className="text-sm font-medium leading-none text-white cursor-pointer"
              >
                I have no medical contraindications for EMS training
              </label>
              <p className="text-xs text-gray-400">
                No pacemakers, pregnancy, acute injuries, or other contraindications
              </p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <Checkbox 
              id="understoodInstructions" 
              checked={checklist.understoodInstructions}
              onCheckedChange={() => handleCheck('understoodInstructions')}
              className="data-[state=checked]:bg-yellow-500 data-[state=checked]:border-yellow-500"
            />
            <div className="grid gap-1.5">
              <label 
                htmlFor="understoodInstructions" 
                className="text-sm font-medium leading-none text-white cursor-pointer"
              >
                I understand how to communicate with my EMS specialist during the session
              </label>
              <p className="text-xs text-gray-400">
                I will immediately report any discomfort or issues
              </p>
            </div>
          </div>
        </div>
        
        <Button 
          onClick={onComplete}
          disabled={!allChecked}
          className="w-full bg-gradient-to-r from-yellow-500 to-pink-500 hover:from-yellow-400 hover:to-pink-400 text-white"
        >
          {allChecked ? (
            <>
              <CheckCircle className="w-4 h-4 mr-2" />
              Confirm & Start EMS Session
            </>
          ) : (
            <>
              <Zap className="w-4 h-4 mr-2" />
              Please Complete Safety Checklist
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  )
}