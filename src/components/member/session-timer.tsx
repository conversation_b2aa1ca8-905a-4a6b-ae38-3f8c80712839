"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Clock, Zap, Battery } from "lucide-react"

interface SessionTimerProps {
  startTime: string
  maxDuration?: number // in minutes
}

export function SessionTimer({ startTime, maxDuration = 20 }: SessionTimerProps) {
  const [elapsed, setElapsed] = useState(0)
  const [remaining, setRemaining] = useState(maxDuration * 60) // convert to seconds
  const [progress, setProgress] = useState(0)
  
  useEffect(() => {
    const start = new Date(startTime).getTime()
    
    const timer = setInterval(() => {
      const now = new Date().getTime()
      const elapsedSeconds = Math.floor((now - start) / 1000)
      const remainingSeconds = Math.max(0, maxDuration * 60 - elapsedSeconds)
      const progressPercent = Math.min(100, (elapsedSeconds / (maxDuration * 60)) * 100)
      
      setElapsed(elapsedSeconds)
      setRemaining(remainingSeconds)
      setProgress(progressPercent)
      
      if (remainingSeconds <= 0) {
        clearInterval(timer)
      }
    }, 1000)
    
    return () => clearInterval(timer)
  }, [startTime, maxDuration])
  
  // Format time as MM:SS
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  
  // Get battery level based on remaining time
  const getBatteryLevel = () => {
    return Math.max(0, Math.floor((remaining / (maxDuration * 60)) * 100))
  }
  
  // Get color based on remaining time
  const getColor = () => {
    const level = getBatteryLevel()
    if (level > 70) return "text-green-400"
    if (level > 30) return "text-yellow-400"
    return "text-red-400"
  }
  
  return (
    <Card className="bg-gray-900/80 backdrop-blur-sm border border-pink-500/50 shadow-lg shadow-pink-500/20">
      <CardContent className="p-6 space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Zap className="w-5 h-5 text-pink-400" />
            <h3 className="font-bold text-white">EMS Session in Progress</h3>
          </div>
          <Badge className="bg-blue-500/20 text-blue-400">
            Active
          </Badge>
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-gray-400" />
              <span className="text-sm text-gray-400">Elapsed Time</span>
            </div>
            <span className="text-sm font-medium text-white">{formatTime(elapsed)}</span>
          </div>
          
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Battery className={`w-4 h-4 ${getColor()}`} />
              <span className="text-sm text-gray-400">Remaining</span>
            </div>
            <span className={`text-sm font-medium ${getColor()}`}>{formatTime(remaining)}</span>
          </div>
        </div>
        
        <div>
          <Progress 
            value={progress} 
            className="h-2 bg-gray-800"
            indicatorClassName="bg-gradient-to-r from-pink-500 to-purple-500"
          />
          <div className="flex justify-between mt-1 text-xs text-gray-500">
            <span>0:00</span>
            <span>{formatTime(maxDuration * 60)}</span>
          </div>
        </div>
        
        <div className="text-center text-xs text-gray-400 mt-2">
          {remaining > 0 ? (
            <p>Your EMS session will complete in {formatTime(remaining)}</p>
          ) : (
            <p>EMS session complete! Please check out.</p>
          )}
        </div>
      </CardContent>
    </Card>
  )
}