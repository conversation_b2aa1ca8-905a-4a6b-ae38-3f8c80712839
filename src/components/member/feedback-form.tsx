"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { NeonButton } from "@/components/ui/neon-button"
import { Star, MessageSquare, Dumbbell, Building, Users } from "lucide-react"
import { cn } from "@/lib/utils"
import { supabase } from "@/lib/supabase/client"
import { toast } from "sonner"

interface FeedbackFormProps {
  className?: string
}

const feedbackCategories = [
  {
    id: "workout",
    label: "Workout Experience",
    icon: Dumbbell,
    description: "Rate your workout session"
  },
  {
    id: "facility",
    label: "Facility & Equipment",
    icon: Building,
    description: "How was the gym facility?"
  },
  {
    id: "trainer",
    label: "Trainer Service",
    icon: Users,
    description: "Rate your trainer interaction"
  },
  {
    id: "general",
    label: "General Feedback",
    icon: MessageSquare,
    description: "Overall gym experience"
  }
]

export function FeedbackForm({ className }: FeedbackFormProps) {
  const [selectedCategory, setSelectedCategory] = useState("workout")
  const [rating, setRating] = useState(0)
  const [hoveredRating, setHoveredRating] = useState(0)
  const [comment, setComment] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    const getUser = async () => {
        const { data: { user } } = await supabase.auth.getUser();
        setUser(user);
    };
    getUser();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) {
        toast.error("You must be logged in to submit feedback.");
        return;
    }
    if (rating === 0) {
        toast.error("Please provide a star rating.");
        return;
    }

    setIsSubmitting(true)
    
    try {
      const { error } = await supabase
        .from('feedback')
        .insert({
            user_id: user.id,
            category: selectedCategory,
            rating: rating,
            comment: comment.trim(),
        });

      if (error) {
        throw error;
      }
      
      // Reset form
      setRating(0)
      setComment("")
      setSelectedCategory("workout")
      
      toast.success("Thank you for your feedback!")
    } catch (error: any) {
      console.error("Error submitting feedback:", error)
      toast.error(`Failed to submit feedback: ${error.message || "Unknown error"}`);
    } finally {
      setIsSubmitting(false)
    }
  }

  const selectedCategoryData = feedbackCategories.find(cat => cat.id === selectedCategory)

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-communication">
          <MessageSquare className="w-5 h-5" />
          Share Your Feedback
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Category Selection */}
          <div>
            <Label className="text-base font-medium mb-3 block">
              What would you like to give feedback about?
            </Label>
            <RadioGroup value={selectedCategory} onValueChange={setSelectedCategory}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {feedbackCategories.map((category) => (
                  <div key={category.id} className="relative">
                    <RadioGroupItem
                      value={category.id}
                      id={category.id}
                      className="peer sr-only"
                    />
                    <Label
                      htmlFor={category.id}
                      className={cn(
                        "flex items-center gap-3 p-4 rounded-lg border-2 cursor-pointer transition-all",
                        "hover:bg-communication/5 hover:border-communication/30",
                        "peer-checked:bg-communication/10 peer-checked:border-communication peer-checked:text-communication"
                      )}
                    >
                      <category.icon className="w-5 h-5" />
                      <div>
                        <div className="font-medium">{category.label}</div>
                        <div className="text-xs text-muted-foreground">
                          {category.description}
                        </div>
                      </div>
                    </Label>
                  </div>
                ))}
              </div>
            </RadioGroup>
          </div>

          {/* Rating */}
          <div>
            <Label className="text-base font-medium mb-3 block">
              How would you rate your {selectedCategoryData?.label.toLowerCase()}?
            </Label>
            <div className="flex items-center gap-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  onClick={() => setRating(star)}
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                  className="transition-all duration-200 hover:scale-110"
                >
                  <Star
                    className={cn(
                      "w-8 h-8 transition-colors",
                      (hoveredRating >= star || rating >= star)
                        ? "fill-member text-member"
                        : "text-muted-foreground"
                    )}
                  />
                </button>
              ))}
              {rating > 0 && (
                <span className="ml-2 text-sm text-muted-foreground">
                  {rating === 1 && "Poor"}
                  {rating === 2 && "Fair"}
                  {rating === 3 && "Good"}
                  {rating === 4 && "Very Good"}
                  {rating === 5 && "Excellent"}
                </span>
              )}
            </div>
          </div>

          {/* Comment */}
          <div>
            <Label htmlFor="comment" className="text-base font-medium mb-3 block">
              Tell us more about your experience (optional)
            </Label>
            <Textarea
              id="comment"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Share your thoughts, suggestions, or any specific details..."
              className="min-h-[100px] resize-none"
              maxLength={500}
            />
            <div className="text-xs text-muted-foreground mt-1 text-right">
              {comment.length}/500 characters
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <NeonButton
              type="submit"
              variant="communication"
              disabled={rating === 0 || isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? "Submitting..." : "Submit Feedback"}
            </NeonButton>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
