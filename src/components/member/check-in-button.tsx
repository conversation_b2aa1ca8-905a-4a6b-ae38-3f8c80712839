"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { NeonButton } from "@/components/ui/neon-button"
import { CheckCircle, Clock, MapPin } from "lucide-react"
import { cn } from "@/lib/utils"
import Confetti from 'react-confetti'

interface CheckInButtonProps {
  isCheckedIn?: boolean
  onCheckIn?: () => void
  onCheckOut?: () => void
  className?: string
}

export function CheckInButton({ 
  isCheckedIn = false, 
  onCheckIn, 
  onCheckOut, 
  className 
}: CheckInButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [showCelebration, setShowCelebration] = useState(false)
  const [confettiActive, setConfettiActive] = useState(false)

  const handleClick = async () => {
    setIsLoading(true)
    
    try {
      if (isCheckedIn) {
        await onCheckOut?.()
        setConfettiActive(false)
      } else {
        await onCheckIn?.()
        // Show celebration animation and confetti for check-in
        setShowCelebration(true)
        setConfettiActive(true)
        setTimeout(() => setShowCelebration(false), 2000)
        setTimeout(() => setConfettiActive(false), 3000)
      }
    } catch (error) {
      console.error("Check-in/out error:", error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className={cn("relative flex flex-col items-center gap-4", className)}>
      {confettiActive && <Confetti tweenDuration={1000} recycle={false} numberOfPieces={500} gravity={0.3} colors={['#FF10F0', '#00FFFF', '#39FF14', '#FF6B35']} />}

      {/* Main Check-in Button */}
      <motion.div
        className="relative"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <button
          onClick={handleClick}
          disabled={isLoading}
          className={cn(
            "w-32 h-32 sm:w-40 sm:h-40 rounded-2xl text-lg font-semibold transition-all duration-300",
            "flex flex-col items-center justify-center gap-2 shadow-lg hover:shadow-xl",
            "transform hover:scale-105 active:scale-95",
            isCheckedIn
              ? "bg-gradient-to-r from-emerald-500 to-teal-500 text-white hover:from-emerald-600 hover:to-teal-600"
              : "bg-gradient-to-r from-blue-500 to-purple-500 text-white hover:from-blue-600 hover:to-purple-600",
            isLoading && "opacity-75 cursor-not-allowed",
            showCelebration && "animate-pulse"
          )}
        >
          {isLoading ? (
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-white border-t-transparent" />
          ) : (
            <>
              {isCheckedIn ? (
                <CheckCircle className="w-8 h-8 sm:w-10 sm:h-10" />
              ) : (
                <MapPin className="w-8 h-8 sm:w-10 sm:h-10" />
              )}
              <span className="text-sm sm:text-base">
                {isCheckedIn ? "Check Out" : "Check In"}
              </span>
            </>
          )}
        </button>

        {/* Celebration Effect */}
        {showCelebration && (
          <motion.div
            className="absolute inset-0 rounded-full"
            initial={{ scale: 1, opacity: 0 }}
            animate={{ scale: 2, opacity: [0, 1, 0] }}
            transition={{ duration: 1.5 }}
            style={{
              background: "radial-gradient(circle, rgba(255, 16, 240, 0.4) 0%, transparent 70%)",
              pointerEvents: "none"
            }}
          />
        )}
      </motion.div>

      {/* Status Information */}
      <div className="text-center space-y-2">
        <div className="text-xs text-slate-500">
          {isCheckedIn ? "Tap to check out when you're done" : "Tap to check in to the gym"}
        </div>
      </div>
    </div>
  )
}
