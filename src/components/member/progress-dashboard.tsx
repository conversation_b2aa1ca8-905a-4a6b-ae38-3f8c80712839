"use client"

import { ProgressRing } from "@/components/ui/progress-ring"
import { AchievementBadge } from "@/components/ui/achievement-badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar, TrendingUp, Target, Flame } from "lucide-react"
import { motion } from "framer-motion"

interface ProgressData {
  monthlyAttendance: number
  currentStreak: number
  monthlyGoal: number
  totalWorkouts: number
}

interface ProgressDashboardProps {
  data: ProgressData
  className?: string
}

export function ProgressDashboard({ data, className }: ProgressDashboardProps) {
  const { monthlyAttendance, currentStreak, monthlyGoal, totalWorkouts } = data
  
  const monthlyProgress = (monthlyAttendance / monthlyGoal) * 100
  const streakProgress = Math.min((currentStreak / 30) * 100, 100) // Max 30 day streak visualization

  const achievements = [
    {
      type: "streak" as const,
      level: "bronze" as const,
      earned: currentStreak >= 3,
      title: "3 Day Streak",
      description: "Keep it up!"
    },
    {
      type: "milestone" as const,
      level: "silver" as const,
      earned: monthlyAttendance >= 10,
      title: "10 Workouts",
      description: "This month"
    },
    {
      type: "goal" as const,
      level: "gold" as const,
      earned: monthlyProgress >= 100,
      title: "Monthly Goal",
      description: "Achieved!"
    },
    {
      type: "special" as const,
      level: "platinum" as const,
      earned: currentStreak >= 30,
      title: "30 Day Warrior",
      description: "Incredible!"
    }
  ]

  return (
    <div className={className}>
      {/* Modern Progress Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 text-center">
            <div className="flex items-center justify-center gap-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                <Calendar className="w-4 h-4 text-white" />
              </div>
              <h3 className="text-sm font-medium text-slate-900">Monthly Progress</h3>
            </div>
            <ProgressRing
              progress={monthlyProgress}
              variant="member"
              size="lg"
              label={`${monthlyAttendance}/${monthlyGoal} workouts`}
            />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 text-center">
            <div className="flex items-center justify-center gap-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                <Flame className="w-4 h-4 text-white" />
              </div>
              <h3 className="text-sm font-medium text-slate-900">Current Streak</h3>
            </div>
            <ProgressRing
              progress={streakProgress}
              variant="marketing"
              size="lg"
              showPercentage={false}
              label={`${currentStreak} days`}
            />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="sm:col-span-2 lg:col-span-1"
        >
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 text-center">
            <div className="flex items-center justify-center gap-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-4 h-4 text-white" />
              </div>
              <h3 className="text-sm font-medium text-slate-900">Total Workouts</h3>
            </div>
            <div className="text-3xl font-bold text-slate-900 mb-2">{totalWorkouts}</div>
            <p className="text-sm text-slate-600">All time</p>
          </div>
        </motion.div>
      </div>

      {/* Modern Achievements */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-8 h-8 bg-gradient-to-r from-violet-500 to-purple-500 rounded-lg flex items-center justify-center">
            <Target className="w-4 h-4 text-white" />
          </div>
          <h3 className="text-lg font-semibold text-slate-900">Achievements</h3>
        </div>
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
          {achievements.map((achievement, index) => (
            <motion.div
              key={`${achievement.type}-${achievement.level}`}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.4 + index * 0.1 }}
            >
              <AchievementBadge
                {...achievement}
                animate={achievement.earned}
              />
            </motion.div>
          ))}
        </div>
      </div>

      {/* Motivational Message */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.8 }}
        className="mt-6 text-center"
      >
        <Card className="bg-gradient-to-r from-member/10 to-marketing/10 border-member/20">
          <CardContent className="pt-6">
            {monthlyProgress >= 100 ? (
              <p className="text-lg font-medium text-member">
                🎉 Amazing! You&apos;ve crushed your monthly goal! Keep up the incredible work!
              </p>
            ) : currentStreak >= 7 ? (
              <p className="text-lg font-medium text-marketing">
                🔥 You&apos;re on fire! {currentStreak} days strong - you&apos;re building an amazing habit!
              </p>
            ) : currentStreak >= 3 ? (
              <p className="text-lg font-medium text-blog">
                💪 Great momentum! You&apos;re {monthlyGoal - monthlyAttendance} workouts away from your monthly goal!
              </p>
            ) : (
              <p className="text-lg font-medium">
                🚀 Ready to build your streak? Every journey starts with a single step!
              </p>
            )}
          </CardContent>
        </Card>
      </motion.div>
      {/* Line 167: Replace ' with &apos; */}
      <p>Here&apos;s your progress so far</p>
      {/* Line 171: Replace ' with &apos; */}
      <p>You&apos;re making great progress! Keep it up!</p>
      {/* Line 175: Replace ' with &apos; */}
      <p>Let&apos;s achieve your fitness goals together</p>
    </div>
  )
}
