"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { supabase } from "@/lib/supabase/client"
import { toast } from "sonner"
import {
  Zap,
  Clock,
  CheckCircle,
  Loader2
} from "lucide-react"

interface EMSSessionCardProps {
  userId: string
  onSessionUpdate?: () => void
}

export function EMSSessionCard({ userId, onSessionUpdate }: EMSSessionCardProps) {
  const [loading, setLoading] = useState(true)
  const [isCheckedIn, setIsCheckedIn] = useState(false)
  const [checkInTime, setCheckInTime] = useState<string | null>(null)
  const [elapsedTime, setElapsedTime] = useState(0)
  const [sessionDuration] = useState(20 * 60) // 20 minutes in seconds

  useEffect(() => {
    const fetchSessionStatus = async () => {
      try {
        const { data: currentCheckIn, error } = await supabase
          .from('check_ins')
          .select('*')
          .eq('user_id', userId)
          .is('check_out_time', null)
          .single()
        
        if (error && error.code !== 'PGRST116') { // PGRST116 is no rows found
          console.error("Error checking current check-in status:", error)
        }

        if (currentCheckIn) {
          setIsCheckedIn(true)
          setCheckInTime(currentCheckIn.check_in_time)
        } else {
          setIsCheckedIn(false)
          setCheckInTime(null)
        }
      } catch (error) {
        console.error("Error fetching session status:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchSessionStatus()
  }, [userId])

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null
    
    if (isCheckedIn && checkInTime) {
      const startTime = new Date(checkInTime).getTime()
      
      timer = setInterval(() => {
        const now = new Date().getTime()
        const elapsed = Math.floor((now - startTime) / 1000)
        setElapsedTime(elapsed)
      }, 1000)
    }
    
    return () => {
      if (timer) clearInterval(timer)
    }
  }, [isCheckedIn, checkInTime])

  const handleCheckIn = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('check_ins')
        .insert({
          user_id: userId,
        })
        .select()
        .single()

      if (error) throw error

      setIsCheckedIn(true)
      setCheckInTime(data.check_in_time)
      toast.success("EMS session started successfully!")
      
      if (onSessionUpdate) onSessionUpdate()
    } catch (error: any) {
      console.error("Error during check-in:", error)
      toast.error(`Failed to check in: ${error.message || "Unknown error"}`)
    } finally {
      setLoading(false)
    }
  }

  const handleCheckOut = async () => {
    if (!checkInTime) {
      toast.info("No active EMS session to end.")
      return
    }

    try {
      setLoading(true)
      const checkInTimeDate = new Date(checkInTime)
      const durationMinutes = Math.round((new Date().getTime() - checkInTimeDate.getTime()) / (1000 * 60))
      
      const { error } = await supabase
        .from('check_ins')
        .update({
          check_out_time: new Date().toISOString(),
          duration_minutes: durationMinutes,
        })
        .eq('user_id', userId)
        .is('check_out_time', null)
      
      if (error) throw error

      setIsCheckedIn(false)
      setCheckInTime(null)
      setElapsedTime(0)
      
      toast.success(`EMS session completed! Duration: ${durationMinutes} minutes.`)
      
      if (onSessionUpdate) onSessionUpdate()
    } catch (error: any) {
      console.error("Error during check-out:", error)
      toast.error(`Failed to end session: ${error.message || "Unknown error"}`)
    } finally {
      setLoading(false)
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const calculateProgress = () => {
    return Math.min(100, (elapsedTime / sessionDuration) * 100)
  }

  if (loading) {
    return (
      <Card className="bg-gray-900/50 border-gray-800">
        <CardContent className="p-6 flex items-center justify-center">
          <Loader2 className="w-6 h-6 animate-spin text-pink-400" />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="bg-gray-900/50 border-gray-800">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <Zap className="w-5 h-5 text-pink-400" />
          EMS Session Control
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isCheckedIn ? (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-blue-400" />
                <span className="text-sm font-medium text-white">Session in Progress</span>
              </div>
              <Badge className="bg-blue-500/20 text-blue-400">
                Active
              </Badge>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Started at:</span>
                <span className="text-white">{new Date(checkInTime!).toLocaleTimeString()}</span>
              </div>
              
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Elapsed time:</span>
                <span className="text-white">{formatTime(elapsedTime)}</span>
              </div>
              
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Session duration:</span>
                <span className="text-white">20:00</span>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between text-xs text-gray-500 mb-1">
                <span>0:00</span>
                <span>20:00</span>
              </div>
              <Progress 
                value={calculateProgress()} 
                className="h-2 bg-gray-800"
              />
            </div>
            
            <Button 
              onClick={handleCheckOut}
              className="w-full bg-gradient-to-r from-green-500 to-cyan-500 hover:from-green-400 hover:to-cyan-400"
            >
              <CheckCircle className="w-4 h-4 mr-2" />
              End EMS Session
            </Button>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="text-center p-4">
              <Zap className="w-12 h-12 text-pink-400 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-white mb-2">Start Your EMS Session</h3>
              <p className="text-sm text-gray-400 mb-4">
                Experience a full-body workout in just 20 minutes with our state-of-the-art EMS technology.
              </p>
            </div>
            
            <Button 
              onClick={handleCheckIn}
              className="w-full bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-400 hover:to-purple-400"
            >
              <Zap className="w-4 h-4 mr-2" />
              Start EMS Session
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}