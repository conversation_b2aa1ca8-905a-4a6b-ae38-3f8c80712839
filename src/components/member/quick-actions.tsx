"use client"

import { Card, CardContent } from "@/components/ui/card"
import { NeonButton } from "@/components/ui/neon-button"
import { 
  CreditCard, 
  MessageSquare, 
  Calendar, 
  User, 
  FileText,
  Bell
} from "lucide-react"
import { motion } from "framer-motion"

interface QuickAction {
  icon: React.ElementType
  label: string
  description: string
  href: string
  variant: "member" | "admin" | "blog" | "marketing" | "communication"
  badge?: string
}

const quickActions: QuickAction[] = [
  {
    icon: CreditCard,
    label: "Payments",
    description: "Manage membership fees",
    href: "/dashboard/payments",
    variant: "member",
    badge: "Due Soon"
  },
  {
    icon: MessageSquare,
    label: "Feedback",
    description: "Share your experience",
    href: "/dashboard/feedback",
    variant: "communication"
  },
  {
    icon: Calendar,
    label: "Book Session",
    description: "Schedule with trainers",
    href: "/dashboard/appointments",
    variant: "blog"
  },
  {
    icon: User,
    label: "Profile",
    description: "Update your info",
    href: "/dashboard/profile",
    variant: "admin"
  },
  {
    icon: FileText,
    label: "Resources",
    description: "Helpful materials",
    href: "/helpful-materials",
    variant: "marketing"
  },
  {
    icon: Bell,
    label: "Messages",
    description: "Staff communications",
    href: "/dashboard/messages",
    variant: "communication",
    badge: "2"
  }
]

interface QuickActionsProps {
  className?: string;
  quickStats?: {
    monthlyAttendance: number;
    currentStreak: number;
    totalWorkouts: number;
  };
}

export function QuickActions({ className, quickStats }: QuickActionsProps) {
  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Modern grid */}
        <div className="grid grid-cols-2 gap-3">
          {quickActions.map((action, index) => (
            <motion.div
              key={action.href}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <a href={action.href} className="block">
                <div className="relative group">
                  <div className="h-full transition-all duration-200 hover:scale-105 bg-slate-50 hover:bg-slate-100 border border-slate-200 rounded-xl hover:shadow-md">
                    <div className="p-3 text-center">
                      <div className="relative mb-3">
                        <div className={`
                          w-10 h-10 mx-auto rounded-xl flex items-center justify-center
                          ${action.variant === 'member' ? 'bg-gradient-to-r from-blue-500 to-purple-500' : ''}
                          ${action.variant === 'admin' ? 'bg-gradient-to-r from-slate-500 to-slate-600' : ''}
                          ${action.variant === 'blog' ? 'bg-gradient-to-r from-emerald-500 to-teal-500' : ''}
                          ${action.variant === 'marketing' ? 'bg-gradient-to-r from-orange-500 to-red-500' : ''}
                          ${action.variant === 'communication' ? 'bg-gradient-to-r from-violet-500 to-purple-500' : ''}
                        `}>
                          <action.icon className="w-5 h-5 text-white" />
                        </div>

                        {action.badge && (
                          <div className={`
                            absolute -top-1 -right-1 w-5 h-5 rounded-full text-xs font-bold
                            flex items-center justify-center text-white
                            ${action.variant === 'member' ? 'bg-blue-500' : ''}
                            ${action.variant === 'communication' ? 'bg-violet-500' : ''}
                          `}>
                            {action.badge === "Due Soon" ? "!" : action.badge}
                          </div>
                        )}
                      </div>

                      <h4 className="font-medium text-sm text-slate-900 mb-1">{action.label}</h4>
                      <p className="text-xs text-slate-600">{action.description}</p>
                    </div>
                  </div>
                </div>
              </a>
            </motion.div>
          ))}
        </div>

        {/* Modern Quick Stats */}
        {quickStats && (
          <div className="pt-6 border-t border-slate-200">
            <div className="grid grid-cols-3 gap-3 text-center">
              <div className="bg-blue-50 rounded-lg p-3">
                <div className="text-xl font-bold text-blue-600">{quickStats.monthlyAttendance}</div>
                <div className="text-xs text-blue-600/70">This Month</div>
              </div>
              <div className="bg-orange-50 rounded-lg p-3">
                <div className="text-xl font-bold text-orange-600">{quickStats.currentStreak}</div>
                <div className="text-xs text-orange-600/70">Day Streak</div>
              </div>
              <div className="bg-emerald-50 rounded-lg p-3">
                <div className="text-xl font-bold text-emerald-600">{quickStats.totalWorkouts}</div>
                <div className="text-xs text-emerald-600/70">Total</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
