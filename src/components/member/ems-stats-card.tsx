"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { 
  Zap, 
  Clock, 
  Target, 
  Flame, 
  TrendingUp 
} from "lucide-react"

interface EMSStatsCardProps {
  totalSessions: number
  currentStreak: number
  monthlyAttendance: number
  monthlyGoal: number
}

export function EMSStatsCard({ 
  totalSessions, 
  currentStreak, 
  monthlyAttendance, 
  monthlyGoal 
}: EMSStatsCardProps) {
  // Calculate time saved compared to traditional workouts
  // EMS 20 min session = 90 min traditional workout
  const timeSavedMinutes = totalSessions * 70 // 90 - 20 = 70 minutes saved per session
  const timeSavedHours = Math.floor(timeSavedMinutes / 60)
  const remainingMinutes = timeSavedMinutes % 60
  
  // Calculate estimated benefits based on sessions
  const postureImprovement = Math.min(100, totalSessions * 2) // 2% improvement per session, max 100%
  const muscleToneProgress = Math.min(100, totalSessions * 1.5) // 1.5% improvement per session, max 100%
  
  // Calculate monthly progress percentage
  const monthlyProgressPercentage = Math.min(100, (monthlyAttendance / monthlyGoal) * 100)
  
  return (
    <Card className="bg-gray-900/50 border-gray-800">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <TrendingUp className="w-5 h-5 text-pink-400" />
          EMS Training Benefits
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Monthly Progress */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4 text-pink-400" />
              <span className="text-sm font-medium text-white">Monthly Progress</span>
            </div>
            <Badge className="bg-pink-500/20 text-pink-400">
              {monthlyAttendance}/{monthlyGoal} sessions
            </Badge>
          </div>
          <Progress 
            value={monthlyProgressPercentage} 
            className="h-2 bg-gray-800"
          />
          <p className="text-xs text-gray-400">
            {monthlyProgressPercentage >= 100 
              ? "Monthly goal achieved! Keep pushing!" 
              : `${Math.round(monthlyProgressPercentage)}% of monthly goal completed`}
          </p>
        </div>
        
        {/* Current Streak */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Flame className="w-4 h-4 text-orange-400" />
              <span className="text-sm font-medium text-white">Current Streak</span>
            </div>
            <Badge className="bg-orange-500/20 text-orange-400">
              {currentStreak} days
            </Badge>
          </div>
          <Progress 
            value={Math.min(100, (currentStreak / 7) * 100)} 
            className="h-2 bg-gray-800"
            indicatorClassName="bg-orange-500"
          />
          <p className="text-xs text-gray-400">
            {currentStreak >= 7 
              ? "7+ day streak! Amazing consistency!" 
              : `${7 - currentStreak} more days to reach a 7-day streak`}
          </p>
        </div>
        
        {/* Time Efficiency */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-cyan-400" />
              <span className="text-sm font-medium text-white">Time Saved</span>
            </div>
            <Badge className="bg-cyan-500/20 text-cyan-400">
              {timeSavedHours}h {remainingMinutes}m
            </Badge>
          </div>
          <p className="text-xs text-gray-400">
            Each 20-minute EMS session equals 90 minutes of traditional training
          </p>
        </div>
        
        {/* Posture Improvement */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4 text-purple-400" />
              <span className="text-sm font-medium text-white">Posture Improvement</span>
            </div>
            <Badge className="bg-purple-500/20 text-purple-400">
              {Math.round(postureImprovement)}%
            </Badge>
          </div>
          <Progress 
            value={postureImprovement} 
            className="h-2 bg-gray-800"
            indicatorClassName="bg-purple-500"
          />
        </div>
        
        {/* Muscle Definition */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Zap className="w-4 h-4 text-green-400" />
              <span className="text-sm font-medium text-white">Muscle Definition</span>
            </div>
            <Badge className="bg-green-500/20 text-green-400">
              {Math.round(muscleToneProgress)}%
            </Badge>
          </div>
          <Progress 
            value={muscleToneProgress} 
            className="h-2 bg-gray-800"
            indicatorClassName="bg-green-500"
          />
        </div>
      </CardContent>
    </Card>
  )
}