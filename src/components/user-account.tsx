"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { LogOutIcon, SettingsIcon, UserIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";
import { supabase } from "@/lib/supabase/client";

interface Profile {
  id: string;
  email: string;
  first_name: string | null;
  last_name: string | null;
  avatar_url: string | null;
  membership_type: string;
}

const UserAccount = () => {
    const [user, setUser] = useState<any>(null);
    const [profile, setProfile] = useState<Profile | null>(null);
    const [loading, setLoading] = useState(true);
    const router = useRouter();

    useEffect(() => {
        getUser();
    }, []);

    const getUser = async () => {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            setUser(user);

            if (user) {
                const { data: profile } = await supabase
                    .from('profiles')
                    .select('*')
                    .eq('id', user.id)
                    .single();
                setProfile(profile);
            }
        } catch (error) {
            console.error('Error fetching user:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleSignOut = async () => {
        try {
            await supabase.auth.signOut();
            router.push('/');
        } catch (error) {
            console.error('Error signing out:', error);
        }
    };

    if (loading) {
        return (
            <div className="w-8 h-8 rounded-full bg-gray-700 animate-pulse" />
        );
    }

    if (!user) {
        return null;
    }

    if (!profile) {
        return (
            <div className="relative aspect-square w-8 h-8 rounded-full border border-border cursor-pointer group">
                <Image
                    src="https://api.dicebear.com/9.x/adventurer/svg?backgroundType=gradientLinear,solid"
                    alt="User"
                    width={1024}
                    height={1024}
                    unoptimized
                    className="rounded-full w-full h-full object-cover group-hover:scale-105 transition-all"
                />
            </div>
        );
    }

    const displayName = profile.first_name && profile.last_name
        ? `${profile.first_name} ${profile.last_name}`
        : profile.first_name || profile.email;

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full p-0">
                    <div className="relative aspect-square w-8 h-8 rounded-full border border-border cursor-pointer group">
                        <Image
                            src={profile.avatar_url || "https://api.dicebear.com/9.x/adventurer/svg?backgroundType=gradientLinear,solid"}
                            alt={displayName}
                            width={32}
                            height={32}
                            unoptimized
                            className="rounded-full w-full h-full object-cover group-hover:scale-105 transition-all"
                        />
                    </div>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-64 p-2">
                <div className="flex items-center gap-2 p-2">
                    <div className="relative aspect-square w-10 h-10 rounded-full border border-border">
                        <Image
                            src={profile.avatar_url || "https://api.dicebear.com/9.x/adventurer/svg?backgroundType=gradientLinear,solid"}
                            alt={displayName}
                            width={40}
                            height={40}
                            unoptimized
                            className="rounded-full w-full h-full object-cover"
                        />
                    </div>
                    <div className="flex flex-col">
                        <p className="text-sm font-medium">{displayName}</p>
                        <p className="text-xs text-muted-foreground">{profile.email}</p>
                        <p className="text-xs text-pink-400 capitalize">{profile.membership_type} Member</p>
                    </div>
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild className="flex items-center gap-2 px-4 py-2 rounded-md">
                    <Link href="/dashboard/settings">
                        <UserIcon className="w-4 h-4" />
                        Profile
                    </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild className="flex items-center gap-2 px-4 py-2 rounded-md">
                    <Link href="/dashboard/settings">
                        <SettingsIcon className="w-4 h-4" />
                        Settings
                    </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut} className="text-destructive hover:!bg-destructive/10 hover:!text-destructive flex items-center gap-2 px-4 py-2 rounded-md">
                    <LogOutIcon className="w-4 h-4" />
                    Sign Out
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    )
};

export default UserAccount