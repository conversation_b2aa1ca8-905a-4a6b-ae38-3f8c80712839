# Database Setup Guide - Pulse.co.za

This guide will help you set up the Supabase database for the Pulse.co.za gym management system.

## Prerequisites

- Supabase account (free tier is sufficient for development)
- Node.js and npm installed
- Access to the Pulse.co.za project repository

## Step 1: Create Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign in
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - **Name**: `pulse-gym-management`
   - **Database Password**: Generate a strong password (save this!)
   - **Region**: Choose closest to your users
5. Click "Create new project"
6. Wait for the project to be created (2-3 minutes)

## Step 2: Get Project Credentials

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (starts with `https://`)
   - **Anon public key** (starts with `eyJ`)
   - **Service role key** (starts with `eyJ`) - Keep this secret!

## Step 3: Configure Environment Variables

1. In your project root, copy `.env.example` to `.env.local`:
   ```bash
   cp .env.example .env.local
   ```

2. Update the Supabase configuration in `.env.local`:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_project_url_here
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
   ```

## Step 4: Set Up Database Schema

1. In your Supabase dashboard, go to **SQL Editor**
2. Copy the contents of `src/lib/supabase/schema.sql`
3. Paste it into the SQL Editor and click **Run**
4. This will create all tables, types, functions, and policies

## Step 5: Seed Initial Data

1. Copy the contents of `src/lib/supabase/seed.sql`
2. Paste it into the SQL Editor and click **Run**
3. This will populate the database with sample blog posts and helpful materials

## Step 6: Verify Setup

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Check the browser console for any Supabase connection errors
3. Visit the admin dashboard to verify data is loading
4. Test the blog and helpful materials pages

## Database Schema Overview

### Core Tables

- **profiles**: User profiles extending Supabase auth
- **check_ins**: Gym visit tracking
- **feedback**: Member feedback and ratings
- **payments**: Membership payments and billing
- **blog_posts**: Content management for blog
- **helpful_materials**: Resource library
- **achievements**: Gamification system

### Key Features

- **Row Level Security (RLS)**: Ensures data privacy
- **Automatic timestamps**: Created/updated tracking
- **Triggers**: Auto-update functionality
- **Functions**: Business logic and analytics
- **Indexes**: Optimized query performance

## Authentication Setup

The system uses Supabase Auth with the following configuration:

### Email Authentication
1. Go to **Authentication** → **Settings**
2. Enable **Email** provider
3. Configure email templates if needed

### Social Authentication (Optional)
1. Enable Google, GitHub, or other providers
2. Configure OAuth credentials
3. Update redirect URLs

## Row Level Security Policies

The database includes comprehensive RLS policies:

- **Users** can only access their own data
- **Admins** (VIP members) can access management data
- **Public** content (blog, materials) is readable by all
- **System** functions handle automated processes

## Backup and Recovery

### Automated Backups
Supabase provides automatic daily backups for 7 days (free tier).

### Manual Backup
```sql
-- Export specific tables
COPY profiles TO '/tmp/profiles_backup.csv' DELIMITER ',' CSV HEADER;
COPY check_ins TO '/tmp/checkins_backup.csv' DELIMITER ',' CSV HEADER;
```

### Point-in-Time Recovery
Available on paid plans for up to 30 days.

## Performance Optimization

### Indexes
The schema includes optimized indexes for:
- User lookups by email
- Check-ins by date and user
- Feedback by status
- Blog posts by category and publication status

### Query Optimization
- Use `select()` to limit returned columns
- Implement pagination for large datasets
- Use `count: 'exact'` sparingly
- Cache frequently accessed data

## Monitoring and Analytics

### Built-in Analytics
Supabase provides:
- Query performance metrics
- Database size and growth
- API usage statistics
- Error tracking

### Custom Analytics
The schema includes functions for:
- Member growth tracking
- Peak hours analysis
- Feedback summaries
- Retention calculations

## Security Best Practices

1. **Environment Variables**: Never commit secrets to version control
2. **RLS Policies**: Always enable and test row-level security
3. **API Keys**: Use anon key for client-side, service key for server-side only
4. **HTTPS**: Always use HTTPS in production
5. **Regular Updates**: Keep Supabase client libraries updated

## Troubleshooting

### Common Issues

**Connection Errors**
- Verify environment variables are correct
- Check if Supabase project is active
- Ensure network connectivity

**Permission Errors**
- Review RLS policies
- Check user authentication status
- Verify admin privileges for management functions

**Performance Issues**
- Review query patterns
- Check for missing indexes
- Monitor database metrics

### Debug Mode
Enable debug logging:
```typescript
const supabase = createClient(url, key, {
  auth: {
    debug: true
  }
})
```

## Migration from Mock Data

If you have existing mock data, use the migration utility:

```typescript
import { runMigration } from '@/lib/supabase/migrate'

// Run migration
const result = await runMigration()
console.log(result)
```

## Production Deployment

### Environment Setup
1. Create production Supabase project
2. Configure production environment variables
3. Set up custom domain (optional)
4. Configure email settings

### Security Checklist
- [ ] RLS enabled on all tables
- [ ] Service role key secured
- [ ] CORS configured properly
- [ ] Rate limiting enabled
- [ ] Backup strategy in place

### Monitoring
- Set up alerts for errors
- Monitor database performance
- Track API usage
- Review security logs

## Support

For issues with this setup:
1. Check the [Supabase documentation](https://supabase.com/docs)
2. Review the project's GitHub issues
3. Contact the development team

## Next Steps

After completing the database setup:
1. Test all functionality thoroughly
2. Set up authentication flows
3. Configure payment processing
4. Deploy to production environment
5. Set up monitoring and alerts

---

**Note**: This setup guide assumes you're using the latest version of Supabase and the Pulse.co.za codebase. Always refer to the official Supabase documentation for the most up-to-date information.
