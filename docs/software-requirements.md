# Pulse.co.za Software Requirements Specification

## System Design

### Overall Architecture
- **Frontend**: Next.js 14+ with App Router for server-side rendering and optimal performance
- **Backend**: Node.js API routes within Next.js application for seamless integration
- **Database**: Supabase PostgreSQL with real-time subscriptions for live updates
- **Authentication**: Supabase Auth with email/password and social login options
- **Deployment**: Vercel for automatic deployments with preview environments
- **File Storage**: Supabase Storage for user avatars and gym-related media

### Core Modules
- **User Management**: Member registration, profiles, and role-based access
- **Attendance System**: Check-in/check-out functionality with real-time tracking
- **Payment Processing**: Integration with payment gateway (Stripe/PayPal recommended)
- **Feedback System**: Multi-category feedback collection and management
- **Communication**: Member-staff messaging and appointment booking
- **Admin Dashboard**: Comprehensive management interface for gym operations
- **Content Management**: Blog posts, helpful materials, and static page content
- **Public Website**: Landing page, blog, contact forms, and marketing content

## Architecture Pattern

### Design Pattern
- **Layered Architecture** with clear separation of concerns
- **Component-Based Architecture** using React components for reusability
- **API-First Design** with RESTful endpoints for data operations

### Folder Structure
```
/src
  /app
    /(auth)
    /(dashboard)
    /blog
    /contact
    /helpful-materials
    /api
  /components
    /ui
    /forms
    /dashboard
    /blog
    /marketing
  /lib
    /supabase
    /utils
    /mdx
  /hooks
  /types
  /content
    /blog
    /materials
```

### Key Architectural Decisions
- **Server Components** for initial page loads and SEO optimization
- **Client Components** for interactive features and real-time updates
- **API Routes** for backend logic and Supabase integration
- **Middleware** for authentication and route protection

## State Management

### Client-Side State
- **React useState/useReducer** for local component state
- **React Context** for global state (user session, theme preferences)
- **Custom Hooks** for reusable state logic and API calls
- **Supabase Realtime** for live data synchronization

### Server-Side State
- **Next.js Server Components** for initial data fetching
- **Supabase Session Management** for user authentication state
- **React Server Components** for dynamic content rendering

### State Categories
- **Authentication State**: User session, role, and permissions
- **UI State**: Theme mode (dark/light), loading states, modals
- **Application State**: Attendance data, payment status, feedback
- **Real-time State**: Live attendance updates, new messages

## Data Flow

### User Authentication Flow
1. User accesses application → Supabase Auth check
2. Unauthenticated → Redirect to login page
3. Login attempt → Supabase Auth verification
4. Success → Generate session → Redirect to dashboard
5. Session management → Automatic refresh and logout handling

### Attendance Tracking Flow
1. Member clicks check-in → Client validation
2. API call to `/api/attendance/checkin` → Server validation
3. Insert record to Supabase → Real-time broadcast
4. Update UI with success animation → Analytics tracking
5. Admin dashboard updates automatically via Supabase subscriptions

### Payment Processing Flow
1. Member initiates payment → Payment form validation
2. Secure payment processing → Third-party gateway integration
3. Payment confirmation → Database update
4. Receipt generation → Email notification
5. Admin notification → Status update across all interfaces

## Technical Stack

### Frontend Technologies
- **Next.js 14+**: React framework with App Router
- **TypeScript**: Type safety and better development experience
- **Tailwind CSS**: Utility-first CSS framework for responsive design
- **Shadcn/ui**: Pre-built accessible components
- **Framer Motion**: Animations for achievement celebrations
- **React Hook Form**: Form handling and validation
- **MDX**: Markdown with JSX for blog and helpful materials content
- **Next-SEO**: SEO optimization for public pages

### Backend Technologies
- **Node.js**: JavaScript runtime for API routes
- **Next.js API Routes**: Serverless functions for backend logic
- **Supabase JavaScript Client**: Database and authentication integration
- **Stripe SDK**: Payment processing (recommended)
- **Nodemailer**: Email notifications and receipts
- **Resend**: Modern email service for transactional emails
- **Gray-matter**: Front-matter parsing for blog posts

### Database & Storage
- **Supabase PostgreSQL**: Primary database with real-time capabilities
- **Supabase Storage**: File storage for user uploads
- **Supabase Auth**: User authentication and authorization
- **Row Level Security**: Database-level access control

### Development Tools
- **ESLint & Prettier**: Code quality and formatting
- **Husky**: Git hooks for code quality
- **Vercel CLI**: Local development and deployment
- **Supabase CLI**: Database migrations and local development

## Authentication Process

### User Registration
- **Email/Password**: Standard registration with email verification
- **Social Login**: Google/Apple sign-in options (optional)
- **Email Verification**: Required before account activation
- **Profile Setup**: Initial user information collection

### Authentication Flow
1. **Login Attempt**: User submits credentials
2. **Supabase Auth**: Validates credentials and generates JWT
3. **Session Creation**: Secure session establishment
4. **Role Assignment**: Member/Admin role determination
5. **Route Protection**: Middleware-based access control

### Security Measures
- **JWT Tokens**: Secure session management
- **Password Hashing**: Supabase handles secure password storage
- **CSRF Protection**: Built-in Next.js security features
- **Rate Limiting**: API endpoint protection against abuse

## Route Design

### Public Routes
- `/` - Landing page with hero section and gym overview
- `/about` - About Pulse gym and self-growth philosophy
- `/services` - Gym services and membership options
- `/blog` - Blog listing page with fitness and wellness content
- `/blog/[slug]` - Individual blog post pages
- `/helpful-materials` - Resource center with guides and tips
- `/helpful-materials/[category]` - Categorized helpful content
- `/contact` - Contact form and gym information
- `/login` - User authentication
- `/register` - New user registration
- `/forgot-password` - Password reset

### Protected Member Routes
- `/dashboard` - Member dashboard with attendance and progress
- `/checkin` - Quick check-in interface
- `/payments` - Payment history and processing
- `/feedback` - Feedback submission forms
- `/appointments` - Booking and scheduling
- `/profile` - User profile management

### Admin Routes
- `/admin` - Admin dashboard overview
- `/admin/members` - Member management
- `/admin/attendance` - Attendance analytics
- `/admin/payments` - Payment administration
- `/admin/feedback` - Feedback management
- `/admin/messages` - Communication center
- `/admin/blog` - Blog post management
- `/admin/blog/new` - Create new blog post
- `/admin/blog/edit/[id]` - Edit existing blog post
- `/admin/materials` - Helpful materials management
- `/admin/contact` - Contact form submissions

### API Routes
- `/api/auth/*` - Authentication endpoints
- `/api/members/*` - Member management
- `/api/attendance/*` - Check-in/check-out operations
- `/api/payments/*` - Payment processing
- `/api/feedback/*` - Feedback operations
- `/api/admin/*` - Administrative functions
- `/api/blog/*` - Blog post CRUD operations
- `/api/materials/*` - Helpful materials management
- `/api/contact` - Contact form submissions
- `/api/newsletter` - Newsletter subscription

## API Design

### RESTful Endpoints

#### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/logout` - User logout
- `GET /api/auth/session` - Get current session

#### Member Management
- `GET /api/members/profile` - Get user profile
- `PUT /api/members/profile` - Update user profile
- `GET /api/members/attendance` - Get attendance history
- `GET /api/members/payments` - Get payment history

#### Attendance System
- `POST /api/attendance/checkin` - Record gym check-in
- `POST /api/attendance/checkout` - Record gym check-out
- `GET /api/attendance/status` - Get current attendance status

#### Feedback System
- `POST /api/feedback/workout` - Submit workout feedback
- `POST /api/feedback/facility` - Submit facility feedback
- `POST /api/feedback/trainer` - Submit trainer feedback
- `GET /api/feedback/history` - Get user feedback history

#### Admin Operations
- `GET /api/admin/members` - Get all members
- `GET /api/admin/attendance/analytics` - Attendance analytics
- `GET /api/admin/feedback` - Get all feedback
- `PUT /api/admin/members/:id` - Update member information

#### Blog Management
- `GET /api/blog/posts` - Get all blog posts (with pagination)
- `GET /api/blog/posts/[slug]` - Get single blog post
- `POST /api/blog/posts` - Create new blog post (Admin only)
- `PUT /api/blog/posts/[id]` - Update blog post (Admin only)
- `DELETE /api/blog/posts/[id]` - Delete blog post (Admin only)
- `GET /api/blog/categories` - Get blog categories

#### Content Management
- `GET /api/materials` - Get helpful materials
- `GET /api/materials/[category]` - Get materials by category
- `POST /api/materials` - Create helpful material (Admin only)
- `PUT /api/materials/[id]` - Update material (Admin only)
- `DELETE /api/materials/[id]` - Delete material (Admin only)

#### Contact & Communication
- `POST /api/contact` - Submit contact form
- `GET /api/contact/submissions` - Get contact submissions (Admin only)
- `POST /api/newsletter/subscribe` - Newsletter subscription
- `GET /api/newsletter/subscribers` - Get subscribers (Admin only)

### Response Format
```json
{
  "success": boolean,
  "data": object | array,
  "message": string,
  "error": string | null
}
```

## Database Design ERD

### Core Tables

#### users
- `id` (UUID, Primary Key)
- `email` (VARCHAR, Unique)
- `full_name` (VARCHAR)
- `role` (ENUM: 'member', 'admin')
- `avatar_url` (TEXT)
- `phone` (VARCHAR)
- `date_joined` (TIMESTAMP)
- `is_active` (BOOLEAN)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

#### attendance_records
- `id` (UUID, Primary Key)
- `user_id` (UUID, Foreign Key → users.id)
- `check_in_time` (TIMESTAMP)
- `check_out_time` (TIMESTAMP, Nullable)
- `duration_minutes` (INTEGER, Nullable)
- `created_at` (TIMESTAMP)

#### payments
- `id` (UUID, Primary Key)
- `user_id` (UUID, Foreign Key → users.id)
- `amount` (DECIMAL)
- `currency` (VARCHAR, Default: 'ZAR')
- `payment_method` (VARCHAR)
- `status` (ENUM: 'pending', 'completed', 'failed')
- `stripe_payment_id` (VARCHAR, Nullable)
- `description` (TEXT)
- `due_date` (DATE)
- `paid_at` (TIMESTAMP, Nullable)
- `created_at` (TIMESTAMP)

#### feedback
- `id` (UUID, Primary Key)
- `user_id` (UUID, Foreign Key → users.id)
- `category` (ENUM: 'workout', 'facility', 'trainer', 'general')
- `rating` (INTEGER, 1-5)
- `comment` (TEXT)
- `trainer_name` (VARCHAR, Nullable)
- `is_anonymous` (BOOLEAN, Default: false)
- `admin_response` (TEXT, Nullable)
- `responded_at` (TIMESTAMP, Nullable)
- `created_at` (TIMESTAMP)

#### appointments
- `id` (UUID, Primary Key)
- `user_id` (UUID, Foreign Key → users.id)
- `trainer_name` (VARCHAR)
- `appointment_date` (DATE)
- `appointment_time` (TIME)
- `duration_minutes` (INTEGER, Default: 60)
- `service_type` (VARCHAR)
- `status` (ENUM: 'scheduled', 'completed', 'cancelled')
- `notes` (TEXT, Nullable)
- `created_at` (TIMESTAMP)

#### messages
- `id` (UUID, Primary Key)
- `sender_id` (UUID, Foreign Key → users.id)
- `recipient_id` (UUID, Foreign Key → users.id, Nullable)
- `subject` (VARCHAR)
- `message` (TEXT)
- `is_read` (BOOLEAN, Default: false)
- `message_type` (ENUM: 'inquiry', 'support', 'appointment')
- `created_at` (TIMESTAMP)

#### blog_posts
- `id` (UUID, Primary Key)
- `title` (VARCHAR)
- `slug` (VARCHAR, Unique)
- `excerpt` (TEXT)
- `content` (TEXT)
- `featured_image` (TEXT, Nullable)
- `author_id` (UUID, Foreign Key → users.id)
- `category` (VARCHAR)
- `tags` (TEXT[])
- `is_published` (BOOLEAN, Default: false)
- `published_at` (TIMESTAMP, Nullable)
- `seo_title` (VARCHAR, Nullable)
- `seo_description` (TEXT, Nullable)
- `read_time_minutes` (INTEGER)
- `views_count` (INTEGER, Default: 0)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

#### helpful_materials
- `id` (UUID, Primary Key)
- `title` (VARCHAR)
- `slug` (VARCHAR, Unique)
- `description` (TEXT)
- `content` (TEXT)
- `category` (ENUM: 'workout_guides', 'nutrition', 'mental_health', 'equipment', 'safety')
- `difficulty_level` (ENUM: 'beginner', 'intermediate', 'advanced')
- `estimated_read_time` (INTEGER)
- `featured_image` (TEXT, Nullable)
- `download_url` (TEXT, Nullable)
- `is_premium` (BOOLEAN, Default: false)
- `author_id` (UUID, Foreign Key → users.id)
- `views_count` (INTEGER, Default: 0)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

#### contact_submissions
- `id` (UUID, Primary Key)
- `name` (VARCHAR)
- `email` (VARCHAR)
- `phone` (VARCHAR, Nullable)
- `subject` (VARCHAR)
- `message` (TEXT)
- `inquiry_type` (ENUM: 'membership', 'services', 'support', 'general')
- `is_member` (BOOLEAN, Default: false)
- `status` (ENUM: 'new', 'in_progress', 'resolved')
- `admin_notes` (TEXT, Nullable)
- `responded_at` (TIMESTAMP, Nullable)
- `ip_address` (INET, Nullable)
- `created_at` (TIMESTAMP)

#### newsletter_subscriptions
- `id` (UUID, Primary Key)
- `email` (VARCHAR, Unique)
- `name` (VARCHAR, Nullable)
- `is_active` (BOOLEAN, Default: true)
- `subscription_source` (VARCHAR)
- `interests` (TEXT[], Nullable)
- `subscribed_at` (TIMESTAMP)
- `unsubscribed_at` (TIMESTAMP, Nullable)

### Relationships
- **users** ↔ **attendance_records** (One-to-Many)
- **users** ↔ **payments** (One-to-Many)
- **users** ↔ **feedback** (One-to-Many)
- **users** ↔ **appointments** (One-to-Many)
- **users** ↔ **messages** (One-to-Many as sender/recipient)
- **users** ↔ **blog_posts** (One-to-Many as author)
- **users** ↔ **helpful_materials** (One-to-Many as author)

### Indexes
- `users.email` (Unique)
- `attendance_records.user_id, check_in_time`
- `payments.user_id, created_at`
- `feedback.category, created_at`
- `appointments.user_id, appointment_date`
- `messages.recipient_id, is_read`
- `blog_posts.slug` (Unique)
- `blog_posts.is_published, published_at`
- `blog_posts.category, published_at`
- `helpful_materials.slug` (Unique)
- `helpful_materials.category, created_at`
- `contact_submissions.status, created_at`
- `newsletter_subscriptions.email` (Unique)
- `newsletter_subscriptions.is_active`

## Additional Technical Considerations

### Content Management System (CMS)
- **MDX Support**: Blog posts and helpful materials written in Markdown with JSX components
- **File-based CMS**: Content stored in `/content` directory for version control
- **Database-driven CMS**: Alternative option using Supabase for dynamic content management
- **Rich Text Editor**: Admin interface for non-technical content creation

### SEO Optimization
- **Next.js Metadata API**: Dynamic meta tags for all pages
- **Structured Data**: JSON-LD for blog posts and business information  
- **XML Sitemap**: Automatically generated sitemap for search engines
- **OpenGraph Tags**: Social media sharing optimization
- **Canonical URLs**: Prevent duplicate content issues

### Performance Optimization
- **Image Optimization**: Next.js Image component with Supabase Storage
- **Static Generation**: Blog posts and helpful materials pre-rendered at build time
- **Incremental Static Regeneration**: Content updates without full rebuilds
- **Edge Caching**: Vercel Edge Network for global content delivery
- **Code Splitting**: Automatic route-based code splitting

### Analytics & Monitoring
- **Google Analytics**: Page views and user behavior tracking
- **Vercel Analytics**: Performance monitoring and Core Web Vitals
- **Error Tracking**: Sentry integration for error monitoring
- **Content Analytics**: Track blog post views and engagement
- **Contact Form Analytics**: Track inquiry types and response rates