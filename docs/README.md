# 💪 Pulse.co.za - Your Gym Management Platform

## 🌟 Introduction
Pulse.co.za is a comprehensive web-based member management platform designed for gyms that emphasize self-growth and personal development. Built with Next.js, Supabase, Stripe, and TypeScript, Pulse empowers gym members to self-manage their fitness journey while providing administrators complete oversight and management capabilities.

## 🚀 Features

### Member Features
- **One-Touch Check-In:** Quick gym attendance tracking with progress visualization
- **Progress Tracking:** Monitor attendance streaks, monthly goals, and achievements
- **Payment Management:** Handle membership fees and service payments seamlessly
- **Multi-Channel Feedback:** Submit feedback on workouts, facilities, and trainers
- **Appointment Booking:** Schedule sessions and services with trainers/staff
- **Communication Hub:** Send inquiries and messages to gym staff

### Administrative Features
- **Real-Time Dashboard:** Monitor member activity, attendance patterns, and gym usage
- **Member Management:** Comprehensive member profiles and communication tools
- **Feedback Management:** Review and respond to member feedback across all categories
- **Content Management:** Create and manage blog posts and helpful materials
- **Analytics & Reporting:** Track engagement, attendance trends, and member satisfaction

## � Tech Stack

### Frontend
* **Next.js 14+** - React framework with App Router
* **TypeScript** - Type safety and better development experience
* **Tailwind CSS** - Utility-first CSS framework with neon pink design system
* **Shadcn/ui** - Pre-built accessible components
* **Framer Motion** - Animations for achievement celebrations
* **React Hook Form** - Form handling and validation

### Backend & Database
* **Supabase** - PostgreSQL database with real-time capabilities
* **Supabase Auth** - User authentication and authorization
* **Supabase Storage** - File storage for user uploads
* **Next.js API Routes** - Serverless functions for backend logic

### Integrations
* **Stripe** - Payment processing for membership fees
* **Resend** - Modern email service for notifications
* **Vercel** - Deployment and hosting platform

## 🛠️ Installation
To run Pulse.co.za locally, follow these steps:

1. Clone the repository:
    ```bash
    git clone https://github.com/your-username/pulse-gym-management.git
    cd pulse-gym-management
    ```

2. Install dependencies:
    ```bash
    npm install
    ```

3. Set up environment variables in a `.env.local` file:
    ```env
    # App Configuration
    NEXT_PUBLIC_APP_NAME="Pulse.co.za"
    NEXT_PUBLIC_APP_URL="http://localhost:3000"

    # Supabase Configuration
    NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
    NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
    SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

    # Stripe Configuration
    STRIPE_SECRET_KEY=your_stripe_secret_key
    NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
    STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

    # Email Configuration (optional)
    RESEND_API_KEY=your_resend_api_key
    ```

4. Set up Supabase:
    - Create a new Supabase project at [supabase.com](https://supabase.com)
    - Copy your project URL and anon key to the environment variables
    - Run the database migrations (to be provided in Phase 5)

5. Run the development server:
    ```bash
    npm run dev
    ```

6. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

## 🎯 Project Phases

This project is being developed in phases:

- **Phase 1**: Foundation & Branding ✅ (Current)
- **Phase 2**: Core Member Features (Check-in, Progress Tracking)
- **Phase 3**: Admin & Management Features
- **Phase 4**: Content & Public Website
- **Phase 5**: Database Setup & Migration

## 🤝 Contributing

We welcome contributions! Please feel free to submit a Pull Request.

## 📜 License
This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.

## 💬 Contact
For questions about Pulse.co.za gym management platform, please open an issue or contact the development team.

---

Built with 💪 for gym communities focused on self-growth and personal development.
