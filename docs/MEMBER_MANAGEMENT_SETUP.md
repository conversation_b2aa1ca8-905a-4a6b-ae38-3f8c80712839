# Member Management Setup Guide

This guide will help you set up and troubleshoot the member management functionality in Pulse.co.za.

## Quick Setup

1. **Run the setup script:**
   ```bash
   npm run setup
   ```

2. **Follow the prompts to enter your Supabase credentials**

3. **Restart your development server:**
   ```bash
   npm run dev
   ```

## Manual Setup

If you prefer to set up manually, create a `.env.local` file in your project root:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Optional: NextAuth Configuration
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000
```

## Database Setup

1. **Apply the schema:**
   - Run the SQL from `src/lib/supabase/schema.sql` in your Supabase SQL editor

2. **Apply admin policies:**
   - Run the SQL from `src/lib/supabase/admin-policies.sql` in your Supabase SQL editor

3. **Create an admin user:**
   - Sign up through your app or create a user in Supabase Auth
   - Update the user's profile in the `profiles` table:
     ```sql
     UPDATE profiles 
     SET role = 'admin' 
     WHERE email = '<EMAIL>';
     ```

## Troubleshooting

### "supabaseUrl is required" Error
- Make sure `NEXT_PUBLIC_SUPABASE_URL` is set in your `.env.local`
- Restart your development server after adding environment variables

### "Missing service role key" Error
- Make sure `SUPABASE_SERVICE_ROLE_KEY` is set in your `.env.local`
- This key is found in your Supabase project settings under "API"

### Members Not Loading
1. Check browser console for errors
2. Verify your admin user has `role = 'admin'` in the profiles table
3. Check that RLS policies are applied correctly

### Cannot Create Members
1. Verify `SUPABASE_SERVICE_ROLE_KEY` is correct
2. Check that the admin policies are applied
3. Look at the browser network tab for API errors

## Features

### Member Management
- ✅ View all members with pagination and search
- ✅ Create new members with email invitations
- ✅ Edit member details and membership status
- ✅ Delete members (with confirmation)
- ✅ View member statistics and attendance

### Member Data
- Basic info: name, email, phone
- Membership: type (basic/premium/vip), status (active/inactive/suspended)
- Activity: check-ins, attendance rate, total visits
- Dates: join date, last visit

### Admin Features
- Role-based access control
- Secure API endpoints with service role authentication
- Real-time data updates
- Mobile-responsive interface

## API Endpoints

### POST /api/admin/invite-member
Creates a new member account.

**Required fields:**
- `email` (string)
- `password` (string)
- `firstName` (string)
- `lastName` (string)

**Optional fields:**
- `phone` (string)
- `dateOfBirth` (string, ISO date)
- `avatarUrl` (string)
- `membershipType` ('basic' | 'premium' | 'vip')
- `membershipStatus` ('active' | 'inactive' | 'suspended')

## Security Notes

- The Service Role Key bypasses RLS and should never be exposed to the client
- Admin access is controlled through the `role` field in the profiles table
- All admin operations require authentication and role verification
- Environment variables are validated before API operations

## Support

If you encounter issues:

1. Check the browser console for errors
2. Verify your environment variables are set correctly
3. Ensure your database schema and policies are up to date
4. Check that your admin user has the correct role assigned
