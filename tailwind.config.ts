import type { Config } from "tailwindcss"

const config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        // Pulse.co.za Brand Colors - Pure Black with Neon Pink Only
        neon: {
          pink: "#FF10F0", // Primary neon pink
        },
        // Unified color scheme - Only neon pink accents
        member: {
          DEFAULT: "#FF10F0", // Neon pink for all sections
          light: "#FF5AF5",
          dark: "#CC0DC0",
        },
        admin: {
          DEFAULT: "#FF10F0", // Unified to neon pink
          light: "#FF5AF5",
          dark: "#CC0DC0",
        },
        blog: {
          DEFAULT: "#FF10F0", // Unified to neon pink
          light: "#FF5AF5",
          dark: "#CC0DC0",
        },
        marketing: {
          DEFAULT: "#FF10F0", // Unified to neon pink
          light: "#FF5AF5",
          dark: "#CC0DC0",
        },
        communication: {
          DEFAULT: "#FF10F0", // Unified to neon pink
          light: "#FF5AF5",
          dark: "#CC0DC0",
        },
      },
      fontFamily: {
        "heading": ["var(--font-dmsans)"],
        "default": ["var(--font-inter)"],
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        "loader": {
          "0%": {
            "transform": "rotate(0deg)",
          },
          "100%": {
            "transform": "rotate(360deg)",
          },
        },
        "pump": {
          "0%, 100%": { transform: "scale(0.8)" },
          "50%": { transform: "scale(1)" },
        },
        // Neon glow animations
        "neon-pulse": {
          "0%, 100%": {
            boxShadow: "0 0 5px #FF10F0, 0 0 10px #FF10F0, 0 0 15px #FF10F0",
            transform: "scale(1)"
          },
          "50%": {
            boxShadow: "0 0 10px #FF10F0, 0 0 20px #FF10F0, 0 0 30px #FF10F0",
            transform: "scale(1.02)"
          },
        },
        "neon-glow": {
          "0%": { boxShadow: "0 0 5px currentColor" },
          "50%": { boxShadow: "0 0 20px currentColor, 0 0 30px currentColor" },
          "100%": { boxShadow: "0 0 5px currentColor" },
        },
        "achievement-bounce": {
          "0%, 20%, 53%, 80%, 100%": { transform: "translate3d(0,0,0)" },
          "40%, 43%": { transform: "translate3d(0, -30px, 0)" },
          "70%": { transform: "translate3d(0, -15px, 0)" },
          "90%": { transform: "translate3d(0, -4px, 0)" },
        },
        "progress-fill": {
          "0%": { transform: "scaleX(0)" },
          "100%": { transform: "scaleX(1)" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "loader": "spin 0.5s linear infinite",
        "pump": "pump 1s cubic-bezier(0.4, 0, 0.6, 1) infinite",
        // Neon animations
        "neon-pulse": "neon-pulse 2s ease-in-out infinite",
        "neon-glow": "neon-glow 2s ease-in-out infinite",
        "achievement-bounce": "achievement-bounce 1s ease-in-out",
        "progress-fill": "progress-fill 1s ease-out forwards",
      },
    },
  },
  plugins: [require("tailwindcss-animate"), require('tailwind-scrollbar-hide')],
} satisfies Config

export default config