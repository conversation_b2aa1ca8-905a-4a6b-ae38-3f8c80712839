#!/usr/bin/env node

/**
 * Environment Setup Script for Pulse.co.za
 * This script helps set up the required environment variables
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function setupEnvironment() {
  console.log('🏋️  Welcome to Pulse.co.za Environment Setup!\n');
  
  console.log('This script will help you set up the required environment variables.');
  console.log('You can find these values in your Supabase project dashboard.\n');

  const envVars = {};

  // Supabase URL
  envVars.NEXT_PUBLIC_SUPABASE_URL = await question('Enter your Supabase Project URL: ');
  
  // Supabase Anon Key
  envVars.NEXT_PUBLIC_SUPABASE_ANON_KEY = await question('Enter your Supabase Anon Key: ');
  
  // Supabase Service Role Key
  console.log('\n⚠️  WARNING: The Service Role Key should be kept secret!');
  console.log('It will only be used on the server side for admin operations.');
  envVars.SUPABASE_SERVICE_ROLE_KEY = await question('Enter your Supabase Service Role Key: ');

  // Optional: NextAuth Secret
  const useNextAuth = await question('\nDo you want to set up NextAuth? (y/n): ');
  if (useNextAuth.toLowerCase() === 'y') {
    envVars.NEXTAUTH_SECRET = await question('Enter your NextAuth Secret (or press Enter to generate one): ');
    if (!envVars.NEXTAUTH_SECRET) {
      envVars.NEXTAUTH_SECRET = require('crypto').randomBytes(32).toString('hex');
      console.log('Generated NextAuth Secret:', envVars.NEXTAUTH_SECRET);
    }
    envVars.NEXTAUTH_URL = await question('Enter your NextAuth URL (default: http://localhost:3000): ') || 'http://localhost:3000';
  }

  // Create .env.local file
  const envContent = Object.entries(envVars)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n');

  const envPath = path.join(process.cwd(), '.env.local');
  
  try {
    fs.writeFileSync(envPath, envContent);
    console.log('\n✅ Environment file created successfully at .env.local');
    console.log('\n📋 Your environment variables:');
    console.log(envContent);
    
    console.log('\n🔒 Security Notes:');
    console.log('- Never commit .env.local to version control');
    console.log('- The Service Role Key should only be used on the server side');
    console.log('- Make sure .env.local is in your .gitignore file');
    
    console.log('\n🚀 Next Steps:');
    console.log('1. Restart your development server: npm run dev');
    console.log('2. Create an admin user in your Supabase dashboard');
    console.log('3. Set the admin user\'s role to "admin" in the profiles table');
    
  } catch (error) {
    console.error('\n❌ Error creating environment file:', error.message);
  }

  rl.close();
}

// Check if .env.local already exists
const envPath = path.join(process.cwd(), '.env.local');
if (fs.existsSync(envPath)) {
  console.log('⚠️  .env.local already exists!');
  rl.question('Do you want to overwrite it? (y/n): ', (answer) => {
    if (answer.toLowerCase() === 'y') {
      setupEnvironment();
    } else {
      console.log('Setup cancelled.');
      rl.close();
    }
  });
} else {
  setupEnvironment();
}
